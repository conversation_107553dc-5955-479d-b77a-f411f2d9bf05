import {
  Action,
  IPermission,
  Subject,
  OrganizationAction,
} from '../../types/permission.type';

export const getOrganizationFieldOfficerPermission = (
  userId: string,
  organizationId: string,
  clinifyId: string,
): IPermission[] => {
  return [
    /* User */
    {
      action: Action.ReadAny,
      subject: Subject.User,
    },
    {
      action: Action.ReadOwn,
      subject: Subject.Profile,
    },
    {
      action: Action.ReadOwn,
      subject: Subject.User,
      conditions: { user: { id: userId } },
    },
    /* Profile */
    {
      action: OrganizationAction.UpdateOwn,
      subject: Subject.PersonalInformation,
      conditions: { clinifyId },
    },
    {
      action: OrganizationAction.UpdateOwn,
      subject: Subject.BackgroundInformation,
      conditions: { clinifyId },
    },
    {
      action: OrganizationAction.CreateTeamAny,
      subject: Subject.Profile,
    },
    {
      action: OrganizationAction.DeleteTeamOwn,
      subject: Subject.Profile,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.ArchiveTeamOwn,
      subject: Subject.Profile,
      conditions: { organizationId },
    },
    /* Patient/Enrollee Registration */
    {
      action: OrganizationAction.CreateTeamAny,
      subject: Subject.PatientRegistration,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.ReadTeamAny,
      subject: Subject.PatientRegistration,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.UpdateTeamOwn,
      subject: Subject.PatientRegistration,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.DeleteTeamOwn,
      subject: Subject.PatientRegistration,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.ArchiveTeamOwn,
      subject: Subject.PatientRegistration,
      conditions: { organizationId },
    },
    /* Coverage Information */
    {
      action: Action.ReadAny,
      subject: Subject.CoverageInformation,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.CreateTeamAny,
      subject: Subject.CoverageInformation,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.UpdateTeamOwn,
      subject: Subject.CoverageInformation,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.DeleteTeamOwn,
      subject: Subject.CoverageInformation,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.ArchiveTeamOwn,
      subject: Subject.CoverageInformation,
      conditions: { organizationId },
    },
    /* Dependents */
    {
      action: Action.ReadAny,
      subject: Subject.Dependents,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.CreateTeamAny,
      subject: Subject.Dependents,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.UpdateTeamOwn,
      subject: Subject.Dependents,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.DeleteTeamOwn,
      subject: Subject.Dependents,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.ArchiveTeamOwn,
      subject: Subject.Dependents,
      conditions: { organizationId },
    },

    /* Personal Details */
    /* Personal Information */
    {
      action: Action.ReadTeamAny,
      subject: Subject.PersonalInformation,
    },
    {
      action: OrganizationAction.CreateTeamAny,
      subject: Subject.PersonalInformation,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.UpdateTeamOwn,
      subject: Subject.PersonalInformation,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.DeleteTeamOwn,
      subject: Subject.PersonalInformation,
      conditions: { organizationId },
    },
    /* Background Information */
    {
      action: Action.ReadTeamAny,
      subject: Subject.BackgroundInformation,
    },
    {
      action: OrganizationAction.CreateTeamAny,
      subject: Subject.BackgroundInformation,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.UpdateTeamOwn,
      subject: Subject.BackgroundInformation,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.DeleteTeamOwn,
      subject: Subject.BackgroundInformation,
      conditions: { organizationId },
    },

    /* Next Of Kin */
    {
      action: Action.ReadAny,
      subject: Subject.NextOfKin,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.CreateTeamAny,
      subject: Subject.NextOfKin,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.UpdateTeamOwn,
      subject: Subject.NextOfKin,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.DeleteTeamOwn,
      subject: Subject.NextOfKin,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.ArchiveTeamOwn,
      subject: Subject.NextOfKin,
      conditions: { organizationId },
    },
  ];
};
