/* eslint-disable max-lines */
import {
  Action,
  IPermission,
  OrganizationAction,
  Subject,
} from '../../types/permission.type';

export const getOrganizationAdminPermission = (
  userId: string,
  organizationId: string,
  clinifyId: string,
): IPermission[] => {
  return [
    /* User */
    {
      action: Action.ReadTeamAny,
      subject: Subject.User,
    },
    {
      action: Action.ReadTeamAny,
      subject: Subject.Profile,
    },
    {
      action: Action.ReadOwn,
      subject: Subject.User,
      conditions: { user: { id: userId } },
    },
    /* Profile */
    {
      action: OrganizationAction.CreateTeamAny,
      subject: Subject.Profile,
    },
    {
      action: OrganizationAction.UpdateOwn,
      subject: Subject.PersonalInformation,
      conditions: { clinifyId },
    },
    {
      action: OrganizationAction.UpdateOwn,
      subject: Subject.BackgroundInformation,
      conditions: { clinifyId },
    },
    {
      action: OrganizationAction.DeleteTeamAny,
      subject: Subject.Profile,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.ArchiveTeamAny,
      subject: Subject.Profile,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.DeleteTeamAny,
      subject: Subject.Staff,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.ArchiveTeamAny,
      subject: Subject.Staff,
      conditions: { organizationId },
    },
    /* Patient/Enrollee Registration */
    {
      action: OrganizationAction.CreateTeamAny,
      subject: Subject.PatientRegistration,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.ReadTeamAny,
      subject: Subject.PatientRegistration,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.UpdateTeamOwn,
      subject: Subject.PatientRegistration,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.DeleteTeamOwn,
      subject: Subject.PatientRegistration,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.ArchiveTeamOwn,
      subject: Subject.PatientRegistration,
      conditions: { organizationId },
    },
    /* Permission */
    {
      action: OrganizationAction.GrantTeamAny,
      subject: Subject.Permission,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.RevokeTeamAny,
      subject: Subject.Permission,
      conditions: { organizationId },
    },
    /* Hospital */
    {
      action: OrganizationAction.UpdateTeamAny,
      subject: Subject.Hospital,
      conditions: { organizationId },
    },
    /* Allergy */
    {
      action: OrganizationAction.ReadTeamAny,
      subject: Subject.Allergy,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.CreateTeamAny,
      subject: Subject.Allergy,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.UpdateTeamAny,
      subject: Subject.Allergy,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.DeleteTeamAny,
      subject: Subject.Allergy,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.ArchiveTeamAny,
      subject: Subject.Allergy,
      conditions: { organizationId },
    },
    /* Admission */
    {
      action: OrganizationAction.ReadTeamAny,
      subject: Subject.Admission,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.CreateTeamAny,
      subject: Subject.Admission,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.UpdateTeamAny,
      subject: Subject.Admission,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.DeleteTeamAny,
      subject: Subject.Admission,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.ArchiveTeamAny,
      subject: Subject.Admission,
      conditions: { organizationId },
    },
    /* BloodTransfusionSubRecord */
    {
      action: OrganizationAction.ReadTeamAny,
      subject: Subject.BloodTransfusionSubRecord,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.UpdateTeamAny,
      subject: Subject.BloodTransfusionSubRecord,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.DeleteTeamAny,
      subject: Subject.BloodTransfusionSubRecord,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.ArchiveTeamAny,
      subject: Subject.BloodTransfusionSubRecord,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.CreateTeamAny,
      subject: Subject.BloodTransfusionSubRecord,
      conditions: { organizationId },
    },
    /* TransferPatientSubRecord */
    {
      action: OrganizationAction.ReadTeamAny,
      subject: Subject.TransferPatientSubRecord,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.UpdateTeamAny,
      subject: Subject.TransferPatientSubRecord,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.DeleteTeamAny,
      subject: Subject.TransferPatientSubRecord,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.ArchiveTeamAny,
      subject: Subject.TransferPatientSubRecord,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.CreateTeamAny,
      subject: Subject.TransferPatientSubRecord,
      conditions: { organizationId },
    },
    /** DischargePatientSubRecord */
    {
      action: OrganizationAction.ReadTeamAny,
      subject: Subject.DischargePatientSubRecord,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.UpdateTeamAny,
      subject: Subject.DischargePatientSubRecord,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.DeleteTeamAny,
      subject: Subject.DischargePatientSubRecord,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.ArchiveTeamAny,
      subject: Subject.DischargePatientSubRecord,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.CreateTeamAny,
      subject: Subject.DischargePatientSubRecord,
      conditions: { organizationId },
    },
    /** AdmissionNoteSubRecord */
    {
      action: OrganizationAction.ReadTeamAny,
      subject: Subject.AdmissionNoteSubRecord,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.UpdateTeamAny,
      subject: Subject.AdmissionNoteSubRecord,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.DeleteTeamAny,
      subject: Subject.AdmissionNoteSubRecord,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.ArchiveTeamAny,
      subject: Subject.AdmissionNoteSubRecord,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.CreateTeamAny,
      subject: Subject.AdmissionNoteSubRecord,
      conditions: { organizationId },
    },
    /** InputOutputSubRecord */
    {
      action: OrganizationAction.ReadTeamAny,
      subject: Subject.InputOutputSubRecord,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.UpdateTeamAny,
      subject: Subject.InputOutputSubRecord,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.DeleteTeamAny,
      subject: Subject.InputOutputSubRecord,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.ArchiveTeamAny,
      subject: Subject.InputOutputSubRecord,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.CreateTeamAny,
      subject: Subject.InputOutputSubRecord,
      conditions: { organizationId },
    },
    /** AdmissionLinesSubRecord */
    {
      action: OrganizationAction.ReadTeamAny,
      subject: Subject.AdmissionLinesSubRecord,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.UpdateTeamAny,
      subject: Subject.AdmissionLinesSubRecord,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.DeleteTeamAny,
      subject: Subject.AdmissionLinesSubRecord,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.ArchiveTeamAny,
      subject: Subject.AdmissionLinesSubRecord,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.CreateTeamAny,
      subject: Subject.AdmissionLinesSubRecord,
      conditions: { organizationId },
    },
    /** Medication */
    {
      action: OrganizationAction.ReadTeamAny,
      subject: Subject.Medication,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.UpdateTeamAny,
      subject: Subject.Medication,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.DeleteTeamAny,
      subject: Subject.Medication,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.CreateTeamAny,
      subject: Subject.Medication,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.ArchiveTeamAny,
      subject: Subject.Medication,
      conditions: { organizationId },
    },
    /** DispenseMedication */
    {
      action: OrganizationAction.ReadTeamAny,
      subject: Subject.DispenseMedication,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.UpdateTeamAny,
      subject: Subject.DispenseMedication,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.DeleteTeamAny,
      subject: Subject.DispenseMedication,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.CreateTeamAny,
      subject: Subject.DispenseMedication,
      conditions: { organizationId },
    },
    /** Immunization */
    {
      action: OrganizationAction.ReadTeamAny,
      subject: Subject.Immunization,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.CreateTeamAny,
      subject: Subject.Immunization,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.UpdateTeamAny,
      subject: Subject.Immunization,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.DeleteTeamAny,
      subject: Subject.Immunization,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.ArchiveTeamAny,
      subject: Subject.Immunization,
      conditions: { organizationId },
    },
    /** Immunization Detail */
    {
      action: OrganizationAction.CreateTeamAny,
      subject: Subject.ImmunizationDetail,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.ReadTeamAny,
      subject: Subject.ImmunizationDetail,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.UpdateTeamAny,
      subject: Subject.ImmunizationDetail,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.DeleteTeamAny,
      subject: Subject.ImmunizationDetail,
      conditions: { organizationId },
    },
    /** Vitals */
    {
      action: OrganizationAction.ReadTeamAny,
      subject: Subject.Vitals,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.CreateTeamAny,
      subject: Subject.Vitals,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.UpdateTeamAny,
      subject: Subject.Vitals,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.DeleteTeamAny,
      subject: Subject.Vitals,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.ArchiveTeamAny,
      subject: Subject.Vitals,
      conditions: { organizationId },
    },
    /** Anthropometry */
    {
      action: OrganizationAction.ReadTeamAny,
      subject: Subject.VitalAnthropometry,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.CreateTeamAny,
      subject: Subject.VitalAnthropometry,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.UpdateTeamAny,
      subject: Subject.VitalAnthropometry,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.DeleteTeamAny,
      subject: Subject.VitalAnthropometry,
      conditions: { organizationId },
    },
    /** VitalBloodGlucose */
    {
      action: OrganizationAction.ReadTeamAny,
      subject: Subject.VitalBloodGlucose,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.CreateTeamAny,
      subject: Subject.VitalBloodGlucose,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.UpdateTeamAny,
      subject: Subject.VitalBloodGlucose,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.DeleteTeamAny,
      subject: Subject.VitalBloodGlucose,
      conditions: { organizationId },
    },
    /** VitalBloodPressure */
    {
      action: OrganizationAction.ReadTeamAny,
      subject: Subject.VitalBloodPressure,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.CreateTeamAny,
      subject: Subject.VitalBloodPressure,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.UpdateTeamAny,
      subject: Subject.VitalBloodPressure,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.DeleteTeamAny,
      subject: Subject.VitalBloodPressure,
      conditions: { organizationId },
    },
    /** VitalPulseRate */
    {
      action: OrganizationAction.ReadTeamAny,
      subject: Subject.VitalPulseRate,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.CreateTeamAny,
      subject: Subject.VitalPulseRate,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.UpdateTeamAny,
      subject: Subject.VitalPulseRate,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.DeleteTeamAny,
      subject: Subject.VitalPulseRate,
      conditions: { organizationId },
    },
    /** VitalRespiratoryRate */
    {
      action: OrganizationAction.ReadTeamAny,
      subject: Subject.VitalRespiratoryRate,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.CreateTeamAny,
      subject: Subject.VitalRespiratoryRate,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.UpdateTeamAny,
      subject: Subject.VitalRespiratoryRate,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.DeleteTeamAny,
      subject: Subject.VitalRespiratoryRate,
      conditions: { organizationId },
    },
    /** VitalTemperature */
    {
      action: OrganizationAction.ReadTeamAny,
      subject: Subject.VitalTemperature,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.CreateTeamAny,
      subject: Subject.VitalTemperature,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.UpdateTeamAny,
      subject: Subject.VitalTemperature,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.DeleteTeamAny,
      subject: Subject.VitalTemperature,
      conditions: { organizationId },
    },
    /** VitalUrineDipstick */
    {
      action: OrganizationAction.ReadTeamAny,
      subject: Subject.VitalUrineDipstick,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.CreateTeamAny,
      subject: Subject.VitalUrineDipstick,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.UpdateTeamAny,
      subject: Subject.VitalUrineDipstick,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.DeleteTeamAny,
      subject: Subject.VitalUrineDipstick,
      conditions: { organizationId },
    },
    /** VitalVisualAcuity */
    {
      action: OrganizationAction.ReadTeamAny,
      subject: Subject.VitalVisualAcuity,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.CreateTeamAny,
      subject: Subject.VitalVisualAcuity,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.UpdateTeamAny,
      subject: Subject.VitalVisualAcuity,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.DeleteTeamAny,
      subject: Subject.VitalVisualAcuity,
      conditions: { organizationId },
    },
    /** VitalPain */
    {
      action: OrganizationAction.ReadTeamAny,
      subject: Subject.VitalPain,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.CreateTeamAny,
      subject: Subject.VitalPain,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.UpdateTeamAny,
      subject: Subject.VitalPain,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.DeleteTeamAny,
      subject: Subject.VitalPain,
      conditions: { organizationId },
    },
    /** Surgery */
    {
      action: OrganizationAction.ReadTeamAny,
      subject: Subject.Surgery,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.CreateTeamAny,
      subject: Subject.Surgery,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.UpdateTeamAny,
      subject: Subject.Surgery,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.DeleteTeamAny,
      subject: Subject.Surgery,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.ArchiveTeamAny,
      subject: Subject.Surgery,
      conditions: { organizationId },
    },
    /** LabTest */
    {
      action: OrganizationAction.ReadTeamAny,
      subject: Subject.LabTest,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.CreateTeamAny,
      subject: Subject.LabTest,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.UpdateTeamAny,
      subject: Subject.LabTest,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.DeleteTeamAny,
      subject: Subject.LabTest,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.ArchiveTeamAny,
      subject: Subject.LabTest,
      conditions: { organizationId },
    },
    /** Radiology */
    {
      action: OrganizationAction.ReadTeamAny,
      subject: Subject.Radiology,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.CreateTeamAny,
      subject: Subject.Radiology,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.UpdateTeamAny,
      subject: Subject.Radiology,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.DeleteTeamAny,
      subject: Subject.Radiology,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.ArchiveTeamAny,
      subject: Subject.Radiology,
      conditions: { organizationId },
    },
    /** Consulation */
    {
      action: OrganizationAction.ReadTeamAny,
      subject: Subject.Consultation,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.CreateTeamAny,
      subject: Subject.Consultation,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.UpdateTeamAny,
      subject: Subject.Consultation,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.DeleteTeamAny,
      subject: Subject.Consultation,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.ArchiveTeamAny,
      subject: Subject.Consultation,
      conditions: { organizationId },
    },
    /* Profile Health */
    /* Gynaecologic History */
    {
      action: Action.ReadTeamAny,
      subject: Subject.GynaecologicHistory,
    },
    {
      action: OrganizationAction.CreateTeamAny,
      subject: Subject.GynaecologicHistory,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.UpdateTeamAny,
      subject: Subject.GynaecologicHistory,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.DeleteTeamAny,
      subject: Subject.GynaecologicHistory,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.ArchiveTeamAny,
      subject: Subject.GynaecologicHistory,
      conditions: { organizationId },
    },
    /* Pre Existing Condition */
    {
      action: Action.ReadTeamAny,
      subject: Subject.PreExistingCondition,
    },
    {
      action: OrganizationAction.CreateTeamAny,
      subject: Subject.PreExistingCondition,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.UpdateTeamAny,
      subject: Subject.PreExistingCondition,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.DeleteTeamAny,
      subject: Subject.PreExistingCondition,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.ArchiveTeamAny,
      subject: Subject.PreExistingCondition,
      conditions: { organizationId },
    },
    /* Past Surgical History */
    {
      action: Action.ReadTeamAny,
      subject: Subject.PastSurgicalHistory,
    },
    {
      action: OrganizationAction.CreateTeamAny,
      subject: Subject.PastSurgicalHistory,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.UpdateTeamAny,
      subject: Subject.PastSurgicalHistory,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.DeleteTeamAny,
      subject: Subject.PastSurgicalHistory,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.ArchiveTeamAny,
      subject: Subject.PastSurgicalHistory,
      conditions: { organizationId },
    },
    /* Obstetric History */
    {
      action: Action.ReadTeamAny,
      subject: Subject.ObstetricHistory,
    },
    {
      action: OrganizationAction.CreateTeamAny,
      subject: Subject.ObstetricHistory,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.UpdateTeamAny,
      subject: Subject.ObstetricHistory,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.DeleteTeamAny,
      subject: Subject.ObstetricHistory,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.ArchiveTeamAny,
      subject: Subject.ObstetricHistory,
      conditions: { organizationId },
    },
    /* Oncology History */
    {
      action: Action.ReadTeamAny,
      subject: Subject.OncologyHistory,
    },
    {
      action: OrganizationAction.CreateTeamAny,
      subject: Subject.OncologyHistory,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.UpdateTeamAny,
      subject: Subject.OncologyHistory,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.DeleteTeamAny,
      subject: Subject.OncologyHistory,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.ArchiveTeamAny,
      subject: Subject.OncologyHistory,
      conditions: { organizationId },
    },
    /* Family History */
    {
      action: Action.ReadTeamAny,
      subject: Subject.FamilyHistory,
    },
    {
      action: OrganizationAction.CreateTeamAny,
      subject: Subject.FamilyHistory,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.UpdateTeamAny,
      subject: Subject.FamilyHistory,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.DeleteTeamAny,
      subject: Subject.FamilyHistory,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.ArchiveTeamAny,
      subject: Subject.FamilyHistory,
      conditions: { organizationId },
    },
    /* Social History */
    {
      action: Action.ReadTeamAny,
      subject: Subject.SocialHistory,
    },
    {
      action: OrganizationAction.CreateTeamAny,
      subject: Subject.SocialHistory,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.UpdateTeamAny,
      subject: Subject.SocialHistory,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.DeleteTeamAny,
      subject: Subject.SocialHistory,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.ArchiveTeamAny,
      subject: Subject.SocialHistory,
      conditions: { organizationId },
    },
    /* Physical History */
    {
      action: Action.ReadTeamAny,
      subject: Subject.PhysicalHistory,
    },
    {
      action: OrganizationAction.CreateTeamAny,
      subject: Subject.PhysicalHistory,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.UpdateTeamAny,
      subject: Subject.PhysicalHistory,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.DeleteTeamAny,
      subject: Subject.PhysicalHistory,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.ArchiveTeamAny,
      subject: Subject.PhysicalHistory,
      conditions: { organizationId },
    },
    /* Disability */
    {
      action: Action.ReadTeamAny,
      subject: Subject.Disability,
    },
    {
      action: OrganizationAction.CreateTeamAny,
      subject: Subject.Disability,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.UpdateTeamAny,
      subject: Subject.Disability,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.DeleteTeamAny,
      subject: Subject.Disability,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.ArchiveTeamAny,
      subject: Subject.Disability,
      conditions: { organizationId },
    },
    /* Past Encounters */
    {
      action: Action.ReadTeamAny,
      subject: Subject.PastEncounters,
    },
    {
      action: OrganizationAction.CreateTeamAny,
      subject: Subject.PastEncounters,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.UpdateTeamAny,
      subject: Subject.PastEncounters,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.DeleteTeamAny,
      subject: Subject.PastEncounters,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.ArchiveTeamAny,
      subject: Subject.PastEncounters,
      conditions: { organizationId },
    },
    /* Coverage Information */
    {
      action: Action.ReadTeamAny,
      subject: Subject.CoverageInformation,
    },
    {
      action: OrganizationAction.CreateTeamAny,
      subject: Subject.CoverageInformation,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.UpdateTeamAny,
      subject: Subject.CoverageInformation,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.DeleteTeamAny,
      subject: Subject.CoverageInformation,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.ArchiveTeamAny,
      subject: Subject.CoverageInformation,
      conditions: { organizationId },
    },
    /* Dependents */
    {
      action: Action.ReadTeamAny,
      subject: Subject.Dependents,
    },
    {
      action: OrganizationAction.CreateTeamAny,
      subject: Subject.Dependents,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.UpdateTeamAny,
      subject: Subject.Dependents,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.DeleteTeamAny,
      subject: Subject.Dependents,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.ArchiveTeamAny,
      subject: Subject.Dependents,
      conditions: { organizationId },
    },

    /* Personal Details */
    /* Personal Information */
    {
      action: Action.ReadTeamAny,
      subject: Subject.PersonalInformation,
    },
    {
      action: OrganizationAction.CreateTeamAny,
      subject: Subject.PersonalInformation,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.UpdateTeamAny,
      subject: Subject.PersonalInformation,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.DeleteTeamAny,
      subject: Subject.PersonalInformation,
      conditions: { organizationId },
    },
    /* Background Information */
    {
      action: Action.ReadTeamAny,
      subject: Subject.BackgroundInformation,
    },
    {
      action: OrganizationAction.CreateTeamAny,
      subject: Subject.BackgroundInformation,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.UpdateTeamAny,
      subject: Subject.BackgroundInformation,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.DeleteTeamAny,
      subject: Subject.BackgroundInformation,
      conditions: { organizationId },
    },

    /* Next Of Kin */
    {
      action: Action.ReadTeamAny,
      subject: Subject.NextOfKin,
    },
    {
      action: OrganizationAction.CreateTeamAny,
      subject: Subject.NextOfKin,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.UpdateTeamAny,
      subject: Subject.NextOfKin,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.DeleteTeamAny,
      subject: Subject.NextOfKin,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.ArchiveTeamAny,
      subject: Subject.NextOfKin,
      conditions: { organizationId },
    },
    /** Investigation */
    {
      action: OrganizationAction.ReadTeamAny,
      subject: Subject.Investigation,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.CreateTeamAny,
      subject: Subject.Investigation,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.UpdateTeamAny,
      subject: Subject.Investigation,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.DeleteTeamAny,
      subject: Subject.Investigation,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.ArchiveTeamAny,
      subject: Subject.Investigation,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.CreateTeamAny,
      subject: Subject.InvestigationLabResult,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.UpdateTeamAny,
      subject: Subject.InvestigationLabResult,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.CreateTeamAny,
      subject: Subject.InvestigationRadiology,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.UpdateTeamAny,
      subject: Subject.InvestigationRadiology,
      conditions: { organizationId },
    },

    // ConsultationTreatmentPlan
    {
      action: OrganizationAction.ReadTeamAny,
      subject: Subject.ConsultationTreatmentPlan,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.CreateTeamAny,
      subject: Subject.ConsultationTreatmentPlan,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.DeleteTeamAny,
      subject: Subject.ConsultationTreatmentPlan,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.UpdateTeamAny,
      subject: Subject.ConsultationTreatmentPlan,
      conditions: { organizationId },
    },
    // SurgeryOperationNote
    {
      action: OrganizationAction.ReadTeamAny,
      subject: Subject.SurgeryOperationNote,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.CreateTeamAny,
      subject: Subject.SurgeryOperationNote,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.DeleteTeamAny,
      subject: Subject.SurgeryOperationNote,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.UpdateTeamAny,
      subject: Subject.SurgeryOperationNote,
      conditions: { organizationId },
    },
    // InvestigationAdditionalNote
    {
      action: OrganizationAction.ReadTeamAny,
      subject: Subject.InvestigationAdditionalNote,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.CreateTeamAny,
      subject: Subject.InvestigationAdditionalNote,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.DeleteTeamAny,
      subject: Subject.InvestigationAdditionalNote,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.UpdateTeamAny,
      subject: Subject.InvestigationAdditionalNote,
      conditions: { organizationId },
    },
    // Merge Records
    {
      action: OrganizationAction.ReadTeamAny,
      subject: Subject.MergeRecord,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.CreateTeamAny,
      subject: Subject.MergeRecord,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.DeleteTeamAny,
      subject: Subject.MergeRecord,
      conditions: { organizationId },
    },
    /** Medication Detail */
    {
      action: OrganizationAction.CreateTeamAny,
      subject: Subject.MedicationDetail,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.ReadTeamAny,
      subject: Subject.MedicationDetail,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.UpdateTeamAny,
      subject: Subject.MedicationDetail,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.DeleteTeamAny,
      subject: Subject.MedicationDetail,
      conditions: { organizationId },
    },
    /* Antenatal */
    {
      action: OrganizationAction.ReadTeamAny,
      subject: Subject.Antenatal,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.CreateTeamAny,
      subject: Subject.Antenatal,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.UpdateTeamAny,
      subject: Subject.Antenatal,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.DeleteTeamAny,
      subject: Subject.Antenatal,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.ArchiveTeamAny,
      subject: Subject.Antenatal,
      conditions: { organizationId },
    },
    /** Antenatal Detail */
    {
      action: OrganizationAction.CreateTeamAny,
      subject: Subject.AntenatalDetail,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.ReadTeamAny,
      subject: Subject.AntenatalDetail,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.UpdateTeamAny,
      subject: Subject.AntenatalDetail,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.DeleteTeamAny,
      subject: Subject.AntenatalDetail,
      conditions: { organizationId },
    },
    /** Partograph */
    {
      action: OrganizationAction.ReadTeamAny,
      subject: Subject.Partograph,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.CreateTeamAny,
      subject: Subject.Partograph,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.UpdateTeamAny,
      subject: Subject.Partograph,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.DeleteTeamAny,
      subject: Subject.Partograph,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.ArchiveTeamAny,
      subject: Subject.Partograph,
      conditions: { organizationId },
    },
    /** Postnatal */
    {
      action: OrganizationAction.ReadTeamAny,
      subject: Subject.Postnatal,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.CreateTeamAny,
      subject: Subject.Postnatal,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.UpdateTeamAny,
      subject: Subject.Postnatal,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.DeleteTeamAny,
      subject: Subject.Postnatal,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.ArchiveTeamAny,
      subject: Subject.Postnatal,
      conditions: { organizationId },
    },
    /** LabourAndDelivery */
    {
      action: OrganizationAction.ReadTeamAny,
      subject: Subject.LabourAndDelivery,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.CreateTeamAny,
      subject: Subject.LabourAndDelivery,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.UpdateTeamAny,
      subject: Subject.LabourAndDelivery,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.DeleteTeamAny,
      subject: Subject.LabourAndDelivery,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.ArchiveTeamAny,
      subject: Subject.LabourAndDelivery,
      conditions: { organizationId },
    },
    /** PreOperationChecklist */
    {
      action: OrganizationAction.ReadTeamAny,
      subject: Subject.PreOperationChecklist,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.CreateTeamAny,
      subject: Subject.PreOperationChecklist,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.UpdateTeamAny,
      subject: Subject.PreOperationChecklist,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.DeleteTeamAny,
      subject: Subject.PreOperationChecklist,
      conditions: { organizationId },
    },
    /** AnaesthesiaCheckList */
    {
      action: OrganizationAction.ReadTeamAny,
      subject: Subject.AnaesthesiaCheckList,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.CreateTeamAny,
      subject: Subject.AnaesthesiaCheckList,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.UpdateTeamAny,
      subject: Subject.AnaesthesiaCheckList,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.DeleteTeamAny,
      subject: Subject.AnaesthesiaCheckList,
      conditions: { organizationId },
    },
    /** NursingService */
    {
      action: OrganizationAction.ReadTeamAny,
      subject: Subject.NursingService,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.CreateTeamAny,
      subject: Subject.NursingService,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.UpdateTeamAny,
      subject: Subject.NursingService,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.DeleteTeamAny,
      subject: Subject.NursingService,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.ArchiveTeamAny,
      subject: Subject.NursingService,
      conditions: { organizationId },
    },
    /** PostOperationChecklist */
    {
      action: OrganizationAction.ReadTeamAny,
      subject: Subject.PostOperationChecklist,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.CreateTeamAny,
      subject: Subject.PostOperationChecklist,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.UpdateTeamAny,
      subject: Subject.PostOperationChecklist,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.DeleteTeamAny,
      subject: Subject.PostOperationChecklist,
      conditions: { organizationId },
    },
    /** PreChemoEducation */
    {
      action: OrganizationAction.ReadTeamAny,
      subject: Subject.PreChemoEducation,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.CreateTeamAny,
      subject: Subject.PreChemoEducation,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.UpdateTeamAny,
      subject: Subject.PreChemoEducation,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.DeleteTeamAny,
      subject: Subject.PreChemoEducation,
      conditions: { organizationId },
    },
    /** CancerScreening */
    {
      action: OrganizationAction.ReadTeamAny,
      subject: Subject.CancerScreening,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.CreateTeamAny,
      subject: Subject.CancerScreening,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.UpdateTeamAny,
      subject: Subject.CancerScreening,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.DeleteTeamAny,
      subject: Subject.CancerScreening,
      conditions: { organizationId },
    },
    /** Inventory */
    {
      action: OrganizationAction.ReadTeamAny,
      subject: Subject.Inventory,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.CreateTeamAny,
      subject: Subject.Inventory,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.UpdateTeamAny,
      subject: Subject.Inventory,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.DeleteTeamAny,
      subject: Subject.Inventory,
      conditions: { organizationId },
    },

    /** Nutritional History */
    {
      action: Action.ReadTeamAny,
      subject: Subject.NutritionalHistory,
    },
    {
      action: OrganizationAction.CreateTeamAny,
      subject: Subject.NutritionalHistory,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.UpdateTeamAny,
      subject: Subject.NutritionalHistory,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.DeleteTeamAny,
      subject: Subject.NutritionalHistory,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.ArchiveTeamAny,
      subject: Subject.NutritionalHistory,
      conditions: { organizationId },
    },
    /** Nutritional History Growth */
    {
      action: Action.ReadTeamAny,
      subject: Subject.NutritionalHistoryGrowth,
    },
    {
      action: OrganizationAction.CreateTeamAny,
      subject: Subject.NutritionalHistoryGrowth,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.UpdateTeamAny,
      subject: Subject.NutritionalHistoryGrowth,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.DeleteTeamAny,
      subject: Subject.NutritionalHistoryGrowth,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.ArchiveTeamAny,
      subject: Subject.NutritionalHistoryGrowth,
      conditions: { organizationId },
    },
    /** Developmental History */
    {
      action: Action.ReadTeamAny,
      subject: Subject.DevelopmentalHistory,
    },
    {
      action: OrganizationAction.CreateTeamAny,
      subject: Subject.DevelopmentalHistory,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.UpdateTeamAny,
      subject: Subject.DevelopmentalHistory,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.DeleteTeamAny,
      subject: Subject.DevelopmentalHistory,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.ArchiveTeamAny,
      subject: Subject.DevelopmentalHistory,
      conditions: { organizationId },
    },
    /** Bill Now */
    {
      action: Action.ReadTeamAny,
      subject: Subject.BillNow,
    },
    {
      action: OrganizationAction.CreateTeamAny,
      subject: Subject.BillNow,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.UpdateTeamAny,
      subject: Subject.BillNow,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.DeleteTeamAny,
      subject: Subject.BillNow,
      conditions: { organizationId },
    },
    /** Bill Later */
    {
      action: Action.ReadTeamAny,
      subject: Subject.BillLater,
    },
    {
      action: OrganizationAction.CreateTeamAny,
      subject: Subject.BillLater,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.UpdateTeamAny,
      subject: Subject.BillLater,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.DeleteTeamAny,
      subject: Subject.BillLater,
      conditions: { organizationId },
    },
    /** Consulation Oncology */
    {
      action: OrganizationAction.ReadTeamAny,
      subject: Subject.ConsultationOncology,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.CreateTeamAny,
      subject: Subject.ConsultationOncology,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.UpdateTeamAny,
      subject: Subject.ConsultationOncology,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.DeleteTeamAny,
      subject: Subject.ConsultationOncology,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.ArchiveTeamAny,
      subject: Subject.ConsultationOncology,
      conditions: { organizationId },
    },
    /** OncologyConsultationTreatmentPlan */
    {
      action: OrganizationAction.ReadTeamAny,
      subject: Subject.OncologyConsultationTreatmentPlan,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.CreateTeamAny,
      subject: Subject.OncologyConsultationTreatmentPlan,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.UpdateTeamAny,
      subject: Subject.OncologyConsultationTreatmentPlan,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.DeleteTeamAny,
      subject: Subject.OncologyConsultationTreatmentPlan,
      conditions: { organizationId },
    },
    /** Consulation Procedure */
    {
      action: OrganizationAction.ReadTeamAny,
      subject: Subject.ConsultationProcedure,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.CreateTeamAny,
      subject: Subject.ConsultationProcedure,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.UpdateTeamAny,
      subject: Subject.ConsultationProcedure,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.DeleteTeamAny,
      subject: Subject.ConsultationProcedure,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.ArchiveTeamAny,
      subject: Subject.ConsultationProcedure,
      conditions: { organizationId },
    },
  ];
};
