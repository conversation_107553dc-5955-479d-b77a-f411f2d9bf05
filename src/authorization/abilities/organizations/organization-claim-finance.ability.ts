/* eslint-disable max-lines */
import {
  Action,
  IPermission,
  Subject,
  OrganizationAction,
} from '../../types/permission.type';

export const getOrganizationClaimFinancePermission = (
  userId: string,
  organizationId: string,
  clinifyId: string,
): IPermission[] => {
  return [
    /* User */
    {
      action: Action.ReadAny,
      subject: Subject.User,
    },
    {
      action: Action.ReadOwn,
      subject: Subject.Profile,
    },
    {
      action: Action.ReadOwn,
      subject: Subject.User,
      conditions: { user: { id: userId } },
    },
    /* Profile */
    {
      action: OrganizationAction.UpdateOwn,
      subject: Subject.PersonalInformation,
      conditions: { clinifyId },
    },
    {
      action: OrganizationAction.UpdateOwn,
      subject: Subject.BackgroundInformation,
      conditions: { clinifyId },
    },
    {
      action: OrganizationAction.CreateTeamAny,
      subject: Subject.Profile,
    },
    {
      action: OrganizationAction.DeleteTeamOwn,
      subject: Subject.Profile,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.ArchiveTeamOwn,
      subject: Subject.Profile,
      conditions: { organizationId },
    },
    /* Patient/Enrollee Registration */
    {
      action: OrganizationAction.CreateTeamAny,
      subject: Subject.PatientRegistration,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.ReadTeamAny,
      subject: Subject.PatientRegistration,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.UpdateTeamOwn,
      subject: Subject.PatientRegistration,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.DeleteTeamOwn,
      subject: Subject.PatientRegistration,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.ArchiveTeamOwn,
      subject: Subject.PatientRegistration,
      conditions: { organizationId },
    },
    /* Coverage Information */
    {
      action: Action.ReadAny,
      subject: Subject.CoverageInformation,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.CreateTeamAny,
      subject: Subject.CoverageInformation,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.UpdateTeamOwn,
      subject: Subject.CoverageInformation,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.DeleteTeamOwn,
      subject: Subject.CoverageInformation,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.ArchiveTeamOwn,
      subject: Subject.CoverageInformation,
      conditions: { organizationId },
    },
    /* Dependents */
    {
      action: Action.ReadAny,
      subject: Subject.Dependents,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.CreateTeamAny,
      subject: Subject.Dependents,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.UpdateTeamOwn,
      subject: Subject.Dependents,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.DeleteTeamOwn,
      subject: Subject.Dependents,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.ArchiveTeamOwn,
      subject: Subject.Dependents,
      conditions: { organizationId },
    },

    /* Personal Details */
    /* Personal Information */
    {
      action: Action.ReadTeamAny,
      subject: Subject.PersonalInformation,
    },
    {
      action: OrganizationAction.CreateTeamAny,
      subject: Subject.PersonalInformation,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.UpdateTeamOwn,
      subject: Subject.PersonalInformation,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.DeleteTeamOwn,
      subject: Subject.PersonalInformation,
      conditions: { organizationId },
    },
    /* Background Information */
    {
      action: Action.ReadTeamAny,
      subject: Subject.BackgroundInformation,
    },
    {
      action: OrganizationAction.CreateTeamAny,
      subject: Subject.BackgroundInformation,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.UpdateTeamOwn,
      subject: Subject.BackgroundInformation,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.DeleteTeamOwn,
      subject: Subject.BackgroundInformation,
      conditions: { organizationId },
    },

    /* Next Of Kin */
    {
      action: Action.ReadAny,
      subject: Subject.NextOfKin,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.CreateTeamAny,
      subject: Subject.NextOfKin,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.UpdateTeamOwn,
      subject: Subject.NextOfKin,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.DeleteTeamOwn,
      subject: Subject.NextOfKin,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.ArchiveTeamOwn,
      subject: Subject.NextOfKin,
      conditions: { organizationId },
    },
    /* Allergy */
    {
      action: Action.ReadAny,
      subject: Subject.Allergy,
      conditions: { organizationId },
    },
    /* Admission */
    {
      action: OrganizationAction.ReadAny,
      subject: Subject.Admission,
      conditions: { organizationId },
    },
    /* BloodTransfusionSubRecord */
    {
      action: Action.ReadAny,
      subject: Subject.BloodTransfusionSubRecord,
      conditions: { organizationId },
    },
    /* TransferPatientSubRecord */
    {
      action: Action.ReadAny,
      subject: Subject.TransferPatientSubRecord,
      conditions: { organizationId },
    },
    /** DischargePatientSubRecord */
    {
      action: Action.ReadAny,
      subject: Subject.DischargePatientSubRecord,
      conditions: { organizationId },
    },
    /** AdmissionNoteSubRecord */
    {
      action: Action.ReadAny,
      subject: Subject.AdmissionNoteSubRecord,
      conditions: { organizationId },
    },
    /** InputOutputSubRecord */
    {
      action: Action.ReadAny,
      subject: Subject.InputOutputSubRecord,
      conditions: { organizationId },
    },
    /** AdmissionLinesSubRecord */
    {
      action: Action.ReadAny,
      subject: Subject.AdmissionLinesSubRecord,
      conditions: { organizationId },
    },
    /* Profile Health */
    /* Gynaecologic History */
    {
      action: Action.ReadAny,
      subject: Subject.GynaecologicHistory,
      conditions: { organizationId },
    },
    /* Pre Existing Condition */
    {
      action: Action.ReadAny,
      subject: Subject.PreExistingCondition,
      conditions: { organizationId },
    },
    /* Past Surgical History */
    {
      action: Action.ReadAny,
      subject: Subject.PastSurgicalHistory,
      conditions: { organizationId },
    },
    /* Obstetric History */
    {
      action: Action.ReadAny,
      subject: Subject.ObstetricHistory,
      conditions: { organizationId },
    },
    /* Oncology History */
    {
      action: Action.ReadAny,
      subject: Subject.OncologyHistory,
      conditions: { organizationId },
    },
    /* Nutritional History */
    {
      action: Action.ReadAny,
      subject: Subject.NutritionalHistory,
      conditions: { organizationId },
    },
    /* Nutritional History Growth */
    {
      action: Action.ReadAny,
      subject: Subject.NutritionalHistoryGrowth,
      conditions: { organizationId },
    },
    /* Developmental History */
    {
      action: Action.ReadAny,
      subject: Subject.DevelopmentalHistory,
      conditions: { organizationId },
    },
    /* Family History */
    {
      action: Action.ReadAny,
      subject: Subject.FamilyHistory,
      conditions: { organizationId },
    },
    /* Social History */
    {
      action: Action.ReadAny,
      subject: Subject.SocialHistory,
      conditions: { organizationId },
    },
    /* Physical History */
    {
      action: Action.ReadAny,
      subject: Subject.PhysicalHistory,
      conditions: { organizationId },
    },
    /* Disability */
    {
      action: Action.ReadAny,
      subject: Subject.Disability,
      conditions: { organizationId },
    },
    /* Past Encounters */
    {
      action: Action.ReadAny,
      subject: Subject.PastEncounters,
      conditions: { organizationId },
    },
    /** Medications */
    {
      action: OrganizationAction.ReadAny,
      subject: Subject.Medication,
      conditions: { organizationId },
    },
    /** DispenseMedication */
    {
      action: OrganizationAction.ReadAny,
      subject: Subject.DispenseMedication,
      conditions: { organizationId },
    },
    /** Immunization */
    {
      action: OrganizationAction.ReadAny,
      subject: Subject.Immunization,
      conditions: { organizationId },
    },
    /** Immunization Detail */
    {
      action: OrganizationAction.ReadAny,
      subject: Subject.ImmunizationDetail,
      conditions: { organizationId },
    },
    /** Vitals */
    {
      action: OrganizationAction.ReadAny,
      subject: Subject.Vitals,
      conditions: { organizationId },
    },
    /** Anthropometry */
    {
      action: OrganizationAction.ReadAny,
      subject: Subject.VitalAnthropometry,
      conditions: { organizationId },
    },
    /** VitalBloodGlucose */
    {
      action: OrganizationAction.ReadAny,
      subject: Subject.VitalBloodGlucose,
      conditions: { organizationId },
    },
    /** VitalBloodPressure */
    {
      action: OrganizationAction.ReadAny,
      subject: Subject.VitalBloodPressure,
      conditions: { organizationId },
    },
    /** VitalPulseRate */
    {
      action: OrganizationAction.ReadAny,
      subject: Subject.VitalPulseRate,
      conditions: { organizationId },
    },
    /** VitalRespiratoryRate */
    {
      action: OrganizationAction.ReadAny,
      subject: Subject.VitalRespiratoryRate,
      conditions: { organizationId },
    },
    /** VitalTemperature */
    {
      action: OrganizationAction.ReadAny,
      subject: Subject.VitalTemperature,
      conditions: { organizationId },
    },
    /** VitalUrineDipstick */
    {
      action: OrganizationAction.ReadAny,
      subject: Subject.VitalUrineDipstick,
      conditions: { organizationId },
    },
    /** VitalVisualAcuity */
    {
      action: OrganizationAction.ReadAny,
      subject: Subject.VitalVisualAcuity,
      conditions: { organizationId },
    },
    /** VitalPain */
    {
      action: OrganizationAction.ReadAny,
      subject: Subject.VitalPain,
      conditions: { organizationId },
    },
    /** Surgery */
    {
      action: OrganizationAction.ReadAny,
      subject: Subject.Surgery,
      conditions: { organizationId },
    },
    /** LabTest */
    {
      action: OrganizationAction.ReadAny,
      subject: Subject.LabTest,
      conditions: { organizationId },
    },
    /** Radiology */
    {
      action: OrganizationAction.ReadAny,
      subject: Subject.Radiology,
      conditions: { organizationId },
    },
    /** Consulation */
    {
      action: OrganizationAction.ReadAny,
      subject: Subject.Consultation,
      conditions: { organizationId },
    },
    /** Investigation */
    {
      action: OrganizationAction.ReadAny,
      subject: Subject.Investigation,
      conditions: { organizationId },
    },
    // ConsultationTreatmentPlan
    {
      action: OrganizationAction.ReadAny,
      subject: Subject.ConsultationTreatmentPlan,
      conditions: { organizationId },
    },
    // SurgeryOperationNote
    {
      action: OrganizationAction.ReadAny,
      subject: Subject.SurgeryOperationNote,
      conditions: { organizationId },
    },
    // InvestigationAdditionalNote
    {
      action: OrganizationAction.ReadAny,
      subject: Subject.InvestigationAdditionalNote,
      conditions: { organizationId },
    },
    /** Medication Detail */
    {
      action: OrganizationAction.ReadAny,
      subject: Subject.MedicationDetail,
      conditions: { organizationId },
    },
    /* Antenatal */
    {
      action: Action.ReadAny,
      subject: Subject.Antenatal,
      conditions: { organizationId },
    },
    /** Antenatal Detail */
    {
      action: OrganizationAction.ReadAny,
      subject: Subject.AntenatalDetail,
      conditions: { organizationId },
    },
    /* Partograph */
    {
      action: Action.ReadAny,
      subject: Subject.Partograph,
      conditions: { organizationId },
    },
    /* Postnatal */
    {
      action: Action.ReadAny,
      subject: Subject.Postnatal,
      conditions: { organizationId },
    },
    /* LabourAndDelivery */
    {
      action: Action.ReadAny,
      subject: Subject.LabourAndDelivery,
      conditions: { organizationId },
    },
    /* PreOperationChecklist */
    {
      action: Action.ReadAny,
      subject: Subject.PreOperationChecklist,
      conditions: { organizationId },
    },
    /* AnaesthesiaCheckList */
    {
      action: Action.ReadAny,
      subject: Subject.AnaesthesiaCheckList,
      conditions: { organizationId },
    },
    /** Inventory */
    {
      action: OrganizationAction.ReadAny,
      subject: Subject.Inventory,
      conditions: { organizationId },
    },
    /* NursingService */
    {
      action: OrganizationAction.ReadAny,
      subject: Subject.NursingService,
      conditions: { organizationId },
    },
    /* PostOperationChecklist */
    {
      action: OrganizationAction.ReadAny,
      subject: Subject.PostOperationChecklist,
      conditions: { organizationId },
    },
    /* PreChemoEducation */
    {
      action: OrganizationAction.ReadAny,
      subject: Subject.PreChemoEducation,
      conditions: { organizationId },
    },
    /* CancerScreening */
    {
      action: OrganizationAction.ReadAny,
      subject: Subject.CancerScreening,
      conditions: { organizationId },
    },
    /** RecordBill */
    {
      action: OrganizationAction.UpdateTeamAny,
      subject: Subject.RecordBill,
      conditions: { organizationId },
    },
    /** Bill Now */
    {
      action: Action.ReadTeamAny,
      subject: Subject.BillNow,
    },
    {
      action: OrganizationAction.CreateTeamAny,
      subject: Subject.BillNow,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.UpdateTeamAny,
      subject: Subject.BillNow,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.DeleteTeamAny,
      subject: Subject.BillNow,
      conditions: { organizationId },
    },
    /** Bill Later */
    {
      action: Action.ReadTeamAny,
      subject: Subject.BillLater,
    },
    {
      action: OrganizationAction.CreateTeamAny,
      subject: Subject.BillLater,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.UpdateTeamAny,
      subject: Subject.BillLater,
      conditions: { organizationId },
    },
    {
      action: OrganizationAction.DeleteTeamAny,
      subject: Subject.BillLater,
      conditions: { organizationId },
    },
    /** Consulation Oncology */
    {
      action: OrganizationAction.ReadAny,
      subject: Subject.ConsultationOncology,
      conditions: { organizationId },
    },
    /** OncologyConsultationTreatmentPlan */
    {
      action: OrganizationAction.ReadAny,
      subject: Subject.OncologyConsultationTreatmentPlan,
      conditions: { organizationId },
    },
    /** Consulation Procedure */
    {
      action: OrganizationAction.ReadAny,
      subject: Subject.ConsultationProcedure,
      conditions: { organizationId },
    },
  ];
};
