/* eslint-disable max-lines */
/* eslint-disable prefer-arrow/prefer-arrow-functions */
import { NotFoundException } from '@nestjs/common';
import cloneDeep from 'lodash.clonedeep';
import moment from 'moment';
import { DataSource, In, Repository, SelectQueryBuilder } from 'typeorm';
import {
  HmoClaimDateFilterType,
  HmoClaimFilterInput,
} from '../inputs/hmo-claim.filter.input';
import { HmoClaimModel } from '../models/hmo-claim.model';
import { HmoClaimResponse } from '../responses/hmo-claim.response';
import { queryDSWithSlave } from '@clinify/database';
import { ClaimsSummary } from '@clinify/hmo-claims/inputs/hmo-claims.input';
import { BenefitCategory } from '@clinify/hmo-providers/inputs/hmo-plan.input';
import { HospitalModel } from '@clinify/hospitals/models/hospital.model';
import { UserType } from '@clinify/shared/enums/users';
import {
  getRequiredRole,
  hmoRoleUtilizationValidation,
} from '@clinify/shared/helper';
import { ClaimStatus } from '@clinify/shared/hmo-providers/modules/leadway/leadway.interface';
import { RecordCreator } from '@clinify/shared/validators/filter.input';
import {
  validateHmoRecordArchiver,
  validClaimRemoval,
} from '@clinify/shared/validators/validate-record-mutation.validator';
import { ProfileModel } from '@clinify/users/models/profile.model';
import { takePaginatedResponses } from '@clinify/utils/pagination';

export interface IHmoClaimRepository extends Repository<HmoClaimModel> {
  this: Repository<HmoClaimModel>;

  baseQuery(
    query: SelectQueryBuilder<HmoClaimModel>,
    options: HmoClaimFilterInput,
    keywordConditions: string,
    skipProviderCheck?: boolean,
    mutator?: ProfileModel,
  ): Promise<HmoClaimResponse>;

  getHmoClaim(
    this: IHmoClaimRepository,
    dataSource: DataSource,
    mutator: ProfileModel,
    id: string,
  ): Promise<HmoClaimModel>;

  findByProfile(
    profileId: string,
    options: Partial<HmoClaimFilterInput>,
    mutator: ProfileModel,
  ): Promise<HmoClaimResponse>;

  findByHospital(
    dataSource: DataSource,
    hospitalId: string,
    options: Partial<HmoClaimFilterInput>,
    mutator: ProfileModel,
  ): Promise<HmoClaimResponse>;

  getClaimsSummary(
    dataSource: DataSource,
    profile: ProfileModel,
    options: Partial<HmoClaimFilterInput>,
  ): Promise<ClaimsSummary>;

  deleteHmoClaims(
    profile: ProfileModel,
    ids: string[],
  ): Promise<HmoClaimModel[]>;

  archiveHmoClaims(
    profile: ProfileModel,
    ids: string[],
    archive: boolean,
  ): Promise<HmoClaimModel[]>;
  getProvidersWithSubmittedClaims(
    dataSource: DataSource,
    profile: ProfileModel,
    filterInput: HmoClaimFilterInput,
  ): Promise<HospitalModel[]>;
}

export const CustomHmoClaimRepoMethods: Pick<
  IHmoClaimRepository,
  | 'baseQuery'
  | 'getHmoClaim'
  | 'findByProfile'
  | 'findByHospital'
  | 'getClaimsSummary'
  | 'deleteHmoClaims'
  | 'archiveHmoClaims'
  | 'getProvidersWithSubmittedClaims'
> = {
  async baseQuery(
    this: IHmoClaimRepository,
    query: SelectQueryBuilder<HmoClaimModel>,
    options: HmoClaimFilterInput,
    keywordConditions: string,
    skipProviderCheck?: boolean,
    mutator?: ProfileModel,
  ): Promise<HmoClaimResponse> {
    const {
      skip = 0,
      take = 10,
      dateRange,
      keyword,
      creator,
      status,
      hospitalId,
      providerId,
      providerType,
      flag,
      filterDateField,
      showCompleted,
      showNotCompleted,
      paymentModel,
    } = options;
    const columnForDateFilter = determineDateField(filterDateField);
    if (creator)
      query = query
        .withDeleted()
        .innerJoinAndSelect('hmoClaims.createdBy', 'createdBy')
        .andWhere(
          creator === RecordCreator.SELF
            ? 'createdBy.type = :patient'
            : 'createdBy.type != :patient',
          { patient: UserType.Patient },
        );

    if (!skipProviderCheck) {
      if (hospitalId) {
        query = query.andWhere('hmoClaims.hospital_id = :hospitalId', {
          hospitalId,
        });
      }

      if (providerId) {
        query = query.andWhere('hmoClaims.provider_id = :providerId', {
          providerId,
        });
      }
    }

    if (providerType) {
      query = query.andWhere('hospital.plan ILIKE :providerType', {
        providerType: `${providerType}%`,
      });
    }
    if (dateRange?.from) {
      query = query.andWhere(`(hmoClaims.${columnForDateFilter} >= :from)`, {
        from: dateRange.from,
      });
    }

    if (dateRange?.to) {
      query = query.andWhere(`(hmoClaims.${columnForDateFilter} < :to)`, {
        to: dateRange.to,
      });
    }

    if (flag) {
      if (flag === 'None') {
        query = query.andWhere('hmoClaims.flags IS NULL');
      } else {
        query = query.andWhere('hmoClaims.flags @> :flagJson', {
          flagJson: JSON.stringify([{ flag }]),
        });
      }
    }

    if (status) {
      if (status === 'Confirmed') {
        query = query.andWhere(
          '(hmoClaims.status ILIKE :status AND utilizations.confirmation = :confirmation)',
          { status: 'Unconfirmed', confirmation: true },
        );
      } else if (status === 'notprocessed') {
        query = query.andWhere(
          '(hmoClaims.status ILIKE :status1 OR hmoClaims.status ILIKE :status2)',
          { status1: 'Unconfirmed', status2: 'submitted' },
        );
      } else if (status === 'processing') {
        query = query.andWhere(
          `(
            hmoClaims.status NOT ILIKE :status1
            AND hmoClaims.status NOT ILIKE :status2
            AND hmoClaims.status NOT ILIKE :status3
          )`,
          { status1: 'Unconfirmed', status2: 'submitted', status3: '%paid%' },
        );
      } else {
        let _status = status;

        if (_status === 'partiallyprocessed') {
          _status = 'Partially Approved';
        } else if (_status === 'processed') {
          _status = 'Approved';
        }
        query = query.andWhere('hmoClaims.status ILIKE :status', {
          status: _status,
        });
      }
    }

    if (keyword)
      query = query.andWhere(keywordConditions, { keyword: `%${keyword}%` });

    // Add completion status filtering
    if (showCompleted && mutator?.id) {
      query = query.andWhere(
        `NOT EXISTS (
          SELECT 1 FROM pre_auth_utilisations u 
          WHERE u.hmo_claim = hmoClaims.id 
          AND NOT EXISTS (
            SELECT 1 FROM jsonb_array_elements(u.utilisation_status) AS status_item
            WHERE status_item->>'creatorId' = :mutatorId
          )
        )`,
        { mutatorId: mutator.id },
      );
    }

    if (showNotCompleted && mutator?.id) {
      query = query.andWhere(
        `EXISTS (
          SELECT 1 FROM pre_auth_utilisations u 
          WHERE u.hmo_claim = hmoClaims.id 
          AND NOT EXISTS (
            SELECT 1 FROM jsonb_array_elements(u.utilisation_status) AS status_item
            WHERE status_item->>'creatorId' = :mutatorId
          )
        )`,
        { mutatorId: mutator.id },
      );
    }

    if (paymentModel) {
      query = query.andWhere('utilizations.paymentModel = :paymentModel', {
        paymentModel,
      });
    }

    query = query
      .orderBy('hmoClaims.createdDate', 'DESC')
      .skip(skip)
      .take(take);

    const response = await query.getManyAndCount();

    return new HmoClaimResponse(...takePaginatedResponses(response, take));
  },

  async getHmoClaim(
    this: IHmoClaimRepository,
    dataSource: DataSource,
    mutator: ProfileModel,
    id: string,
  ): Promise<HmoClaimModel> {
    const HmoClaim = await this.findOneOrFail({
      where: { id },
      relations: ['utilizations', 'provider', 'profile', 'profile.details'],
      withDeleted: true,
      order: {
        utilizations: {
          type: 'ASC',
        },
      },
    }).catch(() => {
      throw new NotFoundException('HMO Claim Not Found');
    });
    if (mutator.hmoId) {
      const hmoProviderRoles = await queryDSWithSlave(
        dataSource,
        `
          SELECT type
          FROM profiles
          WHERE hmo_id = $1
            AND profiles.deleted_date IS NULL
          GROUP BY type
        `,
        [mutator.hmoId],
      );
      const availableVettingGroups = hmoProviderRoles.map((v) => v.type);
      HmoClaim.utilizations = hmoRoleUtilizationValidation(
        HmoClaim.utilizations,
        mutator,
        availableVettingGroups,
      );
      if (!HmoClaim.utilizations.length) {
        throw new NotFoundException('HMO Claim Not Found');
      }
    }
    return HmoClaim;
  },

  async findByProfile(
    this: IHmoClaimRepository,
    profileId: string,
    options: Partial<HmoClaimFilterInput>,
    mutator: ProfileModel,
  ): Promise<HmoClaimResponse> {
    const query = this.createQueryBuilder('hmoClaims')
      .withDeleted()
      .leftJoinAndSelect('hmoClaims.provider', 'provider')
      .leftJoinAndSelect('hmoClaims.hospital', 'hospital')
      .leftJoinAndSelect('hmoClaims.utilizations', 'utilizations')
      .where('hmoClaims.profile_id = :profileId', { profileId })
      .andWhere('hmoClaims.archived = :archived', {
        archived: !!options.archive,
      });
    if (mutator.hmoId) {
      query.andWhere('(provider.id = :hmoId)', { hmoId: mutator.hmoId });
    }

    const keywordConditions = `
      ("hmoClaims".pa_code ILIKE :keyword OR
      "hmoClaims".submitted_by ILIKE :keyword OR
      "hmoClaims".creator_name ILIKE :keyword OR
      "hmoClaims".priority ILIKE :keyword OR
      "hmoClaims".service_type ILIKE :keyword OR
      "hmoClaims".service_name ILIKE :keyword OR
      "hmoClaims".claim_id ILIKE :keyword OR
      utilizations.category ILIKE :keyword OR
      utilizations.pa_code ILIKE :keyword OR
      "hmoClaims".diagnosis::text ILIKE :keyword OR
      provider.name ILIKE :keyword OR
      utilizations.type ILIKE :keyword OR
      hospital.name ILIKE :keyword OR
      hmoClaims.visitId ILIKE :keyword OR
      hmoClaims.claimId ILIKE :keyword OR
      hmoClaims.batchNumber ILIKE :keyword OR
      hmoClaims.enrolleeNumber ILIKE :keyword
      )
    `;

    const res = await this.baseQuery(
      query,
      options,
      keywordConditions,
      !!mutator.hmoId,
      mutator,
    );

    return res;
  },

  async findByHospital(
    this: IHmoClaimRepository,
    dataSource: DataSource,
    hospitalId: string,
    options: Partial<HmoClaimFilterInput>,
    mutator: ProfileModel,
  ): Promise<HmoClaimResponse> {
    const query = this.createQueryBuilder('hmoClaims')
      .leftJoinAndSelect('hmoClaims.profile', 'profile')
      .leftJoinAndSelect('hmoClaims.utilizations', 'utilizations')
      .leftJoinAndSelect('utilizations.transferFund', 'transferFund')
      .leftJoinAndSelect('transferFund.createdBy', 'TransferFundCreatedBy')
      .leftJoinAndSelect('hmoClaims.provider', 'provider')
      .leftJoinAndSelect('hmoClaims.hospital', 'hospital')
      .where('hmoClaims.archived = :archived', {
        archived: !!options.archive,
      });
    if (mutator.hmoId) {
      if (
        [UserType.ClaimAgent, UserType.ClaimAgentHOD].includes(
          mutator.type as UserType,
        )
      ) {
        return new HmoClaimResponse([], 0);
      }
      query.andWhere('hmoClaims.providerId = :providerId', {
        providerId: mutator.hmoId,
      });
      query.andWhere(
        '(LOWER(hmoClaims.status) NOT IN (:...excludedStatuses))',
        {
          excludedStatuses: [ClaimStatus.Draft.toLowerCase(), 'deleted'],
        },
      );
      const hmoProviderRoles = await queryDSWithSlave(
        dataSource,
        `
          SELECT type
          FROM profiles
          WHERE hmo_id = $1
            AND profiles.deleted_date IS NULL
          GROUP BY type
        `,
        [mutator.hmoId],
      );
      const availableVettingGroups = hmoProviderRoles.map((v) => v.type);
      const requiredRole = getRequiredRole(
        availableVettingGroups,
        mutator.type as UserType,
      );

      switch (mutator.type) {
        case UserType.ClaimAudit:
        case UserType.ClaimAuditHOD:
        case UserType.ClaimAdmin:
        case UserType.ClaimFinance:
        case UserType.ClaimAccount:
        case UserType.ClaimReviewerHOD:
          query.andWhere(
            `(EXISTS (
              SELECT 1
              FROM jsonb_array_elements(utilizations.utilisation_status) AS status_element
              WHERE 
                status_element ->> 'vettingGroup' = '${requiredRole}' AND
                status_element->>'status' = 'Approved'
            ))`,
          );
          break;
        case UserType.ClaimReviewer:
          query.andWhere(
            `(EXISTS (
              SELECT 1
              FROM jsonb_array_elements(utilizations.utilisation_status) AS status_element
              WHERE 
                status_element ->> 'vettingGroup' = '${requiredRole}'
            ))`,
          );
          break;
        case UserType.ClaimOfficer:
        case UserType.ClaimOfficerHOD: {
          const hasClaimConfirmation = await queryDSWithSlave(
            dataSource,
            `
              SELECT 1
              FROM profiles
              WHERE type = $1
                AND hmo_id = $2
                AND profiles.deleted_date IS NULL
              GROUP BY type
            `,
            [UserType.ClaimConfirmation, mutator.hmoId],
          );
          if (hasClaimConfirmation.length) {
            query.andWhere('(utilizations.confirmation = :confirmation)', {
              confirmation: true,
            });
          }
          break;
        }
        default:
          break;
      }

      if (
        ['processing', 'partiallyprocessed', 'processed'].includes(
          options.status,
        )
      ) {
        const statusRequiredRole = getRequiredRole(
          availableVettingGroups,
          UserType.ClaimAccount,
        );
        if (options.status === 'processing') {
          query.andWhere(
            `
              NOT utilizations.utilisation_status @> '[{"vettingGroup": "${statusRequiredRole}"}]'
            `,
          );
        } else {
          query.andWhere(
            `
              utilizations.utilisation_status @> '[{"vettingGroup": "${statusRequiredRole}", "status": "Approved"}]'
            `,
          );
        }
      }

      if (options.hospitalId && !options.providerInsight) {
        query.andWhere('(hmoClaims.hospital_id = :hospitalId)', {
          hospitalId: options.hospitalId,
        });
      }
      if (options.providerInsight) {
        query.andWhere('hmoClaims.hospital_id = :hospitalId', {
          hospitalId,
        });
      }
    } else if (mutator.isPartnerProfile && mutator.partnerId) {
      query.andWhere('(hospital.partnerId = :partnerId)', {
        partnerId: mutator.partnerId,
      });
      if (options.hospitalId && !options.providerInsight) {
        query.andWhere('(hmoClaims.hospital_id = :hospitalId)', {
          hospitalId: options.hospitalId,
        });
      }
      if (options.providerInsight) {
        query.andWhere('hmoClaims.hospital_id = :hospitalId', {
          hospitalId,
        });
      }
    } else {
      query.andWhere('hmoClaims.hospital_id = :hospitalId', { hospitalId });
    }

    const keywordConditions = `
      ("hmoClaims".pa_code ILIKE :keyword OR
      "hmoClaims".submitted_by ILIKE :keyword OR
      "hmoClaims".priority ILIKE :keyword OR
      "hmoClaims".service_type ILIKE :keyword OR
      "hmoClaims".service_name ILIKE :keyword OR
      "hmoClaims".creator_name ILIKE :keyword OR
      "hmoClaims".claim_id ILIKE :keyword OR
      utilizations.category ILIKE :keyword OR
      utilizations.pa_code ILIKE :keyword OR
      "hmoClaims".diagnosis::text ILIKE :keyword OR
      provider.name ILIKE :keyword OR
      profile.full_name ILIKE :keyword OR
      profile.clinify_id ILIKE :keyword OR
      utilizations.type ILIKE :keyword OR
      hospital.name ILIKE :keyword OR
      hmoClaims.visitId ILIKE :keyword OR
      hmoClaims.claimId ILIKE :keyword OR
      hmoClaims.batchNumber ILIKE :keyword OR
      hmoClaims.enrolleeNumber ILIKE :keyword)
    `;

    const res = await this.baseQuery(
      query,
      options,
      keywordConditions,
      !!mutator.hmoId,
      mutator,
    );
    if (mutator.hmoId) {
      const hmoProviderRoles = await queryDSWithSlave(
        dataSource,
        `
          SELECT type
          FROM profiles
          WHERE hmo_id = $1
            AND profiles.deleted_date IS NULL
          GROUP BY type
        `,
        [mutator.hmoId],
      );
      const availableVettingGroups = hmoProviderRoles.map((v) => v.type);
      res.list = res.list
        .filter((claim) => {
          claim.utilizations = hmoRoleUtilizationValidation(
            claim.utilizations,
            mutator,
            availableVettingGroups,
          );
          return claim.utilizations.length;
        })
        .filter((preauth) => preauth.utilizations.length);
    }
    return res;
  },

  async getClaimsSummary(
    dataSource: DataSource,
    profile: ProfileModel,
    options: Partial<HmoClaimFilterInput>,
  ): Promise<ClaimsSummary> {
    const {
      providerId,
      status,
      archive = false,
      keyword,
      dateRange,
      profileId,
      providerInsight,
      hospitalId,
      flag,
      providerType,
      filterDateField,
      paymentModel,
    } = options;
    const columnForDateFilter = determineDateField(filterDateField);

    let _status = status;
    if (_status === 'partiallyprocessed') {
      _status = 'Partially Approved';
    } else if (_status === 'processed') {
      _status = 'Approved';
    }

    let statusWhereCondition = `hmo_claims.status ILIKE '${_status}'`;

    if (status === 'Confirmed') {
      statusWhereCondition = `
        hmo_claims.status ILIKE 'Unconfirmed'
        AND pre_auth_utilisations.confirmation = 't'
      `;
    } else if (status === 'notprocessed') {
      statusWhereCondition = `
        hmo_claims.status ILIKE 'Unconfirmed'
        OR hmo_claims.status ILIKE 'submitted'
      `;
    } else if (status === 'processing') {
      statusWhereCondition = `
        hmo_claims.status NOT ILIKE 'Unconfirmed'
        AND hmo_claims.status NOT ILIKE 'submitted'
        AND hmo_claims.status NOT ILIKE '%paid%'
      `;
    }

    let utilizationStatusWhereCondition = '';

    if (
      profile.hmoId &&
      ['processing', 'partiallyprocessed', 'processed'].includes(status)
    ) {
      const hmoProviderRoles = await queryDSWithSlave(
        dataSource,
        `
          SELECT type
          FROM profiles
          WHERE hmo_id = $1
            AND profiles.deleted_date IS NULL
          GROUP BY type
        `,
        [profile.hmoId],
      );
      const availableVettingGroups = hmoProviderRoles.map((v) => v.type);

      const statusRequiredRole = getRequiredRole(
        availableVettingGroups,
        UserType.ClaimAccount,
      );
      if (options.status === 'processing') {
        utilizationStatusWhereCondition = `
          NOT pre_auth_utilisations.utilisation_status @> '[{"vettingGroup": "${statusRequiredRole}"}]'
        `;
      } else {
        utilizationStatusWhereCondition = `
          pre_auth_utilisations.utilisation_status @> '[{"vettingGroup": "${statusRequiredRole}", "status": "Approved"}]'
        `;
      }
    }

    const flagClause = (flag: string | null | undefined): string => {
      if (!flag) return '';
      if (flag === 'None') {
        return 'AND hmo_claims.flags IS NULL';
      } else {
        return `AND hmo_claims.flags @> '[{"flag": "${flag}"}]'`;
      }
    };

    const paymentModelClause = (
      paymentModel: BenefitCategory | null | undefined,
    ): string => {
      if (!paymentModel) return '';
      return `AND pre_auth_utilisations.payment_model = '${paymentModel}'`;
    };

    const result = await queryDSWithSlave(
      dataSource,
      `
      SELECT
        COUNT(distinct hmo_claims.id) AS "totalClaims",
        COUNT(distinct hmo_claims.id) FILTER(WHERE hmo_claims.status ILIKE 'Approved') AS "totalApprovedClaims",
        COUNT(distinct hmo_claims.id) FILTER(WHERE hmo_claims.status ILIKE 'Rejected') AS "totalRejectedClaims",
        COUNT(distinct hmo_claims.id) FILTER(WHERE hmo_claims.status ILIKE 'Paid') AS "totalPaidClaims",
        SUM(pre_auth_utilisations.quantity::float * pre_auth_utilisations.price::float) AS "totalClaimsAmount",
        SUM(pre_auth_utilisations.quantity::float * pre_auth_utilisations.price::float) 
            FILTER(WHERE hmo_claims.status = 'Approved') AS "totalApprovedClaimsAmount",
        SUM(pre_auth_utilisations.quantity::float * pre_auth_utilisations.price::float) 
            FILTER(WHERE hmo_claims.status = 'Rejected') AS "totalRejectedClaimsAmount",
        SUM(CASE
            WHEN pre_auth_utilisations.percentage_covered IS NOT NULL
                THEN ((pre_auth_utilisations.percentage_covered / 100) 
                          * (pre_auth_utilisations.quantity::float * pre_auth_utilisations.price::float))
            ELSE pre_auth_utilisations.quantity::float * pre_auth_utilisations.price::float
           END
           ) FILTER(WHERE hmo_claims.status = 'Paid') AS "totalPaidClaimsAmount"
        FROM hmo_claims
        LEFT JOIN profiles ON hmo_claims.profile_id = profiles.id
        LEFT JOIN hmo_providers ON hmo_claims.provider_id = hmo_providers.id
        INNER JOIN pre_auth_utilisations ON hmo_claims.id = pre_auth_utilisations.hmo_claim
        LEFT JOIN hospitals ON hmo_claims.hospital_id = hospitals.id
        WHERE hmo_claims.archived = '${archive ? 'yes' : 'no'}'
        ${
          providerInsight && profile.hmoId
            ? `AND hmo_claims.provider_id = '${
                profile.hmoId
              }' AND hmo_claims.status NOT IN ('draft', 'deleted') ${
                !!hospitalId
                  ? `AND hmo_claims.hospital_id = '${hospitalId}'`
                  : ''
              }`
            : `AND hmo_claims.hospital_id = '${profile.hospitalId}'`
        }
        ${!!providerId ? `AND hmo_claims.provider_id = '${providerId}'` : ''}
        ${flagClause(flag)}
        ${paymentModelClause(paymentModel)}
        ${!!status ? `AND (${statusWhereCondition})` : ''}
        ${
          !!utilizationStatusWhereCondition
            ? `AND ${utilizationStatusWhereCondition}`
            : ''
        }
        ${!!profileId ? `AND hmo_claims.profile_id = '${profileId}'` : ''}
        ${!!providerType ? `AND hospitals.plan ILIKE '${providerType}%'` : ''}
        ${
          dateRange?.from
            ? `AND hmo_claims.${columnForDateFilter} >= '${moment(
                dateRange.from,
              ).startOf('day')}'`
            : ''
        }
        ${
          dateRange?.to
            ? `AND hmo_claims.${columnForDateFilter} < '${moment(
                dateRange.to,
              ).endOf('day')}'`
            : ''
        }
        ${
          !!keyword
            ? `
          AND (hmo_claims.pa_code ILIKE '%${keyword}%' OR
          hmo_claims.submitted_by ILIKE '%${keyword}%' OR
          hmo_claims.priority ILIKE '%${keyword}%' OR
          hmo_claims.service_type ILIKE '%${keyword}%' OR
          hmo_claims.service_name ILIKE '%${keyword}%' OR
          hmo_claims.creator_name ILIKE '%${keyword}%' OR
          hmo_claims.claim_id ILIKE '%${keyword}%' OR
          hmo_claims.diagnosis::text ILIKE '%${keyword}%' OR
          hmo_providers.name ILIKE '%${keyword}%' OR
          profiles.full_name ILIKE '%${keyword}%' OR
          profiles.clinify_id ILIKE '%${keyword}%'
          OR( EXISTS (SELECT 1
              FROM
                pre_auth_utilisations
              WHERE
                pre_auth_utilisations.hmo_claim = hmo_claims.id
                AND (
                  pre_auth_utilisations.category ILIKE '%${keyword}%'
                  OR pre_auth_utilisations.pa_code ILIKE '%${keyword}%'
                  OR pre_auth_utilisations.type ILIKE '%${keyword}%'
                )
            )
          )
          )
         `
            : ''
        }
    `,
    );

    return result[0];
  },

  async deleteHmoClaims(
    this: IHmoClaimRepository,
    profile: ProfileModel,
    ids: string[],
  ): Promise<HmoClaimModel[]> {
    const claims = await this.find({ where: { id: In(ids) } });
    const validResources = validClaimRemoval(profile, claims);
    if (validResources.length)
      await this.remove(
        cloneDeep(validResources).map((v) => ({
          ...v,
          deletedBy: {
            id: profile.id,
            fullName: profile.fullName,
            entityId: v.id,
          },
        })),
      );
    return validResources;
  },

  async archiveHmoClaims(
    this: IHmoClaimRepository,
    profile: ProfileModel,
    ids: string[],
    archive: boolean,
  ): Promise<HmoClaimModel[]> {
    const items = await this.find({
      relations: ['utilizations'],
      where: { id: In(ids) },
    });
    const validResources = validateHmoRecordArchiver(
      profile,
      items,
    ) as HmoClaimModel[];
    const validIds = validResources.map((v) => v.id);
    if (!validIds.length) return [];
    await this.createQueryBuilder('hmoClaims')
      .update(HmoClaimModel)
      .set({ archived: archive, updatedDate: () => 'updated_date' })
      .whereInIds(validIds)
      .execute();

    return validResources.map((v) => ({ ...v, archived: archive }));
  },

  async getProvidersWithSubmittedClaims(
    this: IHmoClaimRepository,
    dataSource: DataSource,
    mutator: ProfileModel,
    filterInput: HmoClaimFilterInput,
  ): Promise<HospitalModel[]> {
    const { status, archive, profileId, dateRange, keyword, filterDateField } =
      filterInput;
    if (!mutator.hmoId) return [];
    const columnForDateFilter = determineDateField(filterDateField);
    const paramters: string[] = [];
    if (keyword) {
      paramters.push(`%${keyword}%`);
    }
    return queryDSWithSlave(
      dataSource,
      `SELECT DISTINCT hospitals.id,
                hospitals.name,
                hospitals.address,
                hospitals.support_mail AS "supportMail",
                hospitals.support_phone_number AS "hospitalSupportPhoneNumber"
       FROM hmo_claims
       INNER JOIN profiles ON hmo_claims.profile_id = profiles.id
                INNER JOIN hospitals ON hmo_claims.hospital_id = hospitals.id AND hmo_claims.provider_id = '${
                  mutator.hmoId
                }'
       WHERE hmo_claims.archived = '${archive ? 'yes' : 'no'}'
           ${
             !!status
               ? `AND hmo_claims.status ILIKE '${status}'`
               : `
                    AND hmo_claims.status != 'draft'
                    `
           }
           ${!!profileId ? `AND hmo_claims.profile_id = '${profileId}'` : ''}
           ${
             dateRange?.from
               ? `AND hmo_claims.${columnForDateFilter} >= '${moment(
                   dateRange.from,
                 ).startOf('day')}'`
               : ''
           }
           ${
             dateRange?.to
               ? `AND hmo_claims.${columnForDateFilter} <= '${moment(
                   dateRange.to,
                 ).endOf('day')}'`
               : ''
           }
           ${
             filterInput.hospitalId
               ? `AND hmo_claims.hospital_id = '${filterInput.hospitalId}'`
               : ''
           }
           ${
             !!keyword
               ? `
                        AND (hmo_claims.pa_code ILIKE $1 OR
                        hmo_claims.submitted_by ILIKE $1 OR
                        hmo_claims.priority ILIKE $1 OR
                        hmo_claims.service_type ILIKE $1 OR
                        hmo_claims.service_name ILIKE $1 OR
                        hmo_claims.creator_name ILIKE $1 OR
                        hmo_claims.claim_id ILIKE $1 OR
                        hmo_claims.diagnosis::text ILIKE $1 OR
                        profiles.full_name ILIKE $1 OR
                        profiles.clinify_id ILIKE $1 OR
                        hmo_claims.visit_id ILIKE $1 OR
                        hmo_claims.batch_number ILIKE $1 OR
                        hmo_claims."enrolleeNumber" ILIKE $1
                        OR( EXISTS (SELECT 1
                            FROM
                              pre_auth_utilisations
                            WHERE
                              pre_auth_utilisations.hmo_claim = hmo_claims.id
                              AND (
                                pre_auth_utilisations.category ILIKE $1
                                OR pre_auth_utilisations.pa_code ILIKE $1
                                OR pre_auth_utilisations.type ILIKE $1
                              )
                          )
                        )
                      )`
               : ''
           }`,
      paramters,
    );
  },
};

function determineDateField(t: HmoClaimDateFilterType) {
  switch (t) {
    case HmoClaimDateFilterType.ClaimDate:
      return 'claim_date';
    case HmoClaimDateFilterType.CreatedDate:
      return 'created_date';
    case HmoClaimDateFilterType.SubmissionDate:
      return 'submit_date_time';
    default:
      return 'claim_date';
  }
}
