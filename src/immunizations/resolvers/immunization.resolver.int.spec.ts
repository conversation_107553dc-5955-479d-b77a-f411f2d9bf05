import { EventEmitter2 } from '@nestjs/event-emitter';
import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { DataSource, EntityManager } from 'typeorm';
import { ImmunizationResolver } from './immunization.resolver';
import { NotificationsService } from '../../notifications/services/notifications.service';
import { ImmunizationService } from '../services/immunization.service';
import {
  billDetailsFactory,
  billFactory,
} from '@clinify/__mocks__/factories/bill.factory';
import { hospitalFactory } from '@clinify/__mocks__/factories/hospital.factory';
import { preauthorizationDetailsFactory } from '@clinify/__mocks__/factories/preauthorizationdetails.factory';
import { profileFactory } from '@clinify/__mocks__/factories/profile.factory';
import { PermissionModel } from '@clinify/authorization/models/permission.model';
import { PermissionService } from '@clinify/authorization/services/permission.service';
import { HmoClaimService } from '@clinify/hmo-claims/services/hmo-claim.service';
import { PreauthorizationDetailsModel } from '@clinify/preauthorization-details/models/preauthorization-details.model';
import { PreauthorizationDetailsService } from '@clinify/preauthorization-details/services/preauthorization-details.service';
import { ProfileModel } from '@clinify/users/models/profile.model';
import { hmoClaimFactory } from '@mocks/factories/hmo-claim.factory';
import {
  immunizationDetailFactory,
  immunizationFactory,
} from '@mocks/factories/immunization.factory';
import { mockUser } from '@mocks/factories/user.factory';

const user = mockUser;
const clinifyId = 'ckufure';
const immunizationData = immunizationFactory.build();
const profile = profileFactory.build();
const hmoClaimData = hmoClaimFactory.build();

const MockProfileRepository = {
  findOne: jest.fn(() => user.defaultProfile),
};

const ImmunizationserviceMock = {
  saveImmunization: jest.fn(() => immunizationData),
  updateImmunization: jest.fn(() => immunizationData),
  deleteImmunization: jest.fn(() => [immunizationData]),
  getOneImmunization: jest.fn(() => immunizationData),
  archiveImmunization: jest.fn(() => [immunizationData]),
  concealImmunizationAdditionalNote: jest.fn(() => immunizationData),
  linkBillToImmunization: jest.fn(() => immunizationData),
  saveImmunizationDetail: jest.fn(),
  updateImmunizationDetail: jest.fn(),
  deleteImmunizationDetail: jest.fn(),
};

const ManagerMock = {
  findOneOrFail: jest.fn(() => user.defaultProfile),
};

const preauthorizationDetailsRepoMock = {
  find: jest.fn(() => [preauthorizationDetailsFactory.build()]),
};

const dsMock = {
  getRepository: jest.fn(() => ({
    extend: jest.fn(),
  })),
  transaction: jest.fn((cb) => cb(ManagerMock)),
  manager: ManagerMock,
};

const pubSubMock = {
  publish: jest.fn(),
  asyncIterator: jest.fn(),
};

const mockEventEmitter = {
  emit: jest.fn(),
};

const mockHmoClaimService = {
  getHmoClaim: jest.fn(() => hmoClaimData),
};

describe('ImmunizationResolver', () => {
  let resolver: ImmunizationResolver;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ImmunizationResolver,
        ImmunizationService,
        PermissionService,
        {
          provide: getRepositoryToken(PermissionModel),
          useValue: {},
        },
        PreauthorizationDetailsService,
        {
          provide: ImmunizationService,
          useValue: ImmunizationserviceMock,
        },
        {
          provide: getRepositoryToken(ProfileModel),
          useValue: MockProfileRepository,
        },
        {
          provide: EntityManager,
          useValue: ManagerMock,
        },
        {
          provide: DataSource,
          useValue: dsMock,
        },
        {
          provide: getRepositoryToken(PreauthorizationDetailsModel),
          useValue: preauthorizationDetailsRepoMock,
        },
        {
          provide: 'PUB_SUB',
          useValue: pubSubMock,
        },
        {
          provide: EventEmitter2,
          useValue: mockEventEmitter,
        },
        {
          provide: NotificationsService,
          useValue: {
            handleNoticationEvent: jest.fn(),
          },
        },
        {
          provide: HmoClaimService,
          useValue: mockHmoClaimService,
        },
      ],
    }).compile();

    resolver = module.get<ImmunizationResolver>(ImmunizationResolver);
    jest.clearAllMocks();
  });

  it('addImmunization(): should save immunization', async () => {
    const immunizationInput = immunizationData;
    delete immunizationInput.profile;
    const response = await resolver.addImmunization(profile, immunizationInput);
    expect(ImmunizationserviceMock.saveImmunization).toHaveBeenCalledWith(
      profile,
      immunizationInput,
    );
    expect(response).toEqual(immunizationData);
  });

  it('addImmunization(): should save immunization with bill', async () => {
    const immunizationInput = immunizationData;
    delete immunizationInput.profile;
    const bill = billFactory.build();
    const serviceResponse = {
      ...immunizationData,
      bill,
    };
    ImmunizationserviceMock.saveImmunization = jest.fn(() => serviceResponse);

    const response = await resolver.addImmunization(profile, immunizationInput);
    expect(ImmunizationserviceMock.saveImmunization).toHaveBeenCalledWith(
      profile,
      immunizationInput,
    );
    expect(response).toEqual(serviceResponse);
    expect(pubSubMock.publish).toBeCalledWith('OrgBillAdded', {
      OrgBillAdded: bill,
    });
    expect(pubSubMock.publish).toHaveBeenLastCalledWith('BillingEvent', {
      billing: serviceResponse?.createdBy,
      BillingEvent: 'Added',
    });
  });

  it('addImmunizationSubsHandler() should trigger ImmunizationAdded subscription', () => {
    resolver.addImmunizationSubsHandler(clinifyId, 'hospital-id');
    expect(pubSubMock.asyncIterator).toHaveBeenCalledWith('ImmunizationAdded');
  });

  it('getOneImmunization(): should get one immunization', async () => {
    const immunizationInput = immunizationData;
    delete immunizationInput.profile;
    const clinifyId = 'fake-id';
    const response = await resolver.immunization(
      profile,
      immunizationInput.id,
      clinifyId,
    );
    expect(ImmunizationserviceMock.getOneImmunization).toHaveBeenCalledWith(
      profile,
      immunizationInput.id,
    );
    expect(response).toEqual(immunizationData);
  });

  it('updateImmunization(): should update immunization', async () => {
    const immunizationInput = immunizationData;
    delete immunizationInput.profile;
    const response = await resolver.updateImmunization(
      profile,
      immunizationInput,
      immunizationInput.id,
    );
    expect(ImmunizationserviceMock.updateImmunization).toHaveBeenCalledWith(
      profile,
      immunizationInput,
    );
    expect(response).toEqual(immunizationData);
  });

  it('updateImmunization(): should update immunization with bill data', async () => {
    const immunizationInput = {
      ...immunizationData,
      clinifyId: 'clinify-id',
      serviceDetails: [
        {
          type: 'Service Type',
          name: 'Service Name',
          quantity: '2',
          pricePerUnit: '3000',
        },
      ],
    };
    delete immunizationInput.profile;
    const response = await resolver.updateImmunizationBill(
      profile,
      immunizationInput,
      immunizationInput.id,
    );
    expect(ImmunizationserviceMock.updateImmunization).toHaveBeenCalledWith(
      profile,
      {
        id: immunizationInput.id,
        clinifyId: 'clinify-id',
        serviceDetails: immunizationInput.serviceDetails,
      },
      true,
    );
    expect(response).toEqual(immunizationData);
  });

  it('updateImmunizationSubsHandler() should trigger ImmunizationUpdated subscription', () => {
    resolver.updateImmunizationSubsHandler(clinifyId);
    expect(pubSubMock.asyncIterator).toHaveBeenCalledWith(
      'ImmunizationUpdated',
    );
  });

  it('immunizationEventHandler() should trigger ImmunizationEvent subscription', () => {
    resolver.immunizationEventsHandler(clinifyId);
    expect(pubSubMock.asyncIterator).toHaveBeenCalledWith('ImmunizationEvent');
  });

  it('deleteImmunization(): should delete immunization', async () => {
    const immunizationInput = immunizationData;
    delete immunizationInput.profile;
    const response = await resolver.deleteImmunizations(
      profile,
      [immunizationInput.id],
      clinifyId,
    );
    expect(ImmunizationserviceMock.deleteImmunization).toHaveBeenCalledWith(
      profile,
      [immunizationInput.id],
    );
    expect(response).toEqual([immunizationData]);
  });

  it('deleteImmunizationSubsHandler() should trigger ImmunizationDeleted subscription', () => {
    resolver.removeImmunizationSubsHandler(clinifyId);
    expect(pubSubMock.asyncIterator).toHaveBeenCalledWith(
      'ImmunizationRemoved',
    );
  });

  it('archiveImmunizations(): should call archive immunization service', async () => {
    const immunizationInput = immunizationData;
    delete immunizationInput.profile;
    const response = await resolver.archiveImmunizations(
      profile,
      [immunizationInput.id],
      true,
      clinifyId,
    );
    expect(ImmunizationserviceMock.archiveImmunization).toHaveBeenCalledWith(
      profile,
      [immunizationInput.id],
      true,
    );
    expect(response).toEqual([immunizationData]);
  });

  it('archiveImmunizations(): can trigger ImmunizatioUnarchived service', async () => {
    const immunizationInput = immunizationData;
    delete immunizationInput.profile;
    const response = await resolver.archiveImmunizations(
      profile,
      [immunizationInput.id],
      false,
      clinifyId,
    );
    expect(ImmunizationserviceMock.archiveImmunization).toHaveBeenCalledWith(
      profile,
      [immunizationInput.id],
      false,
    );
    expect(pubSubMock.publish).toHaveBeenCalledWith('ImmunizationUnarchived', {
      ImmunizationUnarchived: response,
    });
    expect(response).toEqual([immunizationData]);
  });

  it('unarchiveImmunizationSubsHandler() should trigger ImmunizationUnarchived subscription', () => {
    resolver.unarchiveImmunizationSubsHandler(clinifyId);
    expect(pubSubMock.asyncIterator).toHaveBeenCalledWith(
      'ImmunizationUnarchived',
    );
  });

  it('archiveImmunizationSubsHandler() should trigger ImmunizationArchived subscription', () => {
    resolver.archiveImmunizationSubsHandler(clinifyId);
    expect(pubSubMock.asyncIterator).toHaveBeenCalledWith(
      'ImmunizationArchived',
    );
  });

  it('concealImmunizationAdditionalNote() should call concealImmunizationAdditionalNote from services', async () => {
    await resolver.concealImmunizationAdditionalNote(
      profile,
      'immunization-id',
      true,
    );

    expect(
      ImmunizationserviceMock.concealImmunizationAdditionalNote,
    ).toHaveBeenCalledWith(profile, 'immunization-id', true);
  });

  it('getAdditionalNote() should resolve additionalNote', () => {
    const immunization = immunizationData;
    immunization.concealAdditionalNote = true;
    immunization.additionalNote = 'additional note';
    const currentUser = user;
    user.defaultProfile.type = 'Patient';

    const additionalNote = resolver.getAdditionalNote(
      currentUser?.defaultProfile,
      immunization,
    );
    expect(additionalNote).toEqual(null);
  });

  it('getBill() should get immunization bills', () => {
    const newHospitals = hospitalFactory.buildList(2);
    const newBill = billDetailsFactory.build();
    const profile = profileFactory.build();
    const immunizationBills = resolver.getBill(
      {
        ...profile,
        hospital: newHospitals[0],
        hospitalId: newHospitals[0].id,
      },
      {
        ...immunizationData,
        profile,
        hospital: newHospitals[0],
        hospitalId: newHospitals[0].id,
        createdBy: {
          ...immunizationData.createdBy,
          hospital: newHospitals[0],
          hospitalId: profile.hospitalId,
        },
        details: immunizationData?.details?.map((detail) => ({
          detail,
          billing: newBill,
        })),
      },
    );

    expect(immunizationBills[0]).toStrictEqual(newBill);
  });

  it('getBill() should get immunization bills', () => {
    const newHospitals = hospitalFactory.buildList(2);
    const newBill = billDetailsFactory.build();
    const immunizationBills = resolver.getBill(
      {
        ...profile,
        hospitalId: newHospitals[0].id,
        defaultProfile: {
          ...profile,
          hospital: newHospitals[0],
          hospitalId: newHospitals[0].id,
        },
      },
      {
        ...immunizationData,
        profile: profileFactory.build(),
        profileId: profileFactory.build().id,
        hospital: newHospitals[1],
        hospitalId: newHospitals[1].id,
        createdBy: {
          ...immunizationData.createdBy,
          hospital: newHospitals[1],
          hospitalId: profile.hospitalId,
        },
        billing: newBill,
      },
    );

    expect(immunizationBills).toStrictEqual(null);
  });

  it('getHmoClaim() should call getRecordHmoClaim service', async () => {
    const response = await resolver.getHmoClaim(profile, {
      ...immunizationData,
      hospitalId: profile.hospitalId,
      hmoClaimId: 'id',
    });
    expect(profile.hospitalId).toBeTruthy();
    expect(profile.hospitalId.length).toBeGreaterThan(10);
    expect(mockHmoClaimService.getHmoClaim).toHaveBeenCalledWith(profile, 'id');
    expect(response).toEqual(hmoClaimData);
  });

  it('getHmoClaim() should not call getRecordHmoClaim service', async () => {
    const response = await resolver.getHmoClaim(profile, immunizationData);
    expect(profile.hospitalId).toBeTruthy();
    expect(profile.hospitalId.length).toBeGreaterThan(10);
    expect(mockHmoClaimService.getHmoClaim).not.toHaveBeenCalled();
    expect(response).toEqual(null);
  });

  it('saveImmunizationDetail(): should call saveImmunizationDetail service method', async () => {
    const mutator = profileFactory.build();
    const input = immunizationDetailFactory.build();
    const response = {
      ...input,
      immunization: {
        id: 'immunization-id',
        createdBy: mutator,
      },
      bill: billFactory.build(),
    };

    ImmunizationserviceMock.saveImmunizationDetail = jest.fn(() => response);

    await resolver.saveImmunizationDetail(
      mutator,
      'immunization-id',
      input,
      'clinify-id',
    );

    expect(
      ImmunizationserviceMock.saveImmunizationDetail,
    ).toHaveBeenLastCalledWith('immunization-id', input, mutator);
    expect(pubSubMock.publish).toHaveBeenCalledWith('ImmunizationEvent', {
      immunization: { profile: { clinifyId: 'clinify-id' } },
      ImmunizationEvent: 'Updated',
    });
    expect(pubSubMock.publish).toHaveBeenCalledWith('ImmunizationDetailAdded', {
      ImmunizationDetailAdded: response,
    });
    expect(pubSubMock.publish).toHaveBeenCalledWith('ImmunizationUpdated', {
      ImmunizationUpdated: response.immunization,
    });
  });

  it('updateImmunizationDetail(): should call updateImmunizationDetail service method', async () => {
    const mutator = profileFactory.build();
    const input = immunizationDetailFactory.build();
    const response = {
      ...input,
      immunization: {
        id: 'immunization-id',
        createdBy: mutator,
        profileId: 'profile-id',
      },
    };

    ImmunizationserviceMock.updateImmunizationDetail = jest.fn(() => response);

    await resolver.updateImmunizationDetail(
      mutator,
      input,
      'immunization-detail-id',
      'clinify-id',
    );

    expect(
      ImmunizationserviceMock.updateImmunizationDetail,
    ).toHaveBeenLastCalledWith('immunization-detail-id', input, mutator);
    expect(pubSubMock.publish).toHaveBeenCalledWith('ImmunizationEvent', {
      profileId: 'profile-id',
      ImmunizationEvent: 'Updated',
    });
    expect(pubSubMock.publish).toHaveBeenCalledWith(
      'ImmunizationDetailUpdated',
      {
        ImmunizationDetailUpdated: response,
      },
    );
  });

  it('deleteImmunizationDetail(): should call deleteImmunizationDetail service method', async () => {
    const mutator = profileFactory.build();
    const response = {
      ...immunizationDetailFactory.build(),
      immunization: {
        id: 'immunization-id',
        createdBy: mutator,
        profileId: 'profile-id',
        hospitalId: 'hospital-id',
      },
      billing: billDetailsFactory.build(),
    };
    ImmunizationserviceMock.deleteImmunizationDetail = jest.fn(() => response);

    await resolver.deleteImmunizationDetail(
      mutator,
      'immunization-detail-id',
      'clinify-id',
    );

    expect(pubSubMock.publish).toHaveBeenCalledWith('ImmunizationEvent', {
      profileId: 'profile-id',
      ImmunizationEvent: 'Updated',
    });
    expect(pubSubMock.publish).toHaveBeenCalledWith(
      'ImmunizationDetailRemoved',
      {
        ImmunizationDetailRemoved: response,
      },
    );
    expect(pubSubMock.publish).toHaveBeenCalledWith('ImmunizationUpdated', {
      ImmunizationUpdated: response.immunization,
    });
    expect(pubSubMock.publish).toHaveBeenCalledWith('OrgBillItemRemoved', {
      OrgBillItemRemoved: [response.billing],
      profileId: 'profile-id',
      hospitalId: 'hospital-id',
    });
  });

  it('addImmunizationDetailSubsHandler(): should trigger ImmunizationDetailAdded subscription', () => {
    resolver.addImmunizationDetailSubsHandler('profile-id');

    expect(pubSubMock.asyncIterator).toHaveBeenLastCalledWith(
      'ImmunizationDetailAdded',
    );
  });

  it('updateImmunizationDetailSubHandler(): should trigger ImmunizationDetailUpdated subscription', () => {
    resolver.updateImmunizationDetailSubHandler('profile-id');

    expect(pubSubMock.asyncIterator).toHaveBeenLastCalledWith(
      'ImmunizationDetailUpdated',
    );
  });

  it('deleteImmunizationDetailSubHandler(): should trigger ImmunizationDetailRemoved subscription', () => {
    resolver.deleteImmunizationDetailSubHandler('profile-id');

    expect(pubSubMock.asyncIterator).toHaveBeenLastCalledWith(
      'ImmunizationDetailRemoved',
    );
  });
});
