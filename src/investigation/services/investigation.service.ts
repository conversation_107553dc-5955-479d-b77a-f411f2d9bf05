/* eslint-disable max-lines */
import {
  ForbiddenException,
  forwardRef,
  Inject,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { DataSource, EntityManager } from 'typeorm';
import { v4 as generateUUID } from 'uuid';
import { HmoClaimService } from '../../hmo-claims/services/hmo-claim.service';
import { AdditionalNoteModel } from '../models/investigation-note.model';
import { InvestigationModel } from '../models/investigation.model';
import { IInvestigationRepository } from '../repositories/investigation.repository';
import { InvestigationResponse } from '../responses/investigation.response';
import { AdditionalNoteInput } from '../validator/additional-note-input';
import {
  InvestigationFilterInput,
  LabResultTrendFilterInput,
} from '../validator/investigation-filter.input';
import {
  InvestigationInput,
  InvestigationWithLabInput,
  InvestigationWithRadiologyInput,
  NewInvestigationInput,
  NewInvestigationWithLabResult,
  NewInvestigationWithRadioResult,
} from '../validator/investigation.input';
import { LabResultInput } from '../validator/lab-result.input';
import { RadiologyResultInput } from '../validator/radiology-result-input';
import { HospitalUserType } from '@clinify/authentication/validators/registration.input';
import { BillModel } from '@clinify/bills/models/bill.model';
import { BillService } from '@clinify/bills/services/bill.service';
import {
  inTransaction,
  customDSSerializeInTransaction,
} from '@clinify/database';
import { FacilityPreferenceModel } from '@clinify/facility-preferences/models/facility-preference.model';
import { HospitalModel } from '@clinify/hospitals/models/hospital.model';
import { ServiceType } from '@clinify/shared/enums/bill';
import {
  InvestigationRequestType,
  InvestigationStatus,
} from '@clinify/shared/enums/investigation';
import { UserType } from '@clinify/shared/enums/users';
import { MAILER } from '@clinify/shared/mailer/constants';
import { IMessage } from '@clinify/shared/mailer/mailer.interface';
import { MailerService } from '@clinify/shared/mailer/mailer.service';
import mailerTemplate from '@clinify/shared/mailer/mailer.template';
import { mailDocService } from '@clinify/shared/services/mail-doc.service';
import { ServiceDetailInput } from '@clinify/shared/validators/service-detail.input';
import { validateCreation } from '@clinify/shared/validators/validate-record-mutation.validator';
import { ProfileModel } from '@clinify/users/models/profile.model';
import { UserModel } from '@clinify/users/models/user.model';
import { IProfileRepository } from '@clinify/users/repositories/profile.repository';
import {
  getBillableStatusOf,
  handleSubBillOnCreate,
  handleSubForMultipleBill,
} from '@clinify/utils/helpers/billing.util';
import {
  cacheType,
  updateOverviewCacheOnCreate,
} from '@clinify/utils/redis/overview-cache';

@Injectable()
export class InvestigationService {
  constructor(
    @InjectRepository(InvestigationModel)
    public repository: IInvestigationRepository,
    @InjectRepository(ProfileModel)
    private readonly profileRepository: IProfileRepository,
    @InjectRepository(InvestigationModel)
    private readonly investigationRepository: IInvestigationRepository,
    @Inject(EntityManager) readonly entityManager: EntityManager,
    private billService: BillService,
    private dataSource: DataSource,
    @Inject(forwardRef(() => HmoClaimService))
    private hmoClaimService: HmoClaimService,
    @Inject(MAILER) private readonly mailerService: MailerService,
    @Inject(forwardRef(() => mailDocService))
    private mailDocService: mailDocService,
  ) {}

  async saveInvestigation(
    mutator: ProfileModel,
    investigation: NewInvestigationInput,
    additionalNoteId?: string,
  ): Promise<InvestigationModel> {
    const { external } = investigation;
    let externalHospital: HospitalModel;
    const profile = await this.profileRepository.findOne({
      where: {
        clinifyId: investigation.clinifyId,
      },
    });
    if (external) {
      externalHospital = await this.entityManager.findOneOrFail(HospitalModel, {
        where: {
          id: investigation.externalHospital,
        },
      });
    }

    validateCreation(profile, investigation);
    const { hospital } = mutator;

    let radiologyContrastConfirmation = false;
    if (hospital?.id) {
      const facilityPreference = await this.repository.manager
        .createQueryBuilder()
        .select('preference.radiologyContrastConfirmation')
        .from(FacilityPreferenceModel, 'preference')
        .where('preference.hospitalId = :hospitalId', {
          hospitalId: hospital.id,
        })
        .getRawOne();
      radiologyContrastConfirmation =
        facilityPreference?.preference_radiology_contrast_confirmation;
    }

    const newInvestigation = new InvestigationModel({
      ...(investigation as any),
      profile,
      createdBy: mutator,
      hospital: (external ? externalHospital : hospital) || undefined,
      referringHospital: hospital,
      creatorName: mutator?.fullName,
      radiologyContrastConfirmation,
    });

    const investigationCreated = await customDSSerializeInTransaction(
      this.dataSource,
      (manager) =>
        this.createInvestigationInTransaction(
          manager,
          mutator,
          newInvestigation,
          investigation,
          additionalNoteId,
        ),
    );

    if (
      mutator.id !== profile.id &&
      (investigationCreated.requestType ===
        InvestigationRequestType.Radiology ||
        investigationCreated.requestType ===
          InvestigationRequestType.Laboratory)
    ) {
      const type =
        investigationCreated.requestType === InvestigationRequestType.Radiology
          ? cacheType.Radiology
          : cacheType.LabTest;
      await updateOverviewCacheOnCreate(profile.id, type, investigationCreated);
    }
    return investigationCreated;
  }

  private async createInvestigationInTransaction(
    manager: EntityManager,
    mutator: ProfileModel,
    newInvestigation: InvestigationModel,
    investigationInput: NewInvestigationInput,
    additionalNoteId: string,
  ): Promise<InvestigationModel> {
    const investigationTrxnRepo = manager.withRepository(
      this.investigationRepository,
    );
    const subBillServiceDetail = [];

    const serviceDetails = newInvestigation.serviceDetails?.map((detail) => {
      if (detail.itemId) return detail;

      const detailToUse = {
        ...detail,
        reference: detail.reference || generateUUID(),
      };

      subBillServiceDetail.push(detailToUse);
      return detailToUse;
    });

    const newInvestigation_: InvestigationModel = {
      ...newInvestigation,
      serviceDetails,
      testInfo: newInvestigation.testInfo.map((info) => ({
        ...info,
        ref: generateUUID(),
      })),
      examinationType: newInvestigation.examinationType.map((exam) => ({
        ...exam,
        ref: generateUUID(),
      })),
      subBillRef: subBillServiceDetail?.length ? generateUUID() : null,
    };
    const investigation = await investigationTrxnRepo.save(newInvestigation_);
    const newAdditionalNote = await investigationTrxnRepo.addAdditionalNote(
      mutator,
      investigation.id,
      {
        additionalNote: investigationInput?.additionalNote || '',
        ...(additionalNoteId && { id: additionalNoteId }),
      },
    );

    if (investigation.external) {
      await manager
        .createQueryBuilder()
        .update(InvestigationModel)
        .set({
          updatedDate: () => 'updated_date',
        })
        .where('id = :id', { id: investigation.id })
        .execute();

      return {
        ...investigation,
        createdBy: mutator,
        additionalNotes: [{ ...newAdditionalNote, createdBy: mutator }],
      };
    }

    const billDetails = await this.generateBillForInvestigation(
      investigation,
      manager,
      mutator,
      investigation.serviceDetails,
    );

    return {
      ...investigation,
      bill: billDetails.bill,
      billStatus: billDetails.billStatus,
      createdBy: mutator,
      additionalNotes: [{ ...newAdditionalNote, createdBy: mutator }],
    };
  }

  // @desc: for patient created Investigation
  async saveInvestigationWithLabResult(
    mutator: ProfileModel,
    input: NewInvestigationWithLabResult,
  ): Promise<InvestigationModel> {
    const profile = await this.profileRepository.findOne({
      where: {
        clinifyId: input.clinifyId,
      },
    });
    validateCreation(profile, input);

    const { hospital } = mutator;

    return customDSSerializeInTransaction(this.dataSource, async (manager) => {
      const investigationTrxnRepo = manager.withRepository(
        this.investigationRepository,
      );
      const subBillServiceDetail = input.serviceDetails?.filter(
        (detail) => !detail.itemId,
      );

      const newInvestigation_: NewInvestigationWithLabResult = {
        ...input,
        testInfo: input.testInfo.map((info) => ({
          ...info,
          ref: generateUUID(),
        })),
        subBillRef: subBillServiceDetail?.length ? generateUUID() : null,
        profile,
        createdBy: mutator,
        hospital: hospital || undefined,
        referringHospital: hospital,
        creatorName: mutator.fullName,
      } as any;

      const investigation =
        await investigationTrxnRepo.saveInvestigationWithLabResult(
          mutator,
          newInvestigation_,
        );

      if (mutator.type === UserType.Patient) return investigation;

      const billDetails = await this.generateBillForInvestigation(
        investigation,
        manager,
        mutator,
        investigation.serviceDetails,
      );

      return {
        ...investigation,
        bill: billDetails.bill,
        billStatus: billDetails.billStatus,
        createdBy: mutator,
      };
    });
  }

  async saveInvestigationWithRadiologyResult(
    mutator: ProfileModel,
    input: NewInvestigationWithRadioResult,
  ): Promise<InvestigationModel> {
    const profile = await this.profileRepository.findOne({
      where: {
        clinifyId: input.clinifyId,
      },
    });
    validateCreation(profile, input);

    const { hospital } = mutator;

    return customDSSerializeInTransaction(this.dataSource, async (manager) => {
      const investigationTrxnRepo = manager.withRepository(
        this.investigationRepository,
      );
      const subBillServiceDetail = input.serviceDetails?.filter(
        (detail) => !detail.itemId,
      );

      const newInvestigation_: NewInvestigationWithRadioResult = {
        ...input,
        examinationType: input.examinationType.map((exam) => ({
          ...exam,
          ref: generateUUID(),
        })),
        subBillRef: subBillServiceDetail?.length ? generateUUID() : null,
        profile,
        createdBy: mutator,
        hospital: hospital || undefined,
        referringHospital: hospital,
        creatorName: mutator.fullName,
      } as any;

      const investigation =
        await investigationTrxnRepo.saveInvestigationWithRadResult(
          mutator,
          newInvestigation_,
        );

      if (mutator.type === UserType.Patient) return investigation;

      const billDetails = await this.generateBillForInvestigation(
        investigation,
        manager,
        mutator,
        investigation.serviceDetails,
      );

      return {
        ...investigation,
        bill: billDetails.bill,
        billStatus: billDetails.billStatus,
        createdBy: mutator,
      };
    });
  }

  async updateInvestigationWithRadResult(
    mutator: ProfileModel,
    investigationId: string,
    investigation: InvestigationWithRadiologyInput,
    onlyServiceDetail?: boolean,
  ): Promise<InvestigationModel> {
    return customDSSerializeInTransaction(this.dataSource, async (manager) => {
      const investigationTrxnRepo = manager.withRepository(
        this.investigationRepository,
      );

      const serviceDetails = investigation.serviceDetails?.map((detail) => {
        if (detail.itemId) return detail;

        return {
          ...detail,
          reference: detail.reference || generateUUID(),
        };
      });

      const newlyAddedRefs: Record<string, any> = {};

      const investigation_: InvestigationWithRadiologyInput = {
        ...investigation,
        serviceDetails,
        ...(!onlyServiceDetail
          ? {
              examinationType: investigation.examinationType.map((exam) => {
                const generatedUUID = generateUUID();
                !exam.ref
                  ? (newlyAddedRefs[exam?.itemId] = generatedUUID)
                  : null;
                return {
                  ...exam,
                  ...(!exam.ref ? { ref: generatedUUID } : {}),
                };
              }),
            }
          : {}),
      };

      const [updatedInvestigation, oldInvestigation] =
        await investigationTrxnRepo.updateInvestigationWithRadResult(
          mutator,
          investigationId,
          investigation_,
          onlyServiceDetail,
        );

      if (mutator.type === UserType.Patient) return updatedInvestigation;

      const billRefsToDelete = [...(investigation?.billRefsToDelete || [])];

      const billDetails: any[] = investigation?.billInfo?.map((info) => ({
        ...info,
        serviceType: info.type,
        serviceName: info.name,
        recordServiceName: info.recordName,
        ...(newlyAddedRefs[info?.itemId]
          ? { ref: newlyAddedRefs[info?.itemId] }
          : {}),
      }));

      handleSubForMultipleBill(
        updatedInvestigation,
        oldInvestigation,
        ServiceType.Radiology,
        billRefsToDelete,
        billDetails,
      );

      const updatedBill = await this.billService.updateMultipleBill(
        mutator,
        updatedInvestigation.profile,
        updatedInvestigation,
        ServiceType.Radiology,
        updatedInvestigation.billId,
        billDetails,
        billRefsToDelete,
        manager,
        true,
      );

      return {
        ...updatedInvestigation,
        updatedBy: mutator,
        ...(updatedBill
          ? {
              bill: updatedBill,
              billStatus: getBillableStatusOf(updatedBill.billStatus),
            }
          : {}),
      };
    });
  }

  async updateInvestigationWithLabResult(
    mutator: ProfileModel,
    investigationId: string,
    investigation: InvestigationWithLabInput,
    onlyServiceDetail?: boolean,
  ): Promise<InvestigationModel> {
    return customDSSerializeInTransaction(this.dataSource, async (manager) => {
      const investigationTrxnRepo = manager.withRepository(
        this.investigationRepository,
      );

      const serviceDetails = investigation.serviceDetails?.map((detail) => {
        if (detail.itemId) return detail;

        return {
          ...detail,
          reference: detail.reference || generateUUID(),
        };
      });

      const newlyAddedRefs: Record<string, any> = {};

      const investigation_: InvestigationWithLabInput = {
        ...investigation,
        serviceDetails,
        ...(!onlyServiceDetail
          ? {
              testInfo: investigation.testInfo.map((info) => {
                const generatedUUID = generateUUID();
                !info.ref
                  ? (newlyAddedRefs[info?.itemId] = generatedUUID)
                  : null;
                return {
                  ...info,
                  ...(!info.ref ? { ref: generatedUUID } : {}),
                };
              }),
            }
          : {}),
      };

      const [updatedInvestigation, oldInvestigation] =
        await investigationTrxnRepo.updateInvestigationWithLabResult(
          mutator,
          investigationId,
          investigation_,
          onlyServiceDetail,
        );

      if (mutator.type === UserType.Patient) return updatedInvestigation;

      const billRefsToDelete = [...(investigation?.billRefsToDelete || [])];

      const billDetails: any[] = investigation?.billInfo?.map((info) => ({
        ...info,
        serviceType: info.type,
        serviceName: info.name,
        recordServiceName: info.recordName,
        ...(newlyAddedRefs[info?.itemId]
          ? { ref: newlyAddedRefs[info?.itemId] }
          : {}),
      }));

      handleSubForMultipleBill(
        updatedInvestigation,
        oldInvestigation,
        ServiceType.Laboratory,
        billRefsToDelete,
        billDetails,
      );

      const updatedBill = await this.billService.updateMultipleBill(
        mutator,
        updatedInvestigation.profile,
        updatedInvestigation,
        ServiceType.Laboratory,
        updatedInvestigation.billId,
        billDetails,
        billRefsToDelete,
        manager,
        true,
      );

      return {
        ...updatedInvestigation,
        updatedBy: mutator,
        ...(updatedBill
          ? {
              bill: updatedBill,
              billStatus: getBillableStatusOf(updatedBill.billStatus),
            }
          : {}),
      };
    });
  }

  async getAllInvestigation(
    mutator: ProfileModel,
    profileId: string,
    filter: Partial<InvestigationFilterInput>,
  ): Promise<InvestigationResponse> {
    const investigations = await this.repository.findInvestigationByProfile(
      mutator,
      profileId,
      filter,
    );
    return investigations;
  }

  async getHospitalInvestigations(
    mutator: ProfileModel,
    hospitalId: string,
    filter: Partial<InvestigationFilterInput>,
    hmoId?: string,
    partnerId?: string,
  ): Promise<InvestigationResponse> {
    const investigations = await this.repository.findInvestigationByHospital(
      mutator,
      hospitalId,
      filter,
      hmoId,
      partnerId,
    );
    return investigations;
  }

  async getOneInvestigation(
    profile: ProfileModel,
    investigationId: string,
  ): Promise<InvestigationModel> {
    const investigation = await this.repository.findOneInvestigation(
      profile,
      investigationId,
    );
    if (!investigation) {
      throw new NotFoundException('Record Not Found');
    }
    return investigation;
  }

  private async updateInvestigationInTransaction(
    manager: EntityManager,
    mutator: ProfileModel,
    investigation: InvestigationInput,
    investigationId: string,
    externalHospital?: HospitalModel,
    onlyServiceDetail?: boolean,
  ): Promise<InvestigationModel> {
    const investigationTrxnRepo = manager.withRepository(
      this.investigationRepository,
    );

    const serviceDetails = investigation.serviceDetails?.map((item) => ({
      ...item,
      reference: item.reference || generateUUID(),
    }));

    const { Laboratory } = InvestigationRequestType;
    const isLaboratory = investigation.requestType === Laboratory;
    const newlyAddedRefs: Record<string, any> = {};

    const investigation_: InvestigationInput = {
      ...investigation,
      serviceDetails,
      ...(!onlyServiceDetail
        ? {
            testInfo: investigation.testInfo.map((info) => {
              const generatedUUID = generateUUID();
              !info.ref ? (newlyAddedRefs[info?.itemId] = generatedUUID) : null;
              return {
                ...info,
                ...(!info.ref ? { ref: generatedUUID } : {}),
              };
            }),
            examinationType: investigation.examinationType.map((exam) => {
              const generatedUUID = generateUUID();
              !exam.ref ? (newlyAddedRefs[exam?.itemId] = generatedUUID) : null;
              return {
                ...exam,
                ...(!exam.ref ? { ref: generatedUUID } : {}),
              };
            }),
          }
        : {}),
    };

    const [updatedInvestigation, oldInvestigation] =
      await investigationTrxnRepo.updateInvestigation(
        mutator,
        {
          ...investigation_,
          ...(externalHospital ? { hospital: externalHospital } : {}),
        } as any,
        investigationId,
        onlyServiceDetail,
      );

    if (updatedInvestigation.external || mutator.type === UserType.Patient) {
      return updatedInvestigation;
    }

    const recordServiceType = isLaboratory
      ? ServiceType.Laboratory
      : ServiceType.Radiology;

    const billRefsToDelete = [...(investigation?.billRefsToDelete || [])];

    const billDetails: any[] = investigation?.billInfo?.map((info) => ({
      ...info,
      serviceType: info.type,
      serviceName: info.name,
      recordServiceName: info.recordName,
      ...(newlyAddedRefs[info?.itemId]
        ? { ref: newlyAddedRefs[info?.itemId] }
        : {}),
    }));

    handleSubForMultipleBill(
      updatedInvestigation,
      oldInvestigation,
      recordServiceType,
      billRefsToDelete,
      billDetails,
    );

    const updatedBill = await this.billService.updateMultipleBill(
      mutator,
      updatedInvestigation.profile,
      updatedInvestigation,
      recordServiceType,
      updatedInvestigation.billId,
      billDetails,
      billRefsToDelete,
      manager,
      true,
    );
    return {
      ...updatedInvestigation,
      updatedBy: mutator,
      ...(updatedBill
        ? {
            bill: updatedBill,
            billStatus: getBillableStatusOf(updatedBill.billStatus),
          }
        : {}),
    };
  }

  async generateBillForInvestigation(
    investigation: InvestigationModel,
    manager: EntityManager,
    mutator: ProfileModel,
    serviceDetails: ServiceDetailInput[],
  ): Promise<InvestigationModel> {
    const isLaboratory =
      investigation.requestType === InvestigationRequestType.Laboratory;

    const requestInfo = isLaboratory
      ? investigation.testInfo
      : investigation.examinationType;

    const recordServiceType: ServiceType = isLaboratory
      ? ServiceType.Laboratory
      : ServiceType.Radiology;

    const subBillServiceDetail = serviceDetails?.filter(
      (detail) => !detail.itemId,
    );

    let billDetails: any[] = [...requestInfo].map((details, idx) => {
      const requestName = isLaboratory ? details.testName : details.examType;
      const namePath = isLaboratory ? 'testName' : 'examType';

      const selectedServiceDetail = [...(serviceDetails || [])]?.find(
        (singleService) => singleService?.itemId === details.itemId,
      );

      const {
        pricePerUnit = 0,
        quantity = '1',
        type = '',
        name = '',
        patientType = null,
        paymentType = null,
      } = {
        ...selectedServiceDetail,
      };

      const billName = `${recordServiceType} - ${requestName} -- ${
        [...requestInfo]
          .slice(0, idx + 1)
          .filter((req) => req?.[namePath] === requestName).length
      }`;

      return {
        reference: details.ref,
        billName,
        patientType,
        paymentType,
        serviceType: type,
        serviceName: name,
        price: pricePerUnit,
        quantity,
        subBillsInputs: null,
      };
    });

    billDetails = handleSubBillOnCreate(
      investigation.subBillRef,
      recordServiceType,
      billDetails,
      subBillServiceDetail,
    );

    const generatedBills = await this.billService.generateMultipleBill(
      mutator,
      investigation.profile,
      investigation,
      recordServiceType,
      billDetails,
      manager,
      undefined,
    );

    const billStatus = getBillableStatusOf(generatedBills.billStatus);

    await manager
      .createQueryBuilder()
      .update(InvestigationModel)
      .set({
        bill: generatedBills,
        billStatus,
        updatedDate: () => 'updated_date',
      })
      .where('id = :id', { id: investigation.id })
      .execute();

    return {
      ...investigation,
      bill: generatedBills,
      billStatus,
    };
  }

  async updateBillForExternalInvestigationResult(
    investigation: InvestigationModel,
    manager: EntityManager,
    mutator: ProfileModel,
    result: { details: any[] },
  ): Promise<BillModel> {
    const isLaboratory =
      investigation.requestType === InvestigationRequestType.Laboratory;

    const requestInfo = isLaboratory
      ? investigation.testInfo
      : investigation.examinationType;

    const recordServiceType: ServiceType = isLaboratory
      ? ServiceType.Laboratory
      : ServiceType.Radiology;

    const namePath = isLaboratory ? 'testName' : 'examType';

    const billDetails: any[] = result?.details
      .map(({ serviceDetails, [namePath]: requestName }, idx) => {
        const selectedInvestigationItem = requestInfo.find(
          ({ [namePath]: investigationItem }) =>
            investigationItem.trim() === requestName.trim(),
        );

        const billName = `${ServiceType.Laboratory} - ${requestName} -- ${
          [...requestInfo]
            .slice(0, idx + 1)
            .filter((req) => req?.[namePath] === requestName).length
        }`;

        return {
          ...serviceDetails,
          itemId: selectedInvestigationItem?.itemId || '',
          ref: selectedInvestigationItem.ref,
          serviceType: serviceDetails.type,
          serviceName: serviceDetails.name,
          billName,
        };
      })
      .filter((serviceDetails) => !!serviceDetails.type);

    return await this.billService.updateMultipleBill(
      mutator,
      investigation.profile,
      investigation,
      recordServiceType,
      investigation.billId,
      billDetails,
      [],
      manager,
      true,
    );
  }

  async updateInvestigation(
    profile: ProfileModel,
    investigation: InvestigationInput,
    investigationId: string,
    onlyServiceDetail?: boolean,
  ): Promise<InvestigationModel> {
    let externalReferredHospital = null;
    const { hospital } = profile;
    const { external, externalHospital } = investigation;
    if (external && externalHospital && !onlyServiceDetail) {
      externalReferredHospital = await this.entityManager.findOneOrFail(
        HospitalModel,
        {
          where: {
            id: investigation.externalHospital,
          },
        },
      );
    }

    return customDSSerializeInTransaction(this.dataSource, (manager) =>
      this.updateInvestigationInTransaction(
        manager,
        profile,
        investigation,
        investigationId,
        external ? externalReferredHospital : hospital,
        onlyServiceDetail,
      ),
    );
  }

  private generateApprovalMessage(
    investigation: InvestigationModel,
    mutator: ProfileModel,
  ): string {
    const { requestType, testInfo, examinationType } = investigation;
    const details =
      requestType === InvestigationRequestType.Laboratory
        ? testInfo.map(({ testName }) => testName).join(', ')
        : examinationType.map(({ examType }) => examType).join(', ');
    return `Your ${
      requestType === InvestigationRequestType.Laboratory
        ? 'Lab Test'
        : 'Radiology Examination'
    } on ${details} with <span style="color: #00ABE2">${
      investigation.hospital.name
    }</span> has been approved by <span style="color: #00ABE2">${
      mutator.fullName
    }</span>`;
  }

  async updateInvestigationStatus(
    profile: ProfileModel,
    investigationId: string,
    status: InvestigationStatus,
  ): Promise<InvestigationModel> {
    const { type } = await this.profileRepository.findOne({
      where: { id: profile.id },
    });
    const {
      OrganizationLabTechnician,
      OrganizationRadiographer,
      OrganizationRadiologist,
      Pharmacist,
      OrganizationRecordOfficer,
    } = HospitalUserType;
    const canUpdateStatus = [
      OrganizationLabTechnician,
      OrganizationRadiographer,
      OrganizationRadiologist,
      Pharmacist,
      OrganizationRecordOfficer,
      UserType.OrganizationAdmin,
    ] as string[];
    if (!canUpdateStatus.includes(type))
      throw new ForbiddenException('Not Authorized To Modify This Record');
    const investigation = await this.repository.updateInvestigationStatus(
      profile,
      investigationId,
      status,
    );
    if (status === InvestigationStatus.Approved) {
      const { nonCorporateEmail } = await this.entityManager.findOne(
        UserModel,
        {
          select: ['id', 'nonCorporateEmail'],
          where: { id: investigation.profile.userId },
        },
      );
      if (nonCorporateEmail) {
        const {
          facilityLogo,
          website,
          facebook,
          twitter,
          instagram,
          supportMail,
        } = await this.mailDocService.getFacilityMailProperties(
          profile.hospitalId,
        );
        const msg: IMessage = {
          to: nonCorporateEmail,
          from: `${profile.hospital.name} <${supportMail}>`,
          subject: 'Investigation Approved',
          html: mailerTemplate({
            firstName: investigation.profile?.fullName?.split(' ')[0] || '',
            message: this.generateApprovalMessage(investigation, profile),
            facilityLogo,
            website,
            facebook,
            twitter,
            instagram,
            facilityName: profile.hospital.name,
            supportMail,
          }),
        };
        this.mailerService.sendMail(msg);
      }
    }
    return investigation;
  }

  async updateInvestigationRadiologyResult(
    mutator: ProfileModel,
    investigationId: string,
    radiologyResult: RadiologyResultInput,
  ): Promise<InvestigationModel> {
    const manager = this.entityManager;

    const investigationTrxnRepo = manager.withRepository(this.repository);

    const updatedInvestigation =
      await investigationTrxnRepo.updateInvestigationRadiologyResult(
        mutator,
        investigationId,
        {
          ...radiologyResult,
          details: radiologyResult?.details?.map((_detail) => ({
            ..._detail,
            radiographerSignatureDateTime: _detail?.radiographerSignature
              ? new Date()
              : undefined,
            verifiedBySignatureDateTime: _detail?.verifiedBySignature
              ? new Date()
              : undefined,
            radiologistSignatureDateTime: _detail?.radiologistSignature
              ? new Date()
              : undefined,
          })),
        },
      );

    if (!updatedInvestigation.external) return updatedInvestigation;

    const updatedBill = await this.updateBillForExternalInvestigationResult(
      updatedInvestigation,
      manager,
      mutator,
      radiologyResult,
    );

    return {
      ...updatedInvestigation,
      ...(updatedBill
        ? {
            bill: updatedBill,
            billStatus: getBillableStatusOf(updatedBill.billStatus),
          }
        : {}),
    };
  }

  async updateInvestigationLabResult(
    mutator: ProfileModel,
    investigationId: string,
    labResult: LabResultInput,
  ): Promise<InvestigationModel> {
    const manager = this.entityManager;
    const investigationTrxnRepo = manager.withRepository(this.repository);

    const updatedInvestigation =
      await investigationTrxnRepo.updateInvestigationLabResult(
        mutator,
        investigationId,
        {
          ...labResult,
          details: labResult?.details?.map((_detail) => ({
            ..._detail,
            verifiedBySignatureDateTime: _detail?.verifiedBySignature
              ? new Date()
              : undefined,
            performedBySignatureDateTime: _detail?.performedBySignature
              ? new Date()
              : undefined,
            pathologistSignatureDateTime: _detail?.pathologistSignature
              ? new Date()
              : undefined,
          })),
        },
      );

    if (!updatedInvestigation.external) return updatedInvestigation;

    const updatedBill = await this.updateBillForExternalInvestigationResult(
      updatedInvestigation,
      manager,
      mutator,
      labResult,
    );

    return {
      ...updatedInvestigation,
      ...(updatedBill
        ? {
            bill: updatedBill,
            billStatus: getBillableStatusOf(updatedBill.billStatus),
          }
        : {}),
    };
  }

  async deleteInvestigationInTransaction(
    manager: EntityManager,
    mutator: ProfileModel,
    investigationIds: string[],
  ): Promise<InvestigationModel[]> {
    const repo = manager.withRepository(this.investigationRepository);
    const deleteInvestigations = await repo.deleteInvestigation(
      mutator,
      investigationIds,
    );
    const billItemsRefsToDelete: string[] = [];
    const claimsToDelete: string[] = [];
    deleteInvestigations.forEach((inv) => {
      const isLaboratory =
        inv.requestType === InvestigationRequestType.Laboratory;
      const requestInfo = isLaboratory ? inv.testInfo : inv.examinationType;
      if (inv?.subBillRef) {
        billItemsRefsToDelete.push(inv.subBillRef);
      }
      billItemsRefsToDelete.push(...[...requestInfo].map(({ ref }) => ref));
      if (inv.hmoClaim) {
        claimsToDelete.push(inv.hmoClaim.id);
      }
    });
    if (claimsToDelete.length) {
      claimsToDelete.map((claimId) =>
        this.hmoClaimService.flagHmoClaimDeletedInTransaction(
          manager,
          mutator,
          claimId,
        ),
      );
    }
    await this.billService.deleteAutoGeneratedBillItems(
      mutator,
      InvestigationModel,
      billItemsRefsToDelete,
      manager,
      ServiceType.Investigation,
    );

    return deleteInvestigations;
  }

  async deleteInvestigation(
    profile: ProfileModel,
    investigationIds: string[],
  ): Promise<InvestigationModel[]> {
    return inTransaction(this.entityManager, (manager) =>
      this.deleteInvestigationInTransaction(manager, profile, investigationIds),
    );
  }

  async archiveInvestigation(
    profile: ProfileModel,
    investigationIds: string[],
    archive: boolean,
  ): Promise<InvestigationModel[]> {
    const investigations = await this.repository.archiveInvestigation(
      profile,
      investigationIds,
      archive,
      this.billService,
    );
    return investigations;
  }

  async saveInvestigationLabResult(
    profile: ProfileModel,
    investigationId: string,
    labResult: LabResultInput,
  ): Promise<InvestigationModel> {
    return customDSSerializeInTransaction(
      this.dataSource,
      async (manager: EntityManager) => {
        const investigationTrxnRepo = manager.withRepository(this.repository);
        const investigation =
          await investigationTrxnRepo.addInvestigationLabResult(
            profile,
            investigationId,
            {
              ...labResult,
              details: labResult?.details?.map((_detail) => ({
                ..._detail,
                verifiedBySignatureDateTime: _detail?.verifiedBySignature
                  ? new Date()
                  : undefined,
                performedBySignatureDateTime: _detail?.performedBySignature
                  ? new Date()
                  : undefined,
                pathologistSignatureDateTime: _detail?.pathologistSignature
                  ? new Date()
                  : undefined,
              })),
            },
          );
        if (labResult.hmoClaim) {
          investigation.hmoClaim =
            await this.hmoClaimService.createHmoClaimInTransaction(
              manager,
              profile,
              {
                ...labResult.hmoClaim,
                clinifyId: investigation.clinifyId,
                providerId: labResult.hmoProviderId,
                autoGenerated: true,
              },
              { investigationId: investigation.id },
            );
          await manager
            .withRepository(this.repository)
            .update(investigation.id, {
              hmoClaimId: investigation.hmoClaim.id,
            });
        }

        if (investigation.external) {
          const testInfo = investigation.testInfo;
          const serviceDetails: ServiceDetailInput[] = labResult?.details
            .map(({ serviceDetails, testName: _testName }) => {
              const selectedTest = testInfo.find(
                ({ testName }) => testName.trim() === _testName.trim(),
              );

              return {
                ...serviceDetails,
                itemId: selectedTest?.itemId || '',
              };
            })
            .filter((serviceDetails) => !!serviceDetails.type);

          return await this.generateBillForInvestigation(
            investigation,
            manager,
            profile,
            serviceDetails,
          );
        }

        return investigation;
      },
    );
  }

  async saveInvestigationRadiologyResult(
    profile: ProfileModel,
    investigationId: string,
    radiologyResult: RadiologyResultInput,
  ): Promise<InvestigationModel> {
    return customDSSerializeInTransaction(
      this.dataSource,
      async (manager: EntityManager) => {
        const investigationTrxnRepo = manager.withRepository(this.repository);
        const investigation =
          await investigationTrxnRepo.addInvestigationRadiologyResult(
            profile,
            investigationId,
            {
              ...radiologyResult,
              details: radiologyResult?.details?.map((_detail) => ({
                ..._detail,
                radiographerSignatureDateTime: _detail?.radiographerSignature
                  ? new Date()
                  : undefined,
                verifiedBySignatureDateTime: _detail?.verifiedBySignature
                  ? new Date()
                  : undefined,
                radiologistSignatureDateTime: _detail?.radiologistSignature
                  ? new Date()
                  : undefined,
              })),
            },
          );
        if (radiologyResult.hmoClaim) {
          investigation.hmoClaim =
            await this.hmoClaimService.createHmoClaimInTransaction(
              manager,
              profile,
              {
                ...radiologyResult.hmoClaim,
                clinifyId: investigation.clinifyId,
                autoGenerated: true,
              },
              { investigationId: investigation.id },
            );
          await manager
            .withRepository(this.repository)
            .update(investigation.id, {
              hmoClaimId: investigation.hmoClaim.id,
            });
        }

        const examInfo = investigation.examinationType;
        const serviceDetails: ServiceDetailInput[] = radiologyResult?.details
          .map(({ serviceDetails, examType: _examType }) => {
            const selectedTest = examInfo.find(
              ({ examType }) => examType.trim() === _examType.trim(),
            );

            return {
              ...serviceDetails,
              itemId: selectedTest?.itemId || '',
            };
          })
          .filter((serviceDetails) => !!serviceDetails.type);

        if (investigation.external) {
          return await this.generateBillForInvestigation(
            investigation,
            manager,
            profile,
            serviceDetails,
          );
        }

        return investigation;
      },
    );
  }

  async additionalNotes(
    profile: ProfileModel,
    investigationId: string,
  ): Promise<AdditionalNoteModel[]> {
    return this.repository.additionalNotes(profile, investigationId);
  }

  async addAdditionalNote(
    profile: ProfileModel,
    investigationId: string,
    input: AdditionalNoteInput,
  ): Promise<AdditionalNoteModel> {
    return this.repository.addAdditionalNote(profile, investigationId, input);
  }

  async updateAdditionalNote(
    profile: ProfileModel,
    id: string,
    input: AdditionalNoteInput,
  ): Promise<AdditionalNoteModel> {
    return this.repository.updateAdditionalNote(profile, id, input);
  }

  async deleteAdditionalNote(
    profile: ProfileModel,
    id: string,
  ): Promise<AdditionalNoteModel> {
    return this.repository.deleteAdditionalNote(profile, id);
  }

  concealInvestigationAdditionalNote(
    profile: ProfileModel,
    additionalNoteId: string,
    concealState: boolean,
  ): Promise<AdditionalNoteModel> {
    return this.repository.concealInvestigationAdditionalNote(
      profile,
      additionalNoteId,
      concealState,
    );
  }

  concealLabResultAdditionalNote(
    profile: ProfileModel,
    investigationId: string,
    concealStatus: boolean,
  ): Promise<InvestigationModel> {
    return this.repository.concealLabResultAdditionalNote(
      profile,
      investigationId,
      concealStatus,
    );
  }
  findLabResultTrend(filter: LabResultTrendFilterInput) {
    return this.repository.findLabResultTrend(filter);
  }
}
