/* eslint-disable max-lines */
import {
  archivedPatientFilter,
  byHospital,
  byHospitalIds,
  byHospitalOrProfileId,
  byProfileId,
  byProfileIds,
  byProfileIdsOrHospitalId,
  byRecordId,
  byRecordIds,
  byDirectClinifyId,
  filterAddedAppointments,
  filterAddedWaiterItem,
  filterAdministrationNoteAdded,
  filterAdministrationNoteRemoved,
  filterAdministrationNoteUpdated,
  filterAdmissionAdded,
  filterAdmissionEvent,
  filterAdmissionLineAdded,
  filterAdmissionLineRemoved,
  filterAdmissionLineUpdated,
  filterAdmissionNotesAdded,
  filterAdmissionNotesRemoved,
  filterAdmissionNotesUpdated,
  filterAdmissionsArchived,
  filterAdmissionsRemoved,
  filterAdmissionsUnarchived,
  filterAdmissionUpdated,
  filterAllergyAdded,
  filterAllergyArchived,
  filterAllergyEvent,
  filterAllergyRemoved,
  filterAllergyUnarchived,
  filterAllergy<PERSON>pdated,
  filterAnaesthesiaAdded,
  filterAnaesthesia<PERSON>emoved,
  filterAnaesthesiaUp<PERSON>,
  filterAntenatalAdded,
  filterAntenatalArchived,
  filterAntenatalDetailsAdded,
  filterAntenatalDetailsRemoved,
  filterAntenatalDetailsUpdated,
  filterAntenatalEvent,
  filterAntenatalRemoved,
  filterAntenatalUnarchived,
  filterAntenatalUpdated,
  filterAnthropometryAdded,
  filterAnthropometryRemoved,
  filterAnthropometryUpdated,
  filterAppointmentEvent,
  filterArchivedAppointments,
  filterArchiveWallet,
  filterBillingEvent,
  filterBloodGlucoseAdded,
  filterBloodGlucoseRemoved,
  filterBloodGlucoseUpdated,
  filterBloodPressureAdded,
  filterBloodPressureRemoved,
  filterBloodPressureUpdated,
  filterBloodTransfusionAdded,
  filterBloodTransfusionRemoved,
  filterBloodTransfusionUpdated,
  filterCardAdded,
  filterCardRemoved,
  filterConsultationAdded,
  filterConsultationArchived,
  filterConsultationEvent,
  filterConsultationRemoved,
  filterConsultationTreatmentPlanAdded,
  filterConsultationTreatmentPlanRemoved,
  filterConsultationTreatmentPlanUpdated,
  filterConsultationUnarchived,
  filterConsultationUpdated,
  filterCoverageAdded,
  filterCoverageRemoved,
  filterCoverageUpdated,
  filterDependentAdded,
  filterDependentRemoved,
  filterDependentUpdated,
  filterDevelopmentalHistoryAdded,
  filterDevelopmentalHistoryRemoved,
  filterDevelopmentalHistoryUpdated,
  filterDisabilityAdded,
  filterDisabilityRemoved,
  filterDisabilityUpdated,
  filterDischargePatientsAdded,
  filterDischargePatientsRemoved,
  filterDischargePatientsUpdated,
  filterDispenseAdded,
  filterDispenseRemoved,
  filterDispenseUpdated,
  filterFacilityPreferenceUpdated,
  filterFamilyHistoryAdded,
  filterFamilyHistoryRemoved,
  filterFamilyHistoryUpdated,
  filterGynecologicHistoryAdded,
  filterGynecologicHistoryRemoved,
  filterGynecologicHistoryUpdated,
  filterHabitAdded,
  filterHabitRemoved,
  filterHabitUpdated,
  filterHandoverAdditionalNoteAdded,
  filterHandoverAdditionalNoteRemoved,
  filterHandoverAdditionalNoteUpdated,
  filterHandoverNoteAdded,
  filterHandoverNoteArchived,
  filterHandoverNoteItemAdded,
  filterHandoverNoteItemRemoved,
  filterHandoverNoteItemUpdated,
  filterHandoverNoteRemoved,
  filterHandoverNoteUnarchived,
  filterHandoverNoteUpdated,
  filterImmunizationAdded,
  filterImmunizationArchived,
  filterImmunizationEvent,
  filterImmunizationRemoved,
  filterImmunizationUnarchived,
  filterImmunizationUpdated,
  filterInPatientEvent,
  filterInputAdded,
  filterInputRemoved,
  filterInputUpdated,
  filterInventoryAdded,
  filterInventoryEvent,
  filterInventoryItemUpdated,
  filterInventoryRemoved,
  filterInventoryUpdated,
  filterInvestigationAdded,
  filterInvestigationAdditionalNotesAdded,
  filterInvestigationAdditionalNotesRemoved,
  filterInvestigationAdditionalNotesUpdated,
  filterInvestigationArchived,
  filterInvestigationEvent,
  filterInvestigationRemoved,
  filterInvestigationUnarchived,
  filterInvestigationUpdated,
  filterInvoiceAccountAdded,
  filterInvoiceAdded,
  filterInvoiceArchived,
  filterInvoiceItemAdded,
  filterInvoiceItemRemoved,
  filterInvoiceItemUpdated,
  filterInvoicePaymentAdded,
  filterInvoicePaymentRemoved,
  filterInvoicePaymentUpdated,
  filterInvoiceRemoved,
  filterInvoiceStatusUpdated,
  filterInvoiceUnarchived,
  filterInvoiceUpdated,
  filterLaboratoryAdded,
  filterLaboratoryEvent,
  filterLaboratoryUpdated,
  filterLabourAndDeliveryAdded,
  filterLabourAndDeliveryArchived,
  filterLabourAndDeliveryRemoved,
  filterLabourAndDeliveryUnarchived,
  filterLabourAndDeliveryUpdated,
  filterMedicalReportAdded,
  filterMedicalReportArchived,
  filterMedicalReportEvent,
  filterMedicalReportRemoved,
  filterMedicalReportUpdated,
  filterMedicationAdded,
  filterMedicationArchived,
  filterMedicationBundleAdded,
  filterMedicationBundleArchived,
  filterMedicationBundleItemAdded,
  filterMedicationBundleItemRemoved,
  filterMedicationBundleItemUpdated,
  filterMedicationBundleRemoved,
  filterMedicationBundleUnarchived,
  filterMedicationBundleUpdated,
  filterMedicationDetailsAdded,
  filterMedicationDetailsRemoved,
  filterMedicationDetailsUpdated,
  filterMedicationEvent,
  filterMedicationRemoved,
  filterMedicationUnarchived,
  filterMedicationUpdated,
  filterNextOfKinAdded,
  filterNextOfKinRemoved,
  filterNextOfKinUpdated,
  filterNursingServiceAdded,
  filterNursingServiceArchived,
  filterNursingServiceDetailsAdded,
  filterNursingServiceDetailsRemoved,
  filterNursingServiceDetailsUpdated,
  filterNursingServiceEvent,
  filterNursingServiceRemoved,
  filterNursingServiceUnarchived,
  filterNursingServiceUpdated,
  filterNutritionalHistoryAdded,
  filterNutritionalHistoryGrowthAdded,
  filterNutritionalHistoryGrowthRemoved,
  filterNutritionalHistoryGrowthUpdated,
  filterNutritionalHistoryRemoved,
  filterNutritionalHistoryUpdated,
  filterObstetricAdded,
  filterObstetricRemoved,
  filterObstetricUpdated,
  filterOncologyAdded,
  filterOncologyChartUpdated,
  filterOncologyRemoved,
  filterOncologyUpdated,
  filterOrganizationPermissionGranted,
  filterOrganizationPermissionRevoked,
  filterOrgBillDetailUpdated,
  filterOrgBillsAdded,
  filterOrgBillsArchived,
  filterOrgBillsCancelled,
  filterOrgBillsPaid,
  filterOrgBillsRecalled,
  filterOrgBillsRemoved,
  filterOrgBillsUnarchived,
  filterOrgBillsUpdated,
  filterOutpatientConsultationAdded,
  filterOutputAdded,
  filterOutputRemoved,
  filterOutputUpdated,
  filterPainAdded,
  filterPainRemoved,
  filterPainUpdated,
  filterPartographAdded,
  filterPartographRemoved,
  filterPartographUpdated,
  filterPastEncounterAdded,
  filterPastEncounterRemoved,
  filterPastEncounterUpdated,
  filterPastSurgeryAdded,
  filterPastSurgeryRemoved,
  filterPastSurgeryUpdated,
  filterPatientPermissionGranted,
  filterPatientPermissionRevoked,
  filterPatientTransferred,
  filterPayInvoiceWithWallet,
  filterPaymentDepositAdded,
  filterPaymentDepositRemoved,
  filterPayoutAdded,
  filterPhysicalActivityAdded,
  filterPhysicalActivityRemoved,
  filterPhysicalActivityUpdated,
  filterPostnatalAdded,
  filterPostnatalArchived,
  filterPostnatalRemoved,
  filterPostnatalUnarchived,
  filterPostnatalUpdated,
  filterPostOperationChecklistAdded,
  filterPostOperationChecklistRemoved,
  filterPostOperationChecklistUpdated,
  filterPreauthorizationDetailsUpdated,
  filterPreExistingConditionAdded,
  filterPreExistingConditionRemoved,
  filterPreExistingConditionUpdated,
  filterPreOperationChecklistAdded,
  filterPreOperationChecklistRemoved,
  filterPreOperationChecklistUpdated,
  filterPrescriptionEvent,
  filterProcedureAdded,
  filterProcedureArchived,
  filterProcedureEvent,
  filterProcedureOperationNoteAdded,
  filterProcedureOperationNoteRemoved,
  filterProcedureOperationNoteUpdated,
  filterProcedureRemoved,
  filterProcedureUnarchived,
  filterProcedureUpdated,
  filterProfileDetailsUpdated,
  filterPulseRateAdded,
  filterPulseRateRemoved,
  filterPulseRateUpdated,
  filterRadiologyAdded,
  filterRadiologyEvent,
  filterRadiologyUpdated,
  filterReferralAdded,
  filterRemovedAppointments,
  filterRespiratoryRateAdded,
  filterRespiratoryRateRemoved,
  filterRespiratoryRateUpdated,
  filterTemperatureAdded,
  filterTemperatureRemoved,
  filterTemperatureUpdated,
  filterTransferEvent,
  filterTransferPatientsAdded,
  filterTransferPatientsRemoved,
  filterTransferPatientsUpdated,
  filterUnarchivedAppointments,
  filterUpdatedAppointments,
  filterUpdatedWaiterItem,
  filterUrineDipstickAdded,
  filterUrineDipstickRemove,
  filterUrineDipstickUpdated,
  filterVisualAcuityAdded,
  filterVisualAcuityRemoved,
  filterVisualAcuityUpdated,
  filterVitalsAdded,
  filterVitalsArchived,
  filterVitalsEvent,
  filterVitalsRemoved,
  filterVitalsUnarchived,
  filterVitalsUpdated,
  filterWaiterEvent,
  filterWaitingListArchivedItems,
  filterWaitingListCheckedInOrOutItems,
  filterWaitingListDeletedItems,
  filterWaitingListUnarchivedItems,
  filterWalletBalance,
  filterWalletEvent,
  filterWalletTransactionEvent,
  registeredPatientFilter,
  removedPatientFilter,
  unarchivedPatientFilter,
  filterVirtualCareAppointmentAdded,
  filterVirtualCareAppointmentUpdated,
  filterVirtualCareAppointmentRemoved,
  filterVirtualCareAppointmentArchived,
  filterVirtualCareAppointmentUnarchived,
  filterPatientLookupResultUpdated,
  filterPackageAdded,
  filterPackageUpdated,
  filterPackageRemoved,
  filterPackageArchived,
  filterPackageUnarchived,
  filterRequestPackageAdded,
  filterRequestPackageUpdated,
  filterRequestPackageRemoved,
  filterRequestPackageArchived,
  filterRequestPackageUnarchived,
  filterRequestPackageEvent,
  filterPackageEvent,
  filterAllVitalsInserted,
  filterAllVitalsUpdated,
  filterAllVitalsRemoved,
  filterImmunizationDetailsAdded,
  filterImmunizationDetailsRemoved,
  filterImmunizationDetailsUpdated,
  filterHandoverStaffAdded,
  filterHandoverStaffRemoved,
  filterHandoverStaffUpdated,
  byDirectProfileId,
  filterRefundDepositAdded,
  filterRefundDepositRemoved,
  filterRefundDepositUpdated,
  filterOncologyConsultationAdded,
  filterOncologyConsultationArchived,
  filterOncologyConsultationChartUpdated,
  filterOncologyConsultationRemoved,
  filterOncologyConsultationUnarchived,
  filterOncologyConsultationUpdated,
  filterChemoDrugUpdated,
  filterRequestProcedureAdded,
  filterRequestProcedureArchived,
  filterRequestProcedureRemoved,
  filterRequestProcedureUnarchived,
  filterRequestProcedureUpdated,
  filterWalkInTransferAdded,
  filterWalkInTransferArchived,
  filterWalkInTransferEvent,
  filterWalkInTransferRemoved,
  filterWalkInTransferUnarchived,
  filterWalkInTransferUpdated,
  filterWalkInReferralAdded,
  filterWalkInReferralArchived,
  filterWalkInReferralEvent,
  filterWalkInReferralRemoved,
  filterWalkInReferralUnarchived,
  filterWalkInReferralUpdated,
  filterPreChemoEducationAdded,
  filterPreChemoEducationRemoved,
  filterPreChemoEducationUpdated,
  filterCancerScreeningAdded,
  filterCancerScreeningRemoved,
  filterCancerScreeningUpdated,
  filterOncologyConsultationTreatmentPlanAdded,
  filterOncologyConsultationTreatmentPlanRemoved,
  filterOncologyConsultationTreatmentPlanUpdated,
  filterHMOClaimUpdated,
  filterUtilizationUpdated,
  filterPreauthorizationFlagged,
  filterHmoClaimFlagged,
  filterUtilizationReferralUpdated,
  filterPreauthorisationReferralUpdated,
  filterUtilizationsConfirmed,
  filterUtilizationCoveredUpdated,
  filterHmoClaimConfirmation,
  filterNursingServiceProgressNoteAdded,
  filterNursingServiceProgressNoteUpdated,
  filterNursingServiceProgressNoteRemoved,
  filterTempUserUpdated,
  filterHospitalCapitationDetailsUpdated,
  filterBulkRegistrationStatusUpdate,
} from './filters';

describe('Subscription filter suites', () => {
  let data, variables;

  it('check nullish joins', () => {
    const filterFunctions = [
      byProfileId,
      byHospital,
      byHospitalIds,
      filterAddedAppointments,
      filterAddedWaiterItem,
      filterAdmissionAdded,
      filterAdmissionEvent,
      filterAdmissionsArchived,
      filterAdmissionsRemoved,
      filterAdmissionsUnarchived,
      filterAdmissionUpdated,
      filterAllergyAdded,
      filterAllergyArchived,
      filterAllergyEvent,
      filterAllergyRemoved,
      filterAllergyUnarchived,
      filterAllergyUpdated,
      filterAnthropometryAdded,
      filterAnthropometryRemoved,
      filterAnthropometryUpdated,
      filterArchivedAppointments,
      filterBloodGlucoseAdded,
      filterBloodGlucoseRemoved,
      filterBloodGlucoseUpdated,
      filterBloodPressureAdded,
      filterBloodPressureRemoved,
      filterBloodPressureUpdated,
      filterConsultationAdded,
      filterConsultationArchived,
      filterConsultationRemoved,
      filterConsultationUnarchived,
      filterConsultationUpdated,
      filterCoverageAdded,
      filterCoverageRemoved,
      filterCoverageUpdated,
      filterDependentAdded,
      filterDependentRemoved,
      filterDependentUpdated,
      filterDisabilityAdded,
      filterDisabilityRemoved,
      filterDisabilityUpdated,
      filterDischargePatientsAdded,
      filterDischargePatientsUpdated,
      filterFamilyHistoryAdded,
      filterFamilyHistoryRemoved,
      filterFamilyHistoryUpdated,
      filterGynecologicHistoryAdded,
      filterGynecologicHistoryRemoved,
      filterGynecologicHistoryUpdated,
      filterHabitAdded,
      filterHabitRemoved,
      filterHabitUpdated,
      filterImmunizationAdded,
      filterImmunizationArchived,
      filterImmunizationEvent,
      filterImmunizationRemoved,
      filterImmunizationUnarchived,
      filterImmunizationUpdated,
      filterInvestigationAdded,
      filterInvestigationArchived,
      filterInvestigationRemoved,
      filterInvestigationUnarchived,
      filterInvestigationUpdated,
      filterMedicationAdded,
      filterMedicationArchived,
      filterMedicationEvent,
      filterMedicationRemoved,
      filterMedicationUnarchived,
      filterMedicationUpdated,
      filterNextOfKinAdded,
      filterNextOfKinRemoved,
      filterNextOfKinUpdated,
      filterObstetricAdded,
      filterObstetricRemoved,
      filterObstetricUpdated,
      filterPastEncounterAdded,
      filterPastEncounterRemoved,
      filterPastEncounterUpdated,
      filterPastSurgeryAdded,
      filterPastSurgeryRemoved,
      filterPastSurgeryUpdated,
      filterPhysicalActivityAdded,
      filterPhysicalActivityRemoved,
      filterPhysicalActivityUpdated,
      filterPreExistingConditionAdded,
      filterPreExistingConditionRemoved,
      filterPreExistingConditionUpdated,
      filterProcedureAdded,
      filterProcedureArchived,
      filterProcedureEvent,
      filterProcedureRemoved,
      filterProcedureUnarchived,
      filterProcedureUpdated,
      filterProfileDetailsUpdated,
      filterPulseRateAdded,
      filterPulseRateRemoved,
      filterPulseRateUpdated,
      filterRemovedAppointments,
      filterRespiratoryRateAdded,
      filterRespiratoryRateRemoved,
      filterRespiratoryRateUpdated,
      filterTemperatureAdded,
      filterTemperatureRemoved,
      filterTemperatureUpdated,
      filterUnarchivedAppointments,
      filterUpdatedAppointments,
      filterUpdatedWaiterItem,
      filterUrineDipstickAdded,
      filterUrineDipstickRemove,
      filterUrineDipstickUpdated,
      filterVisualAcuityAdded,
      filterVisualAcuityRemoved,
      filterVisualAcuityUpdated,
      filterPainAdded,
      filterPainUpdated,
      filterPainRemoved,
      filterVitalsAdded,
      filterVitalsArchived,
      filterVitalsEvent,
      filterVitalsRemoved,
      filterVitalsUnarchived,
      filterVitalsUpdated,
      filterWaitingListArchivedItems,
      filterWaitingListCheckedInOrOutItems,
      filterWaitingListDeletedItems,
      filterWaitingListUnarchivedItems,
      filterDischargePatientsRemoved,
      filterTransferPatientsAdded,
      filterPatientTransferred,
      filterTransferPatientsRemoved,
      filterTransferPatientsUpdated,
      filterAdmissionNotesAdded,
      filterAdmissionNotesRemoved,
      filterAdmissionNotesUpdated,
      filterBloodTransfusionAdded,
      filterBloodTransfusionRemoved,
      filterBloodTransfusionUpdated,
      filterConsultationEvent,
      filterConsultationTreatmentPlanAdded,
      filterConsultationTreatmentPlanRemoved,
      filterConsultationTreatmentPlanUpdated,
      filterDispenseAdded,
      filterDispenseRemoved,
      filterDispenseUpdated,
      filterInvestigationAdditionalNotesAdded,
      filterInvestigationAdditionalNotesRemoved,
      filterInvestigationAdditionalNotesUpdated,
      filterLaboratoryAdded,
      filterLaboratoryEvent,
      filterRadiologyAdded,
      filterLaboratoryUpdated,
      filterProcedureOperationNoteAdded,
      filterProcedureOperationNoteRemoved,
      filterProcedureOperationNoteUpdated,
      filterRadiologyEvent,
      filterRadiologyUpdated,
      filterMedicationDetailsAdded,
      filterMedicationDetailsRemoved,
      filterMedicationDetailsUpdated,
      filterTransferEvent,
      filterOrgBillsAdded,
      filterOrgBillsUpdated,
      filterOrgBillsRemoved,
      filterOrgBillsArchived,
      filterOrgBillDetailUpdated,
      filterOrgBillsUnarchived,
      filterOrganizationPermissionRevoked,
      filterOrganizationPermissionGranted,
      filterPatientPermissionRevoked,
      filterPatientPermissionGranted,
      byProfileIds,
      filterReferralAdded,
      filterAppointmentEvent,
      filterWaiterEvent,
      byHospitalOrProfileId,
      filterOrgBillsRecalled,
      filterOrgBillsRemoved,
      filterArchiveWallet,
      filterCardAdded,
      filterInputAdded,
      filterOutputAdded,
      filterInputUpdated,
      filterOutputUpdated,
      filterInputRemoved,
      filterOutputRemoved,
      filterWalletEvent,
      filterWalletTransactionEvent,
      filterCardRemoved,
      filterBillingEvent,
      filterOrgBillsPaid,
      registeredPatientFilter,
      removedPatientFilter,
      archivedPatientFilter,
      unarchivedPatientFilter,
      filterAntenatalAdded,
      filterAntenatalUpdated,
      filterAntenatalRemoved,
      filterAntenatalArchived,
      filterAntenatalUnarchived,
      filterAntenatalEvent,
      filterAntenatalDetailsAdded,
      filterAntenatalDetailsUpdated,
      filterAntenatalDetailsRemoved,
      filterInventoryEvent,
      filterInventoryAdded,
      filterInventoryUpdated,
      filterInventoryRemoved,
      filterInventoryItemUpdated,
      filterAdmissionLineAdded,
      filterAdmissionLineRemoved,
      filterAdmissionLineUpdated,
      filterOutpatientConsultationAdded,
      filterMedicationBundleAdded,
      filterMedicationBundleUpdated,
      filterMedicationBundleRemoved,
      filterMedicationBundleArchived,
      filterMedicationBundleUnarchived,
      filterMedicationBundleItemAdded,
      filterMedicationBundleItemUpdated,
      filterMedicationBundleItemRemoved,
      filterPaymentDepositAdded,
      filterPaymentDepositRemoved,
      filterVirtualCareAppointmentAdded,
      filterVirtualCareAppointmentUpdated,
      filterVirtualCareAppointmentRemoved,
      filterVirtualCareAppointmentArchived,
      filterVirtualCareAppointmentUnarchived,
      filterPatientLookupResultUpdated,
      filterRefundDepositAdded,
      filterRefundDepositRemoved,
      filterRefundDepositUpdated,
      filterRequestProcedureAdded,
      filterRequestProcedureUpdated,
      filterRequestProcedureRemoved,
      filterRequestProcedureArchived,
      filterRequestProcedureUnarchived,
      filterHMOClaimUpdated,
      filterUtilizationUpdated,
      filterPreauthorizationFlagged,
      filterHmoClaimFlagged,
      filterUtilizationReferralUpdated,
      filterPreauthorisationReferralUpdated,
      filterHmoClaimConfirmation,
      filterNursingServiceProgressNoteAdded,
      filterNursingServiceProgressNoteUpdated,
      filterNursingServiceProgressNoteRemoved,
    ];
    filterFunctions.forEach((func) => func(null, {}));
  });

  it('byHospital(): returns false if hospital id in data and variables dont match', () => {
    data = { hospitalId: '1' };
    variables = { hospitalId: '2' };
    expect(byHospital(data, variables)).toBeFalsy();
  });

  it('byHospital(): returns true if hospital id in data and variables dont match', () => {
    data = { hospitalId: '1' };
    variables = { hospitalId: '1' };
    expect(byHospital(data, variables)).toBeTruthy();
  });

  it('byHospitalIds(): returns false if hospital ids in data and variables dont match', () => {
    data = { hospitalIds: ['1', '2'] };
    variables = { hospitalId: '3' };
    expect(byHospitalIds(data, variables)).toBeFalsy();
  });

  it('byHospitalIds(): returns true if hospital ids in data and variables dont match', () => {
    data = { hospitalIds: ['1', '2', '3'] };
    variables = { hospitalId: '3' };
    expect(byHospitalIds(data, variables)).toBeTruthy();
  });

  it('byClinifyId(): returns false if hospital id in data and variables dont match', () => {
    data = { profileId: '1' };
    variables = { profileId: '2' };
    expect(byProfileId(data, variables)).toBeFalsy();
  });

  it('byClinifyId(): returns true if hospital id in data and variables dont match', () => {
    data = { profileId: '1' };
    variables = { profileId: '1' };
    expect(byProfileId(data, variables)).toBeTruthy();
  });

  it('filterWaitingListArchivedItems(): returns false if hospital id in data and variables dont match', () => {
    data = { WaitersArchived: [{ hospitalId: '1' }] };
    variables = { hospitalId: '2' };
    expect(filterWaitingListArchivedItems(data, variables)).toBeFalsy();
  });

  it('filterWaitingListArchivedItems(): returns true if hospital id in data and variables dont match', () => {
    data = { WaitersArchived: [{ hospitalId: '1' }] };
    variables = { hospitalId: '1' };
    expect(filterWaitingListArchivedItems(data, variables)).toBeTruthy();
  });

  it('byHospitalOrClinifyId(): returns false if hospital id in data and variables dont match', () => {
    data = { WaitersUnarchived: [{ hospitalId: '1' }] };
    variables = { hospitalId: '2' };
    expect(filterWaitingListUnarchivedItems(data, variables)).toBeFalsy();
  });

  it('byHospitalOrClinifyId(): returns true if hospital id in data and variables dont match', () => {
    data = { WaitersUnarchived: [{ hospitalId: '1' }] };
    variables = { hospitalId: '1' };
    expect(filterWaitingListUnarchivedItems(data, variables)).toBeTruthy();
  });

  it('filterOrgBillsAdded(): returns true if hospital id in data and variables dont match', () => {
    data = {
      OrgBillAdded: {
        receiverProfileId: '1',
        senderHospitalId: '1',
      },
    };
    variables = { hospitalId: '1', profileId: '1' };
    expect(filterOrgBillsAdded(data, variables)).toBeTruthy();
  });

  it('filterOrgBillsUpdated(): returns true if hospital id in data and variables dont match', () => {
    data = {
      OrgBillUpdated: {
        receiverProfileId: '1',
        senderHospitalId: '1',
      },
    };
    variables = { hospitalId: '1', profileId: '1' };
    expect(filterOrgBillsUpdated(data, variables)).toBeTruthy();
  });

  it('filterOrgBillsRemoved(): returns true if hospital id in data and variables dont match', () => {
    data = {
      OrgBillRemoved: [
        {
          receiverProfileId: '1',
          senderHospitalId: '1',
        },
      ],
    };
    variables = { hospitalId: '1', profileId: '1' };
    expect(filterOrgBillsRemoved(data, variables)).toBeTruthy();
  });

  it('filterOrgBillsArchived(): returns true if hospital id in data and variables dont match', () => {
    data = {
      OrgBillArchived: [
        {
          receiverProfileId: '1',
          senderHospitalId: '1',
        },
      ],
    };
    variables = { hospitalId: '1', profileId: '1' };
    expect(filterOrgBillsArchived(data, variables)).toBeTruthy();
  });

  it('filterOrgBillsArchived(): returns true if hospital id in data and variables dont match', () => {
    data = {
      OrgBillUnarchived: [
        {
          receiverProfileId: '1',
          senderHospitalId: '1',
        },
      ],
    };
    variables = { hospitalId: '1', profileId: '1' };
    expect(filterOrgBillsUnarchived(data, variables)).toBeTruthy();
  });

  it('filterOrgBillsCancelled(): returns true if hospital id in data and variables dont match', () => {
    data = {
      OrgBillCancelled: {
        receiverProfileId: '1',
        hospitalId: '1',
      },
    };
    variables = { hospitalId: '1', profileId: '1' };
    expect(filterOrgBillsCancelled(data, variables)).toBeTruthy();
  });

  it('filterOrgBillDetailUpdated(): returns true if hospital id in data and variables dont match', () => {
    data = {
      OrgBillDetailUpdated: {
        receiverProfileId: '1',
        hospitalId: '1',
      },
    };
    variables = { hospitalId: '1', profileId: '1' };
    expect(filterOrgBillDetailUpdated(data, variables)).toBeTruthy();
  });

  it('filterArchiveWallet(): returns true if profileId id in data and variables dont match', () => {
    data = {
      user: {
        profiles: [
          {
            id: '1',
          },
        ],
      },
    };
    variables = { profileId: '1' };
    expect(filterArchiveWallet(data, variables)).toBeTruthy();
  });

  it('filterOrgBillsRecalled(): returns true if hospital id in data and variables dont match', () => {
    data = {
      OrgBillRecalled: {
        receiverProfileId: '1',
        hospitalId: '1',
      },
    };
    variables = { hospitalId: '1', profileId: '1' };
    expect(filterOrgBillsRecalled(data, variables)).toBeTruthy();
  });

  it('filterWaitingListDeletedItems(): returns false if hospital id in data and variables dont match', () => {
    data = { WaitersRemoved: [{ hospitalId: '1' }] };
    variables = { hospitalId: '2' };
    expect(filterWaitingListDeletedItems(data, variables)).toBeFalsy();
  });

  it('filterWaitingListDeletedItems(): returns true if hospital id in data and variables dont match', () => {
    data = { WaitersRemoved: [{ hospitalId: '1' }] };
    variables = { hospitalId: '1' };
    expect(filterWaitingListDeletedItems(data, variables)).toBeTruthy();
  });

  it('filterWaitingListCheckedInOrOutItems(): returns false if hospital id in data and variables dont match', () => {
    data = { WaitersCheckedInOrOut: [{ hospitalId: '1' }] };
    variables = { hospitalId: '2' };
    expect(filterWaitingListCheckedInOrOutItems(data, variables)).toBeFalsy();
  });

  it('filterWaitingListCheckedInOrOutItems(): returns true if hospital id in data and variables dont match', () => {
    data = { WaitersCheckedInOrOut: [{ hospitalId: '1' }] };
    variables = { hospitalId: '1' };
    expect(filterWaitingListCheckedInOrOutItems(data, variables)).toBeTruthy();
  });

  it('filterWaitingListCheckedInOrOutItems(): returns false if hospital id in data and variables dont match', () => {
    data = { WaiterUpdated: [{ hospitalId: '1' }] };
    variables = { hospitalId: '2' };
    expect(filterUpdatedWaiterItem(data, variables)).toBeFalsy();
  });

  it('filterWaitingListCheckedInOrOutItems(): returns true if hospital id in data and variables dont match', () => {
    data = { WaiterUpdated: { hospitalId: '1' } };
    variables = { hospitalId: '1' };
    expect(filterUpdatedWaiterItem(data, variables)).toBeTruthy();
  });

  it('filterAddedWaiterItem(): returns false if hospital id in data and variables dont match', () => {
    data = { WaiterAdded: { hospitalId: '1' } };
    variables = { hospitalId: '2' };
    expect(filterAddedWaiterItem(data, variables)).toBeFalsy();
  });

  it('filterAddedWaiterItem(): returns true if hospital id in data and variables dont match', () => {
    data = { WaiterAdded: { hospitalId: '1' } };
    variables = { hospitalId: '1' };
    expect(filterAddedWaiterItem(data, variables)).toBeTruthy();
  });

  it('filterAddedAppointments(): returns false if hospital id in data and variables dont match', () => {
    data = {
      AppointmentAdded: { hospitalId: '1', profileId: '1' },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterAddedAppointments(data, variables)).toBeFalsy();
  });

  it('filterAddedAppointments(): returns true if hospital id in data and variables dont match', () => {
    data = {
      AppointmentAdded: { hospitalId: '1', profileId: '1' },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterAddedAppointments(data, variables)).toBeTruthy();
  });

  it('filterUpdatedAppointments(): returns false if hospital id in data and variables dont match', () => {
    data = {
      AppointmentUpdated: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterUpdatedAppointments(data, variables)).toBeFalsy();
  });

  it('filterUpdatedAppointments(): returns false if hospital id in data and variables dont match', () => {
    data = {
      AppointmentUpdated: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterUpdatedAppointments(data, variables)).toBeFalsy();
  });

  it('filterRemovedAppointments(): returns false if hospital id in data and variables dont match', () => {
    data = {
      AppointmentsRemoved: [
        {
          hospitalId: '1',
          profileId: '1',
        },
      ],
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterRemovedAppointments(data, variables)).toBeFalsy();
  });

  it('filterRemovedAppointments(): returns true if hospital id in data and variables dont match', () => {
    data = {
      AppointmentsRemoved: [
        {
          hospitalId: '1',
          profileId: '1',
        },
      ],
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterRemovedAppointments(data, variables)).toBeTruthy();
  });

  it('filterUnarchivedAppointments(): returns false if hospital id in data and variables dont match', () => {
    data = {
      AppointmentsUnarchived: [
        {
          hospitalId: '1',
          profileId: '1',
        },
      ],
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterRemovedAppointments(data, variables)).toBeFalsy();
  });

  it('filterUnarchivedAppointments(): returns true if hospital id in data and variables dont match', () => {
    data = {
      AppointmentsUnarchived: [
        {
          hospitalId: '1',
          profileId: '1',
        },
      ],
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterUnarchivedAppointments(data, variables)).toBeTruthy();
  });

  it('filterArchivedAppointments(): returns false if hospital id in data and variables dont match', () => {
    data = {
      AppointmentsArchived: [
        {
          hospitalId: '1',
          profileId: '1',
        },
      ],
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterArchivedAppointments(data, variables)).toBeFalsy();
  });

  it('filterArchivedAppointments(): returns true if hospital id in data and variables dont match', () => {
    data = {
      AppointmentsArchived: [
        {
          hospitalId: '1',
          profileId: '1',
        },
      ],
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterArchivedAppointments(data, variables)).toBeTruthy();
  });

  it('filterAllergyAdded(): returns false if hospital id in data and variables dont match', () => {
    data = {
      AllergyAdded: { hospitalId: '1', profileId: '1' },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterAllergyAdded(data, variables)).toBeFalsy();
  });

  it('filterAllergyAdded(): returns true if hospital id in data and variables dont match', () => {
    data = {
      AllergyAdded: { hospitalId: '1', profileId: '1' },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterAllergyAdded(data, variables)).toBeTruthy();
  });

  it('filterAllergyUpdated(): returns false if hospital id in data and variables dont match', () => {
    data = {
      AllergyUpdated: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterAllergyUpdated(data, variables)).toBeFalsy();
  });

  it('filterAllergyUpdated(): returns false if hospital id in data and variables dont match', () => {
    data = {
      AllergyUpdated: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterAllergyUpdated(data, variables)).toBeFalsy();
  });

  it('filterAllergyRemoved(): returns false if hospital id in data and variables dont match', () => {
    data = {
      AllergyRemoved: [
        {
          hospitalId: '1',
          profileId: '1',
        },
      ],
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterAllergyRemoved(data, variables)).toBeFalsy();
  });

  it('filterAllergyRemoved(): returns true if hospital id in data and variables dont match', () => {
    data = {
      AllergyRemoved: [
        {
          hospitalId: '1',
          profileId: '1',
        },
      ],
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterAllergyRemoved(data, variables)).toBeTruthy();
  });

  it('filterUnarchivedAppointments(): returns false if hospital id in data and variables dont match', () => {
    data = {
      AllergyUnarchived: [
        {
          hospitalId: '1',
          profileId: '1',
        },
      ],
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterAllergyUnarchived(data, variables)).toBeFalsy();
  });

  it('filterUnarchivedAppointments(): returns true if hospital id in data and variables dont match', () => {
    data = {
      AllergyUnarchived: [
        {
          hospitalId: '1',
          profileId: '1',
        },
      ],
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterAllergyUnarchived(data, variables)).toBeTruthy();
  });

  it('filterAllergyArchived(): returns false if hospital id in data and variables dont match', () => {
    data = {
      AllergyArchived: [
        {
          hospitalId: '1',
          profileId: '1',
        },
      ],
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterAllergyArchived(data, variables)).toBeFalsy();
  });

  it('filterAllergyArchived(): returns true if hospital id in data and variables dont match', () => {
    data = {
      AllergyArchived: [
        {
          hospitalId: '1',
          profileId: '1',
        },
      ],
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterAllergyArchived(data, variables)).toBeTruthy();
  });

  it('filterAllergyEvent(): returns false if clinify id in data and variables dont match', () => {
    data = {
      allergy: {
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterAllergyEvent(data, variables)).toBeFalsy();
  });

  it('filterAllergyEvent(): returns true if hospital id in data and variables dont match', () => {
    data = {
      allergy: {
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterAllergyEvent(data, variables)).toBeTruthy();
  });

  it('filterAdmissionAdded(): returns false if hospital id in data and variables dont match', () => {
    data = {
      AdmissionAdded: { hospitalId: '1', profileId: '1' },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterAdmissionAdded(data, variables)).toBeFalsy();
  });

  it('filterAdmissionAdded(): returns false if hospital id in data and variables dont match', () => {
    data = {
      AdmissionAdded: { hospitalId: '1', profileId: '1' },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterAdmissionAdded(data, variables)).toBeTruthy();
  });

  it('filterTransferEvent(): returns true if hospital id in data and variables match', () => {
    data = {
      transfer: { hospitalIds: ['1', '2'] },
    };
    variables = { hospitalId: '2' };
    expect(filterAdmissionAdded(data, variables)).toBeTruthy();
  });

  it('filterTransferEvent(): returns false if hospital id in data and variables dont match', () => {
    data = {
      transfer: { hospitalIds: ['1', '2'] },
    };
    variables = { hospitalId: '5' };
    expect(filterTransferEvent(data, variables)).toBeFalsy();
  });

  it('filterAdmissionUpdated(): returns false if hospital id in data and variables dont match', () => {
    data = {
      AdmissionUpdated: {
        hospitalId: '1',
        profileId: '1',
      },
      hospitalIds: ['5', '6'],
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterAdmissionUpdated(data, variables)).toBeFalsy();
  });

  it('filterAdmissionUpdated(): returns true if hospital id in data and variables match', () => {
    data = {
      AdmissionUpdated: {
        hospitalId: '1',
        profileId: '1',
      },
      hospitalIds: ['5', '6'],
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterAdmissionUpdated(data, variables)).toBeTruthy();

    variables = { hospitalId: '5', profileId: '9' };
    expect(filterAdmissionUpdated(data, variables)).toBeTruthy();
  });

  it('ffilterAdmissionsRemoved(): returns false if hospital id in data and variables dont match', () => {
    data = {
      AdmissionsRemoved: [
        {
          hospitalId: '1',
          profileId: '1',
        },
      ],
      hospitalIds: ['5', '6'],
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterAdmissionsRemoved(data, variables)).toBeFalsy();
  });

  it('filterAdmissionsRemoved(): returns true if hospital id in data and variables dont match', () => {
    data = {
      AdmissionsRemoved: [
        {
          hospitalId: '1',
          profileId: '1',
        },
      ],
      hospitalIds: ['5', '6'],
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterAdmissionsRemoved(data, variables)).toBeTruthy();

    variables = { hospitalId: '5', profileId: '9' };
    expect(filterAdmissionUpdated(data, variables)).toBeTruthy();
  });

  it('filterAdmissionsUnarchived(): returns false if hospital id in data and variables dont match', () => {
    data = {
      AdmissionsUnarchived: [
        {
          hospitalId: '1',
          profileId: '1',
        },
      ],
      hospitalIds: ['5', '6'],
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterAdmissionsUnarchived(data, variables)).toBeFalsy();
  });

  it('filterAdmissionsUnarchived(): returns true if hospital id in data and variables dont match', () => {
    data = {
      AdmissionsUnarchived: [
        {
          hospitalId: '1',
          profileId: '1',
        },
      ],
      hospitalIds: ['5', '6'],
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterAdmissionsUnarchived(data, variables)).toBeTruthy();

    variables = { hospitalId: '5', profileId: '99' };
    expect(filterAdmissionsUnarchived(data, variables)).toBeTruthy();
  });

  it('filterAdmissionsArchived(): returns false if hospital id in data and variables dont match', () => {
    data = {
      AdmissionsArchived: [
        {
          hospitalId: '1',
          profileId: '1',
        },
      ],
      hospitalIds: ['5', '6'],
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterAdmissionsArchived(data, variables)).toBeFalsy();
  });

  it('filterAdmissionsArchived(): returns true if hospital id in data and variables dont match', () => {
    data = {
      AdmissionsArchived: [
        {
          hospitalId: '1',
          profileId: '1',
        },
      ],
      hospitalIds: ['5', '6'],
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterAdmissionsArchived(data, variables)).toBeTruthy();

    variables = { hospitalId: '5', profileId: '99' };
    expect(filterAdmissionsArchived(data, variables)).toBeTruthy();
  });

  it('filterAdmissionEvent(): returns false if clinify id in data and variables dont match', () => {
    data = {
      admission: {
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterAdmissionEvent(data, variables)).toBeFalsy();
  });

  it('filterAdmissionEvent(): returns true if hospital id in data and variables dont match', () => {
    data = {
      admission: {
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterAdmissionEvent(data, variables)).toBeTruthy();
  });

  it('filterConsultationAdded(): returns false if hospital id in data and variables dont match', () => {
    data = {
      ConsultationAdded: { hospitalId: '1', profileId: '1' },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterConsultationAdded(data, variables)).toBeFalsy();
  });

  it('filterConsultationAdded: returns false if hospital id in data and variables dont match', () => {
    data = {
      ConsultationAdded: { hospitalId: '1', profileId: '1' },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterConsultationAdded(data, variables)).toBeTruthy();
  });

  it('filterConsultationAdded: returns true if hospital id in data and is reerral', () => {
    data = {
      consultation: {
        hospitalIds: ['1', '2'],
      },
      ConsultationAdded: {
        hospitalId: '1',
        profileId: '1',
        referral: true,
      },
    };
    variables = { hospitalId: '1', profileId: '2' };
    expect(filterConsultationAdded(data, variables)).toBeTruthy();
  });

  it('filterConsultationUpdated(): returns false if hospital id in data and variables dont match', () => {
    data = {
      ConsultationUpdated: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterConsultationUpdated(data, variables)).toBeFalsy();
  });

  it('filterConsultationUpdated(): returns false if hospital id in data and variables dont match', () => {
    data = {
      ConsultationUpdated: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterConsultationUpdated(data, variables)).toBeTruthy();
  });

  it('filterConsultationRemoved(): returns false if hospital id in data and variables dont match', () => {
    data = {
      ConsultationRemoved: [
        {
          hospitalId: '1',
          profileId: '1',
        },
      ],
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterConsultationRemoved(data, variables)).toBeFalsy();
  });

  it('filterConsultationRemoved(): returns true if hospital id in data and variables dont match', () => {
    data = {
      ConsultationRemoved: [
        {
          hospitalId: '1',
          profileId: '1',
        },
      ],
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterConsultationRemoved(data, variables)).toBeTruthy();
  });

  it('filterConsultationUnarchived(): returns false if hospital id in data and variables dont match', () => {
    data = {
      ConsultationUnarchived: [
        {
          hospitalId: '1',
          profileId: '1',
        },
      ],
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterConsultationUnarchived(data, variables)).toBeFalsy();
  });

  it('filterConsultationUnarchived(): returns true if hospital id in data and variables dont match', () => {
    data = {
      ConsultationUnarchived: [
        {
          hospitalId: '1',
          profileId: '1',
        },
      ],
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterConsultationUnarchived(data, variables)).toBeTruthy();
  });

  it('filterConsultationArchived(): returns false if hospital id in data and variables dont match', () => {
    data = {
      ConsultationArchived: [
        {
          hospitalId: '1',
          profileId: '1',
        },
      ],
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterConsultationArchived(data, variables)).toBeFalsy();
  });

  it('filterConsultationArchived(): returns true if hospital id in data and variables dont match', () => {
    data = {
      ConsultationArchived: [
        {
          hospitalId: '1',
          profileId: '1',
        },
      ],
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterConsultationArchived(data, variables)).toBeTruthy();
  });

  it('filterImmunizationAdded(): returns false if hospital id in data and variables dont match', () => {
    data = {
      ImmunizationAdded: { hospitalId: '1', profileId: '1' },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterImmunizationAdded(data, variables)).toBeFalsy();
  });

  it('filterImmunizationAdded: returns false if hospital id in data and variables dont match', () => {
    data = {
      ImmunizationAdded: { hospitalId: '1', profileId: '1' },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterImmunizationAdded(data, variables)).toBeTruthy();
  });

  it('filterImmunizationUpdated(): returns false if hospital id in data and variables dont match', () => {
    data = {
      ImmunizationUpdated: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterImmunizationUpdated(data, variables)).toBeFalsy();
  });

  it('filterImmunizationUpdated(): returns false if hospital id in data and variables dont match', () => {
    data = {
      ImmunizationUpdated: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterImmunizationUpdated(data, variables)).toBeTruthy();
  });

  it('filterImmunizationRemoved(): returns false if hospital id in data and variables dont match', () => {
    data = {
      ImmunizationRemoved: [
        {
          hospitalId: '1',
          profileId: '1',
        },
      ],
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterImmunizationRemoved(data, variables)).toBeFalsy();
  });

  it('filterImmunizationRemoved(): returns true if hospital id in data and variables dont match', () => {
    data = {
      ImmunizationRemoved: [
        {
          hospitalId: '1',
          profileId: '1',
        },
      ],
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterImmunizationRemoved(data, variables)).toBeTruthy();
  });

  it('filterImmunizationUnarchived(): returns false if hospital id in data and variables dont match', () => {
    data = {
      ImmunizationUnarchived: [
        {
          hospitalId: '1',
          profileId: '1',
        },
      ],
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterImmunizationUnarchived(data, variables)).toBeFalsy();
  });

  it('filterImmunizationUnarchived(): returns true if hospital id in data and variables dont match', () => {
    data = {
      ImmunizationUnarchived: [
        {
          hospitalId: '1',
          profileId: '1',
        },
      ],
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterImmunizationUnarchived(data, variables)).toBeTruthy();
  });

  it('filterImmunizationArchived(): returns false if hospital id in data and variables dont match', () => {
    data = {
      ImmunizationArchived: [
        {
          hospitalId: '1',
          profileId: '1',
        },
      ],
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterImmunizationArchived(data, variables)).toBeFalsy();
  });

  it('filterImmunizationArchived(): returns true if hospital id in data and variables dont match', () => {
    data = {
      ImmunizationArchived: [
        {
          hospitalId: '1',
          profileId: '1',
        },
      ],
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterImmunizationArchived(data, variables)).toBeTruthy();
  });

  it('filterImmunizationEvent(): returns false if clinify id in data and variables dont match', () => {
    data = {
      immunization: {
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterImmunizationEvent(data, variables)).toBeFalsy();
  });

  it('filterImmunizationEvent(): returns true if hospital id in data and variables dont match', () => {
    data = {
      immunization: {
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterImmunizationEvent(data, variables)).toBeTruthy();
  });

  it('filterInvestigationAdded(): returns false if hospital id in data and variables dont match', () => {
    data = {
      InvestigationAdded: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterInvestigationAdded(data, variables)).toBeFalsy();
  });

  it('filterInvestigationEvent(): returns true if match', () => {
    data = {
      investigation: {
        hospitalId: '1',
      },
    };
    variables = { hospitalId: '1' };
    expect(filterInvestigationEvent(data, variables)).toBeTruthy();
  });

  it('filterInvestigationEvent(): returns false if no match', () => {
    data = {
      investigation: {
        hospitalId: '1',
      },
    };
    variables = { hospitalId: '5' };
    expect(filterInvestigationEvent(data, variables)).toBeFalsy();
  });

  it('filterInvestigationAdded(): returns false if hospital id in data and variables dont match', () => {
    data = {
      InvestigationAdded: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '1', profileId: '1' };
    expect(filterInvestigationAdded(data, variables)).toBeTruthy();
  });

  it('filterInvestigationUpdated(): returns false if hospital id in data and variables dont match', () => {
    data = {
      InvestigationUpdated: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterInvestigationUpdated(data, variables)).toBeFalsy();
  });

  it('filterInvestigationUpdated(): returns false if hospital id in data and variables dont match', () => {
    data = {
      InvestigationUpdated: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterInvestigationUpdated(data, variables)).toBeTruthy();
  });

  it('filterInvestigationRemoved(): returns false if hospital id in data and variables dont match', () => {
    data = {
      InvestigationRemoved: [
        {
          hospitalId: '1',
          profileId: '1',
        },
      ],
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterInvestigationRemoved(data, variables)).toBeFalsy();
  });

  it('filterInvestigationRemoved(): returns true if hospital id in data and variables dont match', () => {
    data = {
      InvestigationRemoved: [
        {
          hospitalId: '1',
          profileId: '1',
        },
      ],
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterInvestigationRemoved(data, variables)).toBeTruthy();
  });

  it('filterInvestigationUnarchived(): returns false if hospital id in data and variables dont match', () => {
    data = {
      ImmunizationUnarchived: [
        {
          hospitalId: '1',
          profileId: '1',
        },
      ],
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterImmunizationUnarchived(data, variables)).toBeFalsy();
  });

  it('filterInvestigationUnarchived(): returns true if hospital id in data and variables dont match', () => {
    data = {
      InvestigationUnarchived: [
        {
          hospitalId: '1',
          profileId: '1',
        },
      ],
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterInvestigationUnarchived(data, variables)).toBeTruthy();
  });

  it('filterInvestigationArchived(): returns false if hospital id in data and variables dont match', () => {
    data = {
      InvestigationArchived: [
        {
          hospitalId: '1',
          profileId: '1',
        },
      ],
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterInvestigationArchived(data, variables)).toBeFalsy();
  });

  it('filterInvestigationArchived(): returns true if hospital id in data and variables dont match', () => {
    data = {
      InvestigationArchived: [
        {
          hospitalId: '1',
          profileId: '1',
        },
      ],
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterInvestigationArchived(data, variables)).toBeTruthy();
  });

  it('filterMedicationAdded(): returns false if hospital id in data and variables dont match', () => {
    data = {
      MedicationAdded: { hospitalId: '1', profileId: '1' },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterMedicationAdded(data, variables)).toBeFalsy();
  });

  it('filterMedicationAdded(): returns false if hospital id in data and variables dont match', () => {
    data = {
      MedicationAdded: { hospitalId: '1', profileId: '1' },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterMedicationAdded(data, variables)).toBeTruthy();
  });

  it('filterMedicationUpdated(): returns false if hospital id in data and variables dont match', () => {
    data = {
      MedicationUpdated: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterMedicationUpdated(data, variables)).toBeFalsy();
  });

  it('filterMedicationUpdated(): returns false if hospital id in data and variables dont match', () => {
    data = {
      MedicationUpdated: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterMedicationUpdated(data, variables)).toBeTruthy();
  });

  it('filterMedicationRemoved(): returns false if hospital id in data and variables dont match', () => {
    data = {
      MedicationRemoved: [
        {
          hospitalId: '1',
          profileId: '1',
        },
      ],
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterMedicationRemoved(data, variables)).toBeFalsy();
  });

  it('filterMedicationRemoved(): returns true if hospital id in data and variables dont match', () => {
    data = {
      MedicationRemoved: [
        {
          hospitalId: '1',
          profileId: '1',
        },
      ],
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterMedicationRemoved(data, variables)).toBeTruthy();
  });

  it('filterMedicationUnarchived(): returns false if hospital id in data and variables dont match', () => {
    data = {
      MedicationUnarchived: [
        {
          hospitalId: '1',
          profileId: '1',
        },
      ],
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterMedicationUnarchived(data, variables)).toBeFalsy();
  });

  it('filterMedicationUnarchived(): returns true if hospital id in data and variables dont match', () => {
    data = {
      MedicationUnarchived: [
        {
          hospitalId: '1',
          profileId: '1',
        },
      ],
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterMedicationUnarchived(data, variables)).toBeTruthy();
  });

  it('filterMedicationArchived(): returns false if hospital id in data and variables dont match', () => {
    data = {
      MedicationArchived: [
        {
          hospitalId: '1',
          profileId: '1',
        },
      ],
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterMedicationArchived(data, variables)).toBeFalsy();
  });

  it('filterMedicationArchived(): returns true if hospital id in data and variables dont match', () => {
    data = {
      MedicationArchived: [
        {
          hospitalId: '1',
          profileId: '1',
        },
      ],
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterMedicationArchived(data, variables)).toBeTruthy();
  });

  it('filterMedicationEvent(): returns false if clinify id in data and variables dont match', () => {
    data = {
      medication: {
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterMedicationEvent(data, variables)).toBeFalsy();
  });

  it('filterMedicationEvent(): returns true if hospital id in data and variables dont match', () => {
    data = {
      medication: {
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterMedicationEvent(data, variables)).toBeTruthy();
  });

  it('filterProcedureAdded(): returns false if hospital id in data and variables dont match', () => {
    data = {
      ProcedureAdded: { hospitalId: '1', profileId: '1' },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterProcedureAdded(data, variables)).toBeFalsy();
  });

  it('filterProcedureAdded(): returns false if hospital id in data and variables dont match', () => {
    data = {
      ProcedureAdded: { hospitalId: '1', profileId: '1' },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterProcedureAdded(data, variables)).toBeTruthy();
  });

  it('filterProcedureUpdated(): returns false if hospital id in data and variables dont match', () => {
    data = {
      ProcedureUpdated: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterProcedureUpdated(data, variables)).toBeFalsy();
  });

  it('filterProcedureUpdated(): returns false if hospital id in data and variables dont match', () => {
    data = {
      ProcedureUpdated: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterProcedureUpdated(data, variables)).toBeTruthy();
  });

  it('filterProcedureRemoved(): returns false if hospital id in data and variables dont match', () => {
    data = {
      ProcedureRemoved: [
        {
          hospitalId: '1',
          profileId: '1',
        },
      ],
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterProcedureRemoved(data, variables)).toBeFalsy();
  });

  it('filterProcedureRemoved(): returns true if hospital id in data and variables dont match', () => {
    data = {
      ProcedureRemoved: [
        {
          hospitalId: '1',
          profileId: '1',
        },
      ],
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterProcedureRemoved(data, variables)).toBeTruthy();
  });

  it('filterProcedureUnarchived(): returns false if hospital id in data and variables dont match', () => {
    data = {
      ProcedureUnarchived: [
        {
          hospitalId: '1',
          profileId: '1',
        },
      ],
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterProcedureUnarchived(data, variables)).toBeFalsy();
  });

  it('filterProcedureUnarchived(): returns true if hospital id in data and variables dont match', () => {
    data = {
      ProcedureUnarchived: [
        {
          hospitalId: '1',
          profileId: '1',
        },
      ],
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterProcedureUnarchived(data, variables)).toBeTruthy();
  });

  it('filterProcedureArchived(): returns false if hospital id in data and variables dont match', () => {
    data = {
      ProcedureArchived: [
        {
          hospitalId: '1',
          profileId: '1',
        },
      ],
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterProcedureArchived(data, variables)).toBeFalsy();
  });

  it('filterProcedureArchived(): returns true if hospital id in data and variables dont match', () => {
    data = {
      ProcedureArchived: [
        {
          hospitalId: '1',
          profileId: '1',
        },
      ],
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterProcedureArchived(data, variables)).toBeTruthy();
  });

  it('filterProcedureEvent(): returns false if clinify id in data and variables dont match', () => {
    data = {
      procedure: {
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterProcedureEvent(data, variables)).toBeFalsy();
  });

  it('filterProcedureEvent(): returns true if hospital id in data and variables dont match', () => {
    data = {
      procedure: {
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterProcedureEvent(data, variables)).toBeTruthy();
  });

  it('filterVitalsAdded(): returns false if hospital id in data and variables dont match', () => {
    data = {
      VitalsAdded: { hospitalId: '1', profileId: '1' },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterVitalsAdded(data, variables)).toBeFalsy();
  });

  it('filterVitalsAdded(): returns false if hospital id in data and variables dont match', () => {
    data = {
      VitalsAdded: { hospitalId: '1', profileId: '1' },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterVitalsAdded(data, variables)).toBeTruthy();
  });

  it('filterVitalsUpdated(): returns false if hospital id in data and variables dont match', () => {
    data = {
      VitalsUpdated: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterVitalsUpdated(data, variables)).toBeFalsy();
  });

  it('filterVitalsUpdated(): returns false if hospital id in data and variables dont match', () => {
    data = {
      VitalsUpdated: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterVitalsUpdated(data, variables)).toBeTruthy();
  });

  it('filterVitalsRemoved(): returns false if hospital id in data and variables dont match', () => {
    data = {
      VitalsRemoved: [
        {
          hospitalId: '1',
          profileId: '1',
        },
      ],
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterVitalsRemoved(data, variables)).toBeFalsy();
  });

  it('filterVitalsRemoved(): returns true if hospital id in data and variables dont match', () => {
    data = {
      VitalsRemoved: [
        {
          hospitalId: '1',
          profileId: '1',
        },
      ],
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterVitalsRemoved(data, variables)).toBeTruthy();
  });

  it('filterVitalsUnarchived(): returns false if hospital id in data and variables dont match', () => {
    data = {
      VitalsUnarchived: [
        {
          hospitalId: '1',
          profileId: '1',
        },
      ],
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterVitalsUnarchived(data, variables)).toBeFalsy();
  });

  it('filterVitalsUnarchived(): returns true if hospital id in data and variables dont match', () => {
    data = {
      VitalsUnarchived: [
        {
          hospitalId: '1',
          profileId: '1',
        },
      ],
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterVitalsUnarchived(data, variables)).toBeTruthy();
  });

  it('filterVitalsArchived(): returns false if hospital id in data and variables dont match', () => {
    data = {
      VitalsArchived: [
        {
          hospitalId: '1',
          profileId: '1',
        },
      ],
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterVitalsArchived(data, variables)).toBeFalsy();
  });

  it('filterVitalsArchived(): returns true if hospital id in data and variables dont match', () => {
    data = {
      VitalsArchived: [
        {
          hospitalId: '1',
          profileId: '1',
        },
      ],
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterVitalsArchived(data, variables)).toBeTruthy();
  });

  it('filterVitalsEvent(): returns false if clinify id in data and variables dont match', () => {
    data = {
      vitals: {
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterVitalsEvent(data, variables)).toBeFalsy();
  });

  it('filterVitalsEvent(): returns true if hospital id in data and variables dont match', () => {
    data = {
      vitals: {
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterVitalsEvent(data, variables)).toBeTruthy();
  });

  it('filterAnthropometryAdded(): returns false if hospital id in data and variables dont match', () => {
    data = {
      AnthropometryAdded: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterAnthropometryAdded(data, variables)).toBeFalsy();
  });

  it('filterAnthropometryAdded(): returns false if hospital id in data and variables dont match', () => {
    data = {
      AnthropometryAdded: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterAnthropometryAdded(data, variables)).toBeTruthy();
  });

  it('filterAnthropometryUpdated(): returns false if hospital id in data and variables dont match', () => {
    data = {
      AnthropometryUpdated: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterAnthropometryUpdated(data, variables)).toBeFalsy();
  });

  it('filterAnthropometryUpdated(): returns false if hospital id in data and variables dont match', () => {
    data = {
      AnthropometryUpdated: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterAnthropometryUpdated(data, variables)).toBeTruthy();
  });

  it('filterAnthropometryRemoved(): returns false if hospital id in data and variables dont match', () => {
    data = {
      AnthropometryRemoved: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterAnthropometryRemoved(data, variables)).toBeFalsy();
  });

  it('filterAnthropometryRemoved(): returns true if hospital id in data and variables dont match', () => {
    data = {
      AnthropometryRemoved: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterAnthropometryRemoved(data, variables)).toBeTruthy();
  });

  it('filterBloodGlucoseAdded(): returns false if hospital id in data and variables dont match', () => {
    data = {
      BloodGlucoseAdded: { hospitalId: '1', profileId: '1' },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterBloodGlucoseAdded(data, variables)).toBeFalsy();
  });

  it('filterBloodGlucoseAdded(): returns false if hospital id in data and variables dont match', () => {
    data = {
      BloodGlucoseAdded: { hospitalId: '1', profileId: '1' },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterBloodGlucoseAdded(data, variables)).toBeTruthy();
  });

  it('filterBloodGlucoseUpdated(): returns false if hospital id in data and variables dont match', () => {
    data = {
      BloodGlucoseUpdated: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterBloodGlucoseUpdated(data, variables)).toBeFalsy();
  });

  it('filterBloodGlucoseUpdated(): returns false if hospital id in data and variables dont match', () => {
    data = {
      BloodGlucoseUpdated: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterBloodGlucoseUpdated(data, variables)).toBeTruthy();
  });

  it('filterBloodGlucoseRemoved(): returns false if hospital id in data and variables dont match', () => {
    data = {
      BloodGlucoseRemoved: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterBloodGlucoseRemoved(data, variables)).toBeFalsy();
  });

  it('filterBloodGlucoseRemoved(): returns true if hospital id in data and variables dont match', () => {
    data = {
      BloodGlucoseRemoved: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterBloodGlucoseRemoved(data, variables)).toBeTruthy();
  });

  it('filterBloodPressureAdded(): returns false if hospital id in data and variables dont match', () => {
    data = {
      BloodPressureAdded: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterBloodPressureAdded(data, variables)).toBeFalsy();
  });

  it('ffilterBloodPressureAdded(): returns false if hospital id in data and variables dont match', () => {
    data = {
      BloodPressureAdded: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterBloodPressureAdded(data, variables)).toBeTruthy();
  });

  it('filterBloodPressureUpdated(): returns false if hospital id in data and variables dont match', () => {
    data = {
      BloodPressureUpdated: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterBloodPressureUpdated(data, variables)).toBeFalsy();
  });

  it('filterBloodPressureUpdated(): returns false if hospital id in data and variables dont match', () => {
    data = {
      BloodPressureUpdated: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterBloodPressureUpdated(data, variables)).toBeTruthy();
  });

  it('filterBloodPressureRemoved(): returns false if hospital id in data and variables dont match', () => {
    data = {
      BloodPressureRemoved: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterBloodPressureRemoved(data, variables)).toBeFalsy();
  });

  it('filterBloodPressureRemoved(): returns true if hospital id in data and variables dont match', () => {
    data = {
      BloodPressureRemoved: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterBloodPressureRemoved(data, variables)).toBeTruthy();
  });

  it('filterRespiratoryRateAdded(): returns false if hospital id in data and variables dont match', () => {
    data = {
      RespiratoryRateAdded: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterRespiratoryRateAdded(data, variables)).toBeFalsy();
  });

  it('filterRespiratoryRateAdded(): returns false if hospital id in data and variables dont match', () => {
    data = {
      RespiratoryRateAdded: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterRespiratoryRateAdded(data, variables)).toBeTruthy();
  });

  it('filterRespiratoryRateUpdated(): returns false if hospital id in data and variables dont match', () => {
    data = {
      RespiratoryRateUpdated: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterRespiratoryRateUpdated(data, variables)).toBeFalsy();
  });

  it('ffilterRespiratoryRateUpdated(): returns false if hospital id in data and variables dont match', () => {
    data = {
      RespiratoryRateUpdated: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterRespiratoryRateUpdated(data, variables)).toBeTruthy();
  });

  it('filterRespiratoryRateRemoved(): returns false if hospital id in data and variables dont match', () => {
    data = {
      RespiratoryRateRemoved: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterRespiratoryRateRemoved(data, variables)).toBeFalsy();
  });

  it('filterRespiratoryRateRemoved(): returns true if hospital id in data and variables dont match', () => {
    data = {
      RespiratoryRateRemoved: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterRespiratoryRateRemoved(data, variables)).toBeTruthy();
  });

  it('filterPulseRateAdded(): returns false if hospital id in data and variables dont match', () => {
    data = {
      PulseRateAdded: { hospitalId: '1', profileId: '1' },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterPulseRateAdded(data, variables)).toBeFalsy();
  });

  it('filterPulseRateAdded(): returns false if hospital id in data and variables dont match', () => {
    data = {
      PulseRateAdded: { hospitalId: '1', profileId: '1' },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterPulseRateAdded(data, variables)).toBeTruthy();
  });

  it('filterPulseRateUpdated(): returns false if hospital id in data and variables dont match', () => {
    data = {
      PulseRateUpdated: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterPulseRateUpdated(data, variables)).toBeFalsy();
  });

  it('filterPulseRateUpdated(): returns false if hospital id in data and variables dont match', () => {
    data = {
      PulseRateUpdated: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterPulseRateUpdated(data, variables)).toBeTruthy();
  });

  it('filterPulseRateRemoved(): returns false if hospital id in data and variables dont match', () => {
    data = {
      PulseRateRemoved: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterPulseRateRemoved(data, variables)).toBeFalsy();
  });

  it('filterPulseRateRemoved(): returns true if hospital id in data and variables dont match', () => {
    data = {
      PulseRateRemoved: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterPulseRateRemoved(data, variables)).toBeTruthy();
  });

  it('filterTemperatureAdded(): returns false if hospital id in data and variables dont match', () => {
    data = {
      TemperatureAdded: { hospitalId: '1', profileId: '1' },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterTemperatureAdded(data, variables)).toBeFalsy();
  });

  it('filterTemperatureAdded(): returns false if hospital id in data and variables dont match', () => {
    data = {
      TemperatureAdded: { hospitalId: '1', profileId: '1' },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterTemperatureAdded(data, variables)).toBeTruthy();
  });

  it('filterTemperatureUpdated(): returns false if hospital id in data and variables dont match', () => {
    data = {
      TemperatureUpdated: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterTemperatureUpdated(data, variables)).toBeFalsy();
  });

  it('filterTemperatureUpdated(): returns false if hospital id in data and variables dont match', () => {
    data = {
      TemperatureUpdated: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterTemperatureUpdated(data, variables)).toBeTruthy();
  });

  it('filterTemperatureRemoved(): returns false if hospital id in data and variables dont match', () => {
    data = {
      TemperatureRemoved: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterTemperatureRemoved(data, variables)).toBeFalsy();
  });

  it('filterTemperatureRemoved(): returns true if hospital id in data and variables dont match', () => {
    data = {
      TemperatureRemoved: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterTemperatureRemoved(data, variables)).toBeTruthy();
  });

  it('filterVisualAcuityAdded(): returns false if hospital id in data and variables dont match', () => {
    data = {
      VisualAcuityAdded: { hospitalId: '1', profileId: '1' },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterVisualAcuityAdded(data, variables)).toBeFalsy();
  });

  it('filterVisualAcuityAdded(): returns false if hospital id in data and variables dont match', () => {
    data = {
      VisualAcuityAdded: { hospitalId: '1', profileId: '1' },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterVisualAcuityAdded(data, variables)).toBeTruthy();
  });

  it('filterVisualAcuityUpdated(): returns false if hospital id in data and variables dont match', () => {
    data = {
      VisualAcuityUpdated: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterVisualAcuityUpdated(data, variables)).toBeFalsy();
  });

  it('filterVisualAcuityUpdated(): returns false if hospital id in data and variables dont match', () => {
    data = {
      VisualAcuityUpdated: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterVisualAcuityUpdated(data, variables)).toBeTruthy();
  });

  it('filterVisualAcuityRemoved(): returns false if hospital id in data and variables dont match', () => {
    data = {
      VisualAcuityRemoved: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterVisualAcuityRemoved(data, variables)).toBeFalsy();
  });

  it('filterVisualAcuityRemoved(): returns true if hospital id in data and variables dont match', () => {
    data = {
      VisualAcuityRemoved: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterVisualAcuityRemoved(data, variables)).toBeTruthy();
  });

  it('filterUrineDipstickAdded(): returns false if hospital id in data and variables dont match', () => {
    data = {
      UrineDipstickAdded: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterUrineDipstickAdded(data, variables)).toBeFalsy();
  });

  it('filterUrineDipstickAdded(): returns false if hospital id in data and variables dont match', () => {
    data = {
      UrineDipstickAdded: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterUrineDipstickAdded(data, variables)).toBeTruthy();
  });

  it('filterUrineDipstickUpdated(): returns false if hospital id in data and variables dont match', () => {
    data = {
      UrineDipstickUpdated: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterUrineDipstickUpdated(data, variables)).toBeFalsy();
  });

  it('filterUrineDipstickUpdated(): returns false if hospital id in data and variables dont match', () => {
    data = {
      UrineDipstickUpdated: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterUrineDipstickUpdated(data, variables)).toBeTruthy();
  });

  it('filterUrineDipstickRemove(): returns false if hospital id in data and variables dont match', () => {
    data = {
      UrineDipstickRemoved: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterUrineDipstickRemove(data, variables)).toBeFalsy();
  });

  it('filterUrineDipstickRemove(): returns true if hospital id in data and variables dont match', () => {
    data = {
      UrineDipstickRemoved: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterUrineDipstickRemove(data, variables)).toBeTruthy();
  });

  it('filterPainAdded(): returns false if neither hospital id nor profile id in data and variables do not match', () => {
    data = {
      PainAdded: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterPainAdded(data, variables)).toBeFalsy();
  });

  it('filterPainAdded(): returns true if either hospital id or profile id in data and variables match', () => {
    data = {
      PainAdded: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterPainAdded(data, variables)).toBeTruthy();
  });

  it('filterPainUpdated(): returns false if neither hospital id nor profile id in data and variables do not match', () => {
    data = {
      PainUpdated: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterPainUpdated(data, variables)).toBeFalsy();
  });

  it('filterPainUpdated(): returns true if either hospital id or profile id in data and variables match', () => {
    data = {
      PainUpdated: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterPainUpdated(data, variables)).toBeTruthy();
  });

  it('filterPainRemoved(): returns false if neither hospital id nor profile id in data and variables do not match', () => {
    data = {
      PainRemoved: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterPainRemoved(data, variables)).toBeFalsy();
  });

  it('filterPainRemoved(): returns true if either hospital id or profile id in data and variables match', () => {
    data = {
      PainRemoved: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterPainRemoved(data, variables)).toBeTruthy();
  });

  it('filterCoverageAdded(): returns false if hospital id in data and variables dont match', () => {
    data = {
      CoverageInformationAdded: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterCoverageAdded(data, variables)).toBeFalsy();
  });

  it('filterCoverageAdded(): returns false if hospital id in data and variables dont match', () => {
    data = {
      CoverageInformationAdded: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterCoverageAdded(data, variables)).toBeTruthy();
  });

  it('filterCoverageUpdated(): returns false if hospital id in data and variables dont match', () => {
    data = {
      CoverageInformationUpdated: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterCoverageUpdated(data, variables)).toBeFalsy();
  });

  it('filterCoverageUpdated(): returns false if hospital id in data and variables dont match', () => {
    data = {
      CoverageInformationUpdated: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterCoverageUpdated(data, variables)).toBeTruthy();
  });

  it('filterCoverageRemoved(): returns false if hospital id in data and variables dont match', () => {
    data = {
      CoverageInformationRemoved: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterCoverageRemoved(data, variables)).toBeFalsy();
  });

  it('filterCoverageRemoved(): returns true if hospital id in data and variables dont match', () => {
    data = {
      CoverageInformationRemoved: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterCoverageRemoved(data, variables)).toBeTruthy();
  });

  it('filterDependentAdded(): returns false if hospital id in data and variables dont match', () => {
    data = {
      DependentAdded: { hospitalId: '1', profileId: '1' },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterDependentAdded(data, variables)).toBeFalsy();
  });

  it('filterDependentAdded(): returns false if hospital id in data and variables dont match', () => {
    data = {
      DependentAdded: { hospitalId: '1', profileId: '1' },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterDependentAdded(data, variables)).toBeTruthy();
  });

  it('filterDependentUpdated(): returns false if hospital id in data and variables dont match', () => {
    data = {
      DependentUpdated: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterDependentUpdated(data, variables)).toBeFalsy();
  });

  it('filterDependentUpdated(): returns false if hospital id in data and variables dont match', () => {
    data = {
      DependentUpdated: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterDependentUpdated(data, variables)).toBeTruthy();
  });

  it('filterDependentRemoved(): returns false if hospital id in data and variables dont match', () => {
    data = {
      DependentRemoved: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterDependentRemoved(data, variables)).toBeFalsy();
  });

  it('filterDependentRemoved(): returns true if hospital id in data and variables dont match', () => {
    data = {
      DependentRemoved: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterDependentRemoved(data, variables)).toBeTruthy();
  });

  it('filterDisabilityAdded(): returns false if hospital id in data and variables dont match', () => {
    data = {
      DisabilityAdded: { hospitalId: '1', profileId: '1' },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterDisabilityAdded(data, variables)).toBeFalsy();
  });

  it('filterDisabilityAdded(): returns false if hospital id in data and variables dont match', () => {
    data = {
      DisabilityAdded: { hospitalId: '1', profileId: '1' },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterDisabilityAdded(data, variables)).toBeTruthy();
  });

  it('filterDisabilityUpdated(): returns false if hospital id in data and variables dont match', () => {
    data = {
      DisabilityUpdated: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterDisabilityUpdated(data, variables)).toBeFalsy();
  });

  it('filterDisabilityUpdated(): returns false if hospital id in data and variables dont match', () => {
    data = {
      DisabilityUpdated: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterDisabilityUpdated(data, variables)).toBeTruthy();
  });

  it('filterDisabilityRemoved(): returns false if hospital id in data and variables dont match', () => {
    data = {
      DisabilityRemoved: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterDisabilityRemoved(data, variables)).toBeFalsy();
  });

  it('filterDisabilityRemoved(): returns true if hospital id in data and variables dont match', () => {
    data = {
      DisabilityRemoved: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterDisabilityRemoved(data, variables)).toBeTruthy();
  });

  it('filterFamilyHistoryAdded(): returns false if hospital id in data and variables dont match', () => {
    data = {
      FamilyHistoryAdded: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterFamilyHistoryAdded(data, variables)).toBeFalsy();
  });

  it('filterFamilyHistoryAdded(): returns false if hospital id in data and variables dont match', () => {
    data = {
      FamilyHistoryAdded: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterFamilyHistoryAdded(data, variables)).toBeTruthy();
  });

  it('filterFamilyHistoryUpdated(): returns false if hospital id in data and variables dont match', () => {
    data = {
      FamilyHistoryUpdated: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterFamilyHistoryUpdated(data, variables)).toBeFalsy();
  });

  it('filterFamilyHistoryUpdated(): returns false if hospital id in data and variables dont match', () => {
    data = {
      FamilyHistoryUpdated: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterFamilyHistoryUpdated(data, variables)).toBeTruthy();
  });

  it('filterFamilyHistoryRemoved(): returns false if hospital id in data and variables dont match', () => {
    data = {
      FamilyHistoryRemoved: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterFamilyHistoryRemoved(data, variables)).toBeFalsy();
  });

  it('filterFamilyHistoryRemoved(): returns true if hospital id in data and variables dont match', () => {
    data = {
      FamilyHistoryRemoved: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterFamilyHistoryRemoved(data, variables)).toBeTruthy();
  });

  it('filterGynecologicHistoryAdded(): returns false if hospital id in data and variables dont match', () => {
    data = {
      GynecologicHistoryAdded: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterGynecologicHistoryAdded(data, variables)).toBeFalsy();
  });

  it('filterGynecologicHistoryAdded(): returns false if hospital id in data and variables dont match', () => {
    data = {
      GynecologicHistoryAdded: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterGynecologicHistoryAdded(data, variables)).toBeTruthy();
  });

  it('filterGynecologicHistoryUpdated(): returns false if hospital id in data and variables dont match', () => {
    data = {
      GynecologicHistoryUpdated: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterGynecologicHistoryUpdated(data, variables)).toBeFalsy();
  });

  it('filterGynecologicHistoryUpdated(): returns false if hospital id in data and variables dont match', () => {
    data = {
      GynecologicHistoryUpdated: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterGynecologicHistoryUpdated(data, variables)).toBeTruthy();
  });

  it('filterGynecologicHistoryRemoved(): returns false if hospital id in data and variables dont match', () => {
    data = {
      GynecologicHistoryRemoved: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterGynecologicHistoryRemoved(data, variables)).toBeFalsy();
  });

  it('filterGynecologicHistoryRemoved(): returns true if hospital id in data and variables dont match', () => {
    data = {
      GynecologicHistoryRemoved: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterGynecologicHistoryRemoved(data, variables)).toBeTruthy();
  });

  it('filterHabitAdded(): returns false if hospital id in data and variables dont match', () => {
    data = {
      HabitAdded: { hospitalId: '1', profileId: '1' },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterHabitAdded(data, variables)).toBeFalsy();
  });

  it('filterHabitAdded(): returns false if hospital id in data and variables dont match', () => {
    data = {
      HabitAdded: { hospitalId: '1', profileId: '1' },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterHabitAdded(data, variables)).toBeTruthy();
  });

  it('filterHabitUpdated(): returns false if hospital id in data and variables dont match', () => {
    data = {
      HabitUpdated: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterHabitUpdated(data, variables)).toBeFalsy();
  });

  it('filterHabitUpdated(): returns false if hospital id in data and variables dont match', () => {
    data = {
      HabitUpdated: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterHabitUpdated(data, variables)).toBeTruthy();
  });

  it('filterHabitRemoved (): returns false if hospital id in data and variables dont match', () => {
    data = {
      HabitRemoved: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterHabitRemoved(data, variables)).toBeFalsy();
  });

  it('filterHabitRemoved(): returns true if hospital id in data and variables dont match', () => {
    data = {
      HabitRemoved: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterHabitRemoved(data, variables)).toBeTruthy();
  });

  it('filterObstetricAdded(): returns false if hospital id in data and variables dont match', () => {
    data = {
      ObstetricAdded: { hospitalId: '1', profileId: '1' },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterObstetricAdded(data, variables)).toBeFalsy();
  });

  it('filterObstetricAdded(): returns false if hospital id in data and variables dont match', () => {
    data = {
      ObstetricAdded: { hospitalId: '1', profileId: '1' },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterObstetricAdded(data, variables)).toBeTruthy();
  });

  it('filterObstetricUpdated(): returns false if hospital id in data and variables dont match', () => {
    data = {
      ObstetricUpdated: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterObstetricUpdated(data, variables)).toBeFalsy();
  });

  it('filterObstetricUpdated(): returns false if hospital id in data and variables dont match', () => {
    data = {
      ObstetricUpdated: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterObstetricUpdated(data, variables)).toBeTruthy();
  });

  it('filterObstetricRemoved(): returns false if hospital id in data and variables dont match', () => {
    data = {
      ObstetricRemoved: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterObstetricRemoved(data, variables)).toBeFalsy();
  });

  it('filterObstetricRemoved(): returns true if hospital id in data and variables dont match', () => {
    data = {
      ObstetricRemoved: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterObstetricRemoved(data, variables)).toBeTruthy();
  });

  it('filterPastEncounterAdded(): returns false if hospital id in data and variables dont match', () => {
    data = {
      PastEncounterAdded: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterPastEncounterAdded(data, variables)).toBeFalsy();
  });

  it('filterPastEncounterAdded(): returns false if hospital id in data and variables dont match', () => {
    data = {
      PastEncounterAdded: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterPastEncounterAdded(data, variables)).toBeTruthy();
  });

  it('filterPastEncounterUpdated(): returns false if hospital id in data and variables dont match', () => {
    data = {
      PastEncounterUpdated: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterPastEncounterUpdated(data, variables)).toBeFalsy();
  });

  it('filterPastEncounterUpdated(): returns false if hospital id in data and variables dont match', () => {
    data = {
      PastEncounterUpdated: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterPastEncounterUpdated(data, variables)).toBeTruthy();
  });

  it('filterPastEncounterRemoved(): returns false if hospital id in data and variables dont match', () => {
    data = {
      PastEncounterRemoved: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterPastEncounterRemoved(data, variables)).toBeFalsy();
  });

  it('filterPastEncounterRemoved(): returns true if hospital id in data and variables dont match', () => {
    data = {
      PastEncounterRemoved: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterPastEncounterRemoved(data, variables)).toBeTruthy();
  });

  it('filterPastSurgeryAdded(): returns false if hospital id in data and variables dont match', () => {
    data = {
      PastSurgeryAdded: { hospitalId: '1', profileId: '1' },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterPastSurgeryAdded(data, variables)).toBeFalsy();
  });

  it('filterPastSurgeryAdded(): returns false if hospital id in data and variables dont match', () => {
    data = {
      PastSurgeryAdded: { hospitalId: '1', profileId: '1' },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterPastSurgeryAdded(data, variables)).toBeTruthy();
  });

  it('filterPastSurgeryUpdated(): returns false if hospital id in data and variables dont match', () => {
    data = {
      PastSurgeryUpdated: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterPastSurgeryUpdated(data, variables)).toBeFalsy();
  });

  it('filterPastSurgeryUpdated(): returns false if hospital id in data and variables dont match', () => {
    data = {
      PastSurgeryUpdated: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterPastSurgeryUpdated(data, variables)).toBeTruthy();
  });

  it('filterPastSurgeryRemoved(): returns false if hospital id in data and variables dont match', () => {
    data = {
      PastSurgeryRemoved: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterPastSurgeryRemoved(data, variables)).toBeFalsy();
  });

  it('filterPastSurgeryRemoved(): returns true if hospital id in data and variables dont match', () => {
    data = {
      PastSurgeryRemoved: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterPastSurgeryRemoved(data, variables)).toBeTruthy();
  });

  it('filterPreExistingConditionAdded(): returns false if hospital id in data and variables dont match', () => {
    data = {
      PreExistingConditionAdded: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterPreExistingConditionAdded(data, variables)).toBeFalsy();
  });

  it('filterPreExistingConditionAdded(): returns false if hospital id in data and variables dont match', () => {
    data = {
      PreExistingConditionAdded: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterPreExistingConditionAdded(data, variables)).toBeTruthy();
  });

  it('filterPreExistingConditionUpdated(): returns false if hospital id in data and variables dont match', () => {
    data = {
      PreExistingConditionUpdated: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterPreExistingConditionUpdated(data, variables)).toBeFalsy();
  });

  it('filterPreExistingConditionUpdated(): returns false if hospital id in data and variables dont match', () => {
    data = {
      PreExistingConditionUpdated: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterPreExistingConditionUpdated(data, variables)).toBeTruthy();
  });

  it('filterPreExistingConditionRemoved(): returns false if hospital id in data and variables dont match', () => {
    data = {
      PreExistingConditionRemoved: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterPreExistingConditionRemoved(data, variables)).toBeFalsy();
  });

  it('filterPreExistingConditionRemoved(): returns true if hospital id in data and variables dont match', () => {
    data = {
      PreExistingConditionRemoved: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterPreExistingConditionRemoved(data, variables)).toBeTruthy();
  });

  it('filterPhysicalActivityAdded(): returns false if hospital id in data and variables dont match', () => {
    data = {
      PhysicalActivityAdded: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterPhysicalActivityAdded(data, variables)).toBeFalsy();
  });

  it('filterPhysicalActivityAdded(): returns false if hospital id in data and variables dont match', () => {
    data = {
      PhysicalActivityAdded: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterPhysicalActivityAdded(data, variables)).toBeTruthy();
  });

  it('filterPhysicalActivityUpdated(): returns false if hospital id in data and variables dont match', () => {
    data = {
      PhysicalActivityUpdated: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterPhysicalActivityUpdated(data, variables)).toBeFalsy();
  });

  it('filterPhysicalActivityUpdated(): returns false if hospital id in data and variables dont match', () => {
    data = {
      PhysicalActivityUpdated: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterPhysicalActivityUpdated(data, variables)).toBeTruthy();
  });

  it('filterPhysicalActivityRemoved(): returns false if hospital id in data and variables dont match', () => {
    data = {
      PhysicalActivityRemoved: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterPhysicalActivityRemoved(data, variables)).toBeFalsy();
  });

  it('filterPhysicalActivityRemoved(): returns true if hospital id in data and variables dont match', () => {
    data = {
      PhysicalActivityRemoved: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterPhysicalActivityRemoved(data, variables)).toBeTruthy();
  });

  it('filterNextOfKinAdded(): returns false if hospital id in data and variables dont match', () => {
    data = {
      NextOfKinAdded: { hospitalId: '1', profileId: '1' },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterNextOfKinAdded(data, variables)).toBeFalsy();
  });

  it('filterNextOfKinAdded(): returns false if hospital id in data and variables dont match', () => {
    data = {
      NextOfKinAdded: { hospitalId: '1', profileId: '1' },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterNextOfKinAdded(data, variables)).toBeTruthy();
  });

  it('filterNextOfKinUpdated(): returns false if hospital id in data and variables dont match', () => {
    data = {
      NextofKinUpdated: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterNextOfKinUpdated(data, variables)).toBeFalsy();
  });

  it('filterNextOfKinUpdated(): returns false if hospital id in data and variables dont match', () => {
    data = {
      NextOfKinUpdated: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterNextOfKinUpdated(data, variables)).toBeTruthy();
  });

  it('filterNextOfKinRemoved(): returns false if hospital id in data and variables dont match', () => {
    data = {
      NextOfKinRemoved: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterNextOfKinRemoved(data, variables)).toBeFalsy();
  });

  it('filterNextOfKinRemoved(): returns true if hospital id in data and variables dont match', () => {
    data = {
      NextOfKinRemoved: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterNextOfKinRemoved(data, variables)).toBeTruthy();
  });

  it('filterProfileDetailsUpdated(): returns false if hospital id in data and variables dont match', () => {
    data = {
      ProfileDetailsUpdated: {
        id: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterProfileDetailsUpdated(data, variables)).toBeFalsy();
  });

  it('filterProfileDetailsUpdated(): returns false if hospital id in data and variables dont match', () => {
    data = {
      ProfileDetailsUpdated: {
        id: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterProfileDetailsUpdated(data, variables)).toBeTruthy();
  });

  it('filterfilterDischargePatientsAdded(): returns false if hospital id in data and variables dont match', () => {
    data = {
      DischargePatientAdded: { admission: { profileId: '1' } },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterDischargePatientsAdded(data, variables)).toBeFalsy();
  });

  it('filterDischargePatientsAdded(): returns false if hospital id in data and variables dont match', () => {
    data = {
      DischargePatientAdded: { admission: { profileId: '1' } },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterDischargePatientsAdded(data, variables)).toBeTruthy();
  });

  it('filterDischargePatientsUpdated(): returns false if hospital id in data and variables dont match', () => {
    data = {
      DischargePatientUpdated: { admission: { profileId: '1' } },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterDischargePatientsUpdated(data, variables)).toBeFalsy();
  });

  it('filterDischargePatientsUpdated(): returns false if hospital id in data and variables dont match', () => {
    data = {
      DischargePatientUpdated: { admission: { profileId: '1' } },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterDischargePatientsUpdated(data, variables)).toBeTruthy();
  });

  it('filterDischargePatientsRemoved(): returns false if hospital id in data and variables dont match', () => {
    data = {
      DischargePatientRemoved: { admission: { profileId: '1' } },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterDischargePatientsRemoved(data, variables)).toBeFalsy();
  });

  it('filterDischargePatientsRemoved(): returns true if hospital id in data and variables dont match', () => {
    data = {
      DischargePatientRemoved: { admission: { profileId: '1' } },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterDischargePatientsRemoved(data, variables)).toBeTruthy();
  });

  it('filterTransferPatientsAdded(): returns false if hospital id in data and variables dont match', () => {
    data = {
      TransferPatientAdded: { admission: { profileId: '1' } },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterTransferPatientsAdded(data, variables)).toBeFalsy();
  });

  it('filterTransferPatientsAdded(): returns false if hospital id in data and variables dont match', () => {
    data = {
      TransferPatientAdded: { admission: { profileId: '1' } },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterTransferPatientsAdded(data, variables)).toBeTruthy();
  });

  it('filterPatientTransferred(): returns false if hospital id does not match', () => {
    data = {
      hospitalId: '1',
      PatientTransferred: { hospitalId: '2' },
    };
    variables = { hospitalId: '3' };
    expect(filterPatientTransferred(data, variables)).toBeFalsy();
  });

  it('filterPatientTransferred(): returns true if hospital id matches', () => {
    data = {
      hospitalId: '1',
      PatientTransferred: { hospitalId: '2' },
    };
    variables = { hospitalId: '1' };
    expect(filterPatientTransferred(data, variables)).toBeTruthy();

    variables = { hospitalId: '2' };
    expect(filterPatientTransferred(data, variables)).toBeTruthy();
  });

  it('filterTransferPatientsUpdated(): returns false if hospital id in data and variables dont match', () => {
    data = {
      TransferPatientUpdated: { admission: { profileId: '1' } },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterTransferPatientsUpdated(data, variables)).toBeFalsy();
  });

  it('filterTransferPatientsUpdated(): returns false if hospital id in data and variables dont match', () => {
    data = {
      TransferPatientUpdated: { admission: { profileId: '1' } },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterTransferPatientsUpdated(data, variables)).toBeTruthy();
  });

  it('filterTransferPatientsRemoved(): returns false if hospital id in data and variables dont match', () => {
    data = {
      TransferPatientRemoved: { admission: { profileId: '1' } },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterTransferPatientsRemoved(data, variables)).toBeFalsy();
  });

  it('filterTransferPatientsRemoved(): returns true if hospital id in data and variables dont match', () => {
    data = {
      TransferPatientRemoved: { admission: { profileId: '1' } },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterTransferPatientsRemoved(data, variables)).toBeTruthy();
  });

  it('filterBloodTransfusionAdded(): returns false if hospital id in data and variables dont match', () => {
    data = {
      BloodTransfusionAdded: { admission: { profileId: '1' } },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterBloodTransfusionAdded(data, variables)).toBeFalsy();
  });

  it('filterBloodTransfusionAdded(): returns false if hospital id in data and variables dont match', () => {
    data = {
      BloodTransfusionAdded: { admission: { profileId: '1' } },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterBloodTransfusionAdded(data, variables)).toBeTruthy();
  });

  it('filterBloodTransfusionUpdated(): returns false if hospital id in data and variables dont match', () => {
    data = {
      BloodTransfusionUpdated: { admission: { profileId: '1' } },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterBloodTransfusionUpdated(data, variables)).toBeFalsy();
  });

  it('filterBloodTransfusionUpdated(): returns false if hospital id in data and variables dont match', () => {
    data = {
      BloodTransfusionUpdated: { admission: { profileId: '1' } },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterBloodTransfusionUpdated(data, variables)).toBeTruthy();
  });

  it('filterBloodTransfusionRemoved(): returns false if hospital id in data and variables dont match', () => {
    data = {
      BloodTransfusionRemoved: { admission: { profileId: '1' } },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterBloodTransfusionRemoved(data, variables)).toBeFalsy();
  });

  it('filterBloodTransfusionRemoved(): returns true if hospital id in data and variables dont match', () => {
    data = {
      BloodTransfusionRemoved: { admission: { profileId: '1' } },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterBloodTransfusionRemoved(data, variables)).toBeTruthy();
  });

  it('filterAdmissionNotesAdded(): returns false if hospital id in data and variables dont match', () => {
    data = {
      AdmissionNoteAdded: { admission: { profileId: '1' } },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterAdmissionNotesAdded(data, variables)).toBeFalsy();
  });

  it('filterAdmissionNotesAdded(): returns false if hospital id in data and variables dont match', () => {
    data = {
      AdmissionNoteAdded: { admission: { profileId: '1' } },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterAdmissionNotesAdded(data, variables)).toBeTruthy();
  });

  it('filterAdmissionNotesUpdated(): returns false if hospital id in data and variables dont match', () => {
    data = {
      AdmissionNoteUpdated: { admission: { profileId: '1' } },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterAdmissionNotesUpdated(data, variables)).toBeFalsy();
  });

  it('filterAdmissionNotesUpdated(): returns false if hospital id in data and variables dont match', () => {
    data = {
      AdmissionNoteUpdated: { admission: { profileId: '1' } },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterAdmissionNotesUpdated(data, variables)).toBeTruthy();
  });

  it('filterAdmissionNotesRemoved(): returns false if hospital id in data and variables dont match', () => {
    data = {
      AdmissionNoteRemoved: { admission: { profileId: '1' } },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterAdmissionNotesRemoved(data, variables)).toBeFalsy();
  });

  it('filterAdmissionNotesRemoved(): returns true if hospital id in data and variables dont match', () => {
    data = {
      AdmissionNoteRemoved: { admission: { profileId: '1' } },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterAdmissionNotesRemoved(data, variables)).toBeTruthy();
  });

  it('filterConsultationTreatmentPlanAdded(): returns false if hospital id in data and variables dont match', () => {
    data = {
      ConsultationTreatmentPlanAdded: {
        consultation: { profileId: '1' },
      },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterConsultationTreatmentPlanAdded(data, variables)).toBeFalsy();
  });

  it('filterConsultationTreatmentPlanAdded(): returns false if hospital id in data and variables dont match', () => {
    data = {
      ConsultationTreatmentPlanAdded: {
        consultation: { profileId: '1' },
      },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterConsultationTreatmentPlanAdded(data, variables)).toBeTruthy();
  });

  it('filterConsultationTreatmentPlanUpdated(): returns false if hospital id in data and variables dont match', () => {
    data = {
      ConsultationTreatmentPlanUpdated: {
        consultation: { profileId: '1' },
      },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterConsultationTreatmentPlanUpdated(data, variables)).toBeFalsy();
  });

  it('filterConsultationTreatmentPlanUpdated(): returns false if hospital id in data and variables dont match', () => {
    data = {
      ConsultationTreatmentPlanUpdated: {
        consultation: { profileId: '1' },
      },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(
      filterConsultationTreatmentPlanUpdated(data, variables),
    ).toBeTruthy();
  });

  it('filterConsultationTreatmentPlanRemoved(): returns false if hospital id in data and variables dont match', () => {
    data = {
      ConsultationTreatmentPlanRemoved: {
        consultation: { profileId: '1' },
      },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterConsultationTreatmentPlanRemoved(data, variables)).toBeFalsy();
  });

  it('filterConsultationTreatmentPlanRemoved(): returns true if hospital id in data and variables dont match', () => {
    data = {
      ConsultationTreatmentPlanRemoved: {
        consultation: { profileId: '1' },
      },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(
      filterConsultationTreatmentPlanRemoved(data, variables),
    ).toBeTruthy();
  });

  it('filterConsultationEvent(): returns false if hospital id in data and variables dont match', () => {
    data = {
      consultation: { profileId: '1' },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterConsultationEvent(data, variables)).toBeFalsy();
  });

  it('filterConsultationEvent(): returns true if hospital id in data and variables dont match', () => {
    data = {
      consultation: { profileId: '1' },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterConsultationEvent(data, variables)).toBeTruthy();
  });

  it('filterAdmissionNotesAdded(): returns false if hospital id in data and variables dont match', () => {
    data = {
      AdmissionNoteAdded: { admission: { profileId: '1' } },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterAdmissionNotesAdded(data, variables)).toBeFalsy();
  });

  it('filterAdmissionNotesAdded(): returns false if hospital id in data and variables dont match', () => {
    data = {
      AdmissionNoteAdded: { admission: { profileId: '1' } },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterAdmissionNotesAdded(data, variables)).toBeTruthy();
  });

  it('filterAdmissionNotesUpdated(): returns false if hospital id in data and variables dont match', () => {
    data = {
      AdmissionNoteUpdated: { admission: { profileId: '1' } },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterAdmissionNotesUpdated(data, variables)).toBeFalsy();
  });

  it('filterAdmissionNotesUpdated(): returns false if hospital id in data and variables dont match', () => {
    data = {
      AdmissionNoteUpdated: { admission: { profileId: '1' } },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterAdmissionNotesUpdated(data, variables)).toBeTruthy();
  });

  it('filterAdmissionNotesRemoved(): returns false if hospital id in data and variables dont match', () => {
    data = {
      AdmissionNoteRemoved: { admission: { profileId: '1' } },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterAdmissionNotesRemoved(data, variables)).toBeFalsy();
  });

  it('filterAdmissionNotesRemoved(): returns true if hospital id in data and variables dont match', () => {
    data = {
      AdmissionNoteRemoved: { admission: { profileId: '1' } },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterAdmissionNotesRemoved(data, variables)).toBeTruthy();
  });

  it('filterInvestigationAdditionalNotesAdded(): returns false if hospital id in data and variables dont match', () => {
    data = {
      InvestigationAdditionalNoteAdded: {
        investigation: { profileId: '1' },
      },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(
      filterInvestigationAdditionalNotesAdded(data, variables),
    ).toBeFalsy();
  });

  it('filterInvestigationAdditionalNotesAdded(): returns false if hospital id in data and variables dont match', () => {
    data = {
      InvestigationAdditionalNoteAdded: {
        investigation: { profileId: '1' },
      },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(
      filterInvestigationAdditionalNotesAdded(data, variables),
    ).toBeTruthy();
  });

  it('filterInvestigationAdditionalNotesUpdated(): returns false if hospital id in data and variables dont match', () => {
    data = {
      InvestigationAdditionalNoteUpdated: {
        investigation: { profileId: '1' },
      },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(
      filterInvestigationAdditionalNotesUpdated(data, variables),
    ).toBeFalsy();
  });

  it('filterInvestigationAdditionalNotesUpdated(): returns false if hospital id in data and variables dont match', () => {
    data = {
      InvestigationAdditionalNoteUpdated: {
        investigation: { profileId: '1' },
      },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(
      filterInvestigationAdditionalNotesUpdated(data, variables),
    ).toBeTruthy();
  });

  it('filterInvestigationAdditionalNotesRemoved(): returns false if hospital id in data and variables dont match', () => {
    data = {
      InvestigationAdditionalNoteRemoved: {
        investigation: { profileId: '1' },
      },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(
      filterInvestigationAdditionalNotesRemoved(data, variables),
    ).toBeFalsy();
  });

  it('filterInvestigationAdditionalNotesRemoved(): returns true if hospital id in data and variables dont match', () => {
    data = {
      InvestigationAdditionalNoteRemoved: {
        investigation: { profileId: '1' },
      },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(
      filterInvestigationAdditionalNotesRemoved(data, variables),
    ).toBeTruthy();
  });

  it('filterRadiologyAdded(): returns false if hospital id in data and variables dont match', () => {
    data = {
      InvestigationAdditionalNoteAdded: {
        investigation: { profileId: '1' },
      },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterRadiologyAdded(data, variables)).toBeFalsy();
  });

  it('filterRadiologyAdded(): returns false if hospital id in data and variables dont match', () => {
    data = {
      RadiologyAdded: { profileId: '1' },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterRadiologyAdded(data, variables)).toBeTruthy();
  });

  it('filterRadiologyUpdated(): returns false if hospital id in data and variables dont match', () => {
    data = {
      RadiologyUpdated: { profileId: '1' },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterRadiologyUpdated(data, variables)).toBeFalsy();
  });

  it('filterRadiologyUpdated(): returns false if hospital id in data and variables dont match', () => {
    data = {
      RadiologyUpdated: { profileId: '1' },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterRadiologyUpdated(data, variables)).toBeTruthy();
  });

  it('filterRadiologyEvent(): returns false if hospital id in data and variables dont match', () => {
    data = {
      radiology: { profileId: '1' },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterRadiologyEvent(data, variables)).toBeFalsy();
  });

  it('filterRadiologyEvent(): returns true if hospital id in data and variables dont match', () => {
    data = {
      radiology: { profileId: '1' },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterRadiologyEvent(data, variables)).toBeTruthy();
  });

  it('filterLaboratoryAdded(): returns false if hospital id in data and variables dont match', () => {
    data = {
      LaboratoryAdded: { investigation: { profileId: '1' } },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterLaboratoryAdded(data, variables)).toBeFalsy();
  });

  it('filterLaboratoryAdded(): returns false if hospital id in data and variables dont match', () => {
    data = {
      LaboratoryAdded: { profileId: '1' },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterLaboratoryAdded(data, variables)).toBeTruthy();
  });

  it('filterLaboratoryUpdated(): returns false if hospital id in data and variables dont match', () => {
    data = {
      LaboratoryUpdated: { profileId: '1' },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterLaboratoryUpdated(data, variables)).toBeFalsy();
  });

  it('filterLaboratoryUpdated(): returns false if hospital id in data and variables dont match', () => {
    data = {
      LaboratoryUpdated: { profileId: '1' },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterLaboratoryUpdated(data, variables)).toBeTruthy();
  });

  it('filterLaboratoryEvent(): returns false if hospital id in data and variables dont match', () => {
    data = {
      laboratory: { profileId: '1' },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterLaboratoryEvent(data, variables)).toBeFalsy();
  });

  it('filterLaboratoryEvent(): returns true if hospital id in data and variables dont match', () => {
    data = {
      laboratory: { profileId: '1' },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterLaboratoryEvent(data, variables)).toBeTruthy();
  });

  it('filterDispenseAdded(): returns false if hospital id in data and variables dont match', () => {
    data = {
      DispenseMedicationAdded: { medication: { profileId: '1' } },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterDispenseAdded(data, variables)).toBeFalsy();
  });

  it('filterDispenseAdded(): returns false if hospital id in data and variables dont match', () => {
    data = {
      DispenseMedicationAdded: { medication: { profileId: '1' } },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterDispenseAdded(data, variables)).toBeTruthy();
  });

  it('filterDispenseUpdated(): returns false if hospital id in data and variables dont match', () => {
    data = {
      DispenseMedicationUpdated: {
        medication: { profileId: '1' },
      },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterDispenseUpdated(data, variables)).toBeFalsy();
  });

  it('filterDispenseUpdated(): returns false if hospital id in data and variables dont match', () => {
    data = {
      DispenseMedicationUpdated: {
        medication: { profileId: '1' },
      },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterDispenseUpdated(data, variables)).toBeTruthy();
  });

  it('filterDispenseRemoved(): returns false if hospital id in data and variables dont match', () => {
    data = {
      DispenseMedicationRemoved: {
        medication: { profileId: '1' },
      },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterDispenseRemoved(data, variables)).toBeFalsy();
  });

  it('filterDispenseRemoved(): returns true if hospital id in data and variables dont match', () => {
    data = {
      DispenseMedicationRemoved: {
        medication: { profileId: '1' },
      },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterDispenseRemoved(data, variables)).toBeTruthy();
  });

  it('filterProcedureOperationNoteAdded(): returns false if hospital id in data and variables dont match', () => {
    data = {
      ProcedureOperationNoteAdded: { surgery: { profileId: '1' } },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterProcedureOperationNoteAdded(data, variables)).toBeFalsy();
  });

  it('filterProcedureOperationNoteAdded(): returns false if hospital id in data and variables dont match', () => {
    data = {
      ProcedureOperationNoteAdded: { surgery: { profileId: '1' } },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterProcedureOperationNoteAdded(data, variables)).toBeTruthy();
  });

  it('filterProcedureOperationNoteUpdated(): returns false if hospital id in data and variables dont match', () => {
    data = {
      ProcedureOperationNoteUpdated: {
        surgery: { profileId: '1' },
      },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterProcedureOperationNoteUpdated(data, variables)).toBeFalsy();
  });

  it('filterProcedureOperationNoteUpdated(): returns false if hospital id in data and variables dont match', () => {
    data = {
      ProcedureOperationNoteUpdated: {
        surgery: { profileId: '1' },
      },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterProcedureOperationNoteUpdated(data, variables)).toBeTruthy();
  });

  it('filterProcedureOperationNoteRemoved(): returns false if hospital id in data and variables dont match', () => {
    data = {
      ProcedureOperationNoteRemoved: {
        surgery: { profileId: '1' },
      },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterProcedureOperationNoteRemoved(data, variables)).toBeFalsy();
  });

  it('filterMedicationDetailsAdded(): returns false if hospital id in data and variables dont match', () => {
    data = {
      MedicationDetailsAdded: { medication: { profileId: '1' } },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterMedicationDetailsAdded(data, variables)).toBeFalsy();
  });

  it('filterMedicationDetailsAdded(): returns false if hospital id in data and variables dont match', () => {
    data = {
      MedicationDetailsAdded: { medication: { profileId: '1' } },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterMedicationDetailsAdded(data, variables)).toBeTruthy();
  });

  it('filterMedicationDetailsUpdated(): returns false if hospital id in data and variables dont match', () => {
    data = {
      MedicationDetailsUpdated: {
        medication: { profileId: '1' },
      },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterMedicationDetailsUpdated(data, variables)).toBeFalsy();
  });

  it('filterMedicationDetailsUpdated(): returns true if hospital id in data and variables dont match', () => {
    data = {
      MedicationDetailsUpdated: {
        medication: { profileId: '1' },
      },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterMedicationDetailsUpdated(data, variables)).toBeTruthy();
  });

  it('filterMedicationDetailsRemoved(): returns false if hospital id in data and variables dont match', () => {
    data = {
      MedicationDetailsRemoved: {
        medication: { profileId: '1' },
      },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterMedicationDetailsRemoved(data, variables)).toBeFalsy();
  });

  it('filterMedicationDetailsRemoved(): returns true if hospital id in data and variables dont match', () => {
    data = {
      MedicationDetailsRemoved: {
        medication: { profileId: '1' },
      },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterMedicationDetailsRemoved(data, variables)).toBeTruthy();
  });

  it('byClinifyIds(): returns true if profileId from variables is contained in profileIds from data', () => {
    data = {
      profileIds: ['1', '2'],
    };
    variables = { profileId: '2' };
    expect(byProfileIds(data, variables)).toBeTruthy();
  });

  it('byClinifyIds(): returns false if profileId from variables is not contained in profileIds from data', () => {
    data = {
      profileIds: ['1', '2'],
    };
    variables = { profileId: '3' };
    expect(byProfileIds(data, variables)).toBeFalsy();
  });

  it('filterReferralAdded() returns true if hospitalIds from data and variables matches and referral is true', () => {
    data = {
      consultation: { hospitalIds: ['1', '2'], referral: true },
    };
    variables = { hospitalId: '1' };
    expect(filterReferralAdded(data, variables)).toBeTruthy();
  });

  it('filterReferralAdded() returns false if hospitalIds from data and variables does not matches and referral is true', () => {
    data = {
      consultation: { hospitalIds: ['1', '3'], referral: true },
    };
    variables = { hospitalId: '2' };
    expect(filterReferralAdded(data, variables)).toBeFalsy();
  });

  it('filterReferralAdded() returns false if hospitalIds from data and variables does not matches and referral is false', () => {
    data = {
      consultation: { hospitalIds: ['1'], referral: false },
    };
    variables = { hospitalId: '1' };
    expect(filterReferralAdded(data, variables)).toBeFalsy();
  });

  it('filterAppointmentEvent() return true if hospital id OR profileIds from variables and data match', () => {
    data = {
      appointment: { hospitalId: '1', profileIds: ['2', '3'] },
    };
    variables = { hospitalId: '5', profileId: '3' };
    expect(filterAppointmentEvent(data, variables)).toBeTruthy();

    data = {
      appointment: { hospitalId: '1', profileIds: ['2', '3'] },
    };
    variables = { hospitalId: '1', profileId: '6' };
    expect(filterAppointmentEvent(data, variables)).toBeTruthy();
  });

  it('filterAppointmentEvent() return FALSE if hospital id OR profileIds from variables and data do not match', () => {
    data = {
      appointment: { hospitalId: '1', profileIds: ['2', '3'] },
    };
    variables = { hospitalId: '5', profileId: '6' };
    expect(filterAppointmentEvent(data, variables)).toBeFalsy();
  });

  it('filterWaiterEvent() returns TRUE if hospitalId from data and variables match', () => {
    data = { waiter: { hospitalId: '1', profileId: '2' } };
    variables = { hospitalId: '1', profileId: '6' };
    expect(filterWaiterEvent(data, variables)).toBeTruthy();
  });

  it('filterWaiterEvent() returns FALSE if hospitalId from data and variables do not match', () => {
    data = { waiter: { hospitalId: '1', profile: { profileId: '2' } } };
    variables = { hospitalId: '5', profileId: '6' };
    expect(filterWaiterEvent(data, variables)).toBeFalsy();
  });

  it('filterOrganizationPermissionGranted(): returns false if recipient id in data and variables dont match', () => {
    data = { OrganizationPermissionGranted: { recipientId: '3' } };
    variables = { recipientId: '1' };
    expect(filterOrganizationPermissionGranted(data, variables)).toBeFalsy();
  });

  it('filterOrganizationPermissionGranted(): returns true if recipient id in data and variables match', () => {
    data = { OrganizationPermissionGranted: { recipientId: '1' } };
    variables = { recipientId: '1' };
    expect(filterOrganizationPermissionGranted(data, variables)).toBeTruthy();
  });

  it('filterOrganizationPermissionRevoked(): returns false if recipient id in data and variables dont match', () => {
    data = { OrganizationPermissionRevoked: { recipientId: '3' } };
    variables = { recipientId: '1' };
    expect(filterOrganizationPermissionRevoked(data, variables)).toBeFalsy();
  });

  it('filterOrganizationPermissionRevoked(): returns true if recipient id in data and variables match', () => {
    data = { OrganizationPermissionRevoked: { recipientId: '1' } };
    variables = { recipientId: '1' };
    expect(filterOrganizationPermissionRevoked(data, variables)).toBeTruthy();
  });

  it('filterPatientPermissionGranted(): returns false if recipient id in data and variables dont match', () => {
    data = { PatientPermissionGranted: { recipientId: '3' } };
    variables = { recipientId: '1' };
    expect(filterPatientPermissionGranted(data, variables)).toBeFalsy();
  });

  it('filterPatientPermissionGranted(): returns true if recipient id in data and variables match', () => {
    data = { PatientPermissionGranted: { recipientId: '1' } };
    variables = { recipientId: '1' };
    expect(filterPatientPermissionGranted(data, variables)).toBeTruthy();
  });

  it('filterPatientPermissionRevoked(): returns false if clinify id in data and variables dont match', () => {
    data = { PatientPermissionRevoked: { recipientId: '3' } };
    variables = { recipientId: '1' };
    expect(filterPatientPermissionRevoked(data, variables)).toBeFalsy();
  });

  it('filterPatientPermissionRevoked(): returns true if recipient id in data and variables match', () => {
    data = { PatientPermissionRevoked: { recipientId: '1' } };
    variables = { recipientId: '1' };
    expect(filterPatientPermissionRevoked(data, variables)).toBeTruthy();
  });

  it('filterInPatientEvent() returns FALSE if hospitalID from data and variables dont match', () => {
    data = { admission: { hospitalId: '1' } };
    variables = { hospitalId: '2' };
    expect(filterInPatientEvent(data, variables)).toBeFalsy();

    data = null;
    variables = { hospitalId: '2' };
    expect(filterInPatientEvent(data, variables)).toBeFalsy();
  });

  it('filterInPatientEvent() returns TRUE if hospitalId from data and variables match', () => {
    data = { admission: { hospitalId: '1' } };
    variables = { hospitalId: '1' };
    expect(filterInPatientEvent(data, variables)).toBeTruthy();
  });

  it('filterPrescriptionEvent() returns FALSE if hospitalID from data and variables dont match', () => {
    data = { medication: { hospitalId: '1' } };
    variables = { hospitalId: '2' };
    expect(filterPrescriptionEvent(data, variables)).toBeFalsy();
  });

  it('filterPrescriptionEvent() returns TRUE if hospitalId from data and variables match', () => {
    data = { medication: { hospitalId: '1' } };
    variables = { hospitalId: '1' };
    expect(filterPrescriptionEvent(data, variables)).toBeTruthy();
  });

  it('filterCardAdded(): returns false if clinify id in data and variables dont match', () => {
    data = { profileId: '1' };
    variables = { profileId: '2' };
    expect(filterCardAdded(data, variables)).toBeFalsy();
  });

  it('filterCardAdded(): returns true if clinify id in data and variables dont match', () => {
    data = { profileId: '1' };
    variables = { profileId: '1' };
    expect(filterCardAdded(data, variables)).toBeTruthy();
  });

  it('filterInputAdded(): returns false if hospital id in data and variables dont match', () => {
    data = {
      InputDetailAdded: {
        admission: { profileId: '1' },
      },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterInputAdded(data, variables)).toBeFalsy();
  });

  it('filterInputAdded(): returns false if hospital id in data and variables dont match', () => {
    data = {
      InputDetailAdded: {
        admission: { profileId: '1' },
      },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterInputAdded(data, variables)).toBeTruthy();
  });

  it('filterOutputAdded(): returns false if hospital id in data and variables dont match', () => {
    data = {
      OutputDetailAdded: {
        admission: { profileId: '1' },
      },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterOutputAdded(data, variables)).toBeFalsy();
  });

  it('filterOutputAdded(): returns false if hospital id in data and variables dont match', () => {
    data = {
      OutputDetailAdded: {
        admission: { profileId: '1' },
      },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterOutputAdded(data, variables)).toBeTruthy();
  });

  it('filterInputUpdated(): returns false if hospital id in data and variables dont match', () => {
    data = {
      InputDetailUpdated: {
        admission: { profileId: '1' },
      },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterInputUpdated(data, variables)).toBeFalsy();
  });

  it('filterInputUpdated(): returns false if hospital id in data and variables dont match', () => {
    data = {
      InputDetailUpdated: {
        admission: { profileId: '1' },
      },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterInputUpdated(data, variables)).toBeTruthy();
  });

  it('filterOutputUpdated(): returns false if hospital id in data and variables dont match', () => {
    data = {
      OutputDetailUpdated: {
        admission: { profileId: '1' },
      },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterOutputUpdated(data, variables)).toBeFalsy();
  });

  it('filterOutputUpdated(): returns false if hospital id in data and variables dont match', () => {
    data = {
      OutputDetailUpdated: {
        admission: { profileId: '1' },
      },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterOutputUpdated(data, variables)).toBeTruthy();
  });

  it('filterInputRemoved(): returns false if hospital id in data and variables dont match', () => {
    data = {
      InputDetailRemoved: {
        admission: { profileId: '1' },
      },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterInputRemoved(data, variables)).toBeFalsy();
  });

  it('filterInputRemoved(): returns true if hospital id in data and variables dont match', () => {
    data = {
      InputDetailRemoved: {
        admission: { profileId: '1' },
      },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterInputRemoved(data, variables)).toBeTruthy();
  });

  it('filterOutputRemoved(): returns false if hospital id in data and variables dont match', () => {
    data = {
      OutputDetailRemoved: {
        admission: { profileId: '1' },
      },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterOutputRemoved(data, variables)).toBeFalsy();
  });

  it('filterOutputRemoved(): returns true if hospital id in data and variables dont match', () => {
    data = {
      OutputDetailRemoved: {
        admission: { profileId: '1' },
      },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterOutputRemoved(data, variables)).toBeTruthy();
  });

  it('filterWalletEvent() should return true if profileId in data and variable match', () => {
    data = {
      profileId: '1',
    };
    variables = { profileId: '1', hospitalId: '11' };

    expect(filterWalletEvent(data, variables)).toBeTruthy();
  });

  it('filterWalletEvent() should return false if profileId in data and variable does not match', () => {
    data = {
      clinifyId: '2',
    };
    variables = { clinifyId: '1', hospitalId: '11' };

    expect(filterWalletEvent(data, variables)).toBeFalsy();
  });

  it('filterWalletTransactionEvent() should return true if profileId in data and variable match', () => {
    data = {
      WalletTransactionEvent: {
        ids: ['1', '2'],
      },
    };
    variables = { walletId: '1' };

    expect(filterWalletTransactionEvent(data, variables)).toBeTruthy();
  });

  it('filterWalletTransactionEvent() should return false if profileId in data and variable does not match', () => {
    data = {
      WalletTransactionEvent: {
        ids: ['1', '2'],
      },
    };
    variables = { recordId: '4' };

    expect(filterWalletTransactionEvent(data, variables)).toBeFalsy();
  });

  it('filterCardRemoved() should return true if profileId in data and variable match', () => {
    data = {
      profileId: '1',
    };
    variables = { profileId: '1', hospitalId: '11' };

    expect(filterCardRemoved(data, variables)).toBeTruthy();
  });

  it('filterCardRemoved() should return false if profileId in data and variable does not match', () => {
    data = {
      profileId: '2',
    };
    variables = { profileId: '1', hospitalId: '11' };

    expect(filterCardRemoved(data, variables)).toBeFalsy();
  });

  it('filterBillingEvent() should return true if hospital in data and variable match', () => {
    data = {
      billing: { hospitalId: '11' },
    };
    variables = { hospitalId: '11' };

    expect(filterBillingEvent(data, variables)).toBeTruthy();
  });

  it('filterBillingEvent() should return false if hospital in data and variable does not match', () => {
    data = {
      billing: { hospitalId: '22' },
    };
    variables = { hospitalId: '11' };

    expect(filterBillingEvent(data, variables)).toBeFalsy();
  });

  it('filterOrgBillsPaid should return true if hospitalId or profileId in data and variables match', () => {
    data = {
      PayOrgBill: {
        receiverProfileId: '1',
        createdBy: { hospitalId: '11' },
      },
    };
    variables = { profileId: '1', hospitalId: '22' };

    expect(filterOrgBillsPaid(data, variables)).toBeTruthy();
  });

  it('filterOrgBillsPaid should return false if hospitalId or profileId in data and variables does not match', () => {
    data = {
      PayOrgBill: {
        receiverProfile: { profileId: '1' },
        createdBy: { hospitalId: '11' },
      },
    };
    variables = { profileId: '2', hospitalId: '22' };

    expect(filterOrgBillsPaid(data, variables)).toBeFalsy();
  });

  it('registeredPatientFilter should return true if hospitalId from data and variables match', () => {
    data = {
      PatientRegistered: {
        hospitalIds: ['11'],
      },
    };
    variables = { hospitalId: '11' };

    expect(registeredPatientFilter(data, variables)).toBeTruthy();
  });

  it('registeredPatientFilter should return false if hospitalId from data and variables does not match', () => {
    data = {
      PatientRegistered: {
        hospitalIds: ['11'],
      },
    };
    variables = { hospitalId: '22' };

    expect(registeredPatientFilter(data, variables)).toBeFalsy();
  });

  it('removedPatientFilter should return true if hospitalId from data and variables match', () => {
    data = {
      PatientRemoved: [
        {
          registeredWithId: '11',
        },
      ],
    };
    variables = { hospitalId: '11' };

    expect(removedPatientFilter(data, variables)).toBeTruthy();
  });

  it('removedPatientFilter should return false if hospitalId from data and variables does not match', () => {
    data = {
      PatientRemoved: [
        {
          registeredWithId: '11',
        },
      ],
    };
    variables = { hospitalId: '22' };

    expect(removedPatientFilter(data, variables)).toBeFalsy();
  });

  it('archivedPatientFilter should return true if hospitalId from data and variables match', () => {
    data = {
      PatientArchived: [
        {
          registeredWithId: '11',
        },
      ],
    };
    variables = { hospitalId: '11' };

    expect(archivedPatientFilter(data, variables)).toBeTruthy();
  });

  it('archivedPatientFilter should return false if hospitalId from data and variables does not match', () => {
    data = {
      PatientArchived: [
        {
          registeredWithId: '11',
        },
      ],
    };
    variables = { hospitalId: '22' };

    expect(archivedPatientFilter(data, variables)).toBeFalsy();
  });

  it('unarchivedPatientFilter should return true if hospitalId from data and variables match', () => {
    data = {
      PatientUnarchived: [
        {
          registeredWithId: '11',
        },
      ],
    };
    variables = { hospitalId: '11' };

    expect(unarchivedPatientFilter(data, variables)).toBeTruthy();
  });

  it('unarchivedPatientFilter should return true if hospitalId from data and variables match', () => {
    data = {
      PatientUnarchived: [
        {
          registeredWithId: '11',
        },
      ],
    };
    variables = { hospitalId: '22' };

    expect(unarchivedPatientFilter(data, variables)).toBeFalsy();
  });

  it('filterAntenatalAdded(): returns false if neither hospital id nor profile id in data and variables do not match', () => {
    data = {
      AntenatalAdded: { hospitalId: '1', profileId: '1' },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterAntenatalAdded(data, variables)).toBeFalsy();
  });

  it('filterAntenatalAdded(): returns true if either hospital id or profile id in data and variables match', () => {
    data = {
      AntenatalAdded: { hospitalId: '1', profileId: '1' },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterAntenatalAdded(data, variables)).toBeTruthy();
  });

  it('filterAntenatalUpdated(): returns false if neither hospital id nor profile id in data and variables do not match', () => {
    data = {
      AntenatalUpdated: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterAntenatalUpdated(data, variables)).toBeFalsy();
  });

  it('filterAntenatalUpdated(): returns true if either hospital id or profile id in data and variables match', () => {
    data = {
      AntenatalUpdated: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterAntenatalUpdated(data, variables)).toBeTruthy();
  });

  it('filterAntenatalRemoved(): returns false if neither hospital id nor profile id in data and variables do not match', () => {
    data = {
      AntenatalRemoved: [
        {
          hospitalId: '1',
          profileId: '1',
        },
      ],
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterAntenatalRemoved(data, variables)).toBeFalsy();
  });

  it('filterAntenatalRemoved(): returns true if either hospital id or profile id in data and variables match', () => {
    data = {
      AntenatalRemoved: [
        {
          hospitalId: '1',
          profileId: '1',
        },
      ],
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterAntenatalRemoved(data, variables)).toBeTruthy();
  });

  it('filterAntenatalUnarchived(): returns false if neither hospital id nor profile id in data and variables do not match', () => {
    data = {
      AntenatalUnarchived: [
        {
          hospitalId: '1',
          profileId: '1',
        },
      ],
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterAntenatalUnarchived(data, variables)).toBeFalsy();
  });

  it('filterAntenatalUnarchived(): returns true if either hospital id or profile id in data and variables match', () => {
    data = {
      AntenatalUnarchived: [
        {
          hospitalId: '1',
          profileId: '1',
        },
      ],
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterAntenatalUnarchived(data, variables)).toBeTruthy();
  });

  it('filterAntenatalArchived(): returns false if neither hospital id nor profile id in data and variables do not match', () => {
    data = {
      AntenatalArchived: [
        {
          hospitalId: '1',
          profileId: '1',
        },
      ],
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterAntenatalArchived(data, variables)).toBeFalsy();
  });

  it('filterAntenatalArchived(): returns true if either hospital id or profile id in data and variables match', () => {
    data = {
      AntenatalArchived: [
        {
          hospitalId: '1',
          profileId: '1',
        },
      ],
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterAntenatalArchived(data, variables)).toBeTruthy();
  });

  it('filterAntenatalEvent(): returns false if profile id in data and variables dont match', () => {
    data = {
      profileId: '1',
    };
    variables = { profileId: '2' };
    expect(filterAntenatalEvent(data, variables)).toBeFalsy();
  });

  it('filterAntenatalEvent(): returns true if profile id in data and variables match', () => {
    data = {
      profileId: '1',
    };
    variables = { profileId: '1' };
    expect(filterAntenatalEvent(data, variables)).toBeTruthy();
  });

  it('filterAntenatalDetailsAdded(): returns false if neither hospital id nor profile id in data and variables do not match', () => {
    data = {
      AntenatalDetailAdded: { antenatal: { profileId: '1' } },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterAntenatalDetailsAdded(data, variables)).toBeFalsy();
  });

  it('filterAntenatalDetailsAdded(): returns true if either hospital id or profile id in data and variables match', () => {
    data = {
      AntenatalDetailAdded: { antenatal: { profileId: '1' } },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterAntenatalDetailsAdded(data, variables)).toBeTruthy();
  });

  it('filterAntenatalDetailsUpdated(): returns false if neither hospital id nor profile id in data and variables do not match', () => {
    data = {
      AntenatalDetailUpdated: {
        antenatal: { profileId: '1' },
      },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterAntenatalDetailsUpdated(data, variables)).toBeFalsy();
  });

  it('filterAntenatalDetailsUpdated(): returns true if either hospital id or profile id in data and variables match', () => {
    data = {
      AntenatalDetailUpdated: {
        antenatal: { profileId: '1' },
      },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterAntenatalDetailsUpdated(data, variables)).toBeTruthy();
  });

  it('filterAntenatalDetailsRemoved(): returns false if neither hospital id nor profile id in data and variables do not match', () => {
    data = {
      AntenatalDetailRemoved: {
        antenatal: { profileId: '1' },
      },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterAntenatalDetailsRemoved(data, variables)).toBeFalsy();
  });

  it('filterAntenatalDetailsRemoved(): returns true if either hospital id or profile id in data and variables match', () => {
    data = {
      AntenatalDetailRemoved: {
        antenatal: { profileId: '1' },
      },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterAntenatalDetailsRemoved(data, variables)).toBeTruthy();
  });

  it('filterPartographAdded(): returns false if neither hospital id nor profile id in data and variables do not match', () => {
    data = {
      PartographAdded: { hospitalId: '1', profileId: '1' },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterPartographAdded(data, variables)).toBeFalsy();
  });

  it('filterPartographAdded(): returns true if either hospital id or profile id in data and variables match', () => {
    data = {
      PartographAdded: { hospitalId: '1', profileId: '1' },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterPartographAdded(data, variables)).toBeTruthy();
  });

  it('filterPartographUpdated(): returns false if neither hospital id nor profile id in data and variables do not match', () => {
    data = {
      PartographUpdated: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterPartographUpdated(data, variables)).toBeFalsy();
  });

  it('filterPartographUpdated(): returns true if either hospital id or profile id in data and variables match', () => {
    data = {
      PartographUpdated: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterPartographUpdated(data, variables)).toBeTruthy();
  });

  it('filterPartographRemoved(): returns false if neither hospital id nor profile id in data and variables do not match', () => {
    data = {
      PartographRemoved: [
        {
          hospitalId: '1',
          profileId: '1',
        },
      ],
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterPartographRemoved(data, variables)).toBeFalsy();
  });

  it('filterPartographRemoved(): returns true if either hospital id or profile id in data and variables match', () => {
    data = {
      PartographRemoved: [
        {
          hospitalId: '1',
          profileId: '1',
        },
      ],
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterPartographRemoved(data, variables)).toBeTruthy();
  });

  it('filterPreOperationChecklistAdded(): returns false if neither hospital id nor profile id in data and variables do not match', () => {
    data = {
      PreOperationChecklistAdded: { hospitalId: '1', profileId: '1' },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterPreOperationChecklistAdded(data, variables)).toBeFalsy();
  });

  it('filterPreOperationChecklistAdded(): returns true if either hospital id or profile id in data and variables match', () => {
    data = {
      PreOperationChecklistAdded: { hospitalId: '1', profileId: '1' },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterPreOperationChecklistAdded(data, variables)).toBeTruthy();
  });

  it('filterPreOperationChecklistUpdated(): returns false if neither hospital id nor profile id in data and variables do not match', () => {
    data = {
      PreOperationChecklistUpdated: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterPreOperationChecklistUpdated(data, variables)).toBeFalsy();
  });

  it('filterPreOperationChecklistUpdated(): returns true if either hospital id or profile id in data and variables match', () => {
    data = {
      PreOperationChecklistUpdated: {
        hospitalId: '1',
        profileId: '1',
      },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterPreOperationChecklistUpdated(data, variables)).toBeTruthy();
  });

  it('filterPreOperationChecklistRemoved(): returns false if neither hospital id nor profile id in data and variables do not match', () => {
    data = {
      PreOperationChecklistRemoved: [
        {
          hospitalId: '1',
          profileId: '1',
        },
      ],
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterPreOperationChecklistRemoved(data, variables)).toBeFalsy();
  });

  it('filterPreOperationChecklistRemoved(): returns true if either hospital id or profile id in data and variables match', () => {
    data = {
      PreOperationChecklistRemoved: [
        {
          hospitalId: '1',
          profileId: '1',
        },
      ],
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterPreOperationChecklistRemoved(data, variables)).toBeTruthy();
  });

  it('filterInventoryEvent(): returns false if hospital id in data and variables dont match', () => {
    data = { inventory: { hospitalId: '4' } };
    variables = { hospitalId: '2' };
    expect(filterInventoryEvent(data, variables)).toBeFalsy();
  });

  it('filterInventoryEvent(): returns true if hospital id in data and variables match', () => {
    data = { inventory: { hospitalId: '4' } };
    variables = { hospitalId: '4' };
    expect(filterInventoryEvent(data, variables)).toBeTruthy();
  });

  it('filterInventoryAdded(): returns false if hospital id in data and variables do not match', () => {
    data = { InventoryAdded: [{ hospitalId: '1' }] };
    variables = { hospitalId: '2' };
    expect(filterInventoryAdded(data, variables)).toBeFalsy();
  });

  it('filterInventoryAdded(): returns true if hospital id in data and variables match', () => {
    data = { InventoryAdded: [{ hospitalId: '1' }] };
    variables = { hospitalId: '1' };
    expect(filterInventoryAdded(data, variables)).toBeTruthy();
  });

  it('filterInventoryUpdated(): returns false if hospital id in data and variables do not match', () => {
    data = { InventoryUpdated: { hospitalId: '1' } };
    variables = { hospitalId: '2' };
    expect(filterInventoryUpdated(data, variables)).toBeFalsy();
  });

  it('filterInventoryUpdated(): returns true if hospital id in data and variables match', () => {
    data = { InventoryUpdated: { hospitalId: '1' } };
    variables = { hospitalId: '1' };
    expect(filterInventoryUpdated(data, variables)).toBeTruthy();
  });

  it('filterInventoryRemoved(): returns false if hospital id in data and variables do not match', () => {
    data = { InventoryRemoved: [{ hospitalId: '1' }] };
    variables = { hospitalId: '2' };
    expect(filterInventoryRemoved(data, variables)).toBeFalsy();
  });

  it('filterInventoryRemoved(): returns true if hospital id in data and variables match', () => {
    data = { InventoryRemoved: [{ hospitalId: '2' }] };
    variables = { hospitalId: '2' };
    expect(filterInventoryRemoved(data, variables)).toBeTruthy();
  });

  it('filterInventoryItemUpdated(): returns false if hospital id in data and variables do not match', () => {
    data = { InventoryItemUpdated: { id: '3' } };
    variables = { hospitalId: '2' };
    expect(filterInventoryItemUpdated(data, variables)).toBeFalsy();
  });

  it('filterInventoryItemUpdated(): returns true if hospital id in data and variables match', () => {
    data = { InventoryItemUpdated: { id: '2' } };
    variables = { hospitalId: '2' };
    expect(filterInventoryItemUpdated(data, variables)).toBeTruthy();
  });

  it('filterOncologyAdded(): returns false if profile id in data and variables do not match', () => {
    data = {
      OncologyAdded: { profileId: '1' },
    };
    variables = { profileId: '2' };
    expect(filterOncologyAdded(data, variables)).toBeFalsy();
  });

  it('filterOncologyAdded(): returns true if profile id in data and variables match', () => {
    data = {
      OncologyAdded: { profileId: '1' },
    };
    variables = { profileId: '1' };
    expect(filterOncologyAdded(data, variables)).toBeTruthy();
  });

  it('filterOncologyUpdated(): returns false if profile id in data and variables do not match', () => {
    data = {
      OncologyUpdated: { profileId: '1' },
    };
    variables = { profileId: '2' };
    expect(filterOncologyUpdated(data, variables)).toBeFalsy();
  });

  it('filterOncologyUpdated(): returns true if profile id in data and variables match', () => {
    data = {
      OncologyUpdated: { profileId: '1' },
    };
    variables = { profileId: '1' };
    expect(filterOncologyUpdated(data, variables)).toBeTruthy();
  });

  it('filterOncologyRemoved(): returns false if profile id in data and variables do not match', () => {
    data = {
      OncologyRemoved: { profileId: '1' },
    };
    variables = { profileId: '2' };
    expect(filterOncologyRemoved(data, variables)).toBeFalsy();
  });

  it('filterOncologyRemoved(): returns false if profile id in data and variables match', () => {
    data = {
      OncologyRemoved: { profileId: '1' },
    };
    variables = { profileId: '1' };
    expect(filterOncologyRemoved(data, variables)).toBeTruthy();
  });

  it('filterOncologyChartUpdated(): returns false if profile id in data and variables do not match', () => {
    data = {
      OncologyChartUpdated: { oncologyHistory: { profileId: '1' } },
    };
    variables = { profileId: '2' };
    expect(filterOncologyChartUpdated(data, variables)).toBeFalsy();
  });

  it('filterOncologyChartUpdated(): returns false if profile id in data and variables match', () => {
    data = {
      OncologyChartUpdated: { oncologyHistory: { profileId: '1' } },
    };
    variables = { profileId: '1' };
    expect(filterOncologyChartUpdated(data, variables)).toBeTruthy();
  });

  it('filterAdmissionLineAdded(): returns false if profile id in data and variables do not match', () => {
    data = {
      AdmissionLineAdded: { admission: { profileId: '1' } },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterAdmissionLineAdded(data, variables)).toBeFalsy();
  });

  it('filterAdmissionLineAdded(): returns true if profile id in data and variables match', () => {
    data = {
      AdmissionLineAdded: { admission: { profileId: '1' } },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterAdmissionLineAdded(data, variables)).toBeTruthy();
  });

  it('filterAdmissionLineUpdated(): returns false if profile id in data and variables do not match', () => {
    data = {
      AdmissionLineUpdated: { admission: { profileId: '1' } },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterAdmissionLineUpdated(data, variables)).toBeFalsy();
  });

  it('filterAdmissionLineUpdated(): returns true if profile id in data and variables match', () => {
    data = {
      AdmissionLineUpdated: { admission: { profileId: '1' } },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterAdmissionLineUpdated(data, variables)).toBeTruthy();
  });

  it('filterAdmissionLineRemoved(): returns false if profile id in data and variables do not match', () => {
    data = {
      AdmissionLineRemoved: { admission: { profileId: '1' } },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterAdmissionLineRemoved(data, variables)).toBeFalsy();
  });

  it('filterAdmissionLineRemoved(): returns true if profile id in data and variables match', () => {
    data = {
      AdmissionLineRemoved: { admission: { profileId: '1' } },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterAdmissionLineRemoved(data, variables)).toBeTruthy();
  });

  it('filterOutpatientConsultationAdded(): returns true if hospital id in data and variables match', () => {
    data = {
      OutpatientConsultationAdded: { hospitalId: '2' },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterOutpatientConsultationAdded(data, variables)).toBeTruthy();
  });

  it('filterOutpatientConsultationAdded(): returns false if hospital id in data and variables do not match', () => {
    data = {
      OutpatientConsultationAdded: { hospitalId: '1' },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterOutpatientConsultationAdded(data, variables)).toBeFalsy();
  });

  it('filterMedicationBundleAdded(): returns true if profile id in data and variables match', () => {
    data = {
      MedicationBundleAdded: { profileId: '1' },
    };
    variables = { profileId: '1' };
    expect(filterMedicationBundleAdded(data, variables)).toBeTruthy();
  });

  it('filterMedicationBundleAdded(): returns false if profile id in data and variables do not match', () => {
    data = {
      MedicationBundleAdded: { profileId: '2' },
    };
    variables = { profileId: '1' };
    expect(filterMedicationBundleAdded(data, variables)).toBeFalsy();
  });

  it('filterMedicationBundleUpdated(): returns true if profile id in data and variables match', () => {
    data = {
      MedicationBundleUpdated: { profileId: '1' },
    };
    variables = { profileId: '1' };
    expect(filterMedicationBundleUpdated(data, variables)).toBeTruthy();
  });

  it('filterMedicationBundleUpdated(): returns false if profile id in data and variables do not match', () => {
    data = {
      MedicationBundleUpdated: { profileId: '2' },
    };
    variables = { profileId: '1' };
    expect(filterMedicationBundleUpdated(data, variables)).toBeFalsy();
  });

  it('filterMedicationBundleRemoved(): returns true if profile id in data and variables match', () => {
    data = {
      MedicationBundleRemoved: [{ profileId: '1' }],
    };
    variables = { profileId: '1' };
    expect(filterMedicationBundleRemoved(data, variables)).toBeTruthy();
  });

  it('filterMedicationBundleRemoved(): returns false if profile id in data and variables do not match', () => {
    data = {
      MedicationBundleRemoved: [{ profileId: '2' }],
    };
    variables = { profileId: '1' };
    expect(filterMedicationBundleRemoved(data, variables)).toBeFalsy();
  });

  it('filterMedicationBundleArchived(): returns true if profile id in data and variables match', () => {
    data = {
      MedicationBundleArchived: [{ profileId: '1' }],
    };
    variables = { profileId: '1' };
    expect(filterMedicationBundleArchived(data, variables)).toBeTruthy();
  });

  it('filterMedicationBundleArchived(): returns false if profile id in data and variables do not match', () => {
    data = {
      MedicationBundleArchived: [{ profileId: '2' }],
    };
    variables = { profileId: '1' };
    expect(filterMedicationBundleArchived(data, variables)).toBeFalsy();
  });

  it('filterMedicationBundleUnarchived(): returns true if profile id in data and variables match', () => {
    data = {
      MedicationBundleUnarchived: [{ profileId: '1' }],
    };
    variables = { profileId: '1' };
    expect(filterMedicationBundleUnarchived(data, variables)).toBeTruthy();
  });

  it('filterMedicationBundleUnarchived(): returns false if profile id in data and variables do not match', () => {
    data = {
      MedicationBundleUnarchived: [{ profileId: '2' }],
    };
    variables = { profileId: '1' };
    expect(filterMedicationBundleUnarchived(data, variables)).toBeFalsy();
  });

  it('filterMedicationBundleItemAdded(): returns true if profile id in data and variables match', () => {
    data = {
      MedicationBundleItemAdded: {
        medicationBundle: { profileId: '1' },
      },
    };
    variables = { profileId: '1' };
    expect(filterMedicationBundleItemAdded(data, variables)).toBeTruthy();
  });

  it('filterMedicationBundleItemAdded(): returns false if profile id in data and variables do not match', () => {
    data = {
      MedicationBundleItemAdded: {
        medicationBundle: { profileId: '2' },
      },
    };
    variables = { profileId: '1' };
    expect(filterMedicationBundleItemAdded(data, variables)).toBeFalsy();
  });

  it('filterMedicationBundleItemUpdated(): returns true if profile id in data and variables match', () => {
    data = {
      MedicationBundleItemUpdated: {
        medicationBundle: { profileId: '1' },
      },
    };
    variables = { profileId: '1' };
    expect(filterMedicationBundleItemUpdated(data, variables)).toBeTruthy();
  });

  it('filterMedicationBundleItemUpdated(): returns false if profile id in data and variables do not match', () => {
    data = {
      MedicationBundleItemUpdated: {
        medicationBundle: { profileId: '2' },
      },
    };
    variables = { profileId: '1' };
    expect(filterMedicationBundleItemUpdated(data, variables)).toBeFalsy();
  });

  it('filterMedicationBundleItemRemoved(): returns true if profile id in data and variables match', () => {
    data = {
      MedicationBundleItemRemoved: {
        medicationBundle: { profileId: '1' },
      },
    };
    variables = { profileId: '1' };
    expect(filterMedicationBundleItemRemoved(data, variables)).toBeTruthy();
  });

  it('filterMedicationBundleItemRemoved(): returns false if profile id in data and variables do not match', () => {
    data = {
      MedicationBundleItemRemoved: {
        medicationBundle: { profileId: '2' },
      },
    };
    variables = { profileId: '1' };
    expect(filterMedicationBundleItemRemoved(data, variables)).toBeFalsy();
  });

  it('filterOrgBillsRemoved(): returns true if hospital id in data and variables match', () => {
    data = {
      OrgBillsRemoved: [{ hospitalId: '1' }],
    };
    variables = { hospitalId: '1' };
    expect(filterOrgBillsRemoved(data, variables)).toBeTruthy();
  });

  it('filterOrgBillsRemoved(): returns false if hospital id in data and variables do not match', () => {
    data = {
      OrgBillsRemoved: [{ hospitalId: '1', profileId: '5' }],
    };
    variables = { hospitalId: '2', profileId: '3' };
    expect(filterOrgBillsRemoved(data, variables)).toBeFalsy();
  });

  it('filterPreauthorizationDetailsUpdated() returns true if profile id in data and variables match', () => {
    data = {
      PreauthorizationDetailsUpdated: { hospitalId: '1' },
    };
    variables = { hospitalId: '1' };
    expect(filterPreauthorizationDetailsUpdated(data, variables)).toBeTruthy();
  });
  it('filterPreauthorizationDetailsUpdated() returns false if profile id in data and variables do not match', () => {
    data = {
      PreauthorizationDetailsUpdated: { hospitalId: '2' },
    };
    variables = { hospitalId: '1' };
    expect(filterPreauthorizationDetailsUpdated(data, variables)).toBeFalsy();
  });
  it('filterAdministrationNoteAdded() returns true if profile id in data and variables match', () => {
    data = {
      AdministrationNoteAdded: { medication: { hospitalId: '1' } },
    };
    variables = { hospitalId: '1' };
    expect(filterAdministrationNoteAdded(data, variables)).toBeTruthy();
  });
  it('filterAdministrationNoteAdded() returns false if profile id in data and variables do not match', () => {
    data = {
      AdministrationNoteAdded: { medication: { hospitalId: '2' } },
    };
    variables = { profileId: '1' };
    expect(filterAdministrationNoteAdded(data, variables)).toBeFalsy();
  });
  it('filterAdministrationNoteUpdated() returns true if profile id in data and variables match', () => {
    data = {
      AdministrationNoteUpdated: { medication: { profileId: '1' } },
    };
    variables = { profileId: '1' };
    expect(filterAdministrationNoteUpdated(data, variables)).toBeTruthy();
  });
  it('filterAdministrationNoteUpdated() returns false if profile id in data and variables do not match', () => {
    data = {
      AdministrationNoteUpdated: { medication: { profileId: '2' } },
    };
    variables = { profileId: '1' };
    expect(filterAdministrationNoteUpdated(data, variables)).toBeFalsy();
  });
  it('filterAdministrationNoteRemoved() returns true if profile id in data and variables do not match', () => {
    data = {
      AdministrationNoteRemoved: { medication: { profileId: '1' } },
    };
    variables = { profileId: '1' };
    expect(filterAdministrationNoteRemoved(data, variables)).toBeTruthy();
  });
  it('filterAdministrationNoteRemoved() returns false if profile id in data and variables do not match', () => {
    data = {
      AdministrationNoteRemoved: { medication: { profileId: '2' } },
    };
    variables = { profileId: '1' };
    expect(filterAdministrationNoteRemoved(data, variables)).toBeFalsy();
  });
  it('filterNursingServiceEvent(): returns false if profile id in data and variables dont match', () => {
    data = {
      nursingService: {
        profileId: '1',
      },
    };
    variables = { profileId: '2' };
    expect(filterNursingServiceEvent(data, variables)).toBeFalsy();
  });
  it('filterNursingServiceEvent(): returns true if profile id in data and variables match', () => {
    data = {
      nursingService: {
        profileId: '1',
      },
    };
    variables = { profileId: '1' };
    expect(filterNursingServiceEvent(data, variables)).toBeTruthy();
  });
  it('filterNursingServiceAdded() returns true if profile id in data and variables match', () => {
    data = {
      NursingServiceAdded: { profileId: '1' },
    };
    variables = { profileId: '1' };
    expect(filterNursingServiceAdded(data, variables)).toBeTruthy();
  });
  it('filterNursingServiceAdded() returns false if profile id in data and variables do not match', () => {
    data = {
      NursingServiceAdded: { profileId: '2' },
    };
    variables = { profileId: '1' };
    expect(filterNursingServiceAdded(data, variables)).toBeFalsy();
  });
  it('filterNursingServiceUpdated() returns true if profile id in data and variables match', () => {
    data = {
      NursingServiceUpdated: { profileId: '1' },
    };
    variables = { profileId: '1' };
    expect(filterNursingServiceUpdated(data, variables)).toBeTruthy();
  });
  it('filterNursingServiceUpdated() returns false if profile id in data and variables do not match', () => {
    data = {
      NursingServiceUpdated: { profileId: '2' },
    };
    variables = { profileId: '1' };
    expect(filterNursingServiceUpdated(data, variables)).toBeFalsy();
  });
  it('filterNursingServiceRemoved() returns true if profile id in data and variables match', () => {
    data = {
      NursingServiceRemoved: [{ profileId: '1' }],
    };
    variables = { profileId: '1' };
    expect(filterNursingServiceRemoved(data, variables)).toBeTruthy();
  });
  it('filterNursingServiceRemoved() returns false if profile id in data and variables do not match', () => {
    data = {
      NursingServiceRemoved: [{ profileId: '2' }],
    };
    variables = { profileId: '1' };
    expect(filterNursingServiceRemoved(data, variables)).toBeFalsy();
  });
  it('filterNursingServiceArchived() returns true if profile id in data and variables match', () => {
    data = {
      NursingServiceArchived: [{ profileId: '1' }],
    };
    variables = { profileId: '1' };
    expect(filterNursingServiceArchived(data, variables)).toBeTruthy();
  });
  it('filterNursingServiceArchived() returns false if profile id in data and variables do not match', () => {
    data = {
      NursingServiceArchived: [{ profileId: '2' }],
    };
    variables = { profileId: '1' };
    expect(filterNursingServiceArchived(data, variables)).toBeFalsy();
  });
  it('filterNursingServiceUnarchived() returns true if profile id in data and variables match', () => {
    data = {
      NursingServiceUnarchived: [{ profileId: '1' }],
    };
    variables = { profileId: '1' };
    expect(filterNursingServiceUnarchived(data, variables)).toBeTruthy();
  });
  it('filterNursingServiceUnarchived() returns false if profile id in data and variables do not match', () => {
    data = {
      NursingServiceUnarchived: [{ profileId: '2' }],
    };
    variables = { profileId: '1' };
    expect(filterNursingServiceUnarchived(data, variables)).toBeFalsy();
  });
  it('filterPostnatalAdded() returns true if profile id in data and variables match', () => {
    data = {
      PostnatalAdded: { profileId: '1' },
    };
    variables = { profileId: '1' };
    expect(filterPostnatalAdded(data, variables)).toBeTruthy();
  });
  it('filterPostnatalAdded() returns false if profile id in data and variables do not match', () => {
    data = {
      PostnatalAdded: { profileId: '2' },
    };
    variables = { profileId: '1' };
    expect(filterPostnatalAdded(data, variables)).toBeFalsy();
  });
  it('filterPostnatalUpdated() returns true if profile id in data and variables match', () => {
    data = {
      PostnatalUpdated: { profileId: '1' },
    };
    variables = { profileId: '1' };
    expect(filterPostnatalUpdated(data, variables)).toBeTruthy();
  });
  it('filterPostnatalUpdated() returns false if profile id in data and variables do not match', () => {
    data = {
      PostnatalUpdated: { profileId: '2' },
    };
    variables = { profileId: '1' };
    expect(filterPostnatalUpdated(data, variables)).toBeFalsy();
  });
  it('filterPostnatalRemoved() returns true if profile id in data and variables match', () => {
    data = {
      PostnatalRemoved: [{ profileId: '1' }],
    };
    variables = { profileId: '1' };
    expect(filterPostnatalRemoved(data, variables)).toBeTruthy();
  });
  it('filterPostnatalRemoved() returns false if profile id in data and variables do not match', () => {
    data = {
      PostnatalRemoved: [{ profileId: '2' }],
    };
    variables = { profileId: '1' };
    expect(filterPostnatalRemoved(data, variables)).toBeFalsy();
  });
  it('filterPostnatalArchived() returns true if profile id in data and variables match', () => {
    data = {
      PostnatalArchived: [{ profileId: '1' }],
    };
    variables = { profileId: '1' };
    expect(filterPostnatalArchived(data, variables)).toBeTruthy();
  });
  it('filterPostnatalArchived() returns false if profile id in data and variables do not match', () => {
    data = {
      PostnatalArchived: [{ profileId: '2' }],
    };
    variables = { profileId: '1' };
    expect(filterPostnatalArchived(data, variables)).toBeFalsy();
  });
  it('filterPostnatalUnarchived() returns true if profile id in data and variables match', () => {
    data = {
      PostnatalUnarchived: [{ profileId: '1' }],
    };
    variables = { profileId: '1' };
    expect(filterPostnatalUnarchived(data, variables)).toBeTruthy();
  });
  it('filterPostnatalUnarchived() returns false if profile id in data and variables do not match', () => {
    data = {
      PostnatalUnarchived: [{ profileId: '2' }],
    };
    variables = { profileId: '1' };
    expect(filterPostnatalUnarchived(data, variables)).toBeFalsy();
  });
  it('filterLabourAndDeliveryAdded() returns true if profile id in data and variables match', () => {
    data = {
      LabourAndDeliveryAdded: { profileId: '1' },
    };
    variables = { profileId: '1' };
    expect(filterLabourAndDeliveryAdded(data, variables)).toBeTruthy();
  });
  it('filterLabourAndDeliveryAdded() returns false if profile id in data and variables do not match', () => {
    data = {
      LabourAndDeliveryAdded: { profileId: '2' },
    };
    variables = { profileId: '1' };
    expect(filterLabourAndDeliveryAdded(data, variables)).toBeFalsy();
  });
  it('filterLabourAndDeliveryUpdated() returns true if profile id in data and variables match', () => {
    data = {
      LabourAndDeliveryUpdated: { profileId: '1' },
    };
    variables = { profileId: '1' };
    expect(filterLabourAndDeliveryUpdated(data, variables)).toBeTruthy();
  });
  it('filterLabourAndDeliveryUpdated() returns false if profile id in data and variables do not match', () => {
    data = {
      LabourAndDeliveryUpdated: { profileId: '2' },
    };
    variables = { profileId: '1' };
    expect(filterLabourAndDeliveryUpdated(data, variables)).toBeFalsy();
  });
  it('filterLabourAndDeliveryRemoved() returns true if profile id in data and variables match', () => {
    data = {
      LabourAndDeliveryRemoved: [{ profileId: '1' }],
    };
    variables = { profileId: '1' };
    expect(filterLabourAndDeliveryRemoved(data, variables)).toBeTruthy();
  });
  it('filterLabourAndDeliveryRemoved() returns false if profile id in data and variables do not match', () => {
    data = {
      LabourAndDeliveryRemoved: [{ profileId: '2' }],
    };
    variables = { profileId: '1' };
    expect(filterLabourAndDeliveryRemoved(data, variables)).toBeFalsy();
  });
  it('filterLabourAndDeliveryArchived() returns true if profile id in data and variables match', () => {
    data = {
      LabourAndDeliveryArchived: [{ profileId: '1' }],
    };
    variables = { profileId: '1' };
    expect(filterLabourAndDeliveryArchived(data, variables)).toBeTruthy();
  });
  it('filterLabourAndDeliveryArchived() returns false if profile id in data and variables do not match', () => {
    data = {
      LabourAndDeliveryArchived: [{ profileId: '2' }],
    };
    variables = { profileId: '1' };
    expect(filterLabourAndDeliveryArchived(data, variables)).toBeFalsy();
  });
  it('filterLabourAndDeliveryUnarchived() returns true if profile id in data and variables match', () => {
    data = {
      LabourAndDeliveryUnarchived: [{ profileId: '1' }],
    };
    variables = { profileId: '1' };
    expect(filterLabourAndDeliveryUnarchived(data, variables)).toBeTruthy();
  });
  it('filterLabourAndDeliveryUnarchived() returns false if profile id in data and variables do not match', () => {
    data = {
      LabourAndDeliveryUnarchived: [{ profileId: '2' }],
    };
    variables = { profileId: '1' };
    expect(filterLabourAndDeliveryUnarchived(data, variables)).toBeFalsy();
  });
  it('filterInvoiceAdded() returns true if hospital id in data and variables match', () => {
    data = {
      InvoiceAdded: { hospitalId: '1', profileId: '2' },
    };
    variables = { hospitalId: '1' };
    expect(filterInvoiceAdded(data, variables)).toBeTruthy();
  });
  it('filterInvoiceAdded() returns false if hospital id in data and variables do not match', () => {
    data = {
      InvoiceAdded: { hospitalId: '2', profileId: '3' },
    };
    variables = { hospitalId: '1' };
    expect(filterInvoiceAdded(data, variables)).toBeFalsy();
  });
  it('filterInvoiceUpdated() returns true if hospital id in data and variables match', () => {
    data = {
      InvoiceUpdated: { hospitalId: '1', profileId: '2' },
    };
    variables = { hospitalId: '1' };
    expect(filterInvoiceUpdated(data, variables)).toBeTruthy();
  });
  it('filterInvoiceUpdated() returns false if hospital id in data and variables do not match', () => {
    data = {
      InvoiceUpdated: { hospitalId: '2', profileId: '3' },
    };
    variables = { hospitalId: '1' };
    expect(filterInvoiceUpdated(data, variables)).toBeFalsy();
  });
  it('filterInvoiceRemoved() returns true if hospital id in data and variables match', () => {
    data = { hospitalId: '1' };
    variables = { hospitalId: '1' };
    expect(filterInvoiceRemoved(data, variables)).toBeTruthy();
  });
  it('filterInvoiceRemoved() returns false if hospital id in data and variables do not match', () => {
    data = { hospitalId: '2', profileId: '3' };
    variables = { hospitalId: '1' };
    expect(filterInvoiceRemoved(data, variables)).toBeFalsy();
  });
  it('filterInvoiceItemAdded() returns true if hospital id in data and variables match', () => {
    data = {
      InvoiceItemAdded: { hospitalId: '1', profileId: '2' },
    };
    variables = { hospitalId: '1' };
    expect(filterInvoiceItemAdded(data, variables)).toBeTruthy();
  });
  it('filterInvoiceItemAdded() returns false if hospital id in data and variables do not match', () => {
    data = {
      InvoiceItemAdded: { hospitalId: '2', profileId: '3' },
    };
    variables = { hospitalId: '1' };
    expect(filterInvoiceItemAdded(data, variables)).toBeFalsy();
  });
  it('filterInvoiceItemUpdated() returns true if hospital id in data and variables match', () => {
    data = {
      InvoiceItemUpdated: { hospitalId: '1', profileId: '2' },
    };
    variables = { hospitalId: '1' };
    expect(filterInvoiceItemUpdated(data, variables)).toBeTruthy();
  });
  it('filterInvoiceItemUpdated() returns false if hospital id in data and variables do not match', () => {
    data = {
      InvoiceItemUpdated: { hospitalId: '2', profileId: '3' },
    };
    variables = { hospitalId: '1' };
    expect(filterInvoiceItemUpdated(data, variables)).toBeFalsy();
  });
  it('filterInvoiceItemRemoved() returns true if hospital id in data and variables match', () => {
    data = {
      InvoiceItemRemoved: { hospitalId: '1', profileId: '2' },
    };
    variables = { hospitalId: '1' };
    expect(filterInvoiceItemRemoved(data, variables)).toBeTruthy();
  });
  it('filterInvoiceItemRemoved() returns false if hospital id in data and variables do not match', () => {
    data = {
      InvoiceItemRemoved: { hospitalId: '2', profileId: '3' },
    };
    variables = { hospitalId: '1' };
    expect(filterInvoiceItemRemoved(data, variables)).toBeFalsy();
  });
  it('filterInvoiceArchived() returns true if hospital id in data and variables match', () => {
    data = { hospitalId: '1', profileId: '2' };
    variables = { hospitalId: '1' };
    expect(filterInvoiceArchived(data, variables)).toBeTruthy();
  });
  it('filterInvoiceArchived() returns false if hospital id in data and variables do not match', () => {
    data = { hospitalId: '2', profileId: '3' };
    variables = { hospitalId: '1' };
    expect(filterInvoiceArchived(data, variables)).toBeFalsy();
  });
  it('filterInvoiceUnarchived() returns true if hospital id in data and variables match', () => {
    data = { hospitalId: '1', profileId: '2' };
    variables = { hospitalId: '1' };
    expect(filterInvoiceUnarchived(data, variables)).toBeTruthy();
  });
  it('filterInvoiceUnarchived() returns false if hospital id in data and variables do not match', () => {
    data = { hospitalId: '2', profileId: '3' };
    variables = { hospitalId: '1' };
    expect(filterInvoiceUnarchived(data, variables)).toBeFalsy();
  });
  it('filterInvoiceStatusUpdated() returns true if hospital id in data and variables match', () => {
    data = {
      InvoiceStatusUpdated: { hospitalId: '1', profileId: '2' },
    };
    variables = { hospitalId: '1' };
    expect(filterInvoiceStatusUpdated(data, variables)).toBeTruthy();
  });
  it('filterInvoiceStatusUpdated() returns false if hospital id in data and variables do not match', () => {
    data = {
      InvoiceStatusUpdated: { hospitalId: '2', profileId: '3' },
    };
    variables = { hospitalId: '1' };
    expect(filterInvoiceStatusUpdated(data, variables)).toBeFalsy();
  });
  it('filterInvoiceAccountAdded() returns true if hospital id in data and variables match', () => {
    data = {
      InvoiceAccountAdded: { hospitalId: '1', profileId: '2' },
    };
    variables = { hospitalId: '1' };
    expect(filterInvoiceAccountAdded(data, variables)).toBeTruthy();
  });
  it('filterInvoiceAccountAdded() returns false if hospital id in data and variables do not match', () => {
    data = {
      InvoiceAccountAdded: { hospitalId: '2', profileId: '3' },
    };
    variables = { hospitalId: '1' };
    expect(filterInvoiceAccountAdded(data, variables)).toBeFalsy();
  });
  it('filterAnaesthesiaAdded() returns true if profileId matches', () => {
    data = { AnaesthesiaAdded: { profileId: '1' } };
    variables = { profileId: '1' };
    expect(filterAnaesthesiaAdded(data, variables)).toBeTruthy();
  });
  it('filterAnaesthesiaAdded() returns false if profileId dont match', () => {
    data = { AnaesthesiaAdded: { profileId: '2' } };
    variables = { profileId: '1' };
    expect(filterAnaesthesiaAdded(data, variables)).toBeFalsy();
  });
  it('filterAnaesthesiaUpdated() returns true if profileId matches', () => {
    data = { AnaesthesiaUpdated: { profileId: '1' } };
    variables = { profileId: '1' };
    expect(filterAnaesthesiaUpdated(data, variables)).toBeTruthy();
  });
  it('filterAnaesthesiaUpdated() returns false if profileId dont match', () => {
    data = { AnaesthesiaUpdated: { profileId: '2' } };
    variables = { profileId: '1' };
    expect(filterAnaesthesiaUpdated(data, variables)).toBeFalsy();
  });
  it('filterAnaesthesiaRemoved() returns true if profileId matches', () => {
    data = { AnaesthesiaRemoved: [{ profileId: '1' }] };
    variables = { profileId: '1' };
    expect(filterAnaesthesiaRemoved(data, variables)).toBeTruthy();
  });
  it('filterAnaesthesiaRemoved() returns false if profileId dont match', () => {
    data = { AnaesthesiaRemoved: [{ profileId: '2' }] };
    variables = { profileId: '1' };
    expect(filterAnaesthesiaRemoved(data, variables)).toBeFalsy();
  });
  it('filterFacilityPreferenceUpdated() returns true if hospitalId matches', () => {
    data = { FacilityPreferenceUpdated: { hospitalId: '1' } };
    variables = { hospitalId: '1' };
    expect(filterFacilityPreferenceUpdated(data, variables)).toBeTruthy();
  });
  it('filterFacilityPreferenceUpdated() returns false if hospitalId matches', () => {
    data = { FacilityPreferenceUpdated: { hospitalId: '2' } };
    variables = { hospitalId: '1' };
    expect(filterFacilityPreferenceUpdated(data, variables)).toBeFalsy();
  });
  it('filterPostOperationChecklistAdded() returns true if profileId matches', () => {
    data = { PostOperationChecklistAdded: { profileId: '1' } };
    variables = { profileId: '1' };
    expect(filterPostOperationChecklistAdded(data, variables)).toBeTruthy();
  });
  it('filterPostOperationChecklistAdded() returns false if profileId dont match', () => {
    data = { PostOperationChecklistAdded: { profileId: '2' } };
    variables = { profileId: '1' };
    expect(filterPostOperationChecklistAdded(data, variables)).toBeFalsy();
  });
  it('filterPostOperationChecklistUpdated() returns true if profileId matches', () => {
    data = { PostOperationChecklistUpdated: { profileId: '1' } };
    variables = { profileId: '1' };
    expect(filterPostOperationChecklistUpdated(data, variables)).toBeTruthy();
  });
  it('filterPostOperationChecklistUpdated() returns false if profileId dont match', () => {
    data = { PostOperationChecklistUpdated: { profileId: '2' } };
    variables = { profileId: '1' };
    expect(filterPostOperationChecklistUpdated(data, variables)).toBeFalsy();
  });
  it('filterPostOperationChecklistRemoved() returns true if profileId matches', () => {
    data = { PostOperationChecklistRemoved: [{ profileId: '1' }] };
    variables = { profileId: '1' };
    expect(filterPostOperationChecklistRemoved(data, variables)).toBeTruthy();
  });
  it('filterPostOperationChecklistRemoved() returns false if profileId dont match', () => {
    data = { PostOperationChecklistRemoved: [{ profileId: '2' }] };
    variables = { profileId: '1' };
    expect(filterPostOperationChecklistRemoved(data, variables)).toBeFalsy();
  });
  it('filterInvoicePaymentAdded() returns true if hospitalId matches', () => {
    data = { InvoicePaymentAdded: { hospitalId: '1' } };
    variables = { hospitalId: '1' };
    expect(filterInvoicePaymentAdded(data, variables)).toBeTruthy();
  });
  it('filterInvoicePaymentAdded() returns false if hospitalId matches', () => {
    data = { InvoicePaymentAdded: { hospitalId: '2' } };
    variables = { hospitalId: '1' };
    expect(filterInvoicePaymentAdded(data, variables)).toBeFalsy();
  });
  it('filterInvoicePaymentUpdated() returns true if hospitalId matches', () => {
    data = { InvoicePaymentUpdated: { hospitalId: '1' } };
    variables = { hospitalId: '1' };
    expect(filterInvoicePaymentUpdated(data, variables)).toBeTruthy();
  });
  it('filterInvoicePaymentUpdated() returns false if hospitalId matches', () => {
    data = { InvoicePaymentUpdated: { hospitalId: '2' } };
    variables = { hospitalId: '1' };
    expect(filterInvoicePaymentUpdated(data, variables)).toBeFalsy();
  });
  it('filterInvoicePaymentRemoved() returns true if hospitalId matches', () => {
    data = { InvoicePaymentRemoved: { hospitalId: '1' } };
    variables = { hospitalId: '1' };
    expect(filterInvoicePaymentRemoved(data, variables)).toBeTruthy();
  });
  it('filterInvoicePaymentRemoved() returns false if hospitalId matches', () => {
    data = { InvoicePaymentRemoved: { hospitalId: '2' } };
    variables = { hospitalId: '1' };
    expect(filterInvoicePaymentRemoved(data, variables)).toBeFalsy();
  });
  it('filterNutritionalHistoryAdded() returns true if profileId matches', () => {
    data = { NutritionalHistoryAdded: { profileId: '1' } };
    variables = { profileId: '1' };
    expect(filterNutritionalHistoryAdded(data, variables)).toBeTruthy();
  });
  it('filterNutritionalHistoryAdded() returns false if profileId dont match', () => {
    data = { NutritionalHistoryAdded: { profileId: '2' } };
    variables = { profileId: '1' };
    expect(filterNutritionalHistoryAdded(data, variables)).toBeFalsy();
  });
  it('filterNutritionalHistoryUpdated() returns true if profileId matches', () => {
    data = { NutritionalHistoryUpdated: { profileId: '1' } };
    variables = { profileId: '1' };
    expect(filterNutritionalHistoryUpdated(data, variables)).toBeTruthy();
  });
  it('filterNutritionalHistoryUpdated() returns false if profileId dont match', () => {
    data = { NutritionalHistoryUpdated: { profileId: '2' } };
    variables = { profileId: '1' };
    expect(filterNutritionalHistoryUpdated(data, variables)).toBeFalsy();
  });
  it('filterNutritionalHistoryRemoved() returns true if profileId matches', () => {
    data = { NutritionalHistoryRemoved: { profileId: '1' } };
    variables = { profileId: '1' };
    expect(filterNutritionalHistoryRemoved(data, variables)).toBeTruthy();
  });
  it('filterNutritionalHistoryRemoved() returns false if profileId dont match', () => {
    data = { NutritionalHistoryRemoved: { profileId: '2' } };
    variables = { profileId: '1' };
    expect(filterNutritionalHistoryRemoved(data, variables)).toBeFalsy();
  });
  it('filterNutritionalHistoryGrowthAdded() returns true if profileId matches', () => {
    data = { NutritionalHistoryGrowthAdded: { profileId: '1' } };
    variables = { profileId: '1' };
    expect(filterNutritionalHistoryGrowthAdded(data, variables)).toBeTruthy();
  });
  it('filterNutritionalHistoryGrowthAdded() returns false if profileId dont match', () => {
    data = { NutritionalHistoryGrowthAdded: { profileId: '2' } };
    variables = { profileId: '1' };
    expect(filterNutritionalHistoryGrowthAdded(data, variables)).toBeFalsy();
  });
  it('filterNutritionalHistoryGrowthUpdated() returns true if profileId matches', () => {
    data = { NutritionalHistoryGrowthUpdated: { profileId: '1' } };
    variables = { profileId: '1' };
    expect(filterNutritionalHistoryGrowthUpdated(data, variables)).toBeTruthy();
  });
  it('filterNutritionalHistoryGrowthUpdated() returns false if profileId dont match', () => {
    data = { NutritionalHistoryGrowthUpdated: { profileId: '2' } };
    variables = { profileId: '1' };
    expect(filterNutritionalHistoryGrowthUpdated(data, variables)).toBeFalsy();
  });
  it('filterNutritionalHistoryGrowthRemoved() returns true if profileId matches', () => {
    data = { NutritionalHistoryGrowthRemoved: { profileId: '1' } };
    variables = { profileId: '1' };
    expect(filterNutritionalHistoryGrowthRemoved(data, variables)).toBeTruthy();
  });
  it('filterNutritionalHistoryGrowthRemoved() returns false if profileId dont match', () => {
    data = { NutritionalHistoryGrowthRemoved: { profileId: '2' } };
    variables = { profileId: '1' };
    expect(filterNutritionalHistoryGrowthRemoved(data, variables)).toBeFalsy();
  });

  it('filterDevelopmentalHistoryAdded() returns true if profileId matches', () => {
    data = { DevelopmentalHistoryAdded: { profileId: '1' } };
    variables = { profileId: '1' };
    expect(filterDevelopmentalHistoryAdded(data, variables)).toBeTruthy();
  });
  it('filterDevelopmentalHistoryAdded() returns false if profileId dont match', () => {
    data = { DevelopmentalHistoryAdded: { profileId: '2' } };
    variables = { profileId: '1' };
    expect(filterDevelopmentalHistoryAdded(data, variables)).toBeFalsy();
  });
  it('filterDevelopmentalHistoryUpdated() returns true if profileId matches', () => {
    data = { DevelopmentalHistoryUpdated: { profileId: '1' } };
    variables = { profileId: '1' };
    expect(filterDevelopmentalHistoryUpdated(data, variables)).toBeTruthy();
  });
  it('filterDevelopmentalHistoryUpdated() returns false if profileId dont match', () => {
    data = { DevelopmentalHistoryUpdated: { profileId: '2' } };
    variables = { profileId: '1' };
    expect(filterDevelopmentalHistoryUpdated(data, variables)).toBeFalsy();
  });
  it('filterDevelopmentalHistoryRemoved() returns true if profileId matches', () => {
    data = { DevelopmentalHistoryRemoved: { profileId: '1' } };
    variables = { profileId: '1' };
    expect(filterDevelopmentalHistoryRemoved(data, variables)).toBeTruthy();
  });
  it('filterDevelopmentalHistoryRemoved() returns false if profileId dont match', () => {
    data = { DevelopmentalHistoryRemoved: { profileId: '2' } };
    variables = { profileId: '1' };
    expect(filterDevelopmentalHistoryRemoved(data, variables)).toBeFalsy();
  });

  it('byProfileIdsOrHospitalId: condition matches', () => {
    data = { profileIds: ['1', '2'] };
    variables = { profileId: '1' };
    expect(byProfileIdsOrHospitalId(data, variables)).toBeTruthy();

    data = { hospitalId: '3' };
    variables = { hospitalId: '3' };
    expect(byProfileIdsOrHospitalId(data, variables)).toBeTruthy();
  });

  it('byProfileIdsOrHospitalId: condition does not match', () => {
    data = { profileIds: ['1', '2'] };
    variables = { profileId: '3' };
    expect(byProfileIdsOrHospitalId(data, variables)).toBeFalsy();

    data = { hospitalId: '3' };
    variables = { hospitalId: '1' };
    expect(byProfileIdsOrHospitalId(data, variables)).toBeFalsy();
  });

  it('byRecordId: condition matches', () => {
    data = { id: '1' };
    variables = { recordId: '1' };
    expect(byRecordId(data, variables)).toBeTruthy();
  });

  it('byRecordId: condition does not match', () => {
    data = { id: '1' };
    variables = { recordId: '3' };
    expect(byRecordId(data, variables)).toBeFalsy();

    data = null;
    variables = { recordId: '3' };
    expect(byRecordId(data, variables)).toBeFalsy();

    variables = null;
    data = { id: '3' };
    expect(byRecordId(data, variables)).toBeFalsy();
  });

  it('byRecordIds: condition matches', () => {
    variables = { recordId: '1' };
    data = { ids: ['1', '2'] };
    expect(byRecordIds(data, variables)).toBeTruthy();
  });

  it('byRecordIds: condition does not match', () => {
    variables = { recordId: '3' };
    data = { ids: ['1', '2'] };
    expect(byRecordIds(data, variables)).toBeFalsy();
  });

  it('byDirectClinifyId: condition matches', () => {
    data = 'A';
    variables = { clinifyId: 'A' };
    expect(byDirectClinifyId(data, variables)).toBeTruthy();

    data = ['A'];
    variables = { clinifyId: 'A' };
    expect(byDirectClinifyId(data, variables)).toBeTruthy();
  });

  it('byDirectClinifyId: condition does not match', () => {
    data = 'A';
    variables = { clinifyId: 'B' };
    expect(byDirectClinifyId(data, variables)).toBeFalsy();

    data = ['A'];
    variables = { clinifyId: 'B' };
    expect(byDirectClinifyId(data, variables)).toBeFalsy();
  });

  it('byDirectProfileId: condition matches', () => {
    data = 'A';
    variables = { profileId: 'A' };
    expect(byDirectProfileId(data, variables)).toBeTruthy();

    data = ['A'];
    variables = { profileId: 'A' };
    expect(byDirectProfileId(data, variables)).toBeTruthy();
  });

  it('byDirectProfileId: condition does not match', () => {
    data = 'A';
    variables = { profileId: 'B' };
    expect(byDirectProfileId(data, variables)).toBeFalsy();

    data = ['A'];
    variables = { profileId: 'B' };
    expect(byDirectProfileId(data, variables)).toBeFalsy();
  });

  it('filterHandoverNoteAdded if condition matches', () => {
    data = {
      HandoverNoteAdded: {
        hospitalId: '1',
      },
    };
    variables = { userType: 'OrganizationAdmin', hospitalId: '1' };
    expect(filterHandoverNoteAdded(data, variables)).toBeTruthy();
  });

  it('filterHandoverNoteAdded if condition does not match', () => {
    data = {
      HandoverNoteAdded: {
        hospitalId: '2',
      },
    };
    variables = { userType: 'OrganizationAdmin', hospitalId: '1' };
    expect(filterHandoverNoteAdded(data, variables)).toBeFalsy();
  });

  it('filterHandoverNoteUpdated if condition matches', () => {
    data = {
      HandoverNoteUpdated: {
        hospitalId: '1',
      },
    };
    variables = { userType: 'OrganizationAdmin', hospitalId: '1' };
    expect(filterHandoverNoteUpdated(data, variables)).toBeTruthy();
  });

  it('filterHandoverNoteUpdated if condition does not match', () => {
    data = {
      HandoverNoteUpdated: {
        hospitalId: '3',
      },
    };
    variables = { userType: 'OrganizationAdmin', hospitalId: '1' };
    expect(filterHandoverNoteUpdated(data, variables)).toBeFalsy();
  });

  it('filterHandoverNoteRemoved if condition matches', () => {
    data = {
      HandoverNoteRemoved: {
        hospitalId: '1',
      },
    };
    variables = { userType: 'OrganizationAdmin', hospitalId: '1' };
    expect(filterHandoverNoteRemoved(data, variables)).toBeTruthy();
  });

  it('filterHandoverNoteRemoved if condition does not match', () => {
    data = {
      HandoverNoteRemoved: {
        hospitalId: '3',
      },
    };
    variables = { userType: 'OrganizationAdmin', hospitalId: '1' };
    expect(filterHandoverNoteRemoved(data, variables)).toBeFalsy();
  });

  it('filterHandoverNoteArchived if condition matches', () => {
    data = {
      HandoverNoteArchived: {
        hospitalId: '1',
      },
    };
    variables = { userType: 'OrganizationAdmin', hospitalId: '1' };
    expect(filterHandoverNoteArchived(data, variables)).toBeTruthy();
  });

  it('filterHandoverNoteArchived if condition does not match', () => {
    data = {
      HandoverNoteArchived: {
        hospitalId: '3',
      },
    };
    variables = { userType: 'OrganizationAdmin', hospitalId: '1' };
    expect(filterHandoverNoteArchived(data, variables)).toBeFalsy();
  });

  it('filterHandoverNoteUnarchived if condition matches', () => {
    data = {
      HandoverNoteUnarchived: {
        hospitalId: '1',
      },
    };
    variables = { userType: 'OrganizationAdmin', hospitalId: '1' };
    expect(filterHandoverNoteUnarchived(data, variables)).toBeTruthy();
  });

  it('filterHandoverNoteUnarchived if condition does not match', () => {
    data = {
      HandoverNoteUnarchived: {
        hospitalId: '3',
      },
    };
    variables = { userType: 'OrganizationAdmin', hospitalId: '1' };
    expect(filterHandoverNoteUnarchived(data, variables)).toBeFalsy();
  });

  it('filterHandoverNoteItemAdded if condition matches', () => {
    data = {
      HandoverNoteItemAdded: {
        hospitalId: '1',
      },
    };
    variables = { userType: 'OrganizationAdmin', hospitalId: '1' };
    expect(filterHandoverNoteItemAdded(data, variables)).toBeTruthy();
  });

  it('filterHandoverNoteItemAdded if condition does not match', () => {
    data = {
      HandoverNoteItemAdded: {
        hospitalId: '3',
      },
    };
    variables = { userType: 'OrganizationAdmin', hospitalId: '1' };
    expect(filterHandoverNoteItemAdded(data, variables)).toBeFalsy();
  });

  it('filterHandoverNoteItemUpdated if condition matches', () => {
    data = {
      HandoverNoteItemUpdated: {
        hospitalId: '1',
      },
    };
    variables = { userType: 'OrganizationAdmin', hospitalId: '1' };
    expect(filterHandoverNoteItemUpdated(data, variables)).toBeTruthy();
  });

  it('filterHandoverNoteItemUpdated if condition does not match', () => {
    data = {
      HandoverNoteItemUpdated: {
        hospitalId: '3',
      },
    };
    variables = { userType: 'OrganizationAdmin', hospitalId: '1' };
    expect(filterHandoverNoteItemUpdated(data, variables)).toBeFalsy();
  });

  it('filterHandoverNoteItemRemoved if condition matches', () => {
    data = {
      HandoverNoteItemRemoved: {
        hospitalId: '1',
      },
    };
    variables = { userType: 'OrganizationAdmin', hospitalId: '1' };
    expect(filterHandoverNoteItemRemoved(data, variables)).toBeTruthy();
  });

  it('filterHandoverNoteItemRemoved if condition does not match', () => {
    data = {
      HandoverNoteItemRemoved: {
        hospitalId: '3',
      },
    };
    variables = { userType: 'OrganizationAdmin', hospitalId: '1' };
    expect(filterHandoverNoteItemRemoved(data, variables)).toBeFalsy();
  });

  it('filterHandoverAdditionalNoteAdded if condition matches', () => {
    data = {
      HandoverAdditionalNoteAdded: {
        hospitalId: '1',
      },
    };
    variables = { userType: 'OrganizationAdmin', hospitalId: '1' };
    expect(filterHandoverAdditionalNoteAdded(data, variables)).toBeTruthy();
  });

  it('filterHandoverAdditionalNoteAdded if condition does not match', () => {
    data = {
      HandoverAdditionalNoteAdded: {
        hospitalId: '3',
      },
    };
    variables = { userType: 'OrganizationAdmin', hospitalId: '1' };
    expect(filterHandoverAdditionalNoteAdded(data, variables)).toBeFalsy();
  });

  it('filterHandoverAdditionalNoteUpdated if condition matches', () => {
    data = {
      HandoverAdditionalNoteUpdated: {
        hospitalId: '1',
      },
    };
    variables = { userType: 'OrganizationAdmin', hospitalId: '1' };
    expect(filterHandoverAdditionalNoteUpdated(data, variables)).toBeTruthy();
  });

  it('filterHandoverAdditionalNoteUpdated if condition does not match', () => {
    data = {
      HandoverAdditionalNoteUpdated: {
        hospitalId: '3',
      },
    };
    variables = { userType: 'OrganizationAdmin', hospitalId: '1' };
    expect(filterHandoverAdditionalNoteUpdated(data, variables)).toBeFalsy();
  });

  it('filterHandoverAdditionalNoteRemoved if condition matches', () => {
    data = {
      HandoverAdditionalNoteRemoved: {
        hospitalId: '1',
      },
    };
    variables = { userType: 'OrganizationAdmin', hospitalId: '1' };
    expect(filterHandoverAdditionalNoteRemoved(data, variables)).toBeTruthy();
  });

  it('filterHandoverAdditionalNoteRemoved if condition does not match', () => {
    data = {
      HandoverAdditionalNoteRemoved: {
        hospitalId: '3',
      },
    };
    variables = { userType: 'OrganizationAdmin', hospitalId: '1' };
    expect(filterHandoverAdditionalNoteRemoved(data, variables)).toBeFalsy();
  });

  it('filterHandoverStaffAdded if condition matches', () => {
    data = {
      HandoverStaffAdded: {
        hospitalId: '1',
      },
    };
    variables = { userType: 'OrganizationAdmin', hospitalId: '1' };
    expect(filterHandoverStaffAdded(data, variables)).toBeTruthy();
  });

  it('filterHandoverStaffAdded if condition does not match', () => {
    data = {
      HandoverStaffAdded: {
        hospitalId: '3',
      },
    };
    variables = { userType: 'OrganizationAdmin', hospitalId: '1' };
    expect(filterHandoverStaffAdded(data, variables)).toBeFalsy();
  });

  it('filterHandoverStaffUpdated if condition matches', () => {
    data = {
      HandoverStaffUpdated: {
        hospitalId: '1',
      },
    };
    variables = { userType: 'OrganizationAdmin', hospitalId: '1' };
    expect(filterHandoverStaffUpdated(data, variables)).toBeTruthy();
  });

  it('filterHandoverStaffUpdated if condition does not match', () => {
    data = {
      HandoverStaffUpdated: {
        hospitalId: '3',
      },
    };
    variables = { userType: 'OrganizationAdmin', hospitalId: '1' };
    expect(filterHandoverStaffUpdated(data, variables)).toBeFalsy();
  });

  it('filterHandoverStaffRemoved if condition matches', () => {
    data = {
      HandoverStaffRemoved: {
        hospitalId: '1',
      },
    };
    variables = { userType: 'OrganizationAdmin', hospitalId: '1' };
    expect(filterHandoverStaffRemoved(data, variables)).toBeTruthy();
  });

  it('filterHandoverStaffRemoved if condition does not match', () => {
    data = {
      HandoverStaffRemoved: {
        hospitalId: '3',
      },
    };
    variables = { userType: 'OrganizationAdmin', hospitalId: '1' };
    expect(filterHandoverStaffRemoved(data, variables)).toBeFalsy();
  });

  it('filterWalletBalance if condition matches', () => {
    data = {
      WalletBalance: {
        id: '1',
      },
    };
    variables = { walletId: '1' };
    expect(filterWalletBalance(data, variables)).toBeTruthy();
  });

  it('filterWalletBalance if condition does not match', () => {
    data = {
      WalletBalance: {
        id: '1',
      },
    };
    variables = { walletId: '2' };
    expect(filterWalletBalance(data, variables)).toBeFalsy();
  });

  it('filterNursingServiceDetailsAdded if condition matches', () => {
    data = {
      NursingServiceDetailsAdded: {
        profileId: '1',
      },
    };
    variables = { profileId: '1' };
    expect(filterNursingServiceDetailsAdded(data, variables)).toBeTruthy();
  });

  it('filterNursingServiceDetailsAdded if condition does not match', () => {
    data = {
      NursingServiceDetailsAdded: {
        profileId: '3',
      },
    };
    variables = { profileId: '2' };
    expect(filterNursingServiceDetailsAdded(data, variables)).toBeFalsy();
  });

  it('filterNursingServiceDetailsUpdated if condition matches', () => {
    data = {
      NursingServiceDetailsUpdated: {
        profileId: '1',
      },
    };
    variables = { profileId: '1' };
    expect(filterNursingServiceDetailsUpdated(data, variables)).toBeTruthy();
  });

  it('filterNursingServiceDetailsUpdated if condition does not match', () => {
    data = {
      NursingServiceDetailsUpdated: {
        hospitalId: '3',
      },
    };
    variables = { hospitalId: '1', profileId: '2' };
    expect(filterNursingServiceDetailsUpdated(data, variables)).toBeFalsy();
  });

  it('filterNursingServiceDetailsRemoved if condition matches', () => {
    data = {
      NursingServiceDetailsRemoved: {
        profileId: '1',
      },
    };
    variables = { profileId: '1' };
    expect(filterNursingServiceDetailsRemoved(data, variables)).toBeTruthy();
  });

  it('filterNursingServiceDetailsRemoved if condition does not match', () => {
    data = {
      NursingServiceDetailsRemoved: {
        hospitalId: '3',
      },
    };
    variables = { hospitalId: '1', profileId: '2' };
    expect(filterNursingServiceDetailsRemoved(data, variables)).toBeFalsy();
  });

  it('filterPayInvoiceWithWallet: if condition matches', () => {
    data = {
      PayInvoiceWithWallet: {
        profileId: '1',
      },
    };
    variables = { profileId: '1' };
    expect(filterPayInvoiceWithWallet(data, variables)).toBeTruthy();
  });

  it('filterPayInvoiceWithWallet: if condition does not match', () => {
    data = {
      PayInvoiceWithWallet: {
        profileId: '2',
      },
    };
    variables = { profileId: '1' };
    expect(filterPayInvoiceWithWallet(data, variables)).toBeFalsy();
  });

  it('filterPayoutAdded: if condition matches', () => {
    data = {
      PayoutAdded: {
        hospitalId: '1',
      },
    };
    variables = { hospitalId: '1' };
    expect(filterPayoutAdded(data, variables)).toBeTruthy();
  });

  it('filterPayoutAdded: if condition does not match', () => {
    data = {
      PayoutAdded: {
        hospitalId: '2',
      },
    };
    variables = { hospitalId: '1' };
    expect(filterPayoutAdded(data, variables)).toBeFalsy();
  });

  it('filterPaymentDepositedAdded: if condition matches', () => {
    data = {
      PaymentDepositAdded: {
        hospitalId: '1',
        profileId: '2',
      },
    };
    variables = { hospitalId: '1', profileId: '2' };
    expect(filterPaymentDepositAdded(data, variables)).toBeTruthy();
  });

  it('filterPaymentDepositedAdded: if condition does not match', () => {
    data = {
      PaymentDepositAdded: {
        hospitalId: '1',
        profileId: '2',
      },
    };
    variables = { hospitalId: '1', profileId: '3' };
    expect(filterPaymentDepositAdded(data, variables)).toBeFalsy();
  });

  it('filterPaymentDepositRemoved', () => {
    data = {
      PaymentDepositRemoved: {
        hospitalId: '1',
        profileId: '2',
      },
    };
    variables = { hospitalId: '1', profileId: '2' };
    expect(filterPaymentDepositRemoved(data, variables)).toBeTruthy();
  });

  it('filterPaymentDepositRemoved', () => {
    data = {
      PaymentDepositRemoved: {
        hospitalId: '1',
        profileId: '2',
      },
    };
    variables = { hospitalId: '1', profileId: '23' };
    expect(filterPaymentDepositRemoved(data, variables)).toBeFalsy();
  });

  it('filterMedicalReportAdded', () => {
    data = {
      MedicalReportAdded: {
        hospitalId: '1',
      },
    };
    variables = { hospitalId: '1' };
    expect(filterMedicalReportAdded(data, variables)).toBeTruthy();
  });

  it('filterMedicalReportUpdated', () => {
    data = {
      MedicalReportUpdated: {
        hospitalId: '1',
      },
    };
    variables = { hospitalId: '1' };
    expect(filterMedicalReportUpdated(data, variables)).toBeTruthy();
  });

  it('filterMedicalReportRemoved', () => {
    data = {
      MedicalReportRemoved: {
        hospitalId: '1',
      },
    };
    variables = { hospitalId: '1' };
    expect(filterMedicalReportRemoved(data, variables)).toBeTruthy();
  });

  it('filterMedicalReportArchived', () => {
    data = {
      MedicalReportArchived: {
        hospitalId: '1',
      },
    };
    variables = { hospitalId: '1' };
    expect(filterMedicalReportArchived(data, variables)).toBeTruthy();
  });

  it('filterMedicalReportEvent', () => {
    data = {
      MedicalReportEvent: {
        hospitalId: '1',
      },
    };
    variables = { hospitalId: '1' };
    expect(filterMedicalReportEvent(data, variables)).toBeTruthy();
  });

  it('filterVirtualCareAppointmentAdded', () => {
    data = {
      VirtualCareAppointmentAdded: {
        hospitalId: '1',
      },
    };
    variables = { hospitalId: '1' };
    expect(filterVirtualCareAppointmentAdded(data, variables)).toBeTruthy();
  });

  it('filterVirtualCareAppointmentAdded', () => {
    data = {
      VirtualCareAppointmentAdded: {
        hospitalId: '1',
        profileId: '2',
      },
    };
    variables = { hospitalId: '3' };
    expect(filterVirtualCareAppointmentAdded(data, variables)).toBeFalsy();
  });

  it('filterVirtualCareAppointmentUpdated', () => {
    data = {
      VirtualCareAppointmentUpdated: {
        hospitalId: '1',
      },
    };
    variables = { hospitalId: '1' };
    expect(filterVirtualCareAppointmentUpdated(data, variables)).toBeTruthy();
  });

  it('filterVirtualCareAppointmentUpdated', () => {
    data = {
      VirtualCareAppointmentUpdated: {
        hospitalId: '2',
        profileId: '3',
      },
    };
    variables = { hospitalId: '1' };
    expect(filterVirtualCareAppointmentUpdated(data, variables)).toBeFalsy();
  });

  it('filterVirtualCareAppointmentRemoved', () => {
    data = {
      VirtualCareAppointmentRemoved: {
        hospitalId: '1',
      },
    };
    variables = { hospitalId: '1' };
    expect(filterVirtualCareAppointmentRemoved(data, variables)).toBeTruthy();
  });

  it('filterVirtualCareAppointmentRemoved', () => {
    data = {
      VirtualCareAppointmentRemoved: {
        hospitalId: '11',
        profileId: '22',
      },
    };
    variables = { hospitalId: '1' };
    expect(filterVirtualCareAppointmentRemoved(data, variables)).toBeFalsy();
  });

  it('filterVirtualCareAppointmentArchived', () => {
    data = {
      VirtualCareAppointmentArchived: {
        hospitalId: '1',
      },
    };
    variables = { hospitalId: '1' };
    expect(filterVirtualCareAppointmentArchived(data, variables)).toBeTruthy();
  });

  it('filterVirtualCareAppointmentArchived', () => {
    data = {
      VirtualCareAppointmentArchived: {
        hospitalId: '1',
        profileId: '2',
      },
    };
    variables = { hospitalId: '21' };
    expect(filterVirtualCareAppointmentArchived(data, variables)).toBeFalsy();
  });

  it('filterVirtualCareAppointmentUnarchived', () => {
    data = {
      VirtualCareAppointmentUnarchived: {
        hospitalId: '1',
      },
    };
    variables = { hospitalId: '1' };
    expect(
      filterVirtualCareAppointmentUnarchived(data, variables),
    ).toBeTruthy();
  });

  it('filterVirtualCareAppointmentUnarchived', () => {
    data = {
      VirtualCareAppointmentUnarchived: {
        hospitalId: '1',
        profileId: '2',
      },
    };
    variables = { hospitalId: '101' };
    expect(filterVirtualCareAppointmentUnarchived(data, variables)).toBeFalsy();
  });

  it('filterPatientLookupResultUpdated to be truthy when requestId matches', () => {
    data = { requestId: '1' };
    variables = { requestId: '1' };
    expect(filterPatientLookupResultUpdated(data, variables)).toBeTruthy();
  });

  it('filterPatientLookupResultUpdated to be falsy when requestId does not match', () => {
    data = { requestId: '1' };
    variables = { requestId: '2' };
    expect(filterPatientLookupResultUpdated(data, variables)).toBeFalsy();
  });

  it('filterPackageEvent() returns true if hospitalId matches', () => {
    data = { package: { hospitalId: '1' } };
    variables = { hospitalId: '1' };
    expect(filterPackageEvent(data, variables)).toBeTruthy();
  });
  it('filterPackageEvent() returns false if hospitalId dont match', () => {
    data = { package: { hospitalId: '2' } };
    variables = { hospitalId: '1' };
    expect(filterPackageEvent(data, variables)).toBeFalsy();
  });
  it('filterPackageAdded() returns true if hospitalId matches', () => {
    data = { PackageAdded: { hospitalId: '1' } };
    variables = { hospitalId: '1' };
    expect(filterPackageAdded(data, variables)).toBeTruthy();
  });
  it('filterPackageAdded() returns false if hospitalId dont match', () => {
    data = { PackageAdded: { hospitalId: '2' } };
    variables = { hospitalId: '1' };
    expect(filterPackageAdded(data, variables)).toBeFalsy();
  });
  it('filterPackageUpdated() returns true if hospitalId matches', () => {
    data = { PackageUpdated: { hospitalId: '1' } };
    variables = { hospitalId: '1' };
    expect(filterPackageUpdated(data, variables)).toBeTruthy();
  });
  it('filterPackageUpdated() returns false if hospitalId dont match', () => {
    data = { PackageUpdated: { hospitalId: '2' } };
    variables = { hospitalId: '1' };
    expect(filterPackageUpdated(data, variables)).toBeFalsy();
  });
  it('filterPackageRemoved() returns true if hospitalId matches', () => {
    data = { PackageRemoved: [{ hospitalId: '1' }] };
    variables = { hospitalId: '1' };
    expect(filterPackageRemoved(data, variables)).toBeTruthy();
  });
  it('filterPackageRemoved() returns false if hospitalId dont match', () => {
    data = { PackageRemoved: [{ hospitalId: '2' }] };
    variables = { hospitalId: '1' };
    expect(filterPackageRemoved(data, variables)).toBeFalsy();
  });
  it('filterPackageArchived() returns true if hospitalId matches', () => {
    data = { PackageArchived: [{ hospitalId: '1' }] };
    variables = { hospitalId: '1' };
    expect(filterPackageArchived(data, variables)).toBeTruthy();
  });
  it('filterPackageArchived() returns false if hospitalId dont match', () => {
    data = { PackageArchived: [{ hospitalId: '2' }] };
    variables = { hospitalId: '1' };
    expect(filterPackageArchived(data, variables)).toBeFalsy();
  });
  it('filterPackageUnarchived() returns true if hospitalId matches', () => {
    data = { PackageUnarchived: [{ hospitalId: '1' }] };
    variables = { hospitalId: '1' };
    expect(filterPackageUnarchived(data, variables)).toBeTruthy();
  });
  it('filterPackageUnarchived() returns false if hospitalId dont match', () => {
    data = { PackageUnarchived: [{ hospitalId: '2' }] };
    variables = { hospitalId: '1' };
    expect(filterPackageUnarchived(data, variables)).toBeFalsy();
  });

  it('filterRequestPackageEvent() returns true if profileId matches', () => {
    data = { requestPackage: { profileId: '1' } };
    variables = { profileId: '1' };
    expect(filterRequestPackageEvent(data, variables)).toBeTruthy();
  });
  it('filterRequestPackageEvent() returns false if profileId dont match', () => {
    data = { requestPackage: { profileId: '2' } };
    variables = { profileId: '1' };
    expect(filterRequestPackageEvent(data, variables)).toBeFalsy();
  });
  it('filterRequestPackageAdded() returns true if profileId matches', () => {
    data = { RequestPackageAdded: { profileId: '1' } };
    variables = { profileId: '1' };
    expect(filterRequestPackageAdded(data, variables)).toBeTruthy();
  });
  it('filterRequestPackageAdded() returns false if profileId dont match', () => {
    data = { RequestPackageAdded: { profileId: '2' } };
    variables = { profileId: '1' };
    expect(filterRequestPackageAdded(data, variables)).toBeFalsy();
  });
  it('filterRequestPackageUpdated() returns true if profileId matches', () => {
    data = { RequestPackageUpdated: { profileId: '1' } };
    variables = { profileId: '1' };
    expect(filterRequestPackageUpdated(data, variables)).toBeTruthy();
  });
  it('filterRequestPackageUpdated() returns false if profileId dont match', () => {
    data = { RequestPackageUpdated: { profileId: '2' } };
    variables = { profileId: '1' };
    expect(filterRequestPackageUpdated(data, variables)).toBeFalsy();
  });
  it('filterRequestPackageRemoved() returns true if profileId matches', () => {
    data = { RequestPackageRemoved: [{ profileId: '1' }] };
    variables = { profileId: '1' };
    expect(filterRequestPackageRemoved(data, variables)).toBeTruthy();
  });
  it('filterRequestPackageRemoved() returns false if profileId dont match', () => {
    data = { RequestPackageRemoved: [{ profileId: '2' }] };
    variables = { profileId: '1' };
    expect(filterRequestPackageRemoved(data, variables)).toBeFalsy();
  });
  it('filterRequestPackageArchived() returns true if profileId matches', () => {
    data = { RequestPackageArchived: [{ profileId: '1' }] };
    variables = { profileId: '1' };
    expect(filterRequestPackageArchived(data, variables)).toBeTruthy();
  });
  it('filterRequestPackageArchived() returns false if profileId dont match', () => {
    data = { RequestPackageArchived: [{ profileId: '2' }] };
    variables = { profileId: '1' };
    expect(filterRequestPackageArchived(data, variables)).toBeFalsy();
  });
  it('filterRequestPackageUnarchived() returns true if profileId matches', () => {
    data = { RequestPackageUnarchived: [{ profileId: '1' }] };
    variables = { profileId: '1' };
    expect(filterRequestPackageUnarchived(data, variables)).toBeTruthy();
  });
  it('filterRequestPackageUnarchived() returns false if profileId dont match', () => {
    data = { RequestPackageUnarchived: [{ profileId: '2' }] };
    variables = { profileId: '1' };
    expect(filterRequestPackageUnarchived(data, variables)).toBeFalsy();
  });
  it('filterAllVitalsInserted returns true if profileId matches', () => {
    data = { AllVitalsInserted: { profileId: '1' } };
    variables = { profileId: '1' };
    expect(filterAllVitalsInserted(data, variables)).toBeTruthy();
  });
  it('filterAllVitalsInserted returns false if profileId does not match', () => {
    data = { AllVitalsInserted: { profileId: '2' } };
    variables = { profileId: '1' };
    expect(filterAllVitalsInserted(data, variables)).toBeFalsy();
  });

  it('filterAllVitalsUpdated returns true if profileId matches', () => {
    data = { AllVitalsUpdated: { profileId: '1' } };
    variables = { profileId: '1' };
    expect(filterAllVitalsUpdated(data, variables)).toBeTruthy();
  });
  it('filterAllVitalsUpdated returns false if profileId does not match', () => {
    data = { AllVitalsUpdated: { profileId: '2' } };
    variables = { profileId: '1' };
    expect(filterAllVitalsUpdated(data, variables)).toBeFalsy();
  });

  it('filterAllVitalsRemoved returns true if profileId matches', () => {
    data = { AllVitalsRemoved: { profileId: '1' } };
    variables = { profileId: '1' };
    expect(filterAllVitalsRemoved(data, variables)).toBeTruthy();
  });
  it('filterAllVitalsRemoved returns false if profileId does not match', () => {
    data = { AllVitalsRemoved: { profileId: '2' } };
    variables = { profileId: '1' };
    expect(filterAllVitalsRemoved(data, variables)).toBeFalsy();
  });

  it('filterImmunizationDetailsAdded(): returns false if neither hospital id nor profile id in data and variables do not match', () => {
    data = {
      ImmunizationDetailAdded: { immunization: { profileId: '1' } },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterImmunizationDetailsAdded(data, variables)).toBeFalsy();
  });

  it('filterImmunizationDetailsAdded(): returns true if either hospital id or profile id in data and variables match', () => {
    data = {
      ImmunizationDetailAdded: { immunization: { profileId: '1' } },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterImmunizationDetailsAdded(data, variables)).toBeTruthy();
  });

  it('filterImmunizationDetailsUpdated(): returns false if neither hospital id nor profile id in data and variables do not match', () => {
    data = {
      ImmunizationDetailUpdated: {
        immunization: { profileId: '1' },
      },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterImmunizationDetailsUpdated(data, variables)).toBeFalsy();
  });

  it('filterImmunizationDetailsUpdated(): returns true if either hospital id or profile id in data and variables match', () => {
    data = {
      ImmunizationDetailUpdated: {
        immunization: { profileId: '1' },
      },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterImmunizationDetailsUpdated(data, variables)).toBeTruthy();
  });

  it('filterImmunizationDetailsRemoved(): returns false if neither hospital id nor profile id in data and variables do not match', () => {
    data = {
      ImmunizationDetailRemoved: {
        immunization: { profileId: '1' },
      },
    };
    variables = { hospitalId: '2', profileId: '2' };
    expect(filterImmunizationDetailsRemoved(data, variables)).toBeFalsy();
  });

  it('filterImmunizationDetailsRemoved(): returns true if either hospital id or profile id in data and variables match', () => {
    data = {
      ImmunizationDetailRemoved: {
        immunization: { profileId: '1' },
      },
    };
    variables = { hospitalId: '2', profileId: '1' };
    expect(filterImmunizationDetailsRemoved(data, variables)).toBeTruthy();
  });

  it('filterOncologyConsultationAdded() returns true if profile id in data and variables match', () => {
    data = {
      OncologyConsultationAdded: { profileId: '1' },
    };
    variables = { profileId: '1' };
    expect(filterOncologyConsultationAdded(data, variables)).toBeTruthy();
  });
  it('filterOncologyConsultationAdded() returns false if profile id in data and variables do not match', () => {
    data = {
      OncologyConsultationAdded: { profileId: '2' },
    };
    variables = { profileId: '1' };
    expect(filterOncologyConsultationAdded(data, variables)).toBeFalsy();
  });

  it('filterOncologyConsultationUpdated() returns true if profile id in data and variables match', () => {
    data = {
      OncologyConsultationUpdated: { profileId: '1' },
    };
    variables = { profileId: '1' };
    expect(filterOncologyConsultationUpdated(data, variables)).toBeTruthy();
  });
  it('filterOncologyConsultationUpdated() returns false if profile id in data and variables do not match', () => {
    data = {
      OncologyConsultationUpdated: { profileId: '2' },
    };
    variables = { profileId: '1' };
    expect(filterOncologyConsultationUpdated(data, variables)).toBeFalsy();
  });

  it('filterOncologyConsultationRemoved() returns true if profile id in data and variables match', () => {
    data = {
      OncologyConsultationRemoved: [{ profileId: '1' }],
    };
    variables = { profileId: '1' };
    expect(filterOncologyConsultationRemoved(data, variables)).toBeTruthy();
  });
  it('filterOncologyConsultationRemoved() returns false if profile id in data and variables do not match', () => {
    data = {
      OncologyConsultationRemoved: [{ profileId: '2' }],
    };
    variables = { profileId: '1' };
    expect(filterOncologyConsultationRemoved(data, variables)).toBeFalsy();
  });

  it('filterOncologyConsultationArchived() returns true if profile id in data and variables match', () => {
    data = {
      OncologyConsultationArchived: [{ profileId: '1' }],
    };
    variables = { profileId: '1' };
    expect(filterOncologyConsultationArchived(data, variables)).toBeTruthy();
  });
  it('filterOncologyConsultationArchived() returns false if profile id in data and variables do not match', () => {
    data = {
      OncologyConsultationArchived: [{ profileId: '2' }],
    };
    variables = { profileId: '1' };
    expect(filterOncologyConsultationArchived(data, variables)).toBeFalsy();
  });

  it('filterOncologyConsultationUnarchived() returns true if profile id in data and variables match', () => {
    data = {
      OncologyConsultationUnarchived: [{ profileId: '1' }],
    };
    variables = { profileId: '1' };
    expect(filterOncologyConsultationUnarchived(data, variables)).toBeTruthy();
  });
  it('filterOncologyConsultationUnarchived() returns false if profile id in data and variables do not match', () => {
    data = {
      OncologyConsultationUnarchived: [{ profileId: '2' }],
    };
    variables = { profileId: '1' };
    expect(filterOncologyConsultationUnarchived(data, variables)).toBeFalsy();
  });

  it('filterOncologyConsultationChartUpdated() returns true if profile id in data and variables match', () => {
    data = {
      OncologyConsultationChartUpdated: { oncologyHistory: { profileId: '1' } },
    };
    variables = { profileId: '1' };
    expect(
      filterOncologyConsultationChartUpdated(data, variables),
    ).toBeTruthy();
  });
  it('filterOncologyConsultationChartUpdated() returns false if profile id in data and variables do not match', () => {
    data = {
      OncologyConsultationChartUpdated: { oncologyHistory: { profileId: '2' } },
    };
    variables = { profileId: '1' };
    expect(filterOncologyConsultationChartUpdated(data, variables)).toBeFalsy();
  });

  it('filterChemoDrugUpdated(): returns true if profile id in data and variables match', () => {
    data = { profileId: '1' };
    variables = { profileId: '1' };
    expect(filterChemoDrugUpdated(data, variables)).toBeTruthy();
  });
  it('filterChemoDrugUpdated(): returns false if profile id in data and variables do not match', () => {
    data = { profileId: '2' };
    variables = { profileId: '1' };
    expect(filterChemoDrugUpdated(data, variables)).toBeFalsy();
  });

  it('filterRequestProcedureAdded() returns true if profile id in data and variables match', () => {
    data = {
      RequestProcedureAdded: { profileId: '1' },
    };
    variables = { profileId: '1' };
    expect(filterRequestProcedureAdded(data, variables)).toBeTruthy();
  });
  it('filterRequestProcedureAdded() returns false if profile id in data and variables do not match', () => {
    data = {
      RequestProcedureAdded: { profileId: '2' },
    };
    variables = { profileId: '1' };
    expect(filterRequestProcedureAdded(data, variables)).toBeFalsy();
  });

  it('filterRequestProcedureUpdated() returns true if profile id in data and variables match', () => {
    data = {
      RequestProcedureUpdated: { profileId: '1' },
    };
    variables = { profileId: '1' };
    expect(filterRequestProcedureUpdated(data, variables)).toBeTruthy();
  });
  it('filterRequestProcedureUpdated() returns false if profile id in data and variables do not match', () => {
    data = {
      RequestProcedureUpdated: { profileId: '2' },
    };
    variables = { profileId: '1' };
    expect(filterRequestProcedureUpdated(data, variables)).toBeFalsy();
  });

  it('filterRequestProcedureRemoved() returns true if profile id in data and variables match', () => {
    data = {
      RequestProcedureRemoved: [{ profileId: '1' }],
    };
    variables = { profileId: '1' };
    expect(filterRequestProcedureRemoved(data, variables)).toBeTruthy();
  });
  it('filterRequestProcedureRemoved() returns false if profile id in data and variables do not match', () => {
    data = {
      RequestProcedureRemoved: [{ profileId: '2' }],
    };
    variables = { profileId: '1' };
    expect(filterRequestProcedureRemoved(data, variables)).toBeFalsy();
  });

  it('filterRequestProcedureArchived() returns true if profile id in data and variables match', () => {
    data = {
      RequestProcedureArchived: [{ profileId: '1' }],
    };
    variables = { profileId: '1' };
    expect(filterRequestProcedureArchived(data, variables)).toBeTruthy();
  });
  it('filterRequestProcedureArchived() returns false if profile id in data and variables do not match', () => {
    data = {
      RequestProcedureArchived: [{ profileId: '2' }],
    };
    variables = { profileId: '1' };
    expect(filterRequestProcedureArchived(data, variables)).toBeFalsy();
  });

  it('filterRequestProcedureUnarchived() returns true if profile id in data and variables match', () => {
    data = {
      RequestProcedureUnarchived: [{ profileId: '1' }],
    };
    variables = { profileId: '1' };
    expect(filterRequestProcedureUnarchived(data, variables)).toBeTruthy();
  });
  it('filterRequestProcedureUnarchived() returns false if profile id in data and variables do not match', () => {
    data = {
      RequestProcedureUnarchived: [{ profileId: '2' }],
    };
    variables = { profileId: '1' };
    expect(filterRequestProcedureUnarchived(data, variables)).toBeFalsy();
  });

  it('filterWalkInTransferEvent(): returns false if hospital id in data and variables do not match', () => {
    data = {
      walkInTransfer: {
        hospitalIds: ['1'],
      },
    };
    variables = { hospitalId: '2' };
    expect(filterWalkInTransferEvent(data, variables)).toBeFalsy();
  });
  it('filterWalkInTransferEvent(): returns true if hospital id in data and variables do not match', () => {
    data = {
      walkInTransfer: {
        hospitalIds: ['1'],
      },
    };
    variables = { hospitalId: '1' };
    expect(filterWalkInTransferEvent(data, variables)).toBeTruthy();
  });

  it('filterWalkInTransferAdded() returns true if hospital id in data and variables match', () => {
    data = {
      walkInTransfer: {
        hospitalIds: ['1'],
      },
    };
    variables = { hospitalId: '1' };
    expect(filterWalkInTransferAdded(data, variables)).toBeTruthy();
  });
  it('filterWalkInTransferAdded() returns false if hospital id in data and variables do not match', () => {
    data = {
      walkInTransfer: {
        hospitalIds: ['2'],
      },
    };
    variables = { hospitalId: '1' };
    expect(filterWalkInTransferAdded(data, variables)).toBeFalsy();
  });

  it('filterWalkInTransferUpdated() returns true if hospital id in data and variables match', () => {
    data = {
      walkInTransfer: {
        hospitalIds: ['4'],
      },
    };
    variables = { hospitalId: '4' };
    expect(filterWalkInTransferUpdated(data, variables)).toBeTruthy();
  });
  it('filterWalkInTransferUpdated() returns false if hospital id in data and variables do not match', () => {
    data = {
      walkInTransfer: {
        hospitalIds: ['2'],
      },
    };
    variables = { hospitalId: '3' };
    expect(filterWalkInTransferUpdated(data, variables)).toBeFalsy();
  });

  it('filterWalkInTransferRemoved() returns true if hospital id in data and variables match', () => {
    data = {
      walkInTransfer: {
        hospitalIds: ['54'],
      },
    };
    variables = { hospitalId: '54' };
    expect(filterWalkInTransferRemoved(data, variables)).toBeTruthy();
  });
  it('filterWalkInTransferRemoved() returns false if hospital id in data and variables do not match', () => {
    data = {
      walkInTransfer: {
        hospitalIds: ['16'],
      },
    };
    variables = { hospitalId: '13' };
    expect(filterWalkInTransferRemoved(data, variables)).toBeFalsy();
  });

  it('filterWalkInTransferArchived() returns true if hospital id in data and variables match', () => {
    data = {
      walkInTransfer: {
        hospitalIds: ['113'],
      },
    };
    variables = { hospitalId: '113' };
    expect(filterWalkInTransferArchived(data, variables)).toBeTruthy();
  });
  it('filterWalkInTransferArchived() returns false if hospital id in data and variables do not match', () => {
    data = {
      walkInTransfer: {
        hospitalIds: ['131'],
      },
    };
    variables = { hospitalId: '112' };
    expect(filterWalkInTransferArchived(data, variables)).toBeFalsy();
  });

  it('filterWalkInTransferUnarchived() returns true if hospital id in data and variables match', () => {
    data = {
      walkInTransfer: {
        hospitalIds: ['12'],
      },
    };
    variables = { hospitalId: '12' };
    expect(filterWalkInTransferUnarchived(data, variables)).toBeTruthy();
  });
  it('filterWalkInTransferUnarchived() returns false if hospital id in data and variables do not match', () => {
    data = {
      walkInTransfer: {
        hospitalIds: ['1'],
      },
    };
    variables = { hospitalId: '14' };
    expect(filterWalkInTransferUnarchived(data, variables)).toBeFalsy();
  });

  it('filterWalkInReferralEvent(): returns false if hospital id in data and variables do not match', () => {
    data = {
      walkInReferral: {
        hospitalIds: ['1'],
      },
    };
    variables = { hospitalId: '2' };
    expect(filterWalkInReferralEvent(data, variables)).toBeFalsy();
  });
  it('filterWalkInReferralEvent(): returns true if hospital id in data and variables do not match', () => {
    data = {
      walkInReferral: {
        hospitalIds: ['1'],
      },
    };
    variables = { hospitalId: '1' };
    expect(filterWalkInReferralEvent(data, variables)).toBeTruthy();
  });

  it('filterWalkInReferralAdded() returns true if hospital id in data and variables match', () => {
    data = {
      walkInReferral: {
        hospitalIds: ['1'],
      },
    };
    variables = { hospitalId: '1' };
    expect(filterWalkInReferralAdded(data, variables)).toBeTruthy();
  });
  it('filterWalkInReferralAdded() returns false if hospital id in data and variables do not match', () => {
    data = {
      walkInReferral: {
        hospitalIds: ['2'],
      },
    };
    variables = { hospitalId: '1' };
    expect(filterWalkInReferralAdded(data, variables)).toBeFalsy();
  });

  it('filterWalkInReferralUpdated() returns true if hospital id in data and variables match', () => {
    data = {
      walkInReferral: {
        hospitalIds: ['4'],
      },
    };
    variables = { hospitalId: '4' };
    expect(filterWalkInReferralUpdated(data, variables)).toBeTruthy();
  });
  it('filterWalkInReferralUpdated() returns false if hospital id in data and variables do not match', () => {
    data = {
      walkInReferral: {
        hospitalIds: ['2'],
      },
    };
    variables = { hospitalId: '3' };
    expect(filterWalkInReferralUpdated(data, variables)).toBeFalsy();
  });

  it('filterWalkInReferralRemoved() returns true if hospital id in data and variables match', () => {
    data = {
      walkInReferral: {
        hospitalIds: ['54'],
      },
    };
    variables = { hospitalId: '54' };
    expect(filterWalkInReferralRemoved(data, variables)).toBeTruthy();
  });
  it('filterWalkInReferralRemoved() returns false if hospital id in data and variables do not match', () => {
    data = {
      walkInReferral: {
        hospitalIds: ['16'],
      },
    };
    variables = { hospitalId: '13' };
    expect(filterWalkInReferralRemoved(data, variables)).toBeFalsy();
  });

  it('filterWalkInReferralArchived() returns true if hospital id in data and variables match', () => {
    data = {
      walkInReferral: {
        hospitalIds: ['113'],
      },
    };
    variables = { hospitalId: '113' };
    expect(filterWalkInReferralArchived(data, variables)).toBeTruthy();
  });
  it('filterWalkInReferralArchived() returns false if hospital id in data and variables do not match', () => {
    data = {
      walkInReferral: {
        hospitalIds: ['131'],
      },
    };
    variables = { hospitalId: '112' };
    expect(filterWalkInReferralArchived(data, variables)).toBeFalsy();
  });

  it('filterWalkInReferralUnarchived() returns true if hospital id in data and variables match', () => {
    data = {
      walkInReferral: {
        hospitalIds: ['12'],
      },
    };
    variables = { hospitalId: '12' };
    expect(filterWalkInReferralUnarchived(data, variables)).toBeTruthy();
  });
  it('filterWalkInReferralUnarchived() returns false if hospital id in data and variables do not match', () => {
    data = {
      walkInReferral: {
        hospitalIds: ['1'],
      },
    };
    variables = { hospitalId: '14' };
    expect(filterWalkInReferralUnarchived(data, variables)).toBeFalsy();
  });
  it('filterPreChemoEducationAdded() returns true if profileId matches', () => {
    data = { PreChemoEducationAdded: { profileId: '1' } };
    variables = { profileId: '1' };
    expect(filterPreChemoEducationAdded(data, variables)).toBeTruthy();
  });
  it('filterPreChemoEducationAdded() returns false if profileId dont match', () => {
    data = { PreChemoEducationAdded: { profileId: '2' } };
    variables = { profileId: '1' };
    expect(filterPreChemoEducationAdded(data, variables)).toBeFalsy();
  });
  it('filterPreChemoEducationUpdated() returns true if profileId matches', () => {
    data = { PreChemoEducationUpdated: { profileId: '1' } };
    variables = { profileId: '1' };
    expect(filterPreChemoEducationUpdated(data, variables)).toBeTruthy();
  });
  it('filterPreChemoEducationUpdated() returns false if profileId dont match', () => {
    data = { PreChemoEducationUpdated: { profileId: '2' } };
    variables = { profileId: '1' };
    expect(filterPreChemoEducationUpdated(data, variables)).toBeFalsy();
  });
  it('filterPreChemoEducationRemoved() returns true if profileId matches', () => {
    data = { PreChemoEducationRemoved: [{ profileId: '1' }] };
    variables = { profileId: '1' };
    expect(filterPreChemoEducationRemoved(data, variables)).toBeTruthy();
  });
  it('filterPreChemoEducationRemoved() returns false if profileId dont match', () => {
    data = { PreChemoEducationRemoved: [{ profileId: '2' }] };
    variables = { profileId: '1' };
    expect(filterPreChemoEducationRemoved(data, variables)).toBeFalsy();
  });
  it('filterCancerScreeningAdded() returns true if profileId matches', () => {
    data = { CancerScreeningAdded: { profileId: '1' } };
    variables = { profileId: '1' };
    expect(filterCancerScreeningAdded(data, variables)).toBeTruthy();
  });
  it('filterCancerScreeningAdded() returns false if profileId dont match', () => {
    data = { CancerScreeningAdded: { profileId: '2' } };
    variables = { profileId: '1' };
    expect(filterCancerScreeningAdded(data, variables)).toBeFalsy();
  });
  it('filterCancerScreeningUpdated() returns true if profileId matches', () => {
    data = { CancerScreeningUpdated: { profileId: '1' } };
    variables = { profileId: '1' };
    expect(filterCancerScreeningUpdated(data, variables)).toBeTruthy();
  });
  it('filterCancerScreeningUpdated() returns false if profileId dont match', () => {
    data = { CancerScreeningUpdated: { profileId: '2' } };
    variables = { profileId: '1' };
    expect(filterCancerScreeningUpdated(data, variables)).toBeFalsy();
  });
  it('filterCancerScreeningRemoved() returns true if profileId matches', () => {
    data = { CancerScreeningRemoved: [{ profileId: '1' }] };
    variables = { profileId: '1' };
    expect(filterCancerScreeningRemoved(data, variables)).toBeTruthy();
  });
  it('filterCancerScreeningRemoved() returns false if profileId dont match', () => {
    data = { CancerScreeningRemoved: [{ profileId: '2' }] };
    variables = { profileId: '1' };
    expect(filterCancerScreeningRemoved(data, variables)).toBeFalsy();
  });
  it('filterOncologyConsultationTreatmentPlanAdded() returns true if profileId matches', () => {
    data = {
      OncologyConsultationTreatmentPlanAdded: {
        oncologyConsultationHistory: { profileId: '1' },
      },
    };
    variables = { profileId: '1' };
    expect(
      filterOncologyConsultationTreatmentPlanAdded(data, variables),
    ).toBeTruthy();
  });
  it('filterOncologyConsultationTreatmentPlanAdded() returns false if profileId dont match', () => {
    data = {
      OncologyConsultationTreatmentPlanAdded: {
        oncologyConsultationHistory: { profileId: '2' },
      },
    };
    variables = { profileId: '1' };
    expect(
      filterOncologyConsultationTreatmentPlanAdded(data, variables),
    ).toBeFalsy();
  });
  it('filterOncologyConsultationTreatmentPlanUpdated() returns true if profileId matches', () => {
    data = {
      OncologyConsultationTreatmentPlanUpdated: {
        oncologyConsultationHistory: { profileId: '1' },
      },
    };
    variables = { profileId: '1' };
    expect(
      filterOncologyConsultationTreatmentPlanUpdated(data, variables),
    ).toBeTruthy();
  });
  it('filterOncologyConsultationTreatmentPlanUpdated() returns false if profileId dont match', () => {
    data = {
      OncologyConsultationTreatmentPlanUpdated: {
        oncologyConsultationHistory: { profileId: '2' },
      },
    };
    variables = { profileId: '1' };
    expect(
      filterOncologyConsultationTreatmentPlanUpdated(data, variables),
    ).toBeFalsy();
  });
  it('filterOncologyConsultationTreatmentPlanRemoved() returns true if profileId matches', () => {
    data = {
      OncologyConsultationTreatmentPlanRemoved: {
        oncologyConsultationHistory: { profileId: '1' },
      },
    };
    variables = { profileId: '1' };
    expect(
      filterOncologyConsultationTreatmentPlanRemoved(data, variables),
    ).toBeTruthy();
  });
  it('filterOncologyConsultationTreatmentPlanRemoved() returns false if profileId dont match', () => {
    data = {
      OncologyConsultationTreatmentPlanRemoved: {
        oncologyConsultationHistory: { profileId: '2' },
      },
    };
    variables = { profileId: '1' };
    expect(
      filterOncologyConsultationTreatmentPlanRemoved(data, variables),
    ).toBeFalsy();
  });

  it('filterHMOClaimUpdated() returns false if there is no match', () => {
    data = {
      HMOClaimUpdated: { providerId: '2', profileId: '1', hospitalId: '2' },
    };
    variables = { hmoProviderId: '1', profileId: '2', hospitalId: '1' };
    expect(filterHMOClaimUpdated(data, variables)).toBeFalsy();
  });
  it('filterHMOClaimUpdated() returns true if hmoProviderId matches', () => {
    data = {
      HMOClaimUpdated: { providerId: '1', profileId: '1', hospitalId: '2' },
    };
    variables = { hmoProviderId: '1', profileId: '2', hospitalId: '1' };
    expect(filterHMOClaimUpdated(data, variables)).toBeTruthy();
  });
  it('filterHMOClaimUpdated() returns true if profileId matches', () => {
    data = {
      HMOClaimUpdated: { providerId: '2', profileId: '2', hospitalId: '2' },
    };
    variables = { hmoProviderId: '1', profileId: '2', hospitalId: '1' };
    expect(filterHMOClaimUpdated(data, variables)).toBeTruthy();
  });
  it('filterHMOClaimUpdated() returns false if hospitalId matches', () => {
    data = {
      HMOClaimUpdated: { providerId: '2', profileId: '1', hospitalId: '1' },
    };
    variables = { hmoProviderId: '1', profileId: '2', hospitalId: '1' };
    expect(filterHMOClaimUpdated(data, variables)).toBeTruthy();
  });

  it('filterHmoClaimFlagged() returns true if hospitalId matches', () => {
    data = {
      HmoClaimFlagged: { hospitalId: '1' },
    };
    variables = { hospitalId: '1' };
    expect(filterHmoClaimFlagged(data, variables)).toBeTruthy();
  });
  it('filterHmoClaimFlagged() returns false if hospital does not match', () => {
    data = {
      HmoClaimFlagged: { hospitalId: '2' },
    };
    variables = { hospitalId: '1' };
    expect(filterHmoClaimFlagged(data, variables)).toBeFalsy();
  });

  it('filterPreauthorizationFlagged() returns true if hospitalId matches', () => {
    data = {
      PreauthorizationFlagged: { hospitalId: '1' },
    };
    variables = { hospitalId: '1' };
    expect(filterPreauthorizationFlagged(data, variables)).toBeTruthy();
  });
  it('filterPreauthorizationFlagged() returns false if hospital does not match', () => {
    data = {
      PreauthorizationFlagged: { hospitalId: '2' },
    };
    variables = { hospitalId: '1' };
    expect(filterPreauthorizationFlagged(data, variables)).toBeFalsy();
  });

  it('filterUtilizationUpdated() returns false if there is no match', () => {
    data = {
      UtilizationUpdated: {
        preAuthorization: { providerId: '2' },
        profileId: '1',
        hospitalId: '2',
      },
    };
    variables = { hmoProviderId: '1', profileId: '2', hospitalId: '1' };
    expect(filterUtilizationUpdated(data, variables)).toBeFalsy();
  });
  it('filterUtilizationUpdated() returns true if hmoProviderId matches', () => {
    data = {
      UtilizationUpdated: {
        preAuthorization: { providerId: '1' },
        profileId: '1',
        hospitalId: '2',
      },
    };
    variables = { hmoProviderId: '1', profileId: '2', hospitalId: '1' };
    expect(filterUtilizationUpdated(data, variables)).toBeTruthy();
  });
  it('filterUtilizationUpdated() returns true if hmoProviderId matches', () => {
    data = {
      UtilizationUpdated: {
        hmoClaim: { providerId: '1' },
        profileId: '1',
        hospitalId: '2',
      },
    };
    variables = { hmoProviderId: '1', profileId: '2', hospitalId: '1' };
    expect(filterUtilizationUpdated(data, variables)).toBeTruthy();
  });
  it('filterUtilizationUpdated() returns true if profileId matches', () => {
    data = {
      UtilizationUpdated: {
        preAuthorization: { providerId: '2' },
        profileId: '2',
        hospitalId: '2',
      },
    };
    variables = { hmoProviderId: '1', profileId: '2', hospitalId: '1' };
    expect(filterUtilizationUpdated(data, variables)).toBeTruthy();
  });
  it('filterUtilizationUpdated() returns false if hospitalId matches', () => {
    data = {
      UtilizationUpdated: {
        preAuthorization: { providerId: '2' },
        profileId: '1',
        hospitalId: '1',
      },
    };
    variables = { hmoProviderId: '1', profileId: '2', hospitalId: '1' };
    expect(filterUtilizationUpdated(data, variables)).toBeTruthy();
  });

  it('filterUtilizationReferralUpdated() returns false if there is no match', () => {
    data = {
      UtilizationReferralUpdated: {
        preAuthorizationReferral: {
          providerId: '2',
          profileId: '1',
          hospitalId: '2',
        },
      },
    };
    variables = { hmoProviderId: '1', profileId: '2', hospitalId: '1' };
    expect(filterUtilizationReferralUpdated(data, variables)).toBeFalsy();
  });
  it('filterUtilizationReferralUpdated() returns true if hmoProviderId matches', () => {
    data = {
      UtilizationReferralUpdated: {
        preAuthorizationReferral: {
          providerId: '1',
          profileId: '1',
          hospitalId: '2',
        },
      },
    };
    variables = { hmoProviderId: '1', profileId: '2', hospitalId: '1' };
    expect(filterUtilizationReferralUpdated(data, variables)).toBeTruthy();
  });
  it('filterUtilizationReferralUpdated() returns true if profileId matches', () => {
    data = {
      UtilizationReferralUpdated: {
        preAuthorizationReferral: {
          providerId: '2',
          profileId: '2',
          hospitalId: '2',
        },
      },
    };
    variables = { hmoProviderId: '1', profileId: '2', hospitalId: '1' };
    expect(filterUtilizationReferralUpdated(data, variables)).toBeTruthy();
  });
  it('filterUtilizationReferralUpdated() returns false if hospitalId matches', () => {
    data = {
      UtilizationReferralUpdated: {
        preAuthorizationReferral: {
          providerId: '2',
          profileId: '1',
          hospitalId: '1',
        },
      },
    };
    variables = { hmoProviderId: '1', profileId: '2', hospitalId: '1' };
    expect(filterUtilizationReferralUpdated(data, variables)).toBeTruthy();
  });

  it('filterPreauthorisationReferralUpdated() returns false if there is no match', () => {
    data = {
      PreauthorisationReferralUpdated: {
        providerId: '2',
        profileId: '1',
        hospitalId: '2',
      },
    };
    variables = { hmoProviderId: '1', profileId: '2', hospitalId: '1' };
    expect(filterPreauthorisationReferralUpdated(data, variables)).toBeFalsy();
  });
  it('filterPreauthorisationReferralUpdated() returns true if hmoProviderId matches', () => {
    data = {
      PreauthorisationReferralUpdated: {
        providerId: '1',
        profileId: '1',
        hospitalId: '2',
      },
    };
    variables = { hmoProviderId: '1', profileId: '2', hospitalId: '1' };
    expect(filterPreauthorisationReferralUpdated(data, variables)).toBeTruthy();
  });
  it('filterPreauthorisationReferralUpdated() returns true if profileId matches', () => {
    data = {
      PreauthorisationReferralUpdated: {
        providerId: '2',
        profileId: '2',
        hospitalId: '2',
      },
    };
    variables = { hmoProviderId: '1', profileId: '2', hospitalId: '1' };
    expect(filterPreauthorisationReferralUpdated(data, variables)).toBeTruthy();
  });
  it('filterPreauthorisationReferralUpdated() returns false if hospitalId matches', () => {
    data = {
      PreauthorisationReferralUpdated: {
        providerId: '2',
        profileId: '1',
        hospitalId: '1',
      },
    };
    variables = { hmoProviderId: '1', profileId: '2', hospitalId: '1' };
    expect(filterPreauthorisationReferralUpdated(data, variables)).toBeTruthy();
  });

  it('filterUtilizationsConfirmed() returns true if hospitalId matches', () => {
    data = {
      hospitalId: '1',
      UtilizationsConfirmed: [],
    };
    variables = { hospitalId: '1' };
    expect(filterUtilizationsConfirmed(data, variables)).toBeTruthy();
  });

  it('filterUtilizationsConfirmed() returns false if hospitalId does not match', () => {
    data = {
      hospitalId: '1',
      UtilizationsConfirmed: [],
    };
    variables = { hospitalId: '2' };
    expect(filterUtilizationsConfirmed(data, variables)).toBeFalsy();
  });

  it('filterUtilizationCoveredUpdated() returns true if hospitalId matches', () => {
    data = {
      hospitalId: '1',
      UtilizationPercentageCoveredUpdated: [],
    };
    variables = { hospitalId: '1' };
    expect(filterUtilizationCoveredUpdated(data, variables)).toBeTruthy();
  });

  it('filterUtilizationCoveredUpdated() returns false if hospitalId does not match', () => {
    data = {
      hospitalId: '1',
      UtilizationPercentageCoveredUpdated: [],
    };
    variables = { hospitalId: '2' };
    expect(filterUtilizationCoveredUpdated(data, variables)).toBeFalsy();
  });

  it('filterHmoClaimConfirmation() returns true if hmoProviderId matches', () => {
    const data = {
      HmoClaimConfirmation: {
        providerId: '1',
      },
    };
    const variables = { hmoProviderId: '1' };
    expect(filterHmoClaimConfirmation(data, variables)).toBeTruthy();
  });

  it('filterHmoClaimConfirmation() returns false if hmoProviderId does not match', () => {
    const data = {
      HmoClaimConfirmation: {
        providerId: '1',
      },
    };
    const variables = { hmoProviderId: '2' };
    expect(filterHmoClaimConfirmation(data, variables)).toBeFalsy();
  });

  it('filterHmoClaimConfirmation() returns false if hmoProviderId is missing in data', () => {
    const data = {
      HmoClaimConfirmation: {},
    };
    const variables = { hmoProviderId: '1' };
    expect(filterHmoClaimConfirmation(data, variables)).toBeFalsy();
  });

  it('should return true when data matches profile ID', () => {
    const mockData = {
      NursingServiceProgressNoteAdded: {
        profileId: 'test-profile-123',
      },
    };
    const mockVariables = {
      profileId: 'test-profile-123',
    };

    expect(filterNursingServiceProgressNoteAdded(mockData, mockVariables)).toBe(
      true,
    );
  });

  it('should return false when profile IDs do not match', () => {
    const mockVariables = {
      profileId: 'test-profile-123',
    };

    const mockData = {
      NursingServiceProgressNoteAdded: {
        profileId: 'different-profile-456',
      },
    };

    expect(filterNursingServiceProgressNoteAdded(mockData, mockVariables)).toBe(
      false,
    );
  });

  it('should handle undefined data', () => {
    const mockVariables = {
      profileId: 'test-profile-123',
    };

    expect(
      filterNursingServiceProgressNoteAdded(undefined, mockVariables),
    ).toBe(false);
  });

  it('should handle null data', () => {
    const mockVariables = {
      profileId: 'test-profile-123',
    };
    expect(filterNursingServiceProgressNoteAdded(null, mockVariables)).toBe(
      false,
    );
  });

  it('should return true when data matches profile ID', () => {
    const mockData = {
      NursingServiceProgressNoteUpdated: {
        profileId: 'test-profile-123',
      },
    };
    const mockVariables = {
      profileId: 'test-profile-123',
    };

    expect(
      filterNursingServiceProgressNoteUpdated(mockData, mockVariables),
    ).toBe(true);
  });

  it('should return false when profile IDs do not match', () => {
    const mockData = {
      NursingServiceProgressNoteUpdated: {
        profileId: 'different-profile-456',
      },
    };
    const mockVariables = {
      profileId: 'test-profile-123',
    };

    expect(
      filterNursingServiceProgressNoteUpdated(mockData, mockVariables),
    ).toBe(false);
  });

  it('should handle undefined data', () => {
    const mockVariables = {
      profileId: 'test-profile-123',
    };
    expect(
      filterNursingServiceProgressNoteUpdated(undefined, mockVariables),
    ).toBe(false);
  });

  it('should handle null data', () => {
    const mockVariables = {
      profileId: 'test-profile-123',
    };
    expect(filterNursingServiceProgressNoteUpdated(null, mockVariables)).toBe(
      false,
    );
  });

  it('should return true when data matches profile ID', () => {
    const mockData = {
      NursingServiceProgressNoteRemoved: {
        profileId: 'test-profile-123',
      },
    };
    const mockVariables = {
      profileId: 'test-profile-123',
    };

    expect(
      filterNursingServiceProgressNoteRemoved(mockData, mockVariables),
    ).toBe(true);
  });

  it('should return false when profile IDs do not match', () => {
    const mockData = {
      NursingServiceProgressNoteRemoved: {
        profileId: 'different-profile-456',
      },
    };
    const mockVariables = {
      profileId: 'test-profile-123',
    };

    expect(
      filterNursingServiceProgressNoteRemoved(mockData, mockVariables),
    ).toBe(false);
  });

  it('should handle undefined data', () => {
    const mockVariables = {
      profileId: 'test-profile-123',
    };
    expect(
      filterNursingServiceProgressNoteRemoved(undefined, mockVariables),
    ).toBe(false);
  });

  it('should handle null data', () => {
    const mockVariables = {
      profileId: 'test-profile-123',
    };
    expect(filterNursingServiceProgressNoteRemoved(null, mockVariables)).toBe(
      false,
    );
  });

  it('should return true when phone numbers match', () => {
    const mockData = {
      TempUserUpdated: {
        phoneNumber: '+**********',
      },
    };
    const mockVariables = {
      phoneNumber: '+**********',
    };

    expect(filterTempUserUpdated(mockData, mockVariables)).toBe(true);
  });

  it('should return false when phone numbers do not match', () => {
    const mockData = {
      TempUserUpdated: {
        phoneNumber: '+**********',
      },
    };
    const mockVariables = {
      phoneNumber: '+9876543210',
    };

    expect(filterTempUserUpdated(mockData, mockVariables)).toBe(false);
  });

  it('should handle undefined data for TempUserUpdated', () => {
    const mockVariables = {
      phoneNumber: '+**********',
    };
    expect(filterTempUserUpdated(undefined, mockVariables)).toBe(false);
  });

  it('should handle null data for TempUserUpdated', () => {
    const mockVariables = {
      phoneNumber: '+**********',
    };
    expect(filterTempUserUpdated(null, mockVariables)).toBe(false);
  });

  it('should return true when hmoProviderId matches', () => {
    const mockData = {
      HospitalCapitationDetailsUpdated: {
        hmoProviderId: 'test-provider-123',
      },
    };
    const mockVariables = {
      hmoProviderId: 'test-provider-123',
    };

    expect(
      filterHospitalCapitationDetailsUpdated(mockData, mockVariables),
    ).toBe(true);
  });

  it('should return false when hmoProviderId does not match', () => {
    const mockData = {
      HospitalCapitationDetailsUpdated: {
        hmoProviderId: 'different-provider-456',
      },
    };
    const mockVariables = {
      hmoProviderId: 'test-provider-123',
    };

    expect(
      filterHospitalCapitationDetailsUpdated(mockData, mockVariables),
    ).toBe(false);
  });

  it('should handle undefined data for HospitalCapitationDetailsUpdated', () => {
    const mockVariables = {
      hmoProviderId: 'test-provider-123',
    };
    expect(
      filterHospitalCapitationDetailsUpdated(undefined, mockVariables),
    ).toBe(false);
  });

  it('should handle null data for HospitalCapitationDetailsUpdated', () => {
    const mockVariables = {
      hmoProviderId: 'test-provider-123',
    };
    expect(filterHospitalCapitationDetailsUpdated(null, mockVariables)).toBe(
      false,
    );
  });

  it('should return true when profileId matches for BulkRegistrationStatusUpdate', () => {
    const mockData = {
      BulkRegistrationStatusUpdate: {
        profileId: 'test-profile-123',
      },
    };
    const mockVariables = {
      profileId: 'test-profile-123',
    };

    expect(filterBulkRegistrationStatusUpdate(mockData, mockVariables)).toBe(
      true,
    );
  });

  it('should return false when profileId does not match for BulkRegistrationStatusUpdate', () => {
    const mockData = {
      BulkRegistrationStatusUpdate: {
        profileId: 'different-profile-456',
      },
    };
    const mockVariables = {
      profileId: 'test-profile-123',
    };

    expect(filterBulkRegistrationStatusUpdate(mockData, mockVariables)).toBe(
      false,
    );
  });

  it('should handle undefined data for BulkRegistrationStatusUpdate', () => {
    const mockVariables = {
      profileId: 'test-profile-123',
    };
    expect(filterBulkRegistrationStatusUpdate(undefined, mockVariables)).toBe(
      false,
    );
  });

  it('should handle null data for BulkRegistrationStatusUpdate', () => {
    const mockVariables = {
      profileId: 'test-profile-123',
    };
    expect(filterBulkRegistrationStatusUpdate(null, mockVariables)).toBe(false);
  });

  it('should handle missing BulkRegistrationStatusUpdate property in data', () => {
    const mockData = {};
    const mockVariables = {
      profileId: 'test-profile-123',
    };
    expect(filterBulkRegistrationStatusUpdate(mockData, mockVariables)).toBe(
      false,
    );
  });

  it('should handle undefined profileId in BulkRegistrationStatusUpdate data', () => {
    const mockData = {
      BulkRegistrationStatusUpdate: {},
    };
    const mockVariables = {
      profileId: 'test-profile-123',
    };
    expect(filterBulkRegistrationStatusUpdate(mockData, mockVariables)).toBe(
      false,
    );
  });
});
