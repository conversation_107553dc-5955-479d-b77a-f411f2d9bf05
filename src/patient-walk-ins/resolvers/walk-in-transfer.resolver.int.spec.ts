import { Test, TestingModule } from '@nestjs/testing';
import { WalkInTransferResolver } from './walk-in-transfer.resolver';
import { WalkInTransferModel } from '../models/walk-in-transfer.model';
import { WalkInTransferService } from '../services/walk-in-transfer.service';
import { hospitalFactory } from '@clinify/__mocks__/factories/hospital.factory';
import { profileFactory } from '@clinify/__mocks__/factories/profile.factory';
import { walkInTransferFactory } from '@clinify/__mocks__/factories/walkInTransfer.factory';
import { UserType } from '@clinify/shared/enums/users';

const mockWalkInTransferService = {
  getOneWalkInTransfer: jest.fn(),
  saveWalkInTransfer: jest.fn(),
  updateWalkInTransfer: jest.fn(),
  deleteWalkInTransfers: jest.fn(),
  archiveWalkInTransfers: jest.fn(),
  concealTransferReason: jest.fn(),
};

const pubSubMock = {
  publish: jest.fn(),
  asyncIterator: jest.fn(),
};

describe('WalkInTransferResolver', () => {
  let resolver: WalkInTransferResolver;

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        WalkInTransferResolver,
        WalkInTransferService,
        {
          provide: WalkInTransferService,
          useValue: mockWalkInTransferService,
        },
        {
          provide: 'PUB_SUB',
          useValue: pubSubMock,
        },
      ],
    }).compile();

    resolver = module.get<WalkInTransferResolver>(WalkInTransferResolver);
  });

  it('walkInTransfer(): should call getOneWalkInTransfer service method', async () => {
    const mutator = profileFactory.build();

    await resolver.walkInTransfer(mutator, 'record-id');
    expect(mockWalkInTransferService.getOneWalkInTransfer).toHaveBeenCalledWith(
      mutator,
      'record-id',
    );
  });

  it('walkInTransferEventHandler(): should call WalkInTransferEvent pub sub event', async () => {
    await resolver.walkInTransferEventHandler('hospital-id');
    expect(pubSubMock.asyncIterator).toHaveBeenCalledWith(
      'WalkInTransferEvent',
    );
  });

  it('addWalkInTransfer(): should call saveWalkInTransfer service method', async () => {
    const mutator = profileFactory.build();
    const hospital = hospitalFactory.build();
    const input = walkInTransferFactory.build();

    const response = {
      ...input,
      createdBy: mutator,
      creatorId: mutator.id,
      hospital,
      hospitalId: hospital.id,
    };

    mockWalkInTransferService.saveWalkInTransfer = jest
      .fn()
      .mockResolvedValue(response);

    await resolver.addWalkInTransfer(mutator, input);

    expect(mockWalkInTransferService.saveWalkInTransfer).toHaveBeenCalledWith(
      mutator,
      input,
    );
  });

  it('addWalkInTransferSubsHandler(): should call WalkInTransferAdded pub sub event', async () => {
    await resolver.addWalkInTransferSubsHandler('hospital-id');
    expect(pubSubMock.asyncIterator).toHaveBeenCalledWith(
      'WalkInTransferAdded',
    );
  });

  it('updateWalkInTransfer(): should call updateWalkInTransfer service method', async () => {
    const mutator = profileFactory.build();
    const hospital = hospitalFactory.build();
    const input = walkInTransferFactory.build();

    const response = {
      ...input,
      createdBy: mutator,
      creatorId: mutator.id,
      hospital,
      hospitalId: hospital.id,
    };

    mockWalkInTransferService.updateWalkInTransfer = jest
      .fn()
      .mockResolvedValue(response);

    await resolver.updateWalkInTransfer(mutator, input, 'record-id');

    expect(mockWalkInTransferService.updateWalkInTransfer).toHaveBeenCalledWith(
      mutator,
      input,
      'record-id',
    );
  });

  it('updateWalkInTransferSubsHandler(): should call WalkInTransferUpdated pub sub event', async () => {
    await resolver.updateWalkInTransferSubsHandler('hospital-id');
    expect(pubSubMock.asyncIterator).toHaveBeenCalledWith(
      'WalkInTransferUpdated',
    );
  });

  it('deleteWalkInTransfers(): should call deleteWalkInTransfers service method', async () => {
    const mutator = profileFactory.build();
    const hospital = hospitalFactory.build();
    const input = walkInTransferFactory.build();

    const response = [
      {
        ...input,
        createdBy: mutator,
        creatorId: mutator.id,
        hospital,
        hospitalId: hospital.id,
      },
    ];

    mockWalkInTransferService.deleteWalkInTransfers = jest
      .fn()
      .mockResolvedValue(response);

    await resolver.deleteWalkInTransfers(mutator, ['record-id']);

    expect(
      mockWalkInTransferService.deleteWalkInTransfers,
    ).toHaveBeenCalledWith(mutator, ['record-id']);
  });

  it('removeWalkInTransfersHandler(): should call WalkInTransferRemoved pub sub event', async () => {
    await resolver.removeWalkInTransfersHandler('hospital-id');
    expect(pubSubMock.asyncIterator).toHaveBeenCalledWith(
      'WalkInTransferRemoved',
    );
  });

  it('archiveWalkInTransfers(): should call archiveWalkInTransfers service method', async () => {
    const mutator = profileFactory.build();
    const hospital = hospitalFactory.build();
    const input = walkInTransferFactory.build();

    const response = [
      {
        ...input,
        createdBy: mutator,
        creatorId: mutator.id,
        hospital,
        hospitalId: hospital.id,
      },
    ];

    mockWalkInTransferService.archiveWalkInTransfers = jest
      .fn()
      .mockResolvedValue(response);

    await resolver.archiveWalkInTransfers(mutator, ['record-id'], true);

    expect(
      mockWalkInTransferService.archiveWalkInTransfers,
    ).toHaveBeenCalledWith(mutator, ['record-id'], true);
  });

  it('archiveWalkInTransfers(): should call archiveWalkInTransfers service method', async () => {
    const mutator = profileFactory.build();
    const hospital = hospitalFactory.build();
    const input = walkInTransferFactory.build();

    const response = [
      {
        ...input,
        createdBy: mutator,
        creatorId: mutator.id,
        hospital,
        hospitalId: hospital.id,
      },
    ];

    mockWalkInTransferService.archiveWalkInTransfers = jest
      .fn()
      .mockResolvedValue(response);

    await resolver.archiveWalkInTransfers(mutator, ['record-id'], false);

    expect(
      mockWalkInTransferService.archiveWalkInTransfers,
    ).toHaveBeenCalledWith(mutator, ['record-id'], false);
  });

  it('archiveWalkInTransfersHandler(): should call WalkInTransferArchived pub sub event', async () => {
    await resolver.archiveWalkInTransfersHandler('hospital-id');
    expect(pubSubMock.asyncIterator).toHaveBeenCalledWith(
      'WalkInTransferArchived',
    );
  });

  it('unArchiveWalkInTransfersHandler(): should call WalkInTransferUnarchived pub sub event', async () => {
    await resolver.unArchiveWalkInTransfersHandler('hospital-id');
    expect(pubSubMock.asyncIterator).toHaveBeenCalledWith(
      'WalkInTransferUnarchived',
    );
  });

  it('concealTransferReason(): should call concealTransferReason service method', async () => {
    const mutator = profileFactory.build();
    const hospital = hospitalFactory.build();
    const input = walkInTransferFactory.build();

    const response = {
      ...input,
      createdBy: mutator,
      creatorId: mutator.id,
      hospital,
      hospitalId: hospital.id,
    };
    mockWalkInTransferService.concealTransferReason = jest
      .fn()
      .mockResolvedValue(response);

    await resolver.concealTransferReason(mutator, 'record-id', true);

    expect(
      mockWalkInTransferService.concealTransferReason,
    ).toHaveBeenCalledWith(mutator, 'record-id', true);
  });

  it('getTransferReason(): should return transfer reason', () => {
    const mutator = profileFactory.build({ type: UserType.OrganizationDoctor });
    const hospital = hospitalFactory.build();
    const input = walkInTransferFactory.build({
      transferReason: 'Specialized Care',
      concealTransferReason: true,
    });

    const root = {
      ...input,
      createdBy: mutator,
      creatorId: mutator.id,
      hospital,
      hospitalId: hospital.id,
    } as WalkInTransferModel;

    const result = resolver.getTransferReason(mutator, root);

    expect(result).toBe('Specialized Care');
  });

  it('getTransferReason(): should return transfer reason', () => {
    const mutator = profileFactory.build({ type: UserType.OrganizationDoctor });
    const hospital = hospitalFactory.build();
    const input = walkInTransferFactory.build({
      transferReason: 'Specialized Care',
      concealTransferReason: false,
    });

    const root = {
      ...input,
      createdBy: mutator,
      creatorId: mutator.id,
      hospital,
      hospitalId: hospital.id,
    } as WalkInTransferModel;

    const result = resolver.getTransferReason(mutator, root);

    expect(result).toBe('Specialized Care');
  });

  it('getTransferReason(): should return transfer reason', () => {
    const mutator = profileFactory.build({ type: UserType.Patient });
    const hospital = hospitalFactory.build();
    const input = walkInTransferFactory.build({
      transferReason: 'Specialized Care',
      concealTransferReason: false,
    });

    const root = {
      ...input,
      createdBy: mutator,
      creatorId: mutator.id,
      hospital,
      hospitalId: hospital.id,
    } as WalkInTransferModel;

    const result = resolver.getTransferReason(mutator, root);

    expect(result).toBe('Specialized Care');
  });

  it('getTransferReason(): should return transfer reason', () => {
    const mutator = profileFactory.build({ type: UserType.Patient });
    const hospital = hospitalFactory.build();
    const input = walkInTransferFactory.build({
      transferReason: 'Specialized Care',
      concealTransferReason: true,
    });

    const root = {
      ...input,
      profile: mutator,
      profileId: mutator.id,
      createdBy: mutator,
      creatorId: mutator.id,
      hospital,
      hospitalId: hospital.id,
    } as WalkInTransferModel;

    const result = resolver.getTransferReason(mutator, root);

    expect(result).toBe('Specialized Care');
  });

  it('getTransferReason(): should not return transfer reason', () => {
    const patient = profileFactory.build({ type: UserType.Patient });
    const mutator = profileFactory.build({ type: UserType.OrganizationDoctor });
    const hospital = hospitalFactory.build();
    const input = walkInTransferFactory.build({
      transferReason: 'Specialized Care',
      concealTransferReason: true,
    });

    const root = {
      ...input,
      profile: patient,
      profileId: patient.id,
      createdBy: mutator,
      creatorId: mutator.id,
      hospital,
      hospitalId: hospital.id,
    } as WalkInTransferModel;

    const result = resolver.getTransferReason(patient, root);

    expect(result).toBe(null);
  });
});
