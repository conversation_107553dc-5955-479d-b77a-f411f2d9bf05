import { EventEmitter2 } from '@nestjs/event-emitter';
import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { DataSource, EntityManager } from 'typeorm';
import { LabourDeliveryResolver } from './labour_delivery.resolver';
import { LabourDeliveryService } from '../services/labour_delivery.service';
import { labourAndDeliveryFactory } from '@clinify/__mocks__/factories/antenatal.factory';
import {
  billDetailsFactory,
  billFactory,
} from '@clinify/__mocks__/factories/bill.factory';
import { hmoClaimFactory } from '@clinify/__mocks__/factories/hmo-claim.factory';
import { hospitalFactory } from '@clinify/__mocks__/factories/hospital.factory';
import { preauthorizationDetailsFactory } from '@clinify/__mocks__/factories/preauthorizationdetails.factory';
import { profileFactory } from '@clinify/__mocks__/factories/profile.factory';
import { mockUser } from '@clinify/__mocks__/factories/user.factory';
import { PermissionModel } from '@clinify/authorization/models/permission.model';
import { PermissionService } from '@clinify/authorization/services/permission.service';
import { HmoClaimService } from '@clinify/hmo-claims/services/hmo-claim.service';
import { NotificationsService } from '@clinify/notifications/services/notifications.service';
import { PreauthorizationDetailsModel } from '@clinify/preauthorization-details/models/preauthorization-details.model';
import { PreauthorizationDetailsService } from '@clinify/preauthorization-details/services/preauthorization-details.service';
import { ProfileModel } from '@clinify/users/models/profile.model';

const user = mockUser;
const clinifyId = 'clinify-id';
const labourDeliveryData = labourAndDeliveryFactory.build();
const profile = profileFactory.build();
const hmoClaimData = hmoClaimFactory.build();

const MockProfileRepository = {
  findOne: jest.fn(() => user.defaultProfile),
};

const LabourDeliveryServiceMock = {
  saveLabourDelivery: jest.fn(() => labourDeliveryData),
  updateLabourDelivery: jest.fn(() => labourDeliveryData),
  deleteLabourDelivery: jest.fn(() => [labourDeliveryData]),
  getOneLabourDelivery: jest.fn(() => labourDeliveryData),
  archiveLabourDelivery: jest.fn(() => [labourDeliveryData]),
  concealAdditionalNote: jest.fn(() => labourDeliveryData),
  concealFirstStageAdditionalNote: jest.fn(() => labourDeliveryData),
  concealSecondStageAdditionalNote: jest.fn(() => labourDeliveryData),
  concealThirdStageAdditionalNote: jest.fn(() => labourDeliveryData),
  concealFourthStageAdditionalNote: jest.fn(() => labourDeliveryData),
  concealFourthStageCordCare: jest.fn(() => labourDeliveryData),
  concealFourthStageGeneralConditionOfTheBaby: jest.fn(
    () => labourDeliveryData,
  ),
  concealFourthStageGeneralConditionOfTheMother: jest.fn(
    () => labourDeliveryData,
  ),
};

const ManagerMock = {
  findOneOrFail: jest.fn(() => user.defaultProfile),
};

const preauthorizationDetailsRepoMock = {
  find: jest.fn(() => [preauthorizationDetailsFactory.build()]),
};

const dsMock = {
  getRepository: jest.fn(() => ({
    extend: jest.fn(),
  })),
  transaction: jest.fn((cb) => cb(ManagerMock)),
  manager: ManagerMock,
};

const pubSubMock = {
  publish: jest.fn(),
  asyncIterator: jest.fn(),
};

const mockEventEmitter = {
  emit: jest.fn(),
};

const mockHmoClaimService = {
  getHmoClaim: jest.fn(() => hmoClaimData),
};

describe('LabourDeliveryResolver', () => {
  let resolver: LabourDeliveryResolver;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        LabourDeliveryResolver,
        LabourDeliveryService,
        PermissionService,
        {
          provide: getRepositoryToken(PermissionModel),
          useValue: {},
        },
        PreauthorizationDetailsService,
        {
          provide: LabourDeliveryService,
          useValue: LabourDeliveryServiceMock,
        },
        {
          provide: getRepositoryToken(ProfileModel),
          useValue: MockProfileRepository,
        },
        {
          provide: EntityManager,
          useValue: ManagerMock,
        },
        {
          provide: DataSource,
          useValue: dsMock,
        },
        {
          provide: getRepositoryToken(PreauthorizationDetailsModel),
          useValue: preauthorizationDetailsRepoMock,
        },
        {
          provide: 'PUB_SUB',
          useValue: pubSubMock,
        },
        {
          provide: EventEmitter2,
          useValue: mockEventEmitter,
        },
        {
          provide: NotificationsService,
          useValue: {
            handleNoticationEvent: jest.fn(),
          },
        },
        {
          provide: HmoClaimService,
          useValue: mockHmoClaimService,
        },
      ],
    }).compile();

    resolver = module.get<LabourDeliveryResolver>(LabourDeliveryResolver);
    jest.clearAllMocks();
  });

  it('addLabourAndDelivery(): should save labour and delivery', async () => {
    const labourDeliveryInput = labourDeliveryData;
    delete labourDeliveryInput.profile;
    const response = await resolver.addLabourAndDelivery(
      profile,
      labourDeliveryInput,
    );
    expect(LabourDeliveryServiceMock.saveLabourDelivery).toHaveBeenCalledWith(
      profile,
      labourDeliveryInput,
    );
    expect(response).toEqual(labourDeliveryData);
  });

  it('addLabourAndDelivery(): should save labour and delivery with bill', async () => {
    const labourDeliveryInput = labourDeliveryData;
    delete labourDeliveryInput.profile;
    const bill = billFactory.build();
    const serviceResponse = {
      ...labourDeliveryData,
      bill,
    };
    LabourDeliveryServiceMock.saveLabourDelivery = jest.fn(
      () => serviceResponse,
    );

    const response = await resolver.addLabourAndDelivery(
      profile,
      labourDeliveryInput,
    );
    expect(LabourDeliveryServiceMock.saveLabourDelivery).toHaveBeenCalledWith(
      profile,
      labourDeliveryInput,
    );
    expect(response).toEqual(serviceResponse);
    expect(pubSubMock.publish).toBeCalledWith('OrgBillAdded', {
      OrgBillAdded: bill,
    });
    expect(pubSubMock.publish).toHaveBeenLastCalledWith('BillingEvent', {
      billing: serviceResponse?.createdBy,
      BillingEvent: 'Added',
    });
  });

  it('addLabourAndDeliverySubsHandler() should trigger LabourAndDeliveryAdded subscription', () => {
    resolver.addLabourAndDeliverySubsHandler(clinifyId);
    expect(pubSubMock.asyncIterator).toHaveBeenCalledWith(
      'LabourAndDeliveryAdded',
    );
  });

  it('labourAndDelivery(): should get one labour and delivery', async () => {
    const labourDeliveryInput = labourDeliveryData;
    delete labourDeliveryInput.profile;
    const clinifyId = 'fake-id';
    const response = await resolver.labourAndDelivery(
      profile,
      labourDeliveryInput.id,
      clinifyId,
    );
    expect(LabourDeliveryServiceMock.getOneLabourDelivery).toHaveBeenCalledWith(
      profile,
      labourDeliveryInput.id,
    );
    expect(response).toEqual(labourDeliveryData);
  });

  it('updateLabourAndDelivery(): should update labour and delivery', async () => {
    const labourDeliveryInput = labourDeliveryData;
    delete labourDeliveryInput.profile;
    const response = await resolver.updateLabourAndDelivery(
      profile,
      labourDeliveryInput,
      labourDeliveryInput.id,
    );
    expect(LabourDeliveryServiceMock.updateLabourDelivery).toHaveBeenCalledWith(
      profile,
      labourDeliveryInput,
    );
    expect(response).toEqual(labourDeliveryData);
  });

  it('updateLabourAndDeliveryBill(): should update labour and delivery with bill data', async () => {
    const labourDeliveryInput = {
      ...labourDeliveryData,
      clinifyId: 'clinify-id',
      serviceDetails: [
        {
          type: 'Service Type',
          name: 'Service Name',
          quantity: '2',
          pricePerUnit: '3000',
        },
      ],
    };
    delete labourDeliveryInput.profile;
    const response = await resolver.updateLabourAndDeliveryBill(
      profile,
      labourDeliveryInput,
      labourDeliveryInput.id,
    );
    expect(LabourDeliveryServiceMock.updateLabourDelivery).toHaveBeenCalledWith(
      profile,
      {
        id: labourDeliveryInput.id,
        clinifyId: 'clinify-id',
        serviceDetails: labourDeliveryInput.serviceDetails,
      },
      true,
    );
    expect(response).toEqual(labourDeliveryData);
  });

  it('updateLabourAndDeliverySubsHandler() should trigger LabourAndDeliveryUpdated subscription', () => {
    resolver.updateLabourAndDeliverySubsHandler(clinifyId);
    expect(pubSubMock.asyncIterator).toHaveBeenCalledWith(
      'LabourAndDeliveryUpdated',
    );
  });

  it('deleteLabourAndDeliverys(): should delete labour and delivery', async () => {
    const labourDeliveryInput = labourDeliveryData;
    delete labourDeliveryInput.profile;
    const response = await resolver.deleteLabourAndDeliverys(
      profile,
      [labourDeliveryInput.id],
      clinifyId,
    );
    expect(LabourDeliveryServiceMock.deleteLabourDelivery).toHaveBeenCalledWith(
      profile,
      [labourDeliveryInput.id],
    );
    expect(response).toEqual([labourDeliveryData]);
  });

  it('removeLabourAndDeliverySubsHandler() should trigger LabourAndDeliveryRemoved subscription', () => {
    resolver.removeLabourAndDeliverySubsHandler(clinifyId);
    expect(pubSubMock.asyncIterator).toHaveBeenCalledWith(
      'LabourAndDeliveryRemoved',
    );
  });

  it('archiveLabourAndDeliverys(): should call archive labour and delivery service', async () => {
    const labourDeliveryInput = labourDeliveryData;
    delete labourDeliveryInput.profile;
    const response = await resolver.archiveLabourAndDeliverys(
      profile,
      [labourDeliveryInput.id],
      true,
      clinifyId,
    );
    expect(
      LabourDeliveryServiceMock.archiveLabourDelivery,
    ).toHaveBeenCalledWith(profile, [labourDeliveryInput.id], true);
    expect(response).toEqual([labourDeliveryData]);
  });

  it('archiveLabourAndDeliverys(): can trigger LabourAndDeliveryUnarchived service', async () => {
    const labourDeliveryInput = labourDeliveryData;
    delete labourDeliveryInput.profile;
    const response = await resolver.archiveLabourAndDeliverys(
      profile,
      [labourDeliveryInput.id],
      false,
      clinifyId,
    );
    expect(
      LabourDeliveryServiceMock.archiveLabourDelivery,
    ).toHaveBeenCalledWith(profile, [labourDeliveryInput.id], false);
    expect(pubSubMock.publish).toHaveBeenCalledWith(
      'LabourAndDeliveryUnarchived',
      {
        LabourAndDeliveryUnarchived: response,
      },
    );
    expect(response).toEqual([labourDeliveryData]);
  });

  it('unarchiveLabourAndDeliverySubsHandler() should trigger LabourAndDeliveryUnarchived subscription', () => {
    resolver.unarchiveLabourAndDeliverySubsHandler(clinifyId);
    expect(pubSubMock.asyncIterator).toHaveBeenCalledWith(
      'LabourAndDeliveryUnarchived',
    );
  });

  it('archiveLabourAndDeliverySubsHandler() should trigger LabourAndDeliveryArchived subscription', () => {
    resolver.archiveLabourAndDeliverySubsHandler(clinifyId);
    expect(pubSubMock.asyncIterator).toHaveBeenCalledWith(
      'LabourAndDeliveryArchived',
    );
  });

  it('concealLabourAndDeliveryAdditionalNote() should call concealAdditionalNote from services', async () => {
    await resolver.concealLabourAndDeliveryAdditionalNote(
      profile,
      'labour-delivery-id',
      true,
    );

    expect(
      LabourDeliveryServiceMock.concealAdditionalNote,
    ).toHaveBeenCalledWith(profile, 'labour-delivery-id', true);
  });

  it('concealLabourAndDeliveryFirstStageAdditionalNote() should call concealFirstStageAdditionalNote from services', async () => {
    await resolver.concealLabourAndDeliveryFirstStageAdditionalNote(
      profile,
      'labour-delivery-id',
      true,
    );

    expect(
      LabourDeliveryServiceMock.concealFirstStageAdditionalNote,
    ).toHaveBeenCalledWith(profile, 'labour-delivery-id', true);
  });

  it('concealLabourAndDeliverySecondStageAdditionalNote() should call concealSecondStageAdditionalNote from services', async () => {
    await resolver.concealLabourAndDeliverySecondStageAdditionalNote(
      profile,
      'labour-delivery-id',
      true,
    );

    expect(
      LabourDeliveryServiceMock.concealSecondStageAdditionalNote,
    ).toHaveBeenCalledWith(profile, 'labour-delivery-id', true);
  });

  it('concealLabourAndDeliveryThirdStageAdditionalNote() should call concealThirdStageAdditionalNote from services', async () => {
    await resolver.concealLabourAndDeliveryThirdStageAdditionalNote(
      profile,
      'labour-delivery-id',
      true,
    );

    expect(
      LabourDeliveryServiceMock.concealThirdStageAdditionalNote,
    ).toHaveBeenCalledWith(profile, 'labour-delivery-id', true);
  });

  it('concealLabourAndDeliveryFourthStageAdditionalNote() should call concealFourthStageAdditionalNote from services', async () => {
    await resolver.concealLabourAndDeliveryFourthStageAdditionalNote(
      profile,
      'labour-delivery-id',
      true,
    );

    expect(
      LabourDeliveryServiceMock.concealFourthStageAdditionalNote,
    ).toHaveBeenCalledWith(profile, 'labour-delivery-id', true);
  });

  it('concealLabourAndDeliveryFourthStageCordCare() should call concealFourthStageCordCare from services', async () => {
    await resolver.concealLabourAndDeliveryFourthStageCordCare(
      profile,
      'labour-delivery-id',
      true,
    );

    expect(
      LabourDeliveryServiceMock.concealFourthStageCordCare,
    ).toHaveBeenCalledWith(profile, 'labour-delivery-id', true);
  });

  it(`concealLabourAndDeliveryFourthStageGeneralConditionOfTheBaby()
        should call concealFourthStageGeneralConditionOfTheBaby from services`, async () => {
    await resolver.concealLabourAndDeliveryFourthStageGeneralConditionOfTheBaby(
      profile,
      'labour-delivery-id',
      true,
    );

    expect(
      LabourDeliveryServiceMock.concealFourthStageGeneralConditionOfTheBaby,
    ).toHaveBeenCalledWith(profile, 'labour-delivery-id', true);
  });

  it(`concealLabourAndDeliveryFourthStageGeneralConditionOfTheMother()
        should call concealFourthStageGeneralConditionOfTheMother from services`, async () => {
    await resolver.concealLabourAndDeliveryFourthStageGeneralConditionOfTheMother(
      profile,
      'labour-delivery-id',
      true,
    );

    expect(
      LabourDeliveryServiceMock.concealFourthStageGeneralConditionOfTheMother,
    ).toHaveBeenCalledWith(profile, 'labour-delivery-id', true);
  });

  it('getAdditionalNote() should resolve additionalNote', () => {
    const labourDelivery = labourDeliveryData;
    labourDelivery.concealAdditionalNote = true;
    labourDelivery.additionalNote = 'additional note';
    const currentUser = user;
    user.defaultProfile.type = 'Patient';

    const additionalNote = resolver.getAdditionalNote(
      currentUser?.defaultProfile,
      labourDelivery,
    );
    expect(additionalNote).toEqual(null);
  });

  it('getFirstStageAdditionalNote() should resolve firstStageAdditionalNote', () => {
    const labourDelivery = labourDeliveryData;
    labourDelivery.concealFirstStageAdditionalNote = true;
    labourDelivery.firstStageAdditionalNote = 'first stage additional note';
    const currentUser = user;
    user.defaultProfile.type = 'Patient';

    const firstStageAdditionalNote = resolver.getFirstStageAdditionalNote(
      currentUser?.defaultProfile,
      labourDelivery,
    );
    expect(firstStageAdditionalNote).toEqual(null);
  });

  it('getSecondStageAdditionalNote() should resolve secondStageAdditionalNote', () => {
    const labourDelivery = labourDeliveryData;
    labourDelivery.concealSecondStageAdditionalNote = true;
    labourDelivery.secondStageAdditionalNote = 'second stage additional note';
    const currentUser = user;
    user.defaultProfile.type = 'Patient';

    const secondStageAdditionalNote = resolver.getSecondStageAdditionalNote(
      currentUser?.defaultProfile,
      labourDelivery,
    );
    expect(secondStageAdditionalNote).toEqual(null);
  });

  it('getThirdStageAdditionalNote() should resolve thirdStageAdditionalNote', () => {
    const labourDelivery = labourDeliveryData;
    labourDelivery.concealThirdStageAdditionalNote = true;
    labourDelivery.thirdStageAdditionalNote = 'third stage additional note';
    const currentUser = user;
    user.defaultProfile.type = 'Patient';

    const thirdStageAdditionalNote = resolver.getThirdStageAdditionalNote(
      currentUser?.defaultProfile,
      labourDelivery,
    );
    expect(thirdStageAdditionalNote).toEqual(null);
  });

  it('getFourthStageAdditionalNote() should resolve fourthStageAdditionalNote', () => {
    const labourDelivery = labourDeliveryData;
    labourDelivery.concealFourthStageAdditionalNote = true;
    labourDelivery.fourthStageAdditionalNote = 'fourth stage additional note';
    const currentUser = user;
    user.defaultProfile.type = 'Patient';

    const fourthStageAdditionalNote = resolver.getFourthStageAdditionalNote(
      currentUser?.defaultProfile,
      labourDelivery,
    );
    expect(fourthStageAdditionalNote).toEqual(null);
  });

  it('getFourthStageCordCare() should resolve fourthStageCordCare', () => {
    const labourDelivery = labourDeliveryData;
    labourDelivery.concealFourthStageCordCare = true;
    labourDelivery.fourthStageCordCare = 'third stage additional note';
    const currentUser = user;
    user.defaultProfile.type = 'Patient';

    const fourthStageCordCare = resolver.getFourthStageCordCare(
      currentUser?.defaultProfile,
      labourDelivery,
    );
    expect(fourthStageCordCare).toEqual(null);
  });

  it('getFourthStageGeneralConditionOfTheBaby() should resolve fourthStageGeneralConditionOfTheBaby', () => {
    const labourDelivery = labourDeliveryData;
    labourDelivery.concealFourthStageGeneralConditionOfTheBaby = true;
    labourDelivery.fourthStageGeneralConditionOfTheBaby =
      'third stage additional note';
    const currentUser = user;
    user.defaultProfile.type = 'Patient';

    const fourthStageGeneralConditionOfTheBaby =
      resolver.getFourthStageGeneralConditionOfTheBaby(
        currentUser?.defaultProfile,
        labourDelivery,
      );
    expect(fourthStageGeneralConditionOfTheBaby).toEqual(null);
  });

  it('getFourthStageGeneralConditionOfTheMother() should resolve fourthStageGeneralConditionOfTheMother', () => {
    const labourDelivery = labourDeliveryData;
    labourDelivery.concealFourthStageGeneralConditionOfTheMother = true;
    labourDelivery.fourthStageGeneralConditionOfTheMother =
      'third stage additional note';
    const currentUser = user;
    user.defaultProfile.type = 'Patient';

    const fourthStageGeneralConditionOfTheMother =
      resolver.getFourthStageGeneralConditionOfTheMother(
        currentUser?.defaultProfile,
        labourDelivery,
      );
    expect(fourthStageGeneralConditionOfTheMother).toEqual(null);
  });

  it('getBill() should get labour and delivery bills', () => {
    const newHospitals = hospitalFactory.buildList(2);
    const newBill = billDetailsFactory.build();
    const profile = profileFactory.build();
    const labourDeliveryBills = resolver.getBill(
      {
        ...profile,
        hospital: newHospitals[0],
      },
      {
        ...labourDeliveryData,
        profile,
        hospital: newHospitals[0],
        createdBy: {
          ...labourDeliveryData.createdBy,
          hospital: newHospitals[0],
          hospitalId: profile.hospitalId,
        },
        bill: newBill,
      },
    );

    expect(labourDeliveryBills).toStrictEqual(newBill);
  });

  it('getBill() should get labour and delivery bills', () => {
    const newHospitals = hospitalFactory.buildList(2);
    const newBill = billDetailsFactory.build();
    const labourDeliveryBills = resolver.getBill(
      {
        ...profile,
        hospitalId: newHospitals[0].id,
        defaultProfile: {
          ...profile,
          hospital: newHospitals[0],
          hospitalId: newHospitals[0].id,
        },
      },
      {
        ...labourDeliveryData,
        profile: profileFactory.build(),
        profileId: profileFactory.build().id,
        hospital: newHospitals[1],
        hospitalId: newHospitals[1].id,
        createdBy: {
          ...labourDeliveryData.createdBy,
          hospital: newHospitals[1],
          hospitalId: profile.hospitalId,
        },
        billing: newBill,
      },
    );

    expect(labourDeliveryBills).toStrictEqual(null);
  });

  it('getPreauthorizationDetails() should get preauthorization details', async () => {
    const labourDeliveryDataToUse = {
      ...labourDeliveryData,
      hospitalId: profile.hospitalId,
    };
    preauthorizationDetailsRepoMock.find.mockReturnValue([
      labourDeliveryDataToUse,
    ]);
    const res2 = await resolver.getPreauthorizationDetails(
      profile,
      labourDeliveryDataToUse,
    );

    expect(res2.id).toEqual(labourDeliveryDataToUse.id);
  });

  it('getHmoClaim() should call getRecordHmoClaim service', async () => {
    const response = await resolver.getHmoClaim(profile, {
      ...labourDeliveryData,
      hospitalId: profile.hospitalId,
      hmoClaimId: 'id',
    });
    expect(profile.hospitalId).toBeTruthy();
    expect(profile.hospitalId.length).toBeGreaterThan(10);
    expect(mockHmoClaimService.getHmoClaim).toHaveBeenCalledWith(profile, 'id');
    expect(response).toEqual(hmoClaimData);
  });

  it('getHmoClaim() should not call getRecordHmoClaim service', async () => {
    const response = await resolver.getHmoClaim(profile, labourDeliveryData);
    expect(profile.hospitalId).toBeTruthy();
    expect(profile.hospitalId.length).toBeGreaterThan(10);
    expect(mockHmoClaimService.getHmoClaim).not.toHaveBeenCalled();
    expect(response).toEqual(null);
  });
});
