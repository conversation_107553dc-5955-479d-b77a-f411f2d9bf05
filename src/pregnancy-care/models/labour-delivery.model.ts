/* eslint-disable max-lines */
import { Field, ID, ObjectType } from '@nestjs/graphql';
import { IsEmpty, IsUUID, IsDate } from 'class-validator';
import {
  Column,
  Entity,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  JoinColumn,
  ManyToOne,
  UpdateDateColumn,
  Index,
  OneToOne,
} from 'typeorm';
import { OrganisationAppointmentModel } from '@clinify/appointments/models/organisation_appointment.model';
import { BillModel } from '@clinify/bills/models/bill.model';
import { HmoClaimModel } from '@clinify/hmo-claims/models/hmo-claim.model';
import { HmoProviderModel } from '@clinify/hmo-providers/models/hmo-provider.model';
import { HospitalModel } from '@clinify/hospitals/models/hospital.model';
import { PreauthorizationDetailsModel } from '@clinify/preauthorization-details/models/preauthorization-details.model';
import { ServiceDetailInput } from '@clinify/shared/validators/service-detail.input';
import { ProfileModel } from '@clinify/users/models/profile.model';

@ObjectType()
export abstract class BaseAudits {
  @IsDate()
  @Field({ nullable: false })
  @CreateDateColumn({ name: 'created_date' })
  createdDate?: Date;

  @IsDate()
  @Field({ nullable: false })
  @UpdateDateColumn({ name: 'updated_date' })
  updatedDate?: Date;

  @Field(() => ProfileModel, { nullable: true })
  @ManyToOne(() => ProfileModel, (profile) => profile, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'updated_by' })
  updatedBy?: ProfileModel;

  @Field(() => String, { nullable: true })
  @Column({ name: 'updated_by', nullable: true })
  lastModifierId?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'last_modifier_name', nullable: true })
  lastModifierName?: string;

  @Field(() => ProfileModel)
  @ManyToOne(() => ProfileModel, (profile) => profile, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'created_by' })
  createdBy?: ProfileModel;

  @Field(() => String, { nullable: true })
  @Column({ name: 'created_by', nullable: true })
  creatorId?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'creator_name' })
  creatorName?: string;
}

@ObjectType()
export abstract class AuditEntitiesWithProfile extends BaseAudits {
  @Field(() => ProfileModel, { nullable: true })
  @ManyToOne(() => ProfileModel, (profile) => profile, {
    onDelete: 'CASCADE',
    nullable: true,
  })
  @JoinColumn({ name: 'profile' })
  profile?: ProfileModel;

  @Index()
  @Column({ name: 'profile' })
  @Field({ nullable: true })
  profileId?: string;
}

@ObjectType()
@Entity({ name: 'labour_delivery' })
export class LabourDeliveryModel extends AuditEntitiesWithProfile {
  @Field(() => ID)
  @PrimaryGeneratedColumn('uuid')
  @IsEmpty()
  @IsUUID('4')
  id: string;

  @Field(() => String, { nullable: true })
  @Column({ nullable: true, name: 'provider' })
  provider: string;

  @IsDate()
  @Column({ name: 'visitation_date_time', nullable: true })
  @Field({ nullable: true })
  visitationDateTime?: Date;

  @Field({ nullable: true })
  @Column({ type: 'text', nullable: true, name: 'gestation_age' })
  gestationAge: string;

  @IsDate()
  @Column({ name: 'estimated_delivery_date', nullable: true })
  @Field({ nullable: true })
  estimatedDeliveryDate?: Date;

  @Field(() => String, { nullable: true })
  @Column({ nullable: true, name: 'induction' })
  induction: string;

  @IsDate()
  @Column({ name: 'induction_date_time', nullable: true })
  @Field({ nullable: true })
  inductionDateTime?: Date;

  @Field(() => String, { nullable: true })
  @Column({ nullable: true, name: 'induced_by' })
  inducedBy: string;

  @Field(() => String, { nullable: true })
  @Column({ nullable: true, name: 'induced_method' })
  inducedMethod: string;

  @Field(() => String, { nullable: true })
  @Column({ nullable: true, name: 'pre_term' })
  preTerm: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'blood_group', nullable: true })
  bloodGroup?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'decision_seeking_care', nullable: true })
  decisionSeekingCare?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'mode_of_transportation', nullable: true })
  modeOfTransportation?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'post_abortion_care', nullable: true })
  postAbortionCare?: string;

  @Field(() => String, { nullable: true })
  @Column({
    name: 'admitted_for_complications_of_unsafe_abortion',
    nullable: true,
  })
  admittedForComplicationsOfUnsafeAbortion?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'abortion', nullable: true })
  abortion?: string;

  @IsDate()
  @Column({ name: 'membranes_rupture_date_time', nullable: true })
  @Field({ nullable: true })
  membranesRuptureDateTime?: Date;

  @IsDate()
  @Column({ name: 'first_stage_start_date_time', nullable: true })
  @Field({ nullable: true })
  firstStageStartDateTime?: Date;

  @Field(() => String, { nullable: true })
  @Column({ name: 'first_stage_duration', nullable: true })
  firstStageDuration?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'first_stage_support_person_present', nullable: true })
  firstStageSupportPersonPresent?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'first_stage_epidural_given', nullable: true })
  firstStageEpiduralGiven?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'partograph_used', nullable: true })
  partographUsed?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'first_stage_maternal_blood_pressure', nullable: true })
  firstStageMaternalBloodPressure?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'first_stage_fetal_heart_rate', nullable: true })
  firstStageFetalHeartRate?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'first_stage_fetal_monitoring', nullable: true })
  firstStageFetalMonitoring?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'first_stage_presentation', nullable: true })
  firstStagePresentation?: string;

  @Field(() => String, { nullable: true })
  @Column({ type: 'text', nullable: true, name: 'first_stage_specify_breech' })
  firstStageSpecifyBreech: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'first_stage_position', nullable: true })
  firstStagePosition?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'first_stage_lie', nullable: true })
  firstStageLie?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'first_stage_fetal_movement', nullable: true })
  firstStageFetalMovement?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'augmentation', nullable: true })
  augmentation?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'augmentation_method', nullable: true })
  augmentationMethod?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'first_stage_mother_status', nullable: true })
  firstStageMotherStatus?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'first_stage_mda_conducted', nullable: true })
  firstStageMdaConducted?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'first_stage_mother_cause_of_death', nullable: true })
  firstStageMotherCauseOfDeath?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'first_stage_baby_status', nullable: true })
  firstStageBabyStatus?: string;

  @Field({ nullable: true })
  @Column({ nullable: true, name: 'first_stage_doctor_name' })
  firstStageDoctorName: string;

  @Field({ nullable: true })
  @Column({ nullable: true, name: 'first_stage_specialty' })
  firstStageSpecialty: string;

  @Field({ nullable: true })
  @Column({ nullable: true, name: 'first_stage_rank' })
  firstStageRank: string;

  @Field({ nullable: true })
  @Column({ nullable: true, name: 'first_stage_department' })
  firstStageDepartment: string;

  @Field({ nullable: true })
  @Column({ nullable: true, name: 'first_stage_nurse_name' })
  firstStageNurseName: string;

  @Field({ nullable: true })
  @Column({ nullable: true, name: 'first_stage_mid_wife' })
  firstStageMidWife: string;

  @Column({ type: 'text', nullable: true, name: 'first_stage_additional_note' })
  firstStageAdditionalNote: string;

  @Field(() => Boolean, { nullable: true })
  @Column({
    nullable: true,
    name: 'conceal_first_stage_additional_note',
    type: 'boolean',
    default: true,
  })
  concealFirstStageAdditionalNote?: boolean;

  @IsDate()
  @Column({ name: 'second_stage_start_date_time', nullable: true })
  @Field({ nullable: true })
  secondStageStartDateTime?: Date;

  @IsDate()
  @Column({ name: 'second_stage_baby_delivery_date_time', nullable: true })
  @Field({ nullable: true })
  secondStageBabyDeliveryDateTime?: Date;

  @Field(() => String, { nullable: true })
  @Column({ name: 'second_stage_duration', nullable: true })
  secondStageDuration?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'second_stage_support_person_present', nullable: true })
  secondStageSupportPersonPresent?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'second_stage_epidural_given', nullable: true })
  secondStageEpiduralGiven?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'second_stage_delivery_method', nullable: true })
  secondStageDeliveryMethod?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'place_of_birth', nullable: true })
  placeOfBirth?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'referred_out', nullable: true })
  referredOut?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'admitted', nullable: true })
  admitted?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'discharged', nullable: true })
  discharged?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'second_stage_maternal_blood_pressure', nullable: true })
  secondStageMaternalBloodPressure?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'second_stage_fetal_heart_rate', nullable: true })
  secondStageFetalHeartRate?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'second_stage_presentation', nullable: true })
  secondStagePresentation?: string;

  @Field(() => String, { nullable: true })
  @Column({ type: 'text', nullable: true, name: 'second_stage_specify_breech' })
  secondStageSpecifyBreech: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'second_stage_position', nullable: true })
  secondStagePosition?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'second_stage_lie', nullable: true })
  secondStageLie?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'second_stage_fetal_movement', nullable: true })
  secondStageFetalMovement?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'episiotomy', nullable: true })
  episiotomy?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'gender', nullable: true })
  gender?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'live_birth_weight', nullable: true })
  liveBirthWeight?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'birth_weight', nullable: true })
  birthWeight?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'birth_weight_unit', type: 'text', nullable: true })
  birthWeightUnit: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'not_breathing_or_crying', nullable: true })
  notBreathingOrCrying?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'baby_resuscitated', nullable: true })
  babyResuscitated?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'cord_clamp_time', nullable: true })
  cordClampTime?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'chx_gel_applied', nullable: true })
  chxGelApplied?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'second_stage_mother_status', nullable: true })
  secondStageMotherStatus?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'second_stage_mda_conducted', nullable: true })
  secondStageMdaConducted?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'second_stage_mother_cause_of_death', nullable: true })
  secondStageMotherCauseOfDeath?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'second_stage_baby_status', nullable: true })
  secondStageBabyStatus?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'still_birth', nullable: true })
  stillBirth?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'live_hiv_birth', nullable: true })
  liveHivBirth?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'delivered_by', nullable: true })
  deliveredBy?: string;

  @Field({ nullable: true })
  @Column({ nullable: true, name: 'second_stage_doctor_name' })
  secondStageDoctorName: string;

  @Field({ nullable: true })
  @Column({ nullable: true, name: 'second_stage_specialty' })
  secondStageSpecialty: string;

  @Field({ nullable: true })
  @Column({ nullable: true, name: 'second_stage_rank' })
  secondStageRank: string;

  @Field({ nullable: true })
  @Column({ nullable: true, name: 'second_stage_department' })
  secondStageDepartment: string;

  @Field({ nullable: true })
  @Column({ nullable: true, name: 'second_stage_nurse_name' })
  secondStageNurseName: string;

  @Field({ nullable: true })
  @Column({ nullable: true, name: 'second_stage_mid_wife' })
  secondStageMidWife: string;

  @Field({ nullable: true })
  @Column({ nullable: true, name: 'second_stage_marternal_complication' })
  secondStageMarternalComplication: string;

  @Column({
    type: 'text',
    nullable: true,
    name: 'second_stage_additional_note',
  })
  secondStageAdditionalNote: string;

  @Field(() => Boolean, { nullable: true })
  @Column({
    nullable: true,
    name: 'conceal_second_stage_additional_note',
    type: 'boolean',
    default: true,
  })
  concealSecondStageAdditionalNote?: boolean;

  @IsDate()
  @Column({ name: 'third_stage_start_date_time', nullable: true })
  @Field({ nullable: true })
  thirdStageStartDateTime?: Date;

  @IsDate()
  @Column({ name: 'placenta_date_time', nullable: true })
  @Field({ nullable: true })
  placentaDateTime?: Date;

  @Field(() => String, { nullable: true })
  @Column({ name: 'third_stage_duration', nullable: true })
  thirdStageDuration?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'placenta_delivery_method', nullable: true })
  placentaDeliveryMethod?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'placenta_complete', nullable: true })
  placentaComplete?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'configuration', nullable: true })
  configuration?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'blood_loss_estimate', nullable: true })
  bloodLossEstimate?: string;

  @Field({ nullable: true })
  @Column({ nullable: true, name: 'osytocin_receiveed' })
  osytocinReceiveed: string;

  @Field({ nullable: true })
  @Column({ nullable: true, name: 'misoprostol_received' })
  misoprostolReceived: string;

  @Field({ nullable: true })
  @Column({ nullable: true, name: 'received_mgso4_with_eclampsia' })
  receivedMgso4WithEclampsia: string;

  @Field({ nullable: true })
  @Column({ nullable: true, name: 'cervical_tear' })
  cervicalTear: string;

  @Field({ nullable: true })
  @Column({ nullable: true, name: 'perineal_laceration' })
  perinealLaceration: string;

  @Field({ nullable: true })
  @Column({ nullable: true, name: 'birth_injury' })
  birthInjury: string;

  @Field({ nullable: true })
  @Column({ nullable: true, name: 'other_trauma' })
  otherTrauma: string;

  @Field({ nullable: true })
  @Column({ nullable: true, name: 'repair' })
  repair: string;

  @Field({ nullable: true })
  @Column({ nullable: true, name: 'specify_repair' })
  specifyRepair: string;

  @Field({ nullable: true })
  @Column({ nullable: true, name: 'anesthesia_type' })
  anesthesiaType: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'anesthesia_given', nullable: true })
  anesthesiaGiven?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'third_stage_mother_status', nullable: true })
  thirdStageMotherStatus?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'third_stage_mda_conducted', nullable: true })
  thirdStageMdaConducted?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'third_stage_mother_cause_of_death', nullable: true })
  thirdStageMotherCauseOfDeath?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'third_stage_baby_status', nullable: true })
  thirdStageBabyStatus?: string;

  @Field({ nullable: true })
  @Column({ nullable: true, name: 'third_stage_doctor_name' })
  thirdStageDoctorName: string;

  @Field({ nullable: true })
  @Column({ nullable: true, name: 'third_stage_specialty' })
  thirdStageSpecialty: string;

  @Field({ nullable: true })
  @Column({ nullable: true, name: 'third_stage_rank' })
  thirdStageRank: string;

  @Field({ nullable: true })
  @Column({ nullable: true, name: 'third_stage_department' })
  thirdStageDepartment: string;

  @Field({ nullable: true })
  @Column({ nullable: true, name: 'third_stage_nurse_name' })
  thirdStageNurseName: string;

  @Field({ nullable: true })
  @Column({ nullable: true, name: 'third_stage_mid_wife' })
  thirdStageMidWife: string;

  @Column({
    type: 'text',
    nullable: true,
    name: 'third_stage_additional_note',
  })
  thirdStageAdditionalNote: string;

  @Field(() => Boolean, { nullable: true })
  @Column({
    nullable: true,
    name: 'conceal_third_stage_additional_note',
    type: 'boolean',
    default: true,
  })
  concealThirdStageAdditionalNote?: boolean;

  @Field(() => String, { nullable: true })
  @Column({ nullable: true, name: 'facility_name' })
  facilityName?: string;

  @Field(() => String, { nullable: true })
  @Column({ nullable: true, name: 'facility_address' })
  facilityAddress?: string;

  @Column({ type: 'text', nullable: true, name: 'additional_note' })
  additionalNote: string;

  @Field(() => Boolean, { nullable: true })
  @Column({
    nullable: true,
    name: 'conceal_additional_note',
    type: 'boolean',
    default: true,
  })
  concealAdditionalNote?: boolean;

  @Field(() => [String], { nullable: true })
  @Column({ name: 'document_url', type: 'text', nullable: true, array: true })
  documentUrl: string[];

  @Column({ nullable: false, name: 'archived', default: false })
  archived: boolean;

  @Field(() => HospitalModel, { nullable: true })
  @ManyToOne(() => HospitalModel, (hospital) => hospital.labourDelivery)
  @JoinColumn({ name: 'hospital' })
  hospital: HospitalModel;

  @Field(() => String, { nullable: true })
  @Index()
  @Column({ name: 'hospital', nullable: true })
  hospitalId?: string;

  @Field({ nullable: true })
  @Column({ name: 'sub_bill_ref', nullable: true })
  subBillRef: string;

  @Field(() => [ServiceDetailInput], { nullable: true })
  @Column({
    nullable: true,
    name: 'service_details',
    type: 'jsonb',
  })
  serviceDetails: ServiceDetailInput[];

  @Field(() => Boolean)
  @Column({
    name: 'is_package',
    type: 'boolean',
    nullable: false,
    default: false,
  })
  isPackage: boolean;

  @ManyToOne(() => BillModel, { nullable: true, onDelete: 'SET NULL' })
  @JoinColumn({ name: 'bill' })
  bill?: BillModel;

  @Field(() => String, { nullable: true })
  @Column({ name: 'bill', nullable: true })
  billId: string;

  @Field(() => String, { nullable: true, defaultValue: 'Pending' })
  @Column({
    nullable: false,
    name: 'bill_status',
    type: 'text',
    default: 'Pending',
  })
  billStatus?: string;

  @Field(() => OrganisationAppointmentModel, { nullable: true })
  @OneToOne(
    () => OrganisationAppointmentModel,
    (appointment) => appointment.labourDelivery,
    {
      onDelete: 'SET NULL',
    },
  )
  @JoinColumn({ name: 'appointment_id' })
  appointment?: OrganisationAppointmentModel;

  @Field(() => String, { nullable: true })
  @Column({ name: 'appointment_id', nullable: true })
  appointmentId: string;

  @OneToOne(
    () => PreauthorizationDetailsModel,
    (preauthDetails) => preauthDetails.labourDelivery,
    { onDelete: 'RESTRICT', nullable: true },
  )
  preauthorizationDetails?: PreauthorizationDetailsModel;

  @ManyToOne(() => HmoProviderModel, { nullable: true })
  @JoinColumn({ name: 'hmo_provider_id' })
  hmoProvider?: HmoProviderModel;

  @Field(() => String, { nullable: true })
  @Column({ name: 'hmo_provider_id', nullable: true })
  hmoProviderId?: string;

  @Column({ name: 'hmo_claim_id', nullable: true })
  hmoClaimId?: string;

  @ManyToOne(() => HmoClaimModel, (hmoClaim) => hmoClaim.labourDeliveries, {
    nullable: true,
    onDelete: 'SET NULL',
  })
  @JoinColumn({ name: 'hmo_claim_id' })
  hmoClaim?: HmoClaimModel;

  @Field(() => String, { nullable: true })
  @Column({ name: 'type_of_client', nullable: true })
  typeOfClient?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'current_pregnancy_number', nullable: true })
  currentPregnancyNumber?: string;

  @Field({ nullable: true })
  @Column({ nullable: true, name: 'parity' })
  parity: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'first_stage_maternal_temperature', nullable: true })
  firstStageMaternalTemperature?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'first_stage_maternal_temperature_unit', nullable: true })
  firstStageMaternalTemperatureUnit?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'first_stage_maternal_pulse_rate', nullable: true })
  firstStageMaternalPulseRate?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'first_stage_maternal_respiratory_rate', nullable: true })
  firstStageMaternalRespiratoryRate?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'second_stage_maternal_temperature', nullable: true })
  secondStageMaternalTemperature?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'second_stage_maternal_temperature_unit', nullable: true })
  secondStageMaternalTemperatureUnit?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'second_stage_maternal_pulse_rate', nullable: true })
  secondStageMaternalPulseRate?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'second_stage_maternal_respiratory_rate', nullable: true })
  secondStageMaternalRespiratoryRate?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'second_stage_mouth_and_nose_suctioned', nullable: true })
  secondStageMouthAndNoseSuctioned?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'baby_cause_of_death', nullable: true })
  babyCauseOfDeath?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'second_stage_head_circumference_of_baby', nullable: true })
  secondStageHeadCircumferenceOfBaby?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'second_stage_chest_circumference_of_baby', nullable: true })
  secondStageChestCircumferenceOfBaby?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'second_stage_body_length_of_baby', nullable: true })
  secondStageBodyLengthOfBaby?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'second_stage_baby_respiratory_rate', nullable: true })
  secondStageBabyRespiratoryRate?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'second_stage_baby_heart_rate', nullable: true })
  secondStageBabyHeartRate?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'second_stage_temperature_at_1_hr', nullable: true })
  secondStageTemperatureAt1hr?: string;

  @Field(() => String, { nullable: true })
  @Column({
    name: 'second_stage_temperature_at_1_hr_unit',
    nullable: true,
  })
  secondStageTemperatureAt1hrUnit?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'fourth_stage_postpartum_complications', nullable: true })
  fourthStagePostpartumComplications?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'baby_put_to_breast', nullable: true })
  babyPutToBreast?: string;

  @Field(() => String, { nullable: true })
  @Column({
    name: 'fourth_stage_time_of_initiating_breastfeeding',
    nullable: true,
  })
  fourthStageTimeOfInitiatingBreastfeeding?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'fourth_stage_post_delivery_massage_given', nullable: true })
  fourthStagePostDeliveryMassageGiven?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'fourth_stage_cervical_tear', nullable: true })
  fourthStageCervicalTear?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'fourth_stage_perineal_laceration', nullable: true })
  fourthStagePerinealLaceration?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'fourth_stage_birth_injury', nullable: true })
  fourthStageBirthInjury?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'fourth_stage_vulva_care_given', nullable: true })
  fourthStageVulvaCareGiven?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'fourth_stage_other_trauma', nullable: true })
  fourthStageOtherTrauma?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'fourth_stage_repair', nullable: true })
  fourthStageRepair?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'fourth_stage_specify_repair', nullable: true })
  fourthStageSpecifyRepair?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'fourth_stage_anesthesia_type', nullable: true })
  fourthStageAnesthesiaType?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'fourth_stage_anesthesia_given', nullable: true })
  fourthStageAnesthesiaGiven?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'fourth_stage_vitamin_k_given_to_baby', nullable: true })
  fourthStageVitaminKGivenToBaby?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'fourth_stage_vitamin_k_dose_given_to_baby', nullable: true })
  fourthStageVitaminKDoseGivenToBaby?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'fourth_stage_maternal_temperature', nullable: true })
  fourthStageMaternalTemperature?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'fourth_stage_maternal_temperature_unit', nullable: true })
  fourthStageMaternalTemperatureUnit?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'fourth_stage_maternal_pulse_rate', nullable: true })
  fourthStageMaternalPulseRate?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'fourth_stage_baby_temperature', nullable: true })
  fourthStageBabyTemperature?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'fourth_stage_baby_temperature_unit', nullable: true })
  fourthStageBabyTemperatureUnit?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'fourth_stage_baby_heart_rate', nullable: true })
  fourthStageBabyHeartRate?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'fourth_stage_baby_respiratory_rate', nullable: true })
  fourthStageBabyRespiratoryRate?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'fourth_stage_status_of_mother', nullable: true })
  fourthStageStatusOfMother?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'fourth_stage_mda_conducted', nullable: true })
  fourthStageMdaConducted: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'fourth_stage_mother_cause_of_death', nullable: true })
  fourthStageMotherCauseOfDeath: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'fourth_stage_status_of_baby', nullable: true })
  fourthStageStatusOfBaby?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'fourth_stage_postpartum_depression', nullable: true })
  fourthStagePostpartumDepression?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'danger_signs', nullable: true })
  dangerSigns?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'first_dose_of_antibiotics_and_referred', nullable: true })
  firstDoseOfAntibioticsAndReferred?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'neonatal_tetanus', nullable: true })
  neonatalTetanus?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'neonatal_jaundice', nullable: true })
  neonatalJaundice?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'admitted_on_kmc', nullable: true })
  admittedOnKMC?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'discharged_after_kmc', nullable: true })
  dischargedAfterKMC?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'given_llin', nullable: true })
  givenLlin?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'birth_certificate_issued', nullable: true })
  birthCertificateIssued?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'birth_certificate_collected', nullable: true })
  birthCertificateCollected?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'fourth_stage_apgar_score_a', nullable: true })
  fourthStageApgarScoreA?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'fourth_stage_apgar_score_p', nullable: true })
  fourthStageApgarScoreP?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'fourth_stage_apgar_score_g', nullable: true })
  fourthStageApgarScoreG?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'fourth_stage_apgar_score_apperance', nullable: true })
  fourthStageApgarScoreApperance?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'fourth_stage_apgar_score_r', nullable: true })
  fourthStageApgarScoreR?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'fourth_stage_apgar_score', nullable: true })
  fourthStageApgarScore?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'fourth_stage_cord_care', nullable: true })
  fourthStageCordCare?: string;

  @Field(() => String, { nullable: true })
  @Column({
    name: 'fourth_stage_general_condition_of_the_baby',
    nullable: true,
  })
  fourthStageGeneralConditionOfTheBaby?: string;

  @Field(() => String, { nullable: true })
  @Column({
    name: 'fourth_stage_general_condition_of_the_mother',
    nullable: true,
  })
  fourthStageGeneralConditionOfTheMother?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'fourth_stage_doctor_name', nullable: true })
  fourthStageDoctorName?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'fourth_stage_specialty', nullable: true })
  fourthStageSpecialty?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'fourth_stage_rank', nullable: true })
  fourthStageRank?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'fourth_stage_department', nullable: true })
  fourthStageDepartment?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'fourth_stage_nurse_name', nullable: true })
  fourthStageNurseName?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'fourth_stage_mid_wife', nullable: true })
  fourthStageMidWife?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'fourth_stage_additional_note', nullable: true })
  fourthStageAdditionalNote?: string;

  @Field(() => Boolean, { nullable: true })
  @Column({
    name: 'conceal_fourth_stage_additional_note',
    nullable: true,
    type: 'boolean',
    default: true,
  })
  concealFourthStageAdditionalNote?: boolean;

  @Field(() => Boolean, { nullable: true })
  @Column({
    name: 'conceal_fourth_stage_cord_care',
    nullable: true,
    type: 'boolean',
    default: true,
  })
  concealFourthStageCordCare?: boolean;

  @Field(() => Boolean, { nullable: true })
  @Column({
    name: 'conceal_fourth_stage_general_condition_of_the_baby',
    nullable: true,
    type: 'boolean',
    default: true,
  })
  concealFourthStageGeneralConditionOfTheBaby?: boolean;

  @Field(() => Boolean, { nullable: true })
  @Column({
    name: 'conceal_fourth_stage_general_condition_of_the_mother',
    nullable: true,
    type: 'boolean',
    default: true,
  })
  concealFourthStageGeneralConditionOfTheMother?: boolean;
  constructor(labourAndDeliveryModel: Partial<LabourDeliveryModel>) {
    super();
    Object.assign(this, labourAndDeliveryModel);
  }
}
