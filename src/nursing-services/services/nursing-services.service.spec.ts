/* eslint-disable max-lines */
import { NotFoundException } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Chance } from 'chance';
import { DataSource, EntityManager } from 'typeorm';
import { NursingServiceService } from './nursing-services.service';
import { NursingServiceModel } from '../models/nursing-services.model';
import { NewNursingServicesInput } from '../validators/nursing-services.input';
import {
  billDetailsFactory,
  billFactory,
} from '@clinify/__mocks__/factories/bill.factory';
import { hmoClaimFactory } from '@clinify/__mocks__/factories/hmo-claim.factory';
import {
  nursingServiceDetailFactory,
  nursingServiceFactory,
  nursingServiceProgressNoteFactory,
} from '@clinify/__mocks__/factories/nursing-service.factory';
import { profileFactory } from '@clinify/__mocks__/factories/profile.factory';
import { userFactory } from '@clinify/__mocks__/factories/user.factory';
import { MockHmoClaimService } from '@clinify/__mocks__/hmo-claim.mock';
import { loggerMock } from '@clinify/__mocks__/logger';
import { AdmissionModel } from '@clinify/admissions/models/admission.model';
import { BillService } from '@clinify/bills/services/bill.service';
import { ConsultationModel } from '@clinify/consultations/models/consultation.model';
import { HmoClaimService } from '@clinify/hmo-claims/services/hmo-claim.service';
import { ImmunizationModel } from '@clinify/immunizations/models/immunization.model';
import { MedicationModel } from '@clinify/medications/models/medication.model';
import { NursingServiceToInvestigation } from '@clinify/nursing-services/models/nursing-services_investigation.model';
import { UserType } from '@clinify/shared/enums/users';
import { LinkService } from '@clinify/shared/services/dashboard-link.service';
import { SurgeryModel } from '@clinify/surgeries/models/surgery.model';
import { ProfileModel } from '@clinify/users/models/profile.model';
import { UserModel } from '@clinify/users/models/user.model';
import { VitalModel } from '@clinify/vitals/models/vital.model';

const mockedUuid = '86e7623d-031c-4ebd-b606-70584833577b';

jest.mock('uuid', () => ({
  v4: () => mockedUuid,
}));

const mockUser = userFactory.build();
const nursingServicesData = nursingServiceFactory.buildList(3);
const nursingServiceDetails = nursingServiceDetailFactory.buildList(3);
const nursingServiceData = nursingServicesData[1];
nursingServiceData.details = nursingServiceDetails;
const chance = new Chance();
const hmoClaim = hmoClaimFactory.build();
hmoClaim.itemId = 'item-id';
const profile = profileFactory.build();
const mockEntityManagerSave = jest.fn(() => nursingServiceData);
const MockNursingServiceDelete = jest.fn(() => [
  {
    ...nursingServiceData,
    subBillRef: 'sub-bill-ref',
  },
]);
const MockNursingServiceDetails = jest.fn(() => [nursingServiceDetails]);
const MockNursingServiceRepository = {
  getOneNursingService: jest.fn(() => nursingServiceData),
  findByUserProfile: jest.fn(() => nursingServicesData),
  archiveNursingServices: jest.fn(() => [nursingServiceData]),
  updateNursingServicesConsentSignature: jest.fn(() => nursingServiceData),
  saveNursingServicesDetails: MockNursingServiceDetails,
  updateNursingServicesDetails: MockNursingServiceDetails,
  deleteNursingServicesDetails: MockNursingServiceDetails,
  saveNursingServicesProgressNote: jest.fn(() => ({})),
  updateNursingServicesProgressNote: jest.fn(() => ({})),
  deleteNursingServicesProgressNote: jest.fn(() => ({})),
  concealNursingServicesProgressNote: jest.fn(() => ({})),
  concealNursingServiceAdditionalNote: jest.fn(() => ({})),
  isVitalsLinked: jest.fn(() => Promise.resolve(true)),
  isAdmissionsLinked: jest.fn(() => Promise.resolve(false)),
  isImmunizationsLinked: jest.fn(() => Promise.resolve(false)),
  isConsultationsLinked: jest.fn(() => Promise.resolve(false)),
  isSurgeriesLinked: jest.fn(() => Promise.resolve(false)),
  isMedicationsLinked: jest.fn(() => Promise.resolve(false)),
  isRadiologyInvestigationLinked: jest.fn(() => Promise.resolve(false)),
  isLaboratoryInvestigationLinked: jest.fn(() => Promise.resolve(false)),
  getLinkedRadiologyInvestigationRecords: jest.fn(() => Promise.resolve([])),
  getLinkedLaboratoryInvestigationRecords: jest.fn(() => Promise.resolve([])),
  getLinkedInvestigationRecords: jest.fn(() => Promise.resolve([])),
};
const MockVitalRepository = {};

const commonRepoMethods = {
  update: jest.fn().mockReturnThis(),
  set: jest.fn().mockReturnThis(),
  where: jest.fn().mockReturnThis(),
  execute: jest.fn(),
  save: mockEntityManagerSave,
  updateNursingService: jest.fn(() => [nursingServiceData, nursingServiceData]),
  deleteNursingServices: MockNursingServiceDelete,
  saveNursingServicesDetails: MockNursingServiceDetails,
  updateNursingServicesDetails: MockNursingServiceDetails,
  deleteNursingServicesDetails: MockNursingServiceDetails,
  createQueryBuilder: jest.fn(() => ({
    innerJoin: jest.fn().mockReturnThis(),
    getMany: jest.fn(() => Promise.resolve([])),
  })),
};

const MockLinkService = {
  getLinkedConnections: jest.fn(() => ({})),
};

const ManagerMock = {
  getCustomRepository: jest.fn().mockReturnValue({
    save: mockEntityManagerSave,
    updateNursingService: jest.fn(() => [
      nursingServiceData,
      nursingServiceData,
    ]),
    deleteNursingServices: MockNursingServiceDelete,
    saveNursingServicesDetails: MockNursingServiceDetails,
    updateNursingServicesDetails: MockNursingServiceDetails,
    deleteNursingServicesDetails: MockNursingServiceDetails,
  }),
  createQueryBuilder: jest.fn(() => commonRepoMethods),
  withRepository: jest.fn(() => commonRepoMethods),
  queryRunner: { isTransactionActive: true },
};
const mockBill = billFactory.build();
const BillServiceMock = {
  updateMultipleBill: jest.fn(() => billFactory.build()),

  addOrgBill: jest.fn(() => billFactory.build()),
  generateBill: jest.fn(() => mockBill),
  deleteAutoGeneratedBill: jest.fn(),
  deleteAutoGeneratedBillItems: jest.fn(() => [mockBill]),
  generateEmptyBillIfNone: jest.fn(() => ({
    ...mockBill,
    details: [],
  })),
  generateMultipleBill: jest.fn(() => mockBill),
  updateBillDetail: jest.fn(),
};

const dsMock = {
  getRepository: jest.fn(() => ({
    extend: jest.fn(),
  })),
  transaction: jest.fn((cb) => cb(ManagerMock)),
  manager: ManagerMock,
};

const MockProfileRepository = {
  findOne: jest.fn(() => mockUser.defaultProfile),
};

describe('NursingServiceService', () => {
  let service: NursingServiceService;
  const user = mockUser;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        NursingServiceService,
        {
          provide: getRepositoryToken(NursingServiceModel),
          useValue: MockNursingServiceRepository,
        },
        {
          provide: EntityManager,
          useValue: ManagerMock,
        },
        {
          provide: DataSource,
          useValue: dsMock,
        },
        {
          provide: BillService,
          useValue: BillServiceMock,
        },
        {
          provide: getRepositoryToken(ProfileModel),
          useValue: MockProfileRepository,
        },
        {
          provide: getRepositoryToken(VitalModel),
          useValue: MockVitalRepository,
        },
        {
          provide: getRepositoryToken(AdmissionModel),
          useValue: {},
        },
        {
          provide: getRepositoryToken(ConsultationModel),
          useValue: {},
        },
        {
          provide: getRepositoryToken(SurgeryModel),
          useValue: {},
        },
        {
          provide: getRepositoryToken(MedicationModel),
          useValue: {},
        },
        {
          provide: getRepositoryToken(ImmunizationModel),
          useValue: {},
        },
        {
          provide: getRepositoryToken(NursingServiceToInvestigation),
          useValue: {},
        },
        {
          provide: HmoClaimService,
          useValue: MockHmoClaimService,
        },
        {
          provide: LinkService,
          useValue: MockLinkService,
        },
        { ...loggerMock },
      ],
    }).compile();

    service = module.get<NursingServiceService>(NursingServiceService);
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
    expect(service).toBeTruthy();
  });

  it('nursingService(): should get a nursing service record', async () => {
    const nursingServiceInput = nursingServiceFactory.build();
    delete nursingServiceInput.profile;
    await service.nursingService(user.defaultProfile, nursingServiceInput.id);
    expect(
      MockNursingServiceRepository.getOneNursingService,
    ).toHaveBeenCalledWith(user.defaultProfile, nursingServiceInput.id);
  });

  it('getAllNursingServices(): should get all nursing services', async () => {
    const input = nursingServiceFactory.build();
    const mutator = profileFactory.build();
    const profile = profileFactory.build();
    await service.getAllNursingServices(mutator, profile.id, input);
    expect(MockNursingServiceRepository.findByUserProfile).toHaveBeenCalledWith(
      mutator,
      profile.id,
      input,
    );
  });

  it('saveNursingService() should save a nursing service using user profile', async () => {
    const input: NewNursingServicesInput = nursingServiceFactory.build({
      serviceDetails: [
        {
          type: 'service-type-1',
          name: 'service-name-1',
          quantity: '1',
          pricePerUnit: '14000',
          itemId: 'dasw223ew',
        },
        {
          type: 'service-type-2',
          name: 'service-name-2',
          quantity: '2',
          pricePerUnit: '1100',
          itemId: '',
        },
        {
          type: 'service-type-3',
          name: 'service-name-3',
          quantity: '4',
          pricePerUnit: '300',
          itemId: '',
          reference: 'sub-bill-reference',
        },
      ],
    });
    input.details = nursingServiceDetailFactory.buildList(1);
    input.progressNotes = nursingServiceProgressNoteFactory.buildList(1);
    await service.saveNursingService(user.defaultProfile, input);
    expect(mockEntityManagerSave).toHaveBeenCalled();
  });

  it('saveNursingService() should save a nursing service using user profile', async () => {
    const input: NewNursingServicesInput = nursingServiceFactory.build();
    input.details = nursingServiceDetailFactory.buildList(1);
    input.progressNotes = nursingServiceProgressNoteFactory.buildList(1);
    input.serviceDetails = [
      {
        pricePerUnit: 100,
        itemId: 'item-id',
        priceId: 'price-id',
        type: 'service',
        name: 'service-name',
        quantity: 1,
      },
    ];
    input.hmoClaim = hmoClaim;
    MockHmoClaimService.createHmoClaimInTransaction.mockReturnValue(hmoClaim);
    const profile = profileFactory.build();
    profile.type = UserType.OrganizationDoctor;
    await service.saveNursingService(profile, input);
    expect(MockHmoClaimService.createHmoClaimInTransaction).toHaveBeenCalled();
    expect(mockEntityManagerSave).toHaveBeenCalled();
    expect(BillServiceMock.generateMultipleBill).toHaveBeenCalled();
  });

  it('saveNursingService() should save a nursing service for another user', async () => {
    const newUser = mockUser as UserModel;
    const input: NewNursingServicesInput = nursingServiceFactory.build();
    input.details = nursingServiceDetailFactory.buildList(1);
    input.progressNotes = nursingServiceProgressNoteFactory.buildList(1);
    await service.saveNursingService(
      { ...newUser.defaultProfile, id: 'different-profile-id' } as any,
      input,
    );
    expect(mockEntityManagerSave).toHaveBeenCalled();
  });

  it('updateNursingService(): should update a nursing service', async () => {
    const nursingServiceInput = nursingServiceFactory.build({
      serviceDetails: [
        {
          type: 'service-type-1',
          name: 'service-name-1',
          quantity: '1',
          pricePerUnit: '14000',
          itemId: 'dasw223ew',
        },
        {
          type: 'service-type-2',
          name: 'service-name-2',
          quantity: '2',
          pricePerUnit: '1100',
          itemId: '',
        },
        {
          type: 'service-type-3',
          name: 'service-name-3',
          quantity: '4',
          pricePerUnit: '300',
          itemId: '',
          reference: 'sub-bill-reference',
        },
      ],
    });
    delete nursingServiceInput.profile;
    await service.updateNursingService(
      user.defaultProfile,
      nursingServiceInput,
      nursingServiceInput.id,
    );
    nursingServiceInput.serviceDetails = [
      {
        type: 'service-type-1',
        name: 'service-name-1',
        quantity: '1',
        pricePerUnit: '14000',
        itemId: 'dasw223ew',
      },
      {
        type: 'service-type-2',
        name: 'service-name-2',
        quantity: '2',
        pricePerUnit: '1100',
        itemId: '',
        reference: mockedUuid,
      },
      {
        type: 'service-type-3',
        name: 'service-name-3',
        quantity: '4',
        pricePerUnit: '300',
        itemId: '',
        reference: 'sub-bill-reference',
      },
    ];
    expect(
      ManagerMock.withRepository().updateNursingService,
    ).toHaveBeenCalledWith(
      user.defaultProfile,
      nursingServiceInput,
      nursingServiceInput.id,
      undefined,
    );
  });

  it('updateNursingService(): should update a nursing service', async () => {
    const nursingServiceInput = nursingServiceFactory.build({
      serviceDetails: [
        {
          type: 'service-type-1',
          name: 'service-name-1',
          quantity: '1',
          pricePerUnit: '14000',
          itemId: 'dasw223ew',
        },
        {
          type: 'service-type-2',
          name: 'service-name-2',
          quantity: '2',
          pricePerUnit: '1100',
          itemId: '',
        },
        {
          type: 'service-type-3',
          name: 'service-name-3',
          quantity: '4',
          pricePerUnit: '300',
          itemId: '',
          reference: 'sub-bill-reference',
        },
      ],
    });
    delete nursingServiceInput.profile;
    await service.updateNursingService(
      user.defaultProfile,
      nursingServiceInput,
      nursingServiceInput.id,
      true,
    );
    nursingServiceInput.serviceDetails = [
      {
        type: 'service-type-1',
        name: 'service-name-1',
        quantity: '1',
        pricePerUnit: '14000',
        itemId: 'dasw223ew',
      },
      {
        type: 'service-type-2',
        name: 'service-name-2',
        quantity: '2',
        pricePerUnit: '1100',
        itemId: '',
        reference: mockedUuid,
      },
      {
        type: 'service-type-3',
        name: 'service-name-3',
        quantity: '4',
        pricePerUnit: '300',
        itemId: '',
        reference: 'sub-bill-reference',
      },
    ];
    expect(
      ManagerMock.withRepository().updateNursingService,
    ).toHaveBeenCalledWith(
      user.defaultProfile,
      nursingServiceInput,
      nursingServiceInput.id,
      true,
    );
  });

  it('updateNursingService(): should update nursing service and hmoClaim', async () => {
    const nursingServiceInput = nursingServiceFactory.build();
    delete nursingServiceInput.profile;
    const hmoClaim = hmoClaimFactory.build();
    delete hmoClaim.profile;
    nursingServiceInput.hmoClaim = hmoClaim;
    await service.updateNursingService(
      user.defaultProfile,
      nursingServiceInput,
      nursingServiceInput.id,
    );
  });

  it('updateNursingService(): should throw error if nursing service not found', async () => {
    ManagerMock.withRepository().updateNursingService.mockReturnValue([
      null,
      null,
    ]);

    await expect(
      service.updateNursingService(
        user.defaultProfile,
        {
          id: 'fake-id',
          details: [],
          billInfo: [],
        },
        'fake-id',
      ),
    ).rejects.toThrow(NotFoundException);
  });
  it('updateNursingService(): should update an existing  nursing service record with service type', async () => {
    const nursingServiceInput = nursingServiceFactory.build();
    const serviceDetail = {
      itemId: '',
      pricePerUnit: '',
      quantity: '1',
      name: '',
    };

    ManagerMock.withRepository().updateNursingService.mockReturnValue([
      {
        ...nursingServiceInput,
        serviceDetails: [serviceDetail],
      },
      { ...nursingServiceData, serviceDetails: [serviceDetail, serviceDetail] },
    ]);
    await service.updateNursingService(
      { ...profile, type: 'OrganizationDoctor' },
      {
        ...nursingServiceInput,
        billInfo: [
          {
            itemId: 'oqwae_ws',
            type: chance.word(),
            name: chance.word(),
          },
          {
            itemId: 'z_seewd',
            type: chance.word(),
            name: chance.word(),
          },
        ],
      },
      nursingServiceInput.id,
    );
  });
  it('deleteNursingServices(): should delete nursing services', async () => {
    const nursingServiceInput = nursingServiceFactory.build();
    delete nursingServiceInput.profile;
    await service.deleteNursingServices(user.defaultProfile, [
      nursingServiceInput.id,
    ]);
    expect(MockNursingServiceDelete).toHaveBeenCalledWith(user.defaultProfile, [
      nursingServiceInput.id,
    ]);
  });

  it('deleteNursingServices(): should delete nursing services with hmo claim', async () => {
    ManagerMock.withRepository().deleteNursingServices = jest.fn(() => [
      {
        ...nursingServiceData,
        subBillRef: 'sub-bill-ref',
        hmoClaim: {
          id: 'fake-id',
        },
      },
    ]);
    await service.deleteNursingServices(user.defaultProfile, [
      nursingServiceData.id,
    ]);
    expect(
      MockHmoClaimService.flagHmoClaimDeletedInTransaction,
    ).toHaveBeenCalled();
  });

  it('archiveNursingServices(): should archive nursing services', async () => {
    const nursingServiceInput = nursingServiceFactory.build();
    delete nursingServiceInput.profile;
    await service.archiveNursingServices(
      user.defaultProfile,
      [nursingServiceInput.id],
      true,
    );
    expect(
      MockNursingServiceRepository.archiveNursingServices,
    ).toHaveBeenCalledWith(
      user.defaultProfile,
      [nursingServiceInput.id],
      true,
      BillServiceMock,
    );
  });

  it(`saveNursingServicesConsentSignature():
    should call the updateNursingServicesConsentSignature repository with shouldSave true`, async () => {
    await service.saveNursingServicesConsentSignature(
      user.defaultProfile,
      'record-id',
      {
        patientConsentSignature: 'John Doe',
        patientConsentSignatureType: 'uploads',
      },
    );

    expect(
      MockNursingServiceRepository.updateNursingServicesConsentSignature,
    ).toHaveBeenLastCalledWith(
      user.defaultProfile,
      'record-id',
      'John Doe',
      'uploads',
      true,
    );
  });

  it(`updateNursingServicesConsentSignature():
    should call the updateNursingServicesConsentSignature repository without shouldSave true`, async () => {
    await service.updateNursingServicesConsentSignature(
      user.defaultProfile,
      'record-id',
      {
        patientConsentSignature: 'Mary Slice',
        patientConsentSignatureType: 'draw',
      },
    );

    expect(
      MockNursingServiceRepository.updateNursingServicesConsentSignature,
    ).toHaveBeenLastCalledWith(
      user.defaultProfile,
      'record-id',
      'Mary Slice',
      'draw',
    );
  });

  it(`removeNursingServicesConsentSignature():
    should call the removeNursingServicesConsentSignature repository without empty signature and signature type`, async () => {
    await service.removeNursingServicesConsentSignature(
      user.defaultProfile,
      'record-id',
    );

    expect(
      MockNursingServiceRepository.updateNursingServicesConsentSignature,
    ).toHaveBeenLastCalledWith(user.defaultProfile, 'record-id', '', '');
  });
  it('saveNursingServicesDetails(): should create bill with none patient', async () => {
    ManagerMock.withRepository().saveNursingServicesDetails.mockReturnValue({
      ...nursingServiceDetails[0],
      nursingService: {
        ...nursingServiceData,
        profile,
      },
    });
    const _profile = profileFactory.build({
      type: UserType.OrganizationDoctor,
    });
    const input = nursingServiceData;

    await service.saveNursingServicesDetails(
      nursingServiceData.id,
      input,
      _profile,
    );
    expect(
      MockNursingServiceRepository.saveNursingServicesDetails,
    ).toHaveBeenCalledWith(nursingServiceData.id, input, _profile);
  });
  it('saveNursingServicesDetails(): should save nursing services details', async () => {
    const nursingServiceDetailsInput = nursingServiceDetailFactory.build();
    delete nursingServiceDetailsInput.profile;
    await service.saveNursingServicesDetails(
      user.defaultProfile,
      nursingServiceData.id,
      nursingServiceDetailsInput,
    );
    expect(
      MockNursingServiceRepository.saveNursingServicesDetails,
    ).toHaveBeenCalled();
  });

  it('updateNursingServicesDetails(): should update nursing services details', async () => {
    const nursingServiceDetailsInput = nursingServiceDetailFactory.build();
    delete nursingServiceDetailsInput.profile;
    await service.updateNursingServicesDetails(
      user.defaultProfile,
      nursingServiceData.id,
      nursingServiceDetailsInput,
    );
    expect(
      MockNursingServiceRepository.updateNursingServicesDetails,
    ).toHaveBeenCalled();
  });

  it('deleteNursingServicesDetails(): should delete nursing services details', async () => {
    const nursingServiceDetailsInput = nursingServiceDetailFactory.build();
    delete nursingServiceDetailsInput.profile;

    ManagerMock.withRepository().deleteNursingServicesDetails = jest
      .fn()
      .mockResolvedValue(nursingServiceDetailsInput);

    await service.removeNursingServicesDetails(
      user.defaultProfile,
      nursingServiceData.id,
    );
    expect(
      ManagerMock.withRepository().deleteNursingServicesDetails,
    ).toHaveBeenCalled();
  });

  it('deleteNursingServicesDetails(): should delete nursing services details with bill', async () => {
    const nursingServiceDetailsInput = nursingServiceDetailFactory.build();
    delete nursingServiceDetailsInput.profile;

    ManagerMock.withRepository().deleteNursingServicesDetails = jest
      .fn()
      .mockResolvedValue({
        ...nursingServiceDetailsInput,
        billing: billDetailsFactory.build(),
      });
    await service.removeNursingServicesDetails(
      user.defaultProfile,
      nursingServiceData.id,
    );
    expect(
      ManagerMock.withRepository().deleteNursingServicesDetails,
    ).toHaveBeenCalled();
    expect(BillServiceMock.deleteAutoGeneratedBillItems).toHaveBeenCalled();
  });

  it('saveNursingServiceProgressNote', async () => {
    await service.saveNursingServicesProgressNote(
      profile,
      'id',
      nursingServiceData,
    );
    expect(
      MockNursingServiceRepository.saveNursingServicesProgressNote,
    ).toBeCalledWith(profile, 'id', nursingServiceData);
  });

  it('updateNursingServiceProgressNote', async () => {
    await service.updateNursingServicesProgressNote(
      profile,
      'id',
      nursingServiceData,
    );
    expect(
      MockNursingServiceRepository.updateNursingServicesProgressNote,
    ).toBeCalledWith(profile, 'id', nursingServiceData);
  });

  it('removeNursingServiceProgressNote', async () => {
    await service.removeNursingServicesProgressNote(profile, 'id');
    expect(
      MockNursingServiceRepository.deleteNursingServicesProgressNote,
    ).toBeCalledWith(profile, 'id');
  });

  it('concealNursingServiceProgressNote', async () => {
    await service.concealNursingServicesProgressNote(profile, 'id', true);
    expect(
      MockNursingServiceRepository.concealNursingServicesProgressNote,
    ).toBeCalledWith(profile, 'id', true);
  });

  it('concealNursingServiceAdditionalNote', async () => {
    await service.concealNursingServiceAdditionalNote(profile, 'id', true);
    expect(
      MockNursingServiceRepository.concealNursingServiceAdditionalNote,
    ).toBeCalledWith(profile, 'id', true);
  });

  it('isVitalsLinked', async () => {
    const isVitalsLinked = await service.isVitalsLinked('id');
    expect(MockNursingServiceRepository.isVitalsLinked).toHaveBeenCalledWith(
      'id',
    );
    expect(isVitalsLinked).toBe(true);
  });

  it('getVitals', async () => {
    const vitals = await service.getVitals(nursingServicesData[0]);
    expect(ManagerMock.withRepository).toHaveBeenCalled();
    expect(vitals).toEqual([]);
  });

  it('isAdmissionsLinked', async () => {
    const isAdmissionLinked = await service.isAdmissionsLinked('id');
    expect(
      MockNursingServiceRepository.isAdmissionsLinked,
    ).toHaveBeenCalledWith('id');
    expect(isAdmissionLinked).toBe(false);
  });

  it('getAdmissions', async () => {
    const admissions = await service.getAdmissions(nursingServiceData);
    expect(ManagerMock.withRepository).toHaveBeenCalled();
    expect(admissions).toEqual([]);
  });

  it('isImmunizationsLinked', async () => {
    const isImmunizationsLinked = await service.isImmunizationsLinked('id');
    expect(
      MockNursingServiceRepository.isImmunizationsLinked,
    ).toHaveBeenCalledWith('id');
    expect(isImmunizationsLinked).toBe(false);
  });

  it('getImmunizations', async () => {
    const immunizations = await service.getImmunizations(nursingServiceData);
    expect(ManagerMock.withRepository).toHaveBeenCalled();
    expect(immunizations).toEqual([]);
  });

  it('isConsultationsLinked', async () => {
    const isConsultationsLinked = await service.isConsultationsLinked('id');
    expect(
      MockNursingServiceRepository.isConsultationsLinked,
    ).toHaveBeenCalledWith('id');
    expect(isConsultationsLinked).toBe(false);
  });

  it('getConsultations', async () => {
    const consultations = await service.getConsultations(nursingServiceData);
    expect(ManagerMock.withRepository).toHaveBeenCalled();
    expect(consultations).toEqual([]);
  });

  it('isSurgeriesLinked', async () => {
    const isSurgeriesLinked = await service.isSurgeriesLinked('id');
    expect(MockNursingServiceRepository.isSurgeriesLinked).toHaveBeenCalledWith(
      'id',
    );
    expect(isSurgeriesLinked).toBe(false);
  });

  it('getSurgeries', async () => {
    const surgeries = await service.getSurgeries(nursingServiceData);
    expect(ManagerMock.withRepository).toHaveBeenCalled();
    expect(surgeries).toEqual([]);
  });

  it('isMedicationsLinked', async () => {
    const isMedicationsLinked = await service.isMedicationsLinked('id');
    expect(
      MockNursingServiceRepository.isMedicationsLinked,
    ).toHaveBeenCalledWith('id');
    expect(isMedicationsLinked).toBe(false);
  });

  it('getMedications', async () => {
    const medications = await service.getMedications(nursingServiceData);
    expect(ManagerMock.withRepository).toHaveBeenCalled();
    expect(medications).toEqual([]);
  });

  it('isLaboratoryInvestigationLinked', async () => {
    const isLabLinked = await service.isLaboratoryInvestigationLinked('id');
    expect(
      MockNursingServiceRepository.isLaboratoryInvestigationLinked,
    ).toHaveBeenCalledWith('id');
    expect(isLabLinked).toBe(false);
  });

  it('getLinkedLaboratoryInvestigationRecords', async () => {
    const lab = await service.getLinkedLaboratoryInvestigationRecords(
      nursingServiceData,
    );
    expect(
      MockNursingServiceRepository.getLinkedLaboratoryInvestigationRecords,
    ).toHaveBeenCalled();
    expect(lab).toEqual([]);
  });

  it('isRadiologyInvestigationLinked', async () => {
    const isRadLinked = await service.isRadiologyInvestigationLinked('id');
    expect(
      MockNursingServiceRepository.isRadiologyInvestigationLinked,
    ).toHaveBeenCalledWith('id');
    expect(isRadLinked).toBe(false);
  });

  it('getLinkedRadiologyInvestigationRecords', async () => {
    const rad = await service.getLinkedRadiologyInvestigationRecords(
      nursingServiceData,
    );
    expect(
      MockNursingServiceRepository.getLinkedRadiologyInvestigationRecords,
    ).toHaveBeenCalled();
    expect(rad).toEqual([]);
  });
});
