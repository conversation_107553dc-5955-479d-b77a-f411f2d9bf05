/* eslint-disable max-classes-per-file */

import { Field, InputType, ObjectType } from '@nestjs/graphql';
import { IsOptional, MinLength } from 'class-validator';
import { UserType, SubUserType } from '@clinify/shared/enums/users';
import {
  Gender,
  PersonalInformationInput,
} from '@clinify/shared/validators/personal-information.input';
import { PhoneNumberInput } from '@clinify/shared/validators/phone-number.input';

@ObjectType('PersonalInformation')
@InputType()
export class CreatePersonalInformationInput extends PersonalInformationInput {
  @Field(() => String, { nullable: false })
  firstName?: string;

  @Field(() => String, { nullable: false })
  lastName?: string;
}

@ObjectType('SelfRegistrationNextOfKin')
@InputType()
export class SelfRegistrationNextOfKinInput {
  @Field(() => String, { nullable: true })
  title?: string;

  @Field(() => String, { nullable: true })
  firstName?: string;

  @Field(() => String, { nullable: true })
  middleName?: string;

  @Field(() => String, { nullable: true })
  lastName?: string;

  @Field(() => String, { nullable: true })
  gender?: Gender;

  @Field(() => String, { nullable: true })
  relationship?: string;

  @Field(() => PhoneNumberInput, { nullable: true })
  phoneNumber?: PhoneNumberInput;

  @Field(() => String, { nullable: true })
  email?: string;
}

@ObjectType('SelfRegistrationDependent')
@InputType()
export class SelfRegistrationDependentInput {
  @Field({ nullable: true })
  enrolleeId: string;

  @Field({ nullable: true })
  title: string;

  @Field({ nullable: true })
  firstName: string;

  @Field({ nullable: true })
  middleName?: string;

  @Field({ nullable: true })
  lastName: string;

  @Field(() => String, { nullable: true })
  gender?: Gender;

  @Field({ nullable: true })
  dateOfBirth?: Date;

  @Field({ nullable: true })
  bloodGroup?: string;

  @Field({ nullable: true })
  relationship?: string;

  @Field(() => PhoneNumberInput, { nullable: true })
  phoneNumber?: PhoneNumberInput;

  @Field({ nullable: true })
  email?: string;

  @Field({ nullable: true })
  displayPictureUrl?: string;
}

@InputType()
export class CompleteRegistrationInput {
  @Field(() => CreatePersonalInformationInput, { nullable: false })
  details: CreatePersonalInformationInput;

  @Field(() => SelfRegistrationNextOfKinInput, { nullable: true })
  nextOfKin?: SelfRegistrationNextOfKinInput;

  @Field(() => SubUserType)
  userType: SubUserType;

  @Field(() => UserType, { nullable: true })
  subUserType?: UserType;

  @Field(() => String, { nullable: true })
  email?: string;

  @Field(() => String, { nullable: true })
  country?: string;

  @Field(() => String, { nullable: true })
  nationality?: string;

  @Field(() => String, { nullable: true })
  state?: string;

  @MinLength(10)
  @IsOptional()
  @Field(() => String, { nullable: true })
  phoneNumber?: string;

  @Field(() => String, { nullable: true })
  enrolleeId?: string;

  @Field(() => String, { nullable: true })
  enrolleePlanType?: string;

  @Field(() => String, { nullable: true })
  facilityId?: string;

  @Field(() => String, { nullable: true })
  providerId?: string;

  @Field(() => String, { nullable: true })
  providerCode?: string;

  @Field(() => String, { nullable: true })
  coverageName?: string;

  @Field(() => String, { nullable: true })
  contactAddress?: string;

  @Field(() => String, { nullable: true })
  companyName?: string;

  @Field(() => String, { nullable: true })
  companyAddress?: string;

  @Field(() => String, { nullable: true })
  familyName?: string;

  @Field(() => String, { nullable: true })
  familyAddress?: string;

  @Field(() => String, { nullable: true })
  coverageType?: string;

  @Field({ nullable: true })
  tempUserId?: string;

  @Field({ nullable: true })
  enrolleePlanCategory?: string;

  @Field({ nullable: true })
  enrolleePlanFrequency?: string;

  @Field({ nullable: true })
  registrationSource?: string;

  @Field(() => [SelfRegistrationDependentInput], {
    nullable: true,
  })
  dependents?: SelfRegistrationDependentInput[];
}
