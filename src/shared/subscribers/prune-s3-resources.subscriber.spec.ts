import { Test, TestingModule } from '@nestjs/testing';
import { TypeOrmModule } from '@nestjs/typeorm';
import { EntityManager } from 'typeorm';
import { loggerMock } from '@clinify/__mocks__/logger';
import { TestDataSourceOptions } from '@clinify/data-source';
import * as deleteObject from '@clinify/shared/helper/s3/delete-object';
import * as sharedLexical from '@clinify/shared/lexical';
import {
  getFilePathFromUrl,
  PruneS3ResourcesSubscriber,
} from '@clinify/shared/subscribers/prune-s3-resources.subscriber';
import { initialEditorState } from '@clinify/utils/dynamic-pdf';

jest.mock('lexical', () => ({
  ...jest.requireActual('lexical'),
  $nodesOfType: jest.fn().mockReturnValue([
    {
      type: 'image',
      src: 'https://bucket-name.s3.eu-west-1.amazonaws.com/path/to/file.jpg',
      getSrc: jest
        .fn()
        .mockReturnValue(
          'https://bucket-name.s3.eu-west-1.amazonaws.com/path/to/file.jpg',
        ),
      __isTemplateResource: false,
    } as any,
  ]),
}));

const deleteObjectSpy = jest
  .spyOn(deleteObject, 'deleteObjectFromUploads')
  .mockResolvedValue({} as any);
const sharedLexicalSpy = jest.spyOn(
  sharedLexical,
  'createLexicalHeadlessEditor',
);

const MockManager = {
  getRepository: jest.fn((target?: string) => ({
    findOne: jest.fn().mockResolvedValue({}),
  })),
};

describe('PruneS3ResourcesSubscriber', () => {
  let subscriber: PruneS3ResourcesSubscriber;
  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [TypeOrmModule.forRoot(TestDataSourceOptions)],
      providers: [
        PruneS3ResourcesSubscriber,
        { ...loggerMock },
        { provide: EntityManager, useValue: MockManager },
      ],
    }).compile();

    subscriber = module.get(PruneS3ResourcesSubscriber);

    sharedLexicalSpy.mockReturnValue({
      setEditorState: jest.fn(),
      getEditorState: jest.fn().mockReturnValue({
        read: jest.fn((callback) => callback()),
        write: jest.fn(),
      }),
      parseEditorState: jest.fn(),
    } as any);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('does not delete objects when entity does not have documentUrl', async () => {
    const eventMock: any = {
      entity: {},
      metadata: {
        targetName: 'AdmissionModel',
      },
    };
    await subscriber.afterRemove(eventMock);
  });

  it('returns the correct file path from a valid S3 URL', () => {
    const url =
      'https://bucket-name.s3.eu-west-1.amazonaws.com/path/to/file.jpg';
    const result = getFilePathFromUrl(url);
    expect(result).toEqual('path/to/file.jpg');
  });

  it('returns undefined when the URL does not contain the S3 domain', () => {
    const url = 'https://bucket-name.amazonaws.com/path/to/file.jpg';
    const result = getFilePathFromUrl(url);
    expect(result).toBeUndefined();
  });

  it('deletes objects from S3 when entity has documentUrl on delete', async () => {
    const eventMock: any = {
      entity: {
        documentUrl: [
          'https://bucket-name.s3.eu-west-1.amazonaws.com/path/to/file.jpg',
        ],
        complaint: 'complaint',
      },
      metadata: {
        targetName: 'ConsultationModel',
      },
    };

    expect(deleteObjectSpy).not.toBeCalled();
    await subscriber.afterRemove(eventMock);

    expect(deleteObjectSpy).toBeCalled();
  });

  it('deletes objects from S3 for entity with rich text on delete, Radiology Result', async () => {
    const eventMock: any = {
      entity: {
        documentUrl: [
          'https://bucket-name.s3.eu-west-1.amazonaws.com/path/to/file.jpg',
        ],
        details: [
          {
            radiographerReport: '',
            impression: '',
          },
        ],
      },
      metadata: {
        targetName: 'RadiologyResultModel',
      },
    };

    expect(deleteObjectSpy).not.toBeCalled();
    await subscriber.afterRemove(eventMock);

    expect(deleteObjectSpy).toBeCalled();
  });

  it('deletes objects from S3 for entity with rich text on delete, MedicalReportModel', async () => {
    const eventMock: any = {
      entity: {
        documentUrl: [
          'https://bucket-name.s3.eu-west-1.amazonaws.com/path/to/file.jpg',
        ],
        report: [
          {
            report: '',
          },
        ],
      },
      metadata: {
        targetName: 'MedicalReportModel',
      },
    };

    expect(deleteObjectSpy).not.toBeCalled();
    await subscriber.afterRemove(eventMock);

    expect(deleteObjectSpy).toBeCalled();
  });

  it('updates objects from S3 when entity has documentUrl on update', async () => {
    const eventMock: any = {
      entity: {
        documentUrl: [
          'https://bucket-name.s3.eu-west-1.amazonaws.com/path/to/file.jpg',
          'https://bucket-name.s3.eu-west-1.amazonaws.com/path/to/file_1.jpg',
        ],
        complaint: 'complaint',
      },
      databaseEntity: {
        documentUrl: [
          'https://bucket-name.s3.eu-west-1.amazonaws.com/path/to/file.jpg',
          'https://bucket-name.s3.eu-west-1.amazonaws.com/path/to/file_2.jpg',
        ],
        complaint: 'complaint',
      },
      metadata: {
        targetName: 'ConsultationModel',
      },
    };

    expect(deleteObjectSpy).not.toBeCalled();
    await subscriber.beforeUpdate(eventMock);

    expect(deleteObjectSpy).toBeCalled();
  });

  it('updates objects from S3 for entity with rich text on update, Radiology Result', async () => {
    const eventMock: any = {
      entity: {
        documentUrl: [
          'https://bucket-name.s3.eu-west-1.amazonaws.com/path/to/file.jpg',
          'https://bucket-name.s3.eu-west-1.amazonaws.com/path/to/file_1.jpg',
        ],
        details: [
          {
            radiographerReport: '',
            impression: '',
          },
        ],
      },
      databaseEntity: {
        documentUrl: [
          'https://bucket-name.s3.eu-west-1.amazonaws.com/path/to/file.jpg',
          'https://bucket-name.s3.eu-west-1.amazonaws.com/path/to/file_2.jpg',
        ],
        details: [
          {
            radiographerReport: '',
            impression: '',
          },
        ],
      },
      metadata: {
        targetName: 'RadiologyResultModel',
      },
    };

    expect(deleteObjectSpy).not.toBeCalled();
    await subscriber.beforeUpdate(eventMock);

    expect(deleteObjectSpy).toBeCalled();
  });

  it('updates objects from S3 for entity with rich text on update, MedicalReportModel', async () => {
    const eventMock: any = {
      entity: {
        documentUrl: [
          'https://bucket-name.s3.eu-west-1.amazonaws.com/path/to/file.jpg',
          'https://bucket-name.s3.eu-west-1.amazonaws.com/path/to/file.jpg_1',
        ],
        report: [
          {
            report: '',
          },
        ],
      },
      databaseEntity: {
        documentUrl: [
          'https://bucket-name.s3.eu-west-1.amazonaws.com/path/to/file.jpg',
          'https://bucket-name.s3.eu-west-1.amazonaws.com/path/to/file_2.jpg',
        ],
        report: [
          {
            report: '',
          },
        ],
      },
      metadata: {
        targetName: 'MedicalReportModel',
      },
    };

    expect(deleteObjectSpy).not.toBeCalled();
    await subscriber.beforeUpdate(eventMock);

    expect(deleteObjectSpy).toBeCalled();
  });

  it('updates objects from S3 for entity with rich text on update, InvestigationModel', async () => {
    const eventMock: any = {
      entity: {
        labResult: {
          details: [
            {
              additionalNote: initialEditorState,
            },
          ],
        },
        radiologyResult: {
          details: [{ impression: initialEditorState }],
        },
      },
      metadata: {
        targetName: 'InvestigationModel',
      },
    };

    expect(deleteObjectSpy).not.toBeCalled();
    await subscriber.afterRemove(eventMock);

    expect(deleteObjectSpy).toBeCalled();
  });

  it('remove signature object from S3 when field on entity has been changed', () => {
    const eventMock: any = {
      entity: {
        id: 'id',
        documentUrl: [
          'https://bucket-name.s3.eu-west-1.amazonaws.com/path/to/file.jpg',
        ],
        patientConsentSignature:
          'https://bucket-name.s3.eu-west-1.amazonaws.com/path/to/new-consent-signature.jpg',
      },
      databaseEntity: {
        id: 'id',
        documentUrl: [
          'https://bucket-name.s3.eu-west-1.amazonaws.com/path/to/file.jpg',
        ],
        patientConsentSignature:
          'https://bucket-name.s3.eu-west-1.amazonaws.com/path/to/consent-signature.jpg',
      },
      metadata: {
        targetName: 'AdmissionModel',
      },
    };
    subscriber.beforeUpdate(eventMock);
    expect(deleteObjectSpy).toHaveBeenNthCalledWith(
      1,
      'path/to/consent-signature.jpg',
    );
  });

  it('remove signature object from S3 when field on entity has been deleted', () => {
    const eventMock: any = {
      entity: {
        id: 'id',
        documentUrl: [
          'https://bucket-name.s3.eu-west-1.amazonaws.com/path/to/file.jpg',
        ],
        patientConsentSignature:
          'https://bucket-name.s3.eu-west-1.amazonaws.com/path/to/del-consent-signature.jpg',
      },
      metadata: {
        targetName: 'SurgeryModel',
      },
    };
    subscriber.afterRemove(eventMock);
    expect(deleteObjectSpy).toHaveBeenCalledWith(
      'path/to/del-consent-signature.jpg',
    );
  });

  it('remove signature object from S3 when field on entity has been changed - For LabResult', () => {
    const eventMock = {
      entity: {
        id: 'id',
        details: [
          {
            performedBySignature:
              'https://bucket-name.s3.eu-west-1.amazonaws.com/path/to/performed-by-signature.jpg',
            verifiedBySignature:
              'https://bucket-name.s3.eu-west-1.amazonaws.com/path/to/verified-by-signature.jpg',
            pathologistSignature:
              'https://bucket-name.s3.eu-west-1.amazonaws.com/path/to/old-pathologist-signature.jpg',
          },
        ],
      },
      databaseEntity: {
        id: 'id',
        details: [
          {
            performedBySignature:
              'https://bucket-name.s3.eu-west-1.amazonaws.com/path/to/old-performed-by-signature.jpg',
            verifiedBySignature:
              'https://bucket-name.s3.eu-west-1.amazonaws.com/path/to/old-verified-by-signature.jpg',
            pathologistSignature:
              'https://bucket-name.s3.eu-west-1.amazonaws.com/path/to/old-pathologist-signature.jpg',
          },
        ],
      },
      metadata: {
        targetName: 'LabResultModel',
      },
    } as any;
    subscriber.beforeUpdate(eventMock);
    expect(deleteObjectSpy).toHaveBeenCalledWith(
      'path/to/old-performed-by-signature.jpg',
    );
    expect(deleteObjectSpy).toHaveBeenCalledWith(
      'path/to/old-verified-by-signature.jpg',
    );
    expect(deleteObjectSpy).not.toHaveBeenCalledWith(
      'path/to/old-pathologist-signature.jpg',
    );
  });

  it('remove signature object from S3 when field on entity has been changed - For RadiologyResult', () => {
    const eventMock = {
      entity: {
        id: 'id',
        details: [
          {
            radiographerSignature:
              'https://bucket-name.s3.eu-west-1.amazonaws.com/path/to/radiographer-signature.jpg',
          },
        ],
      },
      databaseEntity: {
        id: 'id',
        details: [
          {
            radiographerSignature:
              'https://bucket-name.s3.eu-west-1.amazonaws.com/path/to/old-radiographer-signature.jpg',
          },
        ],
      },
      metadata: {
        targetName: 'RadiologyResultModel',
      },
    } as any;
    subscriber.beforeUpdate(eventMock);
    expect(deleteObjectSpy).toHaveBeenCalledWith(
      'path/to/old-radiographer-signature.jpg',
    );
    expect(deleteObjectSpy).not.toHaveBeenCalledWith(
      'path/to/radiographer-signature.jpg',
    );
  });

  it('remove signature object from S3 when field on entity has been deleted - For LabResult', () => {
    const eventMock = {
      entity: {
        id: 'id',
        details: [
          {
            performedBySignature:
              'https://bucket-name.s3.eu-west-1.amazonaws.com/path/to/performed-by-signature.jpg',
            verifiedBySignature:
              'https://bucket-name.s3.eu-west-1.amazonaws.com/path/to/verified-by-signature.jpg',
            pathologistSignature:
              'https://bucket-name.s3.eu-west-1.amazonaws.com/path/to/pathologist-signature.jpg',
          },
        ],
      },
      metadata: {
        targetName: 'LabResultModel',
      },
    } as any;
    subscriber.afterRemove(eventMock);
    expect(deleteObjectSpy).toHaveBeenCalledWith(
      'path/to/performed-by-signature.jpg',
    );
    expect(deleteObjectSpy).toHaveBeenCalledWith(
      'path/to/verified-by-signature.jpg',
    );
    expect(deleteObjectSpy).toHaveBeenCalledWith(
      'path/to/pathologist-signature.jpg',
    );
  });

  it('remove signature object from S3 when field on entity has been deleted - For RadiologyResult', () => {
    const eventMock = {
      entity: {
        id: 'id',
        details: [
          {
            radiographerSignature:
              'https://bucket-name.s3.eu-west-1.amazonaws.com/path/to/radiographer-signature.jpg',
          },
        ],
      },
      metadata: {
        targetName: 'RadiologyResultModel',
      },
    } as any;
    subscriber.afterRemove(eventMock);
    expect(deleteObjectSpy).toHaveBeenCalledWith(
      'path/to/radiographer-signature.jpg',
    );
  });

  it('Should call find method on manager when no databaseEntity exists', async () => {
    MockManager.getRepository.mockImplementationOnce(() => ({
      findOne: jest.fn().mockResolvedValue({
        id: 'id',
        details: [
          {
            radiographerSignature:
              'https://bucket-name.s3.eu-west-1.amazonaws.com/path/to/old-radiographer-signature.jpg',
          },
        ],
      }),
    }));
    const eventMock = {
      entity: {
        id: 'id',
        details: [
          {
            radiographerSignature:
              'https://bucket-name.s3.eu-west-1.amazonaws.com/path/to/radiographer-signature.jpg',
          },
        ],
      },
      metadata: {
        targetName: 'RadiologyResultModel',
      },
    } as any;
    await subscriber.beforeUpdate(eventMock);
    expect(MockManager.getRepository).toHaveBeenCalledWith(
      'RadiologyResultModel',
    );
    expect(deleteObjectSpy).toHaveBeenCalledWith(
      'path/to/old-radiographer-signature.jpg',
    );
    expect(deleteObjectSpy).not.toHaveBeenCalledWith(
      'path/to/radiographer-signature.jpg',
    );
  });

  it('remove signature objects from s3 when investigation record is deleted', () => {
    const eventMock = {
      entity: {
        id: 'id',
        labResult: {
          details: [
            {
              performedBySignature:
                'https://bucket-name.s3.eu-west-1.amazonaws.com/path/to/performed-by-signature.jpg',
              verifiedBySignature:
                'https://bucket-name.s3.eu-west-1.amazonaws.com/path/to/verified-by-signature.jpg',
              pathologistSignature:
                'https://bucket-name.s3.eu-west-1.amazonaws.com/path/to/pathologist-signature.jpg',
            },
          ],
        },
        radiologyResult: {
          details: [
            {
              radiographerSignature:
                'https://bucket-name.s3.eu-west-1.amazonaws.com/path/to/radiographer-signature.jpg',
            },
          ],
        },
      },
      metadata: {
        targetName: 'InvestigationModel',
      },
    } as any;
    subscriber.afterRemove(eventMock);
    expect(deleteObjectSpy).toHaveBeenCalledWith(
      'path/to/performed-by-signature.jpg',
    );
    expect(deleteObjectSpy).toHaveBeenCalledWith(
      'path/to/verified-by-signature.jpg',
    );
    expect(deleteObjectSpy).toHaveBeenCalledWith(
      'path/to/pathologist-signature.jpg',
    );
    expect(deleteObjectSpy).toHaveBeenCalledWith(
      'path/to/radiographer-signature.jpg',
    );
  });

  it('remove signature object from s3 when nursing service is deleted', () => {
    const eventMock = {
      entity: {
        id: 'id',
        details: [
          {
            patientConsentSignature:
              'https://bucket-name.s3.eu-west-1.amazonaws.com/path/to/signature.jpg',
          },
        ],
      },
      metadata: {
        targetName: 'NursingServiceModel',
      },
    } as any;
    subscriber.afterRemove(eventMock);
    expect(deleteObjectSpy).toHaveBeenCalledWith('path/to/signature.jpg');
  });

  it('deletes objects from S3 when admission has documentUrl, consent signature on delete', () => {
    const eventMock: any = {
      entity: {
        documentUrl: [
          'https://bucket-name.s3.eu-west-1.amazonaws.com/path/to/file.jpg',
        ],
        patientConsentSignature:
          'https://bucket-name.s3.eu-west-1.amazonaws.com/path/to/consent-signature.jpg',
        bloodTransfusions: [
          {
            id: 'id',
            patientConsentSignature:
              'https://bucket-name.s3.eu-west-1.amazonaws.com/path/to/blood-consent-signature.jpg',
          },
        ],
      },
      metadata: {
        targetName: 'AdmissionModel',
      },
    };

    subscriber.afterRemove(eventMock);

    expect(deleteObjectSpy).toHaveBeenCalledWith('path/to/file.jpg');
    expect(deleteObjectSpy).toHaveBeenCalledWith(
      'path/to/consent-signature.jpg',
    );
    expect(deleteObjectSpy).toHaveBeenCalledWith(
      'path/to/blood-consent-signature.jpg',
    );
  });

  it('deletes objects from S3 when consultation has documentUrl, consent signature (Treatment Plan) on delete', () => {
    const eventMock: any = {
      entity: {
        documentUrl: [
          'https://bucket-name.s3.eu-west-1.amazonaws.com/path/to/file.jpg',
        ],
        treatmentPlans: [
          {
            id: 'id',
            patientConsentSignature:
              'https://bucket-name.s3.eu-west-1.amazonaws.com/path/to/consent-signature.jpg',
          },
        ],
      },
      metadata: {
        targetName: 'ConsultationModel',
      },
    };

    subscriber.afterRemove(eventMock);

    expect(deleteObjectSpy).toHaveBeenCalledWith('path/to/file.jpg');
    expect(deleteObjectSpy).toHaveBeenCalledWith(
      'path/to/consent-signature.jpg',
    );
  });
});
