/* eslint-disable @typescript-eslint/unbound-method */
import { createAdapter } from '@socket.io/redis-adapter';
import Redis from 'ioredis';
import { Server as SocketIOServer, Socket } from 'socket.io';
import { HealthScribeService } from '@clinify/integrations/health-scribe/services/health-scribe.service';
import { TranscribeServer } from '@clinify/transcribe/server';

jest.mock('socket.io');
jest.mock('ioredis');
jest.mock('@socket.io/redis-adapter');
jest.mock('@clinify/integrations/health-scribe/services/health-scribe.service');

describe('TranscribeServer', () => {
  let transcribeServer: TranscribeServer;
  let mockHealthScribeService: jest.Mocked<HealthScribeService>;
  let mockSocket: jest.Mocked<Socket>;
  let mockIOServer: jest.Mocked<SocketIOServer>;

  const config = {
    port: 3000,
    redisUrl: 'redis://localhost:6379',
  };

  beforeEach(() => {
    jest.clearAllMocks();

    mockHealthScribeService = {
      startStreamTranscription: jest.fn(),
    } as any;

    mockSocket = {
      id: 'test-socket-id',
      emit: jest.fn(),
      on: jest.fn(),
      once: jest.fn(),
      removeAllListeners: jest.fn(),
    } as any;

    mockIOServer = {
      on: jest.fn(),
      adapter: jest.fn(),
    } as any;

    (SocketIOServer as unknown as jest.Mock).mockImplementation(
      () => mockIOServer,
    );
    (Redis as unknown as jest.Mock).mockImplementation(() => ({
      duplicate: jest.fn().mockReturnThis(),
    }));
    (createAdapter as jest.Mock).mockReturnValue({});

    transcribeServer = new TranscribeServer(config, mockHealthScribeService);
  });

  describe('initialization', () => {
    it('should initialize Redis adapter', () => {
      expect(Redis).toHaveBeenCalledWith(config.redisUrl);
      expect(createAdapter).toHaveBeenCalled();
      expect(mockIOServer.adapter).toHaveBeenCalled();
    });

    it('should setup socket handlers', () => {
      expect(mockIOServer.on).toHaveBeenCalledWith(
        'connection',
        expect.any(Function),
      );
    });
  });

  describe('connection handling', () => {
    let connectionHandler: (socket: Socket) => void;

    beforeEach(() => {
      connectionHandler = mockIOServer.on.mock.calls.find(
        (call) => call[0] === 'connection',
      )[1];
    });

    it('should set up event handlers for new connections', () => {
      connectionHandler(mockSocket);

      expect(mockSocket.on).toHaveBeenCalledWith(
        'startTranscription',
        expect.any(Function),
      );
      expect(mockSocket.on).toHaveBeenCalledWith(
        'startMedicalTranscription',
        expect.any(Function),
      );
      expect(mockSocket.on).toHaveBeenCalledWith(
        'audioData',
        expect.any(Function),
      );
      expect(mockSocket.on).toHaveBeenCalledWith(
        'disconnect',
        expect.any(Function),
      );
    });
  });

  describe('audio handling', () => {
    let audioDataHandler: (payload: Buffer) => void;

    beforeEach(() => {
      const connectionHandler = mockIOServer.on.mock.calls.find(
        (call) => call[0] === 'connection',
      )[1];
      connectionHandler(mockSocket);

      audioDataHandler = mockSocket.on.mock.calls.find(
        (call) => call[0] === 'audioData',
      )[1];
    });

    it('should handle audio data correctly', () => {
      const testPayload = Buffer.from('test audio data');
      audioDataHandler(testPayload);

      const audioStream = (transcribeServer as any).audioStreams.get(
        mockSocket.id,
      );
      expect(audioStream).toBeDefined();
    });
  });

  describe('transcription handling', () => {
    let startTranscriptionHandler: () => void;

    beforeEach(() => {
      const connectionHandler = mockIOServer.on.mock.calls.find(
        (call) => call[0] === 'connection',
      )[1];
      connectionHandler(mockSocket);

      startTranscriptionHandler = mockSocket.on.mock.calls.find(
        (call) => call[0] === 'startTranscription',
      )[1];
    });

    it('should handle transcription start correctly', async () => {
      const mockTranscriptStream: any = {
        TranscriptResultStream: [
          {
            TranscriptEvent: {
              Transcript: {
                Results: ['test result'],
              },
            },
          },
        ],
      };

      mockHealthScribeService.startStreamTranscription.mockResolvedValue(
        mockTranscriptStream,
      );

      await startTranscriptionHandler();

      expect(
        mockHealthScribeService.startStreamTranscription,
      ).toHaveBeenCalled();
      expect(mockSocket.emit).toHaveBeenCalledWith('transcriptionData', [
        'test result',
      ]);
    });

    it('should handle transcription errors gracefully', async () => {
      mockHealthScribeService.startStreamTranscription.mockRejectedValue(
        new Error('Transcription error'),
      );

      await startTranscriptionHandler();

      expect(
        mockHealthScribeService.startStreamTranscription,
      ).toHaveBeenCalled();
    });
  });

  describe('cleanup', () => {
    it('should clean up resources on disconnect', () => {
      const connectionHandler = mockIOServer.on.mock.calls.find(
        (call) => call[0] === 'connection',
      )[1];
      connectionHandler(mockSocket);

      const disconnectHandler = mockSocket.on.mock.calls.find(
        (call) => call[0] === 'disconnect',
      )[1];

      disconnectHandler();

      // Verify cleanup
      expect(
        (transcribeServer as any).audioStreams.has(mockSocket.id),
      ).toBeFalsy();
    });
  });
});
