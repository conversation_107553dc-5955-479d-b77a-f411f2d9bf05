import { Field, ID, ObjectType } from '@nestjs/graphql';
import { IsEmpty, IsUUID, IsDate } from 'class-validator';
import {
  Column,
  CreateDateColumn,
  Entity,
  Index,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

@ObjectType()
@Entity({ name: 'payment_histories' })
export class PaymentHistoryModel {
  @Field(() => ID)
  @PrimaryGeneratedColumn('uuid')
  @IsEmpty()
  @IsUUID('4')
  id: string;

  @Field(() => String)
  @Column({ name: 'gateway_provider', type: 'varchar', length: 50 })
  gatewayProvider: string;

  @Field(() => String)
  @Index()
  @Column({
    name: 'transaction_reference',
    type: 'varchar',
    length: 255,
    unique: true,
    nullable: true,
  })
  transactionReference?: string;

  @Field(() => String)
  @Index()
  @Column({ name: 'merchant_reference', type: 'varchar', length: 255 })
  merchantReference: string;

  // Company Invoice Related Fields
  @Field(() => String)
  @Index()
  @Column({ name: 'invoice_id', type: 'varchar', length: 255, nullable: true })
  invoiceId?: string;

  @Field(() => String)
  @Index()
  @Column({ name: 'employer_id', type: 'varchar', length: 255, nullable: true })
  employerId?: string;

  // HMO Profile Fields
  @Field(() => String)
  @Index()
  @Column({ name: 'hmo_provider_id', type: 'varchar', length: 255 })
  hmoProviderId: string;

  @Field(() => String, { nullable: true })
  @Index()
  @Column({
    name: 'membership_number',
    type: 'varchar',
    length: 100,
    nullable: true,
  })
  membershipNumber?: string;

  @Field(() => String, { nullable: true })
  @Index()
  @Column({
    name: 'member_number',
    type: 'varchar',
    length: 100,
    nullable: true,
  })
  memberNumber?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'member_plan', type: 'varchar', length: 255, nullable: true })
  memberPlan?: string;

  @Field(() => String, { nullable: true })
  @Column({
    name: 'member_plan_id',
    type: 'varchar',
    length: 100,
    nullable: true,
  })
  memberPlanId?: string;

  @Field(() => String, { nullable: true })
  @Column({
    name: 'employee_number',
    type: 'varchar',
    length: 100,
    nullable: true,
  })
  employeeNumber?: string;

  @Field(() => String, { nullable: true })
  @Column({
    name: 'member_status',
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  memberStatus?: string;

  @IsDate()
  @Field(() => Date, { nullable: true })
  @Column({ name: 'member_due_date', type: 'date', nullable: true })
  memberDueDate?: Date;

  @IsDate()
  @Field(() => Date, { nullable: true })
  @Column({ name: 'plan_start_date', type: 'date', nullable: true })
  planStartDate?: Date;

  @IsDate()
  @Field(() => Date, { nullable: true })
  @Column({ name: 'plan_activation_date', type: 'date', nullable: true })
  planActivationDate?: Date;

  @Field(() => String, { nullable: true })
  @Column({
    name: 'company_name',
    type: 'varchar',
    length: 255,
    nullable: true,
  })
  companyName?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'company_address', type: 'text', nullable: true })
  companyAddress?: string;

  @Field(() => Number, { nullable: true })
  @Column({ name: 'dependent_count', type: 'integer', default: 0 })
  dependentCount?: number;

  @Field(() => Number, { nullable: true })
  @Column({
    name: 'premium_collected',
    type: 'decimal',
    precision: 12,
    scale: 2,
    default: 0,
  })
  premiumCollected?: number;

  @Field(() => Number, { nullable: true })
  @Column({
    name: 'premium_outstanding',
    type: 'decimal',
    precision: 12,
    scale: 2,
    default: 0,
  })
  premiumOutstanding?: number;

  @Field(() => Number, { nullable: true })
  @Column({
    name: 'premium_due',
    type: 'decimal',
    precision: 12,
    scale: 2,
    default: 0,
  })
  premiumDue?: number;

  @IsDate()
  @Field(() => Date, { nullable: true })
  @Column({ name: 'enrollment_date_time', type: 'timestamp', nullable: true })
  enrollmentDateTime?: Date;

  @Field(() => Number, { nullable: true })
  @Column({
    name: 'total_premium_amount_paid',
    type: 'decimal',
    precision: 12,
    scale: 2,
    default: 0,
  })
  totalPremiumAmountPaid?: number;

  @Field(() => Number, { nullable: true })
  @Column({
    name: 'member_premium',
    type: 'decimal',
    precision: 12,
    scale: 2,
    nullable: true,
  })
  memberPremium?: number;

  @Field(() => String, { nullable: true })
  @Column({
    name: 'plan_category',
    type: 'varchar',
    length: 100,
    nullable: true,
  })
  planCategory?: string;

  @IsDate()
  @Field(() => Date, { nullable: true })
  @Column({ name: 'payment_start_date', type: 'date', nullable: true })
  paymentStartDate?: Date;

  @Field(() => String, { nullable: true })
  @Column({ name: 'payment_type', type: 'varchar', length: 50, nullable: true })
  paymentType?: string;

  @Field(() => String, { nullable: true })
  @Column({
    name: 'receipt_number',
    type: 'varchar',
    length: 100,
    nullable: true,
  })
  receiptNumber?: string;

  @IsDate()
  @Field(() => Date, { nullable: true })
  @Column({ name: 'termination_date', type: 'date', nullable: true })
  terminationDate?: Date;

  @IsDate()
  @Field(() => Date, { nullable: true })
  @Column({ name: 'next_renewal_date', type: 'date', nullable: true })
  nextRenewalDate?: Date;

  @Field(() => String, { nullable: true })
  @Column({
    name: 'registration_source',
    type: 'varchar',
    length: 100,
    nullable: true,
  })
  registrationSource?: string;

  @Field(() => Number, { nullable: true })
  @Column({
    name: 'commission_payable',
    type: 'decimal',
    precision: 10,
    scale: 2,
    default: 0,
  })
  commissionPayable?: number;

  @Field(() => String, { nullable: true })
  @Column({
    name: 'enrollment_agency',
    type: 'varchar',
    length: 255,
    nullable: true,
  })
  enrollmentAgency?: string;

  @Field(() => Number, { nullable: true })
  @Column({
    name: 'commission_rate',
    type: 'decimal',
    precision: 5,
    scale: 4,
    default: 0,
  })
  commissionRate?: number;

  @Field(() => String, { nullable: true })
  @Column({
    name: 'enrollment_agent',
    type: 'varchar',
    length: 255,
    nullable: true,
  })
  enrollmentAgent?: string;

  @Field(() => Number, { nullable: true })
  @Column({
    name: 'referrer_commission_rate',
    type: 'decimal',
    precision: 5,
    scale: 4,
    default: 0,
  })
  referrerCommissionRate?: number;

  @Field(() => Number, { nullable: true })
  @Column({
    name: 'referrer_commission_payable',
    type: 'decimal',
    precision: 10,
    scale: 2,
    default: 0,
  })
  referrerCommissionPayable?: number;

  @Field(() => String, { nullable: true })
  @Column({
    name: 'referrer_code',
    type: 'varchar',
    length: 100,
    nullable: true,
  })
  referrerCode?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'plan_code', type: 'varchar', length: 100, nullable: true })
  planCode?: string;

  @Field(() => String, { nullable: true })
  @Column({
    name: 'employer_code',
    type: 'varchar',
    length: 100,
    nullable: true,
  })
  employerCode?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'enrolled_by', type: 'varchar', length: 255, nullable: true })
  enrolledBy?: string;

  // Payment Transaction Fields
  @Field(() => String, { nullable: true })
  @Column({
    name: 'customer_reference',
    type: 'varchar',
    length: 255,
    nullable: true,
  })
  customerReference?: string;

  @Field(() => String, { nullable: true })
  @Column({
    name: 'payment_reference',
    type: 'varchar',
    length: 255,
    nullable: true,
  })
  paymentReference?: string;

  @Field(() => Number)
  @Column({ name: 'amount', type: 'decimal', precision: 12, scale: 2 })
  amount: number;

  @IsDate()
  @Field(() => Date, { nullable: true })
  @Index()
  @Column({ name: 'payment_date', type: 'timestamp', nullable: true })
  paymentDate?: Date;

  @IsDate()
  @Field(() => Date, { nullable: true })
  @Column({ name: 'payment_time', type: 'timestamp', nullable: true })
  paymentTime?: Date;

  @Field(() => String, { nullable: true })
  @Column({ name: 'channel', type: 'varchar', length: 50, nullable: true })
  channel?: string;

  @Field(() => String, { nullable: true })
  @Column({
    name: 'institution_code',
    type: 'varchar',
    length: 10,
    nullable: true,
  })
  institutionCode?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'bank_code', type: 'varchar', length: 10, nullable: true })
  bankCode?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'branch_code', type: 'varchar', length: 10, nullable: true })
  branchCode?: string;

  @Field(() => Number, { nullable: true })
  @Column({
    name: 'fee_amount',
    type: 'decimal',
    precision: 10,
    scale: 2,
    nullable: true,
  })
  feeAmount?: number;

  @IsDate()
  @Field(() => Date, { nullable: true })
  @Column({ name: 'settlement_date', type: 'date', nullable: true })
  settlementDate?: Date;

  @Field(() => String, {
    description: 'PENDING, COMPLETED, FAILED, CANCELLED',
  })
  @Index()
  @Column({
    name: 'payment_status',
    type: 'varchar',
    length: 20,
    default: 'PENDING',
  })
  paymentStatus: string;

  @Field(() => String, {
    description: 'PENDING, RECEIVED, PROCESSED, FAILED',
  })
  @Column({
    name: 'notification_status',
    type: 'varchar',
    length: 20,
    default: 'PENDING',
  })
  notificationStatus: string;

  // System Fields for storing raw requests/responses
  @Column({ name: 'request_payload', type: 'jsonb', nullable: true })
  requestPayload?: Record<string, any>;

  @Column({ name: 'response_payload', type: 'jsonb', nullable: true })
  responsePayload?: Record<string, any>;

  @Column({ name: 'notification_payload', type: 'jsonb', nullable: true })
  notificationPayload?: Record<string, any>;

  @Field(() => String, { nullable: true })
  @Column({ name: 'ip_address', type: 'inet', nullable: true })
  ipAddress?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'notes', type: 'text', nullable: true })
  notes?: string;

  @IsDate()
  @Field(() => Date, { nullable: true })
  @Column({ name: 'processed_at', type: 'timestamp', nullable: true })
  processedAt?: Date;

  @IsDate()
  @Field({ nullable: false })
  @CreateDateColumn({ name: 'created_date' })
  createdDate?: Date;

  @IsDate()
  @Field({ nullable: false })
  @UpdateDateColumn({ name: 'updated_date' })
  updatedDate?: Date;
}
