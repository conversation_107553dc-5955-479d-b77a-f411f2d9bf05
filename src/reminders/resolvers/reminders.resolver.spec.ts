import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { EntityManager } from 'typeorm/entity-manager/EntityManager';
import { RemindersResolver } from './reminders.resolver';
import { RemindersResponse } from '../responses/reminders.response';
import { RemindersService } from '../services/reminders.service';
import { ReminderFilterInput } from '../validators/reminders-filter.input';
import { mockUser } from '@clinify/__mocks__/factories/user.factory';
import { PermissionModel } from '@clinify/authorization/models/permission.model';
import { PermissionService } from '@clinify/authorization/services/permission.service';
import { ProfileModel } from '@clinify/users/models/profile.model';
import { PUB_SUB } from '@clinify/utils/subscriptions/pubSub';
import { SubscriptionTypes } from '@clinify/utils/subscriptions/types';

const ManagerMock = {
  getCustomRepository: jest.fn().mockReturnValue({
    save: jest.fn(),
  }),
  createQueryBuilder: jest.fn(() => ({
    where: jest.fn().mockReturnThis(),
    execute: jest.fn(),
  })),
  save: jest.fn(),
};
const mockRemindersResponse = new RemindersResponse([], 0, 0);
const user = mockUser;
const mockProfile = user.defaultProfile;
const mockService = {
  getAllReminders: jest.fn().mockResolvedValue(mockRemindersResponse),
  markAsSeen: jest.fn().mockResolvedValue(true),
  markAllAsSeen: jest.fn().mockResolvedValue(true),
};

const mockPubSub = {
  publish: jest.fn(),
  asyncIterator: jest.fn().mockReturnValue({ next: jest.fn() }),
};

describe('RemindersResolver', () => {
  let resolver: RemindersResolver;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        RemindersResolver,
        PermissionService,
        { provide: RemindersService, useValue: mockService },
        {
          provide: getRepositoryToken(PermissionModel),
          useValue: {},
        },
        {
          provide: getRepositoryToken(ProfileModel),
          useValue: user.defaultProfile,
        },
        {
          provide: EntityManager,
          useValue: ManagerMock,
        },
        { provide: PUB_SUB, useValue: mockPubSub },
      ],
    }).compile();

    resolver = module.get<RemindersResolver>(RemindersResolver);
  });

  it('should be defined', () => {
    expect(resolver).toBeDefined();
  });

  describe('getReminders', () => {
    it('should get reminders', async () => {
      const filter = { take: 10 } as ReminderFilterInput;
      const result = await resolver.getReminders(mockProfile, null, filter);
      expect(mockService.getAllReminders).toHaveBeenCalledWith(
        mockProfile,
        null,
        filter,
      );
      expect(result).toEqual(mockRemindersResponse);
    });
  });

  describe('markRemindersAsSeen', () => {
    it('should mark as seen', async () => {
      const result = await resolver.markRemindersAsSeen(mockProfile, ['id']);
      expect(mockService.markAsSeen).toHaveBeenCalledWith(mockProfile.id, [
        'id',
      ]);
      expect(result).toBe(true);
    });
  });

  describe('markAllRemindersAsSeen', () => {
    it('should mark all as seen', async () => {
      const result = await resolver.markAllRemindersAsSeen(mockProfile);
      expect(mockService.markAllAsSeen).toHaveBeenCalledWith(mockProfile.id);
      expect(result).toBe(true);
    });
  });

  describe('subscribeReminderUpdate', () => {
    it('should return async iterator', () => {
      const result = resolver.subscribeReminderUpdate();
      expect(mockPubSub.asyncIterator).toHaveBeenCalledWith(
        SubscriptionTypes.ReminderSubscription,
      );
      expect(result).toEqual(expect.any(Object));
    });
  });
});
