import { organizationHospitalMapper } from './organization.mapper';
import { HospitalModel } from '@clinify/hospitals/models/hospital.model';
import { PlanStatus } from '@clinify/shared/enums/hospital';

describe('Organization Mapper', () => {
  describe('organizationHospitalMapper', () => {
    it('should map hospital to FHIR organization with all fields', () => {
      const hospital: HospitalModel = {
        id: '1',
        clinifyId: 'hosp123',
        name: 'Test Hospital',
        facilityType: 'Healthcare Provider',
        planStatus: PlanStatus.Paid,
        phoneNumber: {
          value: '+**********',
          countryCode: '+1',
          countryName: 'United States',
        },
        supportMail: '<EMAIL>',
        address: '123 Medical Street',
        city: 'Medical City',
        state: 'Medical State',
        country: 'Medical Country',
        lga: 'Medical District',
      };

      const result = organizationHospitalMapper(hospital);

      expect(result).toEqual({
        resourceType: 'Organization',
        id: 'hosp123',
        identifier: [
          {
            system: 'urn:clinify:hospital',
            value: 'hosp123',
          },
        ],
        active: true,
        type: [
          {
            coding: [
              {
                system:
                  'http://terminology.hl7.org/CodeSystem/organization-type',
                code: 'prov',
                display: 'Healthcare Provider',
              },
            ],
          },
        ],
        name: 'Test Hospital',
        telecom: [
          {
            system: 'phone',
            value: '+**********',
            use: 'work',
          },
          {
            system: 'email',
            value: '<EMAIL>',
            use: 'work',
          },
        ],
        address: [
          {
            use: 'work',
            type: 'both',
            text: '123 Medical Street',
            city: 'Medical City',
            state: 'Medical State',
            country: 'Medical Country',
            district: 'Medical District',
          },
        ],
      });
    });

    it('should map hospital to FHIR organization with minimal fields', () => {
      const hospital: HospitalModel = {
        id: '2',
        clinifyId: 'hosp123',
        name: 'Test Hospital',
        planStatus: PlanStatus.Unpaid,
        facilityType: 'Other',
        address: '123 Medical Street',
        city: 'Medical City',
        state: 'Medical State',
        country: 'Medical Country',
        lga: '',
      };

      const result = organizationHospitalMapper(hospital);

      expect(result).toEqual({
        resourceType: 'Organization',
        id: 'hosp123',
        identifier: [
          {
            system: 'urn:clinify:hospital',
            value: 'hosp123',
          },
        ],
        active: false,
        type: [
          {
            coding: [
              {
                system:
                  'http://terminology.hl7.org/CodeSystem/organization-type',
                code: 'other',
                display: 'Other',
              },
            ],
          },
        ],
        name: 'Test Hospital',
        telecom: [],
        address: [
          {
            use: 'work',
            type: 'both',
            text: '123 Medical Street',
            city: 'Medical City',
            state: 'Medical State',
            country: 'Medical Country',
            district: '',
          },
        ],
      });
    });

    it('should handle missing contact information', () => {
      const hospital: HospitalModel = {
        id: '3',
        clinifyId: 'hosp123',
        name: 'Test Hospital',
        planStatus: PlanStatus.Paid,
        facilityType: 'Healthcare Provider',
        phoneNumber: null,
        supportMail: null,
        address: '123 Medical Street',
        city: 'Medical City',
        state: 'Medical State',
        country: 'Medical Country',
        lga: '',
      };

      const result = organizationHospitalMapper(hospital);

      expect(result.telecom).toEqual([]);
    });

    it('should handle only phone number present', () => {
      const hospital: HospitalModel = {
        id: '4',
        clinifyId: 'hosp123',
        name: 'Test Hospital',
        planStatus: PlanStatus.Paid,
        facilityType: 'Healthcare Provider',
        phoneNumber: {
          value: '+**********',
          countryCode: '+1',
          countryName: 'United States',
        },
        address: '123 Medical Street',
        city: 'Medical City',
        state: 'Medical State',
        country: 'Medical Country',
        lga: '',
      };

      const result = organizationHospitalMapper(hospital);

      expect(result.telecom).toEqual([
        {
          system: 'phone',
          value: '+**********',
          use: 'work',
        },
      ]);
    });

    it('should handle only email present', () => {
      const hospital: HospitalModel = {
        id: '5',
        clinifyId: 'hosp123',
        name: 'Test Hospital',
        planStatus: PlanStatus.Paid,
        facilityType: 'Healthcare Provider',
        supportMail: '<EMAIL>',
        address: '123 Medical Street',
        city: 'Medical City',
        state: 'Medical State',
        country: 'Medical Country',
        lga: '',
      };

      const result = organizationHospitalMapper(hospital);

      expect(result.telecom).toEqual([
        {
          system: 'email',
          value: '<EMAIL>',
          use: 'work',
        },
      ]);
    });

    it('should handle headquarters reference', () => {
      const hospital: HospitalModel = {
        id: '6',
        clinifyId: 'hosp123',
        name: 'Test Hospital',
        planStatus: PlanStatus.Paid,
        facilityType: 'Healthcare Provider',
        address: '123 Medical Street',
        city: 'Medical City',
        state: 'Medical State',
        country: 'Medical Country',
        lga: '',
        hqFacilityId: 'hq123',
      };

      const result = organizationHospitalMapper(hospital);

      expect(result.partOf).toEqual({
        reference: 'Organization/hq123',
      });
    });
  });
});
