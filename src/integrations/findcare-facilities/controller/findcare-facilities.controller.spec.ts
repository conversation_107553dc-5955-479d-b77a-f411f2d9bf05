import { INestApplication, ValidationPipe } from '@nestjs/common';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { getModelToken } from '@nestjs/mongoose';
import { Test } from '@nestjs/testing';
import { TypeOrmModule } from '@nestjs/typeorm';
import request from 'supertest';
import { TestDataSourceOptions } from '../../../data-source';
import { FindCareFacilitiesService } from '../services/findcare-facilities.service';
import { findCareFacilitiesMock } from '@clinify/__mocks__/findcare-facilities.mock';
import { GqlAuthGuard } from '@clinify/authentication/guards/gql.auth.guard';
import {
  DefaultInvImage,
  InventoryUpload,
} from '@clinify/integrations/inventory-blk/entities/FileUpload.entity';
import { IntegrationsModule } from '@clinify/integrations/module';
import { PriceUpload } from '@clinify/integrations/price-blk/entities/PriceFileUpload.entity';
import gqlAuthGuardMock from '@mocks/gqlAuthGuard.mock';

jest.mock('../services/findcare-facilities.service');
jest.mock('@aws-sdk/client-bedrock-runtime', () => {
  const mockInvokeModelCommand = jest.fn();
  return {
    BedrockRuntimeClient: jest.fn(() => ({
      send: jest.fn(),
    })),
    InvokeModelCommand: mockInvokeModelCommand,
  };
});

const findCareFacilitiesServiceMock = {
  fetchFindCareFacilityList: jest.fn(() => findCareFacilitiesMock),
};

describe('FindCareFacilitiesController', () => {
  let testHttpServer: request.SuperTest<request.Test>;
  let app: INestApplication;

  beforeAll(async () => {
    const builder = await Test.createTestingModule({
      imports: [
        IntegrationsModule,
        TypeOrmModule.forRoot(TestDataSourceOptions),
        EventEmitterModule.forRoot(),
      ],
    })
      .overrideProvider(FindCareFacilitiesService)
      .useValue(findCareFacilitiesServiceMock)
      .overrideProvider(getModelToken(InventoryUpload.name))
      .useValue({})
      .overrideProvider(getModelToken(DefaultInvImage.name))
      .useValue({})
      .overrideProvider(getModelToken(PriceUpload.name))
      .useValue({})
      .overrideGuard(GqlAuthGuard)
      .useValue(gqlAuthGuardMock())
      .compile();

    app = builder.createNestApplication();
    app.useGlobalPipes(new ValidationPipe());
    await app.init();

    testHttpServer = await request(app.getHttpServer());
  });

  afterAll(async () => await app.close());

  describe('findCareFacilities', () => {
    test('should fetch find care facility list', (done) => {
      testHttpServer
        .get('/integrations/findcare-facilities')
        .expect(({ body }) => {
          expect(body).toEqual(findCareFacilitiesMock);
        })
        .expect(200)
        .end(done);
    });
  });
});
