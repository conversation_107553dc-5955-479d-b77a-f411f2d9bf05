import { Logger } from '@nestjs/common';
import { getModelToken } from '@nestjs/mongoose';
import { Test, TestingModule } from '@nestjs/testing';
import { TypeOrmModule, getRepositoryToken } from '@nestjs/typeorm';
import { DataSource } from 'typeorm';
import { PriceUploadConsumer } from './price-blk.consumer';
import { PriceUpload } from '../entities/PriceFileUpload.entity';
import { PriceBlkService } from '../service/price-blk.service';
import { hmoPriceListInputFactory } from '@clinify/__mocks__/factories/price.factory';
import { logMock, loggerMock } from '@clinify/__mocks__/logger';
import { MockPriceUploadModel } from '@clinify/__mocks__/price-upload-model.mock';
import { TestDataSourceOptions } from '@clinify/data-source';
import { extendModel } from '@clinify/database/extendModel';
import { IPriceUploadJob } from '@clinify/integrations/price-blk/interface/price.interface';
import { PricesModel } from '@clinify/prices/models/price.model';

const csvPayload = hmoPriceListInputFactory.buildList(2);
const info: IPriceUploadJob = {
  id: 'job-id',
  fileKey: 'file-key',
  hospitalId: 'hmo-services-private',
  clinifyId: 'clinify-id',
  providerCode: 'provider-code',
};
const unzipCSV = {
  pipe: jest.fn(() => csvPayload),
};

const fileToUpload = {
  fieldname: 'file',
  originalname: 'file-to-upload.csv',
  encoding: 'utf-8',
  mimetype: 'text/csv',
  size: 1024,
  stream: null,
  destination: '/uploads',
  filename: 'file-to-upload.csv',
  path: '/uploads/file-to-upload.csv',
  buffer: Buffer.from(''),
  Body: {
    pipe: jest.fn(() => unzipCSV),
  },
};
const mutator = {
  id: 'profile-id',
  fullName: 'John Doe',
  hospital: {
    id: 'hospital-id',
    hqFacilityId: 'hq-facility-id',
  },
};
const mockPriceBlkService = {
  getS3Object: jest.fn(() => fileToUpload),
  deleteFromS3: jest.fn(),
  processUploadHandler: jest.fn(),
};
const mockPriceRepository = {
  getPriceList: jest.fn(() => Promise.resolve({ list: [] })),
  findOne: jest.fn(() => Promise.resolve({})),
};

const entityManagerMocks = {
  queryRunner: { isTransactionActive: true },
  save: jest.fn(),
  delete: jest.fn(),
  find: jest.fn(() => Promise.resolve([])),
  findOne: jest.fn(() => Promise.resolve(mutator)),
  withRepository: jest.fn((cb) => cb),
  getCustomRepository: jest.fn((cb) => cb),
  getRepository: jest.fn((cb) => cb),
  createQueryBuilder: jest.fn(() => ({
    select: jest.fn().mockReturnThis(),
    where: jest.fn().mockReturnThis(),
    innerJoinAndSelect: jest.fn().mockReturnThis(),
    getOne: jest.fn(() => Promise.resolve(mutator)),
    getMany: jest.fn(() => Promise.resolve([])),
  })),
};

const dataSourceMock = {
  transaction: jest.fn((cb) => cb(entityManagerMocks)),

  getRepository: jest.fn(() => ({
    extend: jest.fn(),
  })),
  manager: entityManagerMocks,
};

describe('PriceUploadConsumer', () => {
  let consumer: PriceUploadConsumer;
  let module: TestingModule;

  beforeAll(async () => {
    module = await Test.createTestingModule({
      imports: [TypeOrmModule.forRoot(TestDataSourceOptions)],
      providers: [
        PriceUploadConsumer,
        {
          provide: getModelToken(PriceUpload.name),
          useValue: MockPriceUploadModel,
        },
        extendModel(PricesModel, mockPriceRepository),
        {
          provide: PriceBlkService,
          useValue: mockPriceBlkService,
        },
        {
          provide: DataSource,
          useValue: dataSourceMock,
        },
        { ...loggerMock },
      ],
    })
      .overrideProvider(getModelToken(PriceUpload.name))
      .useValue(MockPriceUploadModel)
      .overrideProvider(getRepositoryToken(PricesModel))
      .useValue(mockPriceRepository)
      .overrideProvider(Logger)
      .useValue(logMock)
      .compile();

    consumer = module.get<PriceUploadConsumer>(PriceUploadConsumer);
  });

  beforeEach(() => jest.clearAllMocks());

  afterAll(async () => await module.close());

  it('processUploadHandler(): should return true for complete job', async () => {
    const jobMock = {
      isCompleted: jest.fn(() => Promise.resolve(true)),
    } as any;
    const response = await consumer.processUploadHandler(jobMock);
    expect(response).toBe(true);
  });

  it('processUploadHandler(): should fail then retry', async () => {
    const jobMock = {
      id: 'job-id',
      data: info,
      isCompleted: jest.fn(() => Promise.resolve(false)),
    } as any;
    entityManagerMocks.save = jest
      .fn()
      .mockRejectedValueOnce('syntax id not recognized')
      .mockResolvedValue({});

    await consumer.processUploadHandler(jobMock);
    expect(mockPriceBlkService.deleteFromS3).toHaveBeenCalledTimes(1);
    expect(mockPriceBlkService.deleteFromS3).toHaveBeenLastCalledWith(
      'file-key',
    );
  });

  it('processUploadHandler(): should true if upload is processed', async () => {
    const jobMock = {
      id: 'job-id',
      data: info,
      isCompleted: jest.fn(() => Promise.resolve(false)),
    } as any;
    jest
      .spyOn(entityManagerMocks, 'createQueryBuilder')
      .mockImplementationOnce(() => ({
        where: jest.fn().mockReturnThis(),
        innerJoinAndSelect: jest.fn().mockReturnThis(),
        select: jest.fn().mockReturnThis(),
        getOne: jest.fn(() => Promise.resolve(mutator)),
        getMany: jest.fn(() => Promise.resolve([])),
      }));
    entityManagerMocks.findOne = jest
      .fn()
      .mockImplementationOnce(() => Promise.resolve(mutator));

    entityManagerMocks.findOne = jest.fn().mockImplementationOnce(() =>
      Promise.resolve({
        id: 'hq-facility-id',
        useHQFacilityTariffs: true,
      } as any),
    );

    entityManagerMocks.find = jest.fn(() => Promise.resolve([]));
    entityManagerMocks.save = jest.fn().mockResolvedValue({});

    const response = await consumer.processUploadHandler(jobMock);
    expect(entityManagerMocks.save).toHaveBeenCalledTimes(1);
    expect(mockPriceBlkService.deleteFromS3).toHaveBeenCalledTimes(1);
    expect(mockPriceBlkService.deleteFromS3).toHaveBeenLastCalledWith(
      'file-key',
    );
    expect(response).toBe(true);
  });

  it('processUploadHandler(): should throw error when profile is not found', async () => {
    const jobMock = {
      id: 'job-id',
      data: info,
      isCompleted: jest.fn(() => Promise.resolve(false)),
    } as any;
    jest
      .spyOn(entityManagerMocks, 'createQueryBuilder')
      .mockImplementationOnce(() => ({
        where: jest.fn().mockReturnThis(),
        innerJoinAndSelect: jest.fn().mockReturnThis(),
        select: jest.fn().mockReturnThis(),
        getOne: jest.fn(() => Promise.resolve(null)),
        getMany: jest.fn(() => Promise.resolve([])),
      }));

    await consumer.processUploadHandler(jobMock);
    expect(mockPriceBlkService.deleteFromS3).toHaveBeenCalledTimes(1);
    expect(mockPriceBlkService.deleteFromS3).toHaveBeenLastCalledWith(
      'file-key',
    );
  });
});
