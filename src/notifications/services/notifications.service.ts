/* eslint-disable max-lines */
import { Inject, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { RedisPubSub } from 'graphql-redis-subscriptions';
import { HmoProfileModel } from '../../hmo-profiles/models/hmo-profile.model';
import { DispenseDetailsModel } from '../../medications/models/dispense-details.model';
import { NursingServiceDetailModel } from '../../nursing-services/models/nursing-services-details.model';
import { InvestigationRequestType } from '../../shared/enums/investigation';
import { UserType } from '../../shared/enums/users';
import { SurgeryModel } from '../../surgeries/models/surgery.model';
import {
  DependentModel,
  DisabilityModel,
  FamilyHistoryModel,
  GynecologicHistoryModel,
  HabitModel,
  NextOfKinModel,
  ObstetricHistoryModel,
  OncologyHistoryModel,
  PastSurgeryModel,
  PhysicalActivityModel,
  PreExistingConditionModel,
} from '../../users/models';
import { PastEncounterModel } from '../../users/models/past-encounters.model';
import { WaitingListStatus } from '../../waiting-list/interfaces/waiting-list.interface';
import {
  IDetails,
  INotification,
} from '../interfaces/notifications.interfaces';
import { NotificationsModel } from '../models/notifications.model';
import { INotificationRepository } from '../repositories/notifications.respository';
import { NotificationsResponse } from '../responses/notifications.response';
import { NotificationFilterInput } from '../validators/notifications-filter.input';
import { AdmissionModel } from '@clinify/admissions/models/admission.model';
import { AllergyModel } from '@clinify/allergies/models/allergy.model';
import { OrganisationAppointmentModel } from '@clinify/appointments/models/organisation_appointment.model';
import { BillModel } from '@clinify/bills/models/bill.model';
import { ConsultationModel } from '@clinify/consultations/models/consultation.model';
import { HandoverNoteItemModel } from '@clinify/handover-notes/models/handover-note-item.model';
import { HandoverNoteModel } from '@clinify/handover-notes/models/handover-note.model';
import { HmoClaimModel } from '@clinify/hmo-claims/models/hmo-claim.model';
import { HmoHospitalModel } from '@clinify/hmo-providers/models/hmo-hospital.model';
import { HospitalModel } from '@clinify/hospitals/models/hospital.model';
import { ImmunizationModel } from '@clinify/immunizations/models/immunization.model';
import { InventoryModel } from '@clinify/inventory/models/inventory.model';
import { InvestigationModel } from '@clinify/investigation/models/investigation.model';
import { MedicationModel } from '@clinify/medications/models/medication.model';
import { MedicationDetailsModel } from '@clinify/medications/models/medication_details.model';
import { CancerScreeningModel } from '@clinify/nursing-services/models/cancer-screening.model';
import { PostOperationModel } from '@clinify/nursing-services/models/post-operation.model';
import { PreChemoEducationModel } from '@clinify/nursing-services/models/pre-chemo-education.model';
import { OncologyConsultationHistoryModel } from '@clinify/oncology-consultation-history/models/oncology-consultation-history.model';
import { PreauthorisationReferralModel } from '@clinify/pre-authorisations-referral/models/preauthorisation-referral.model';
import { PreauthorisationModel } from '@clinify/pre-authorisations/models/preauthorisation.model';
import { AntenatalModel } from '@clinify/pregnancy-care/models/antenatal.model';
import { LabourDeliveryModel } from '@clinify/pregnancy-care/models/labour-delivery.model';
import { PartographModel } from '@clinify/pregnancy-care/models/Partograph.model';
import { PostnatalModel } from '@clinify/pregnancy-care/models/postnatal.model';
import { PricesModel } from '@clinify/prices/models/price.model';
import {
  AdmissionSubModule,
  DashboardIcon,
  NotificationTag,
} from '@clinify/shared/enums/notifications';
import {
  joinWithAnd,
  prefixAOrAn,
  startsWithVowel,
} from '@clinify/shared/helper';
import { NotificationsMetaData } from '@clinify/shared/validators/notifications.input';
import { PreOperationChecklistModel } from '@clinify/surgeries/models/preoperation-checklist.model';
import { ProfileModel } from '@clinify/users/models/profile.model';
import { PUB_SUB } from '@clinify/utils/subscriptions/pubSub';
import { SubscriptionTypes } from '@clinify/utils/subscriptions/types';
import { VitalModel } from '@clinify/vitals/models/vital.model';
import { WaitingListModel } from '@clinify/waiting-list/models/waiting-list.model';

interface IComposerReturn {
  description: string;
  hospitalId: string;
  profileId: string;
  modelName?: string;
  hmoProviderId?: string;
  metaData: NotificationsMetaData;
}

@Injectable()
export class NotificationsService {
  constructor(
    @InjectRepository(NotificationsModel)
    public repository: INotificationRepository,
    @Inject(PUB_SUB) private pubSub: RedisPubSub,
  ) {}

  get composers() {
    return {
      [DashboardIcon.Consultation]: this.consultationComposer.bind(this),
      [DashboardIcon.Vital]: this.vitalsComposer.bind(this),
      [DashboardIcon.Claim]: this.hmoClaimComposer.bind(this),
      [DashboardIcon.Antenatal]: this.antenatalComposer.bind(this),
      [DashboardIcon.AntenatalPartograph]:
        this.antenatalPartographComposer.bind(this),
      [DashboardIcon.Allergy]: this.allergyComposer.bind(this),
      [DashboardIcon.Admission]: this.admissionComposer.bind(this),
      [DashboardIcon.Procedure]: this.procedureComposer.bind(this),
      [DashboardIcon.ProcedureChecklist]:
        this.procedureChecklistComposer.bind(this),
      [DashboardIcon.Medication]: this.medicationComposer.bind(this),
      [DashboardIcon.Radiology]: this.radiologyComposer.bind(this),
      [DashboardIcon.Laboratory]: this.laboratoryComposer.bind(this),
      [DashboardIcon.Immunization]: this.immunizationComposer.bind(this),
      [DashboardIcon.PreAuthorization]:
        this.preauthorizationComposer.bind(this),
      [DashboardIcon.Inventory]: this.inventorynComposer.bind(this),
      [DashboardIcon.Appointment]: this.appointmentComposer.bind(this),
      [DashboardIcon.PatientTransfers]:
        this.patientTransfersComposer.bind(this),
      [DashboardIcon.Price]: this.priceComposer.bind(this),
      [DashboardIcon.Billing]: this.billingComposer.bind(this),
      [DashboardIcon.PatientWaitingList]: this.waitlistComposer.bind(this),
      [DashboardIcon.PatientRegistration]:
        this.patientRegistrationComposer.bind(this),
      [DashboardIcon.CoverageInformation]:
        this.coverageInformationComposer.bind(this),
      [DashboardIcon.PersonalInformation]:
        this.personalInformationComposer.bind(this),
      [DashboardIcon.BackgroundInformation]:
        this.backgroundInformationComposer.bind(this),
      [DashboardIcon.PatientProfile]: this.patientProfileComposer.bind(this),
      [DashboardIcon.Investigation]: this.investigationComposer.bind(this),
      [DashboardIcon.NursingService]: this.nursingServiceComposer.bind(this),
      [DashboardIcon.PostOperation]: this.postOperationComposer.bind(this),
      [DashboardIcon.Postnatal]: this.postnatalComposer.bind(this),
      [DashboardIcon.LabourDelivery]: this.labourAndDeliveryComposer.bind(this),
      [DashboardIcon.HandoverNote]: this.handoverComposer.bind(this),
      [DashboardIcon.OncologyConsultationHistory]:
        this.oncologyConsultationComposer.bind(this),
      [DashboardIcon.RequestProcedure]:
        this.requestProcedureComposer.bind(this),
      [DashboardIcon.PreChemoEducation]:
        this.preChemoEducationComposer.bind(this),
      [DashboardIcon.CancerScreening]: this.cancerScreeningComposer.bind(this),
      [DashboardIcon.PreAuthorizationReferral]:
        this.preAuthorizationReferralComposer.bind(this),
      [DashboardIcon.ConsultationReferral]:
        this.consultationReferralComposer.bind(this),
    };
  }

  async getAllNotifications(
    mutator: ProfileModel,
    profileId: string,
    filter: Partial<NotificationFilterInput>,
  ): Promise<NotificationsResponse> {
    if (mutator.type === UserType.Patient) {
      return this.repository.findByProfile(mutator, mutator.id, filter);
    }
    if (mutator.hospitalId) {
      if (profileId) {
        return this.repository.findByProfileAndHospital(
          mutator,
          profileId,
          mutator.hospitalId,
          filter,
        );
      }
      return this.repository.findByHospital(
        mutator,
        mutator.hospitalId,
        filter,
      );
    }
    return this.repository.findByProfile(mutator, profileId, filter);
  }
  async markAsSeen(mutator: ProfileModel, ids: string[]): Promise<boolean> {
    await this.repository.markAsSeen(mutator.id, ids);
    return true;
  }
  async markAllAsSeen(
    mutator: ProfileModel,
    profileId?: string,
  ): Promise<boolean> {
    await this.repository.markAllAsSeen(mutator.id, profileId);
    return true;
  }
  async constructNotificationModel(
    mutator: ProfileModel,
    details: IDetails,
  ): Promise<NotificationsModel | null> {
    const { modelName, action, item } = details;
    const composed = await this.composers[modelName].call(
      this,
      mutator,
      details,
    );

    if (!composed) return null;
    const {
      description,
      profileId,
      metaData,
      hospitalId,
      modelName: model,
      hmoProviderId,
    } = composed;
    return {
      title: model || modelName,
      tag: action,
      profileId,
      hospitalId: hospitalId || mutator.hospitalId,
      description,
      hmoProviderId,
      metaData: {
        ...metaData,
        clinifyId: item?.clinifyId,
        specialty: item?.specialty,
        department: item?.department,
      },
    } as NotificationsModel;
  }

  async handleNoticationEvent(payload: INotification): Promise<void> {
    try {
      const { profile, details } = payload;
      const formattedData = await this.constructNotificationModel(
        profile,
        details,
      );
      if (formattedData) {
        const noti = await this.repository.save(formattedData);
        this.pubSub.publish(
          SubscriptionTypes.NotificationFacilitySubscription,
          {
            [SubscriptionTypes.NotificationFacilitySubscription]: {
              data: noti,
              triggeredBy: profile.id,
            },
          },
        );
      }
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error(error);
    }
  }

  private async ensureHospitalAndProfile(record: {
    hospitalId: string;
    profileId: string;
    profile?: ProfileModel;
    hospital?: HospitalModel;
  }) {
    if (!record.hospital && record.hospitalId) {
      record.hospital = await this.repository.manager.findOne(HospitalModel, {
        where: { id: record.hospitalId },
      });
    }
    if (!record.profile && record.profileId) {
      record.profile = await this.repository.manager.findOne(ProfileModel, {
        where: { id: record.profileId },
      });
    }
    return record as any;
  }

  private async ensureHandoverParties(
    record: Partial<HandoverNoteModel>,
  ): Promise<[handover: HandoverNoteModel, patientCount: number]> {
    const { hospital, hospitalId, handoverBy, handoverById, items } = record;
    let patientCount = items?.length || 0;
    if (!hospital && hospitalId) {
      record.hospital = await this.repository.manager.findOne(HospitalModel, {
        where: { id: hospitalId },
        select: { id: true, name: true, address: true },
      });
    }
    if (!handoverBy && handoverById) {
      record.handoverBy = await this.repository.manager.findOne(ProfileModel, {
        where: { id: handoverById },
        select: { id: true, fullName: true },
      });
    }
    if (!items) {
      patientCount = await this.repository.manager.count(
        HandoverNoteItemModel,
        {
          where: { handoverNoteId: record.id },
        },
      );
    }

    return [record as HandoverNoteModel, patientCount];
  }

  private formatHospitalNameAndAddress(hospital: HospitalModel) {
    return `at ${hospital.name}${
      hospital.address ? `, located in ${hospital.address}` : ''
    }`;
  }

  private async admissionComposer(
    mutator: ProfileModel,
    details: IDetails,
  ): Promise<IComposerReturn> {
    const action = details.action;
    let item = details.item;
    if ('item' in item) {
      const { item: _item, moduleName } = item as {
        item: any;
        moduleName: AdmissionSubModule;
      };
      let description = '';
      const hospitalId = mutator.hospitalId;
      const profileId = _item.admission.profileId;
      const recordId = _item?.admissionId;
      const clinicDetails = this.formatHospitalNameAndAddress(mutator.hospital);

      const profile = await this.repository.manager.findOne(ProfileModel, {
        where: { id: _item.admission.profileId },
      });
      const profileName = profile?.fullName;
      switch (moduleName) {
        case AdmissionSubModule.DischargePatient: {
          if (action === NotificationTag.Created) {
            description = `${mutator.fullName} discharged ${profileName} ${clinicDetails}`;
          } else {
            description = `${
              mutator.fullName
            } ${action.toLowerCase()} a Discharge for ${profileName} ${clinicDetails}`;
          }
          break;
        }
        case AdmissionSubModule.BloodTransfusion:
        case AdmissionSubModule.Input:
        case AdmissionSubModule.Output:
        case AdmissionSubModule.Lines:
        case AdmissionSubModule.TransferPatient: {
          description = `${
            mutator.fullName
          } ${action.toLowerCase()}  ${prefixAOrAn(
            moduleName,
          )} for ${profileName} ${clinicDetails}`;
          break;
        }

        default:
          break;
      }
      return {
        description,
        hospitalId,
        profileId,
        metaData: {
          id: recordId,
          recordType: DashboardIcon.Admission,
        },
      };
    }
    let deletedCount = 0;
    let deletedIds: string[] = [];
    if (action === NotificationTag.Deleted) {
      deletedCount = item.length;
      deletedIds = item.map((i) => i.id);
      item = item[0];
    }

    const record: AdmissionModel = await this.ensureHospitalAndProfile(item);
    const clinicDetails = this.formatHospitalNameAndAddress(record.hospital);
    const profileName = record?.profile?.fullName || '';
    let description = '';
    if ([NotificationTag.Admitted].includes(action)) {
      description = `${
        mutator.fullName
      } ${action.toLowerCase()} ${profileName} ${clinicDetails}`;
    } else if (action === NotificationTag.Deleted) {
      description = `${mutator.fullName} deleted ${deletedCount} admissions for ${profileName} ${clinicDetails}`;
    } else {
      description = `${
        mutator.fullName
      } ${action.toLowerCase()} an admission for ${profileName} ${clinicDetails}.`;
    }
    return {
      description,
      hospitalId: record.hospitalId,
      profileId: record.profileId,
      metaData: {
        id: record.id,
        ids: deletedIds,
        recordType: DashboardIcon.Admission,
        recordId: record.id,
        clinifyId: record?.profile?.clinifyId,
        hospitalId: record.hospitalId,
        profileId: record.profileId,
      },
    };
  }

  private async consultationComposer(
    mutator: ProfileModel,
    details: IDetails,
  ): Promise<IComposerReturn> {
    let item = details.item;
    const action = details.action;
    let deletedCount = 0;
    let deletedIds: string[] = [];
    if (action === NotificationTag.Deleted) {
      deletedCount = item.length;
      deletedIds = item.map((i) => i.id);
      item = item[0];
    }
    const record: ConsultationModel = await this.ensureHospitalAndProfile(item);
    const clinicDetails = this.formatHospitalNameAndAddress(record.hospital);
    const profileName = record?.profile?.fullName || '';
    let description = `${
      mutator.fullName
    } ${action.toLowerCase()} a Consultation for ${profileName} ${clinicDetails}.`;
    if (action === NotificationTag.Deleted) {
      description = `${mutator.fullName} deleted ${deletedCount} consultations for ${profileName} ${clinicDetails}`;
    }
    return {
      description,
      hospitalId: record.hospitalId,
      profileId: record.profileId,
      metaData: {
        id: record.id,
        ids: deletedIds,
        recordType: DashboardIcon.Consultation,
        recordId: record.id,
        clinifyId: record?.profile?.clinifyId,
        hospitalId: record.hospitalId,
        profileId: record.profileId,
      },
    };
  }

  private async immunizationComposer(
    mutator: ProfileModel,
    details: IDetails,
  ): Promise<IComposerReturn> {
    let item = details.item;
    const action = details.action;
    let deletedCount = 0;
    let deletedIds: string[] = [];
    if (action === NotificationTag.Deleted) {
      deletedCount = item.length;
      deletedIds = item.map((i) => i.id);
      item = item[0];
    }
    const record: ImmunizationModel = await this.ensureHospitalAndProfile(item);
    const clinicDetails = this.formatHospitalNameAndAddress(record.hospital);
    const profileName = record?.profile?.fullName || '';
    let description = `${
      mutator.fullName
    } ${action.toLowerCase()} an Immunization for ${profileName} ${clinicDetails}.`;
    if (action === NotificationTag.Deleted) {
      description = `${mutator.fullName} deleted ${deletedCount} immunizations for ${profileName} ${clinicDetails}`;
    }
    return {
      description,
      hospitalId: record.hospitalId,
      profileId: record.profileId,
      metaData: {
        id: record.id,
        ids: deletedIds,
        recordType: DashboardIcon.Immunization,
        recordId: record.id,
        clinifyId: record?.profile?.clinifyId,
        hospitalId: record.hospitalId,
        profileId: record.profileId,
      },
    };
  }
  private async radiologyComposer(
    mutator: ProfileModel,
    details: IDetails,
  ): Promise<IComposerReturn> {
    let item = details.item;
    const action = details.action;
    let deletedCount = 0;
    let deletedIds: string[] = [];
    if (action === NotificationTag.Deleted) {
      deletedCount = item.length;
      deletedIds = item.map((i) => i.id);
      item = item[0];
    }
    const record: InvestigationModel = await this.ensureHospitalAndProfile(
      item,
    );
    const clinicDetails = this.formatHospitalNameAndAddress(record.hospital);
    const profileName = record?.profile?.fullName || '';
    const testName = record?.examinationType?.length
      ? joinWithAnd(
          record?.examinationType?.map((test) => test.examType).filter(Boolean),
        )
      : '';
    const preposition = NotificationTag.Emailed === action ? 'to' : 'for';
    let description = `${
      mutator.fullName
    } ${action.toLowerCase()} a Radiology Investigation (${testName}) ${preposition} ${profileName} ${clinicDetails}.`;
    if (action === NotificationTag.Deleted) {
      description = `${mutator.fullName} deleted ${deletedCount} radiology investigations for ${profileName} ${clinicDetails}`;
    }
    return {
      description,
      hospitalId: record.hospitalId,
      profileId: record.profileId,
      metaData: {
        id: record.id,
        ids: deletedIds,
        recordType: DashboardIcon.Radiology,
        recordId: record.id,
        clinifyId: record?.profile?.clinifyId,
        hospitalId: record.hospitalId,
        profileId: record.profileId,
      },
    };
  }

  private async laboratoryComposer(
    mutator: ProfileModel,
    details: IDetails,
  ): Promise<IComposerReturn> {
    let item = details.item;
    const action = details.action;
    let deletedCount = 0;
    let deletedIds: string[] = [];
    if (action === NotificationTag.Deleted) {
      deletedCount = item.length;
      deletedIds = item.map((i) => i.id);
      item = item[0];
    }
    const record: InvestigationModel = await this.ensureHospitalAndProfile(
      item,
    );
    const clinicDetails = this.formatHospitalNameAndAddress(record.hospital);
    const profileName = record?.profile?.fullName || '';
    const testName = record?.testInfo?.length
      ? joinWithAnd(
          record?.testInfo?.map((test) => test.testName).filter(Boolean),
        )
      : '';
    const preposition = NotificationTag.Emailed === action ? 'to' : 'for';
    let description = `${
      mutator.fullName
    } ${action.toLowerCase()} a Laboratory Investigation (${testName}) ${preposition} ${profileName} ${clinicDetails}.`;
    if (action === NotificationTag.Deleted) {
      description = `${mutator.fullName} deleted ${deletedCount} laboratory investigations for ${profileName} ${clinicDetails}`;
    }
    return {
      description,
      hospitalId: record.hospitalId,
      profileId: record.profileId,
      metaData: {
        id: record.id,
        ids: deletedIds,
        recordType: DashboardIcon.Laboratory,
        recordId: record.id,
        clinifyId: record?.profile?.clinifyId,
        hospitalId: record.hospitalId,
        profileId: record.profileId,
      },
    };
  }

  private async medicationComposer(
    mutator: ProfileModel,
    details: IDetails,
  ): Promise<IComposerReturn> {
    let item = details.item;
    const action = details.action;
    let deletedCount = 0;
    let deletedIds: string[] = [];
    if (action === NotificationTag.Deleted) {
      deletedCount = item.length;
      deletedIds = item.map((i) => i.id);
      item = item[0];
    }
    let record: MedicationModel;
    let medicationNames = '';
    if (
      [
        NotificationTag.Dispensed,
        NotificationTag.DispensedUpdated,
        NotificationTag.DispensedDeleted,
      ].includes(action)
    ) {
      const medicationDetails = item as DispenseDetailsModel;

      const medication = await this.repository.manager.findOne(
        MedicationModel,
        {
          where: { id: medicationDetails.medication.id },
          relations: ['profile', 'hospital', 'details'],
        },
      );
      const clinicDetails = this.formatHospitalNameAndAddress(
        medication.hospital,
      );
      const profileName = medication?.profile?.fullName || '';
      details.action === NotificationTag.DispensedDeleted
        ? 'deleted'
        : 'updated';
      let description = '';
      const medicationNames = joinWithAnd(medicationDetails.medicationName);
      switch (details.action) {
        case NotificationTag.Dispensed:
          description = `${mutator.fullName} dispensed a medication (${medicationNames}) for ${profileName} ${clinicDetails}.`;
          break;
        case NotificationTag.DispensedUpdated:
          description = `${mutator.fullName} updated a dispensed medication (${medicationNames}) for ${profileName} ${clinicDetails}.`;
          break;
        case NotificationTag.DispensedDeleted:
          description = `${mutator.fullName} deleted a dispensed medication (${medicationNames}) for ${profileName} ${clinicDetails}.`;
          break;
        default:
          break;
      }
      return {
        description,
        hospitalId: medication.hospitalId,
        profileId: medication.profileId,
        metaData: {
          id: medicationDetails.id,
          recordType: DashboardIcon.Medication,
          recordId: medication.id,
          clinifyId: medication.clinifyId,
          hospitalId: medication.hospitalId,
          profileId: medication.profileId,
        },
      };
    }
    if (NotificationTag.Emailed === action) {
      record = await this.repository.manager.findOne(MedicationModel, {
        where: { id: item.id },
        relations: ['profile', 'hospital', 'details'],
      });
    } else {
      record = await this.ensureHospitalAndProfile(item);
    }
    const clinicDetails = this.formatHospitalNameAndAddress(record.hospital);
    const profileName = record?.profile?.fullName || '';

    if (!record.details?.length) {
      record.details = await this.repository.manager.find(
        MedicationDetailsModel,
        {
          where: { medicationId: record.id },
        },
      );
    }
    if (record.details?.length) {
      let medDetailsName = '';
      let medConsumablesName = '';
      const medDetails = record.details.filter(
        (detail) => !!detail.medicationName,
      );
      const medConsumables = record.details
        .filter((detail) => !!detail.medicationConsumables?.length)
        .reduce((acc, detail) => {
          acc.push(...(detail.medicationConsumables || []));
          return acc;
        }, []);
      if (medConsumables?.length) {
        medConsumablesName = joinWithAnd(
          medConsumables.map((consumable) => consumable.name),
        );
      }
      if (medDetails?.length) {
        medDetailsName = joinWithAnd(
          medDetails.map((detail) => detail.medicationName),
        );
      }
      if (medConsumablesName) {
        medDetailsName = `consumables (${medConsumablesName})`;
      }
      if (medDetailsName) {
        medDetailsName = `medication (${medDetailsName})`;
      }

      if (medDetailsName && medConsumablesName) {
        medicationNames = `${medDetailsName} and ${medConsumablesName}`;
      } else {
        medicationNames = `${
          medDetailsName || medConsumablesName || 'medication'
        }`;
      }
    }
    if (
      [
        NotificationTag.PrescribedUpdated,
        NotificationTag.PrescribedDeleted,
      ].includes(action)
    ) {
      const action =
        details.action === NotificationTag.PrescribedDeleted
          ? 'deleted'
          : 'updated';
      return {
        description: `${mutator.fullName} ${action} a prescribed ${medicationNames} for ${profileName} ${clinicDetails}`,
        hospitalId: record.hospitalId,
        profileId: record.profileId,
        metaData: {
          id: record.id,
        },
      };
    }
    const preposition = NotificationTag.Emailed === action ? 'to' : 'for';
    let description = `${
      mutator.fullName
    } ${action.toLowerCase()} a ${medicationNames} ${preposition} ${profileName} ${clinicDetails}.`;
    if (action === NotificationTag.Deleted) {
      description = `${mutator.fullName} deleted ${deletedCount} medications for ${profileName} ${clinicDetails}`;
    }
    return {
      description,
      hospitalId: record.hospitalId,
      profileId: record.profileId,
      metaData: {
        id: record.id,
        ids: deletedIds,
        recordType: DashboardIcon.Medication,
        recordId: record.id,
        clinifyId: record?.profile?.clinifyId,
        hospitalId: record.hospitalId,
        profileId: record.profileId,
      },
    };
  }

  private async procedureComposer(
    mutator: ProfileModel,
    details: IDetails,
  ): Promise<IComposerReturn> {
    let item = details.item;
    const action = details.action;
    let deletedCount = 0;
    let deletedIds: string[] = [];
    if (action === NotificationTag.Deleted) {
      deletedCount = item.length;
      deletedIds = item.map((i) => i.id);
      item = item[0];
    }
    const record: SurgeryModel = await this.ensureHospitalAndProfile(item);
    const clinicDetails = this.formatHospitalNameAndAddress(record.hospital);
    const profileName = record?.profile?.fullName || '';
    const testName =
      joinWithAnd(
        record?.procedureType?.map((test) => test.type).filter(Boolean) || [],
      ) || '';
    const preposition = NotificationTag.Emailed === action ? 'to' : 'for';
    let description = `
      ${
        mutator.fullName
      } ${action.toLowerCase()} a Procedure (${testName}) ${preposition} ${profileName} ${clinicDetails}.`;
    if (action === NotificationTag.Deleted) {
      description = `${mutator.fullName} deleted ${deletedCount} procedures for ${profileName} ${clinicDetails}`;
    }
    return {
      description,
      hospitalId: record.hospitalId,
      profileId: record.profileId,
      metaData: {
        id: record.id,
        ids: deletedIds,
        recordType: DashboardIcon.Procedure,
        recordId: record.id,
        clinifyId: record?.profile?.clinifyId,
        hospitalId: record.hospitalId,
        profileId: record.profileId,
      },
    };
  }

  private async procedureChecklistComposer(
    mutator: ProfileModel,
    details: IDetails,
  ): Promise<IComposerReturn> {
    let item = details.item;
    const action = details.action;
    let deletedCount = 0;
    let deletedIds: string[] = [];
    if (action === NotificationTag.Deleted) {
      deletedCount = item.length;
      deletedIds = item.map((i) => i.id);
      item = item[0];
    }
    const record: PreOperationChecklistModel =
      await this.ensureHospitalAndProfile(item);
    const creator = await this.repository.manager.findOne(ProfileModel, {
      where: { id: record.creatorId },
      relations: ['hospital'],
    });
    const clinicDetails = this.formatHospitalNameAndAddress(creator.hospital);
    const profileName = record?.profile?.fullName || '';
    let moduleName = '';
    if (record.preSurgeryReadiness)
      moduleName = 'Presurgery Readiness Checklist';
    if (record.patientPreparation) moduleName = 'Patient Readiness Checklist';
    if (record.orStaffReviewed) moduleName = 'OR Staff Readiness Checklist';
    if (record.surgicalSafetyChecklist)
      moduleName = 'Surgical Safety Checklist';
    let description = `
      ${
        mutator.fullName
      } ${action.toLowerCase()} a ${moduleName} for ${profileName} ${clinicDetails}.`;
    if (action === NotificationTag.Deleted) {
      description = `${mutator.fullName} deleted ${deletedCount} procedures for ${profileName} ${clinicDetails}`;
    }
    return {
      description,
      hospitalId: creator.hospitalId,
      profileId: record.profileId,
      metaData: {
        id: record.id,
        ids: deletedIds,
        recordType: DashboardIcon.Procedure,
        recordId: record.id,
        clinifyId: record?.profile?.clinifyId,
        hospitalId: record.hospitalId,
        profileId: record.profileId,
      },
    };
  }

  private async vitalsComposer(
    mutator: ProfileModel,
    details: IDetails,
  ): Promise<IComposerReturn> {
    let item = details.item;
    const action = details.action;
    let deletedCount = 0;
    let deletedIds: string[] = [];
    if (action === NotificationTag.Deleted) {
      deletedCount = item.length;
      deletedIds = item.map((i) => i.id);
      item = item[0];
    }
    const record: VitalModel = await this.ensureHospitalAndProfile(item);
    const creator = await this.repository.manager.findOne(ProfileModel, {
      where: { id: record.creatorId },
      relations: ['hospital'],
    });
    const clinicDetails = this.formatHospitalNameAndAddress(creator.hospital);
    const profileName = record?.profile?.fullName || '';
    let description = `
      ${
        mutator.fullName
      } ${action.toLowerCase()} a Vital signs for ${profileName} ${clinicDetails}.`;
    if (action === NotificationTag.Deleted) {
      description = `${mutator.fullName} deleted ${deletedCount} Vital signs for ${profileName} ${clinicDetails}`;
    }
    return {
      description,
      hospitalId: creator.hospital.id,
      profileId: record.profileId,
      metaData: {
        id: record.id,
        ids: deletedIds,
        recordType: DashboardIcon.Vital,
        recordId: record.id,
        clinifyId: record?.profile?.clinifyId,
        hospitalId: record.hospitalId,
        profileId: record.profileId,
      },
    };
  }

  private async antenatalComposer(
    mutator: ProfileModel,
    details: IDetails,
  ): Promise<IComposerReturn> {
    const action = details.action;
    let item = details.item;
    let deletedCount = 0;
    let deletedIds: string[] = [];
    if (action === NotificationTag.Deleted) {
      deletedCount = item.length;
      deletedIds = item.map((i) => i.id);
      item = item[0];
    }
    const record: AntenatalModel = await this.ensureHospitalAndProfile(item);
    const clinicDetails = `${record.hospitalName}, located in ${
      record.hospitalAddress || ''
    }`;
    const profileName = record?.profile?.fullName || '';
    let description = `${
      mutator.fullName
    } ${action.toLowerCase()} an Antenatal for ${profileName} at ${clinicDetails}.`;
    if (action === NotificationTag.Deleted) {
      description = `${mutator.fullName} deleted ${deletedCount} antenatal records for ${profileName} at ${clinicDetails}`;
    }
    return {
      description,
      hospitalId: record.hospitalId,
      profileId: record.profileId,
      metaData: {
        id: record.id,
        ids: deletedIds,
        recordType: DashboardIcon.Antenatal,
        recordId: record.id,
        clinifyId: record?.profile?.clinifyId,
        hospitalId: record.hospitalId,
        profileId: record.profileId,
      },
    };
  }

  private async antenatalPartographComposer(
    mutator: ProfileModel,
    details: IDetails,
  ): Promise<IComposerReturn> {
    const action = details.action;
    let item = details.item;
    if ('item' in item) {
      const { item: _item, moduleName } = item;
      const record: PartographModel = await this.ensureHospitalAndProfile(
        _item,
      );
      const profileName = record?.profile?.fullName || '';
      const clinicDetails = this.formatHospitalNameAndAddress(mutator.hospital);
      const description = `${
        mutator.fullName
      } ${action.toLowerCase()} ${prefixAOrAn(
        moduleName,
      )} for ${profileName} ${clinicDetails}.`;

      return {
        description,
        hospitalId: mutator.hospitalId,
        profileId: record.profileId,
        metaData: {
          id: record.id,
          recordType: DashboardIcon.AntenatalPartograph,
          recordId: record.id,
          clinifyId: record?.profile?.clinifyId,
          hospitalId: record.hospitalId,
          profileId: record.profileId,
        },
      };
    }
    let deletedCount = 0;
    let deletedIds: string[] = [];
    if (action === NotificationTag.Deleted) {
      deletedCount = item.length;
      deletedIds = item.map((i) => i.id);
      item = item[0];
    }
    const record: PartographModel = await this.ensureHospitalAndProfile(item);

    const clinicDetails = this.formatHospitalNameAndAddress(mutator.hospital);
    const profileName = record?.profile?.fullName || '';
    const moduleName = 'Antenatal Partograph';

    let description = `${mutator.fullName} ${action.toLowerCase()} ${
      startsWithVowel(moduleName[0].toLowerCase()) ? 'an' : 'a'
    } ${moduleName} for ${profileName} ${clinicDetails}.`;

    if (action === NotificationTag.Deleted) {
      description = `${mutator.fullName} deleted ${deletedCount} ${
        deletedCount > 1 ? 'records' : 'record'
      } from ${moduleName} for ${profileName} ${clinicDetails}.`;
    }
    return {
      description,
      hospitalId: mutator.hospitalId,
      profileId: record.profileId,
      metaData: {
        id: record.id,
        ids: deletedIds,
        recordType: DashboardIcon.AntenatalPartograph,
        recordId: record.id,
        clinifyId: record?.profile?.clinifyId,
        hospitalId: record.hospitalId,
        profileId: record.profileId,
      },
    };
  }

  private async allergyComposer(
    mutator: ProfileModel,
    details: IDetails,
  ): Promise<IComposerReturn> {
    const action = details.action;
    let item = details.item;
    let deletedCount = 0;
    let deletedIds: string[] = [];
    if (action === NotificationTag.Deleted) {
      deletedCount = item.length;
      deletedIds = item.map((i) => i.id);
      item = item[0];
    }
    const record: AllergyModel = await this.ensureHospitalAndProfile(item);
    const creator = await this.repository.manager.findOne(ProfileModel, {
      where: { id: record.creatorId },
      relations: ['hospital'],
    });
    const clinicDetails = this.formatHospitalNameAndAddress(creator.hospital);
    const profileName = record?.profile?.fullName || '';
    let description = `${
      mutator.fullName
    } ${action.toLowerCase()} an Allergy for ${profileName} ${clinicDetails}.`;
    if (action === NotificationTag.Deleted) {
      description = `${mutator.fullName} deleted ${deletedCount} Allergies for ${profileName} ${clinicDetails}.`;
    }
    return {
      description,
      hospitalId: creator.hospital.id,
      profileId: record.profileId,
      metaData: {
        id: record.id,
        ids: deletedIds,
        recordType: DashboardIcon.Allergy,
        recordId: record.id,
        clinifyId: record?.profile?.clinifyId,
        hospitalId: record.hospitalId,
        profileId: record.profileId,
      },
    };
  }

  private async billingComposer(
    mutator: ProfileModel,
    details: IDetails,
  ): Promise<IComposerReturn> {
    let item = details.item;
    const action = details.action;
    let deletedCount = 0;
    let deletedIds: string[] = [];
    if (action === NotificationTag.Deleted) {
      deletedCount = item.length;
      deletedIds = item.map((i) => i.id);
      item = item[0];
    }
    if (NotificationTag.Emailed === action) {
      const bill = await this.repository.manager.findOne(BillModel, {
        where: { id: item.id },
        relations: ['receiverProfile', 'createdBy'],
      });
      item = {
        ...bill,
        profile: bill.receiverProfile,
      };
    } else {
      item.profile = item.receiverProfile;
      item.profileId = item.receiverProfileId;
    }
    const record: BillModel & {
      profile: ProfileModel;
    } = await this.ensureHospitalAndProfile(item);
    let creator: ProfileModel;
    if (action === NotificationTag.Paid) {
      creator = await this.repository.manager.findOne(ProfileModel, {
        where: { id: record.receiverProfileId },
        relations: ['hospital'],
      });
    } else {
      creator = await this.repository.manager.findOne(ProfileModel, {
        where: { id: record.creatorId },
      });
    }

    const splitArr = record?.id?.split('-');
    const sliceId = splitArr[splitArr.length - 1];
    const billId = sliceId.toUpperCase();
    const clinicDetails = `at ${record.hospitalName}, located in ${
      record.hospitalAddress || ''
    }`;
    const profileName = record?.profile?.fullName || '';
    let description = '';
    switch (action) {
      case NotificationTag.Emailed:
        description = `${mutator.fullName} emailed a Bill (${billId}) to ${profileName} ${clinicDetails}.`;
        break;
      case NotificationTag.Raised:
        description = `${mutator.fullName} raised a Bill (${billId}) for ${profileName} ${clinicDetails}.`;
        break;
      case NotificationTag.Paid:
      case NotificationTag.PartiallyPaid:
        description = `${
          mutator.fullName
        } ${action.toLowerCase()} for a Bill (${billId}) for ${
          creator.fullName
        } ${clinicDetails}.`;
        break;
      case NotificationTag.Deleted:
        description = `${mutator.fullName} deleted ${deletedCount} ${
          deletedCount > 1 ? 'Bills' : 'Bill'
        } for ${profileName} ${clinicDetails}.`;
        break;
      default:
        description = `${
          mutator.fullName
        } ${action.toLowerCase()} a Bill (${billId}) for ${profileName} ${clinicDetails}.`;
        break;
    }

    return {
      description,
      hospitalId: creator.hospitalId,
      profileId: record.receiverProfileId,
      metaData: {
        id: record.id,
        ids: deletedIds,
        recordType: DashboardIcon.Billing,
        recordId: record.id,
        clinifyId: record?.profile?.clinifyId,
        hospitalId: record.receiverHospitalId,
        profileId: record.receiverProfileId,
      },
    };
  }

  private async hmoClaimComposer(
    mutator: ProfileModel,
    details: IDetails,
  ): Promise<IComposerReturn> {
    let item = details.item as HmoClaimModel | HmoClaimModel[];
    const action = details.action;
    let deletedCount = 0;
    let deletedIds: string[] = [];
    let submittedCount = 0;
    let submittedIds: string[] = [];
    let claimIds = '';
    if (action === NotificationTag.Deleted) {
      if (Array.isArray(item)) {
        deletedCount = item.length;
        deletedIds = item.map((i) => i.id);
        item = item[0];
      } else {
        deletedCount = 1;
        deletedIds = [item.id];
      }
    }
    if (action === NotificationTag.Submitted) {
      if (Array.isArray(item)) {
        submittedCount = item.length;
        claimIds = joinWithAnd(item.map((i) => i.claimId));
        submittedIds = item.map((i) => i.id);
        item = item[0];
      } else {
        submittedCount = 1;
        claimIds = item.claimId;
        submittedIds = [item.id];
      }
    }
    const record: HmoClaimModel = await this.ensureHospitalAndProfile(
      item as any,
    );

    const clinicDetails = this.formatHospitalNameAndAddress(record.hospital);
    const profileName = record?.profile?.fullName || '';
    if (action === NotificationTag.Submitted) {
      const description = `${mutator.fullName} submitted ${submittedCount} ${
        submittedCount > 1 ? 'Claims' : 'Claim'
      } (${claimIds}) for ${profileName} ${clinicDetails}.`;
      return {
        description,
        hospitalId: record.hospitalId,
        profileId: record.profileId,
        hmoProviderId: record.providerId,
        metaData: {
          id: record.id,
          ids: submittedIds,
          recordType: DashboardIcon.Claim,
          recordId: record.id,
          clinifyId: record?.profile?.clinifyId,
          hospitalId: record.hospitalId,
          profileId: record.profileId,
        },
      };
    } else if (action === NotificationTag.Deleted) {
      const description = `${mutator.fullName} deleted ${deletedCount} ${
        deletedCount > 1 ? 'Claims' : 'Claim'
      } for ${profileName} ${clinicDetails}.`;
      return {
        description,
        hospitalId: record.hospitalId,
        profileId: record.profileId,
        hmoProviderId:
          record.status === 'draft' ? undefined : record.providerId,
        metaData: {
          id: record.id,
          ids: deletedIds,
          recordType: DashboardIcon.Claim,
          recordId: record.id,
          clinifyId: record?.profile?.clinifyId,
          hospitalId: record.hospitalId,
          profileId: record.profileId,
        },
      };
    }
    const description = `${mutator.fullName} ${action.toLowerCase()} a Claim ${
      record.claimId ? `(${record.claimId})` : ''
    } for ${profileName} ${clinicDetails}.`;
    return {
      description,
      hospitalId: record.hospitalId,
      profileId: record.profileId,
      hmoProviderId:
        record.status?.toLowerCase() === 'draft'
          ? undefined
          : record.providerId,
      metaData: {
        id: record.id,
        recordType: DashboardIcon.Claim,
        recordId: record.id,
        clinifyId: record?.profile?.clinifyId,
        hospitalId: record.hospitalId,
        profileId: record.profileId,
      },
    };
  }

  private async preauthorizationComposer(
    mutator: ProfileModel,
    details: IDetails,
  ): Promise<IComposerReturn> {
    let item = details.item;
    const action = details.action;
    let deletedCount = 0;
    let deletedIds: string[] = [];
    if (action === NotificationTag.Deleted) {
      deletedCount = item.length;
      deletedIds = item.map((i) => i.id);
      item = item[0];
    }
    const record: PreauthorisationModel = await this.ensureHospitalAndProfile(
      item,
    );
    const clinicDetails = this.formatHospitalNameAndAddress(record.hospital);
    const profileName = record?.profile?.fullName || '';
    let description = `${
      mutator.fullName
    } ${action.toLowerCase()} a Preauthorization for ${profileName} ${clinicDetails}.`;
    if (action === NotificationTag.Deleted) {
      const paCodes = joinWithAnd(
        details.item.map((i: { code: any }) => i.code),
      );
      description = `${mutator.fullName} deleted ${deletedCount} ${
        deletedCount > 1 ? 'Preauthorizations' : 'Preauthorization'
      } (${paCodes}) for ${profileName} ${clinicDetails}.`;
    }
    return {
      description,
      hospitalId: record.hospitalId,
      profileId: record.profileId,
      hmoProviderId: record.providerId,
      metaData: {
        id: record.id,
        ids: deletedIds,
        recordType: DashboardIcon.PreAuthorization,
        recordId: record.id,
        clinifyId: record?.profile?.clinifyId,
        hospitalId: record.hospitalId,
        profileId: record.profileId,
      },
    };
  }

  private async waitlistComposer(
    mutator: ProfileModel,
    details: IDetails,
  ): Promise<IComposerReturn> {
    let item = details.item;
    let action = details.action;
    let deletedCount = 0;
    let deletedIds: string[] = [];
    if (action === NotificationTag.Deleted) {
      deletedCount = item.length;
      deletedIds = item.map((i) => i.id);
      item = item[0];
    }
    item.profileId = item.patientId;
    const record: WaitingListModel = await this.ensureHospitalAndProfile(item);
    const clinicDetails = this.formatHospitalNameAndAddress(record.hospital);
    const profileName = record?.patientFullName || '';
    switch (record.status) {
      case WaitingListStatus.CheckedInAndAssigned:
        action = NotificationTag.Assigned;
        break;
      case WaitingListStatus.CheckedInAndUnassigned:
        action = NotificationTag.Added;
        break;
      case WaitingListStatus.CheckedOut:
        action = NotificationTag.Checkedout;
        break;
      case WaitingListStatus.Transferred:
        action = NotificationTag.Transferred;
        break;
      default:
        break;
    }

    const _action =
      action === NotificationTag.Checkedout
        ? 'checked out'
        : action.toLowerCase();
    let description = '';
    switch (action) {
      case NotificationTag.Checkedout:
        description = `${mutator.fullName} checked out ${profileName} from waitlist ${clinicDetails}.`;
        break;
      case NotificationTag.Added:
        description = `${mutator.fullName} checked in ${profileName} to waitlist ${clinicDetails}.`;
        break;
      case NotificationTag.Assigned:
        description = `${
          mutator.fullName
        } assigned ${profileName} to ${record?.assignedTo
          ?.map(({ fullName }) => fullName)
          .join(', ')} ${clinicDetails}.`;
        break;
      case NotificationTag.Updated:
        description = `${mutator.fullName} updated ${profileName} waitlist ${clinicDetails}.`;
        break;
      case NotificationTag.Transferred:
        description = `${
          mutator.fullName
        } transferred ${profileName} to ${record?.assignedTo
          ?.map(({ fullName }) => fullName)
          .join(', ')} ${clinicDetails}.`;
        break;
      case NotificationTag.Deleted:
        description = `${mutator.fullName} deleted ${deletedCount} ${
          deletedCount > 1 ? 'Patients' : 'Patient'
        } from waitlist ${clinicDetails}.`;
        break;
      default:
        description = `${mutator.fullName} ${_action} ${profileName} from waitlist ${clinicDetails}.`;
        break;
    }

    return {
      description,
      hospitalId: record.hospitalId,
      profileId: record.patientId,
      metaData: {
        id: record.id,
        ids: deletedIds,
        recordType: DashboardIcon.PatientWaitingList,
        recordId: record.id,
        clinifyId: record?.patientClinifyId,
        hospitalId: record.hospitalId,
        profileId: record.patientId,
      },
    };
  }

  private patientRegistrationComposer(
    mutator: ProfileModel,
    details: IDetails,
  ): IComposerReturn {
    let item = details.item;
    const action = details.action;
    let deletedCount = 0;
    let deletedIds: string[] = [];
    if (action === NotificationTag.Deleted) {
      deletedCount = item.length;
      deletedIds = item.map((i) => i.id);
      item = item[0];
    }
    const record: ProfileModel = item;
    const clinicDetails = this.formatHospitalNameAndAddress(mutator.hospital);
    const profileName = record?.fullName || '';
    let description = `${
      mutator.fullName
    } ${action.toLowerCase()} for ${profileName} ${clinicDetails}.`;
    if (action === NotificationTag.Deleted) {
      description = `${mutator.fullName} deleted ${deletedCount} ${
        deletedCount > 1 ? 'Patients' : 'Patient'
      }  ${clinicDetails}.`;
    }
    return {
      description,
      hospitalId: mutator.hospital.id,
      ...(action === NotificationTag.Deleted
        ? { profileId: mutator.id }
        : { profileId: record.id }),
      metaData: {
        id: record.id,
        ids: deletedIds,
        recordType: DashboardIcon.PatientRegistration,
        recordId: record.id,
        clinifyId: record?.clinifyId,
        hospitalId: record.hospitalId,
        profileId: record.id,
      },
    };
  }

  private async coverageInformationComposer(
    mutator: ProfileModel,
    details: IDetails,
  ): Promise<IComposerReturn> {
    let item = details.item;
    const action = details.action;
    let deletedCount = 0;
    let deletedIds: string[] = [];
    if (action === NotificationTag.Deleted) {
      deletedCount = item.length;
      deletedIds = item.map((i) => i.id);
      item = item[0];
    }
    const record: HmoProfileModel = await this.ensureHospitalAndProfile(item);
    const clinicDetails = this.formatHospitalNameAndAddress(mutator.hospital);
    const profileName = record.profile?.fullName || '';
    let description = `${
      mutator.fullName
    } ${action.toLowerCase()} a Coverage for ${profileName} ${clinicDetails}.`;
    if (action === NotificationTag.Deleted) {
      description = `${mutator.fullName} deleted ${deletedCount} ${
        deletedCount > 1 ? 'Coverages' : 'Coverage'
      } for ${profileName} ${clinicDetails}.`;
    }
    return {
      description,
      hospitalId: mutator.hospitalId,
      profileId: record.profile.id,
      metaData: {
        id: record.id,
        ids: deletedIds,
        recordType: DashboardIcon.CoverageInformation,
        recordId: record.id,
        clinifyId: record?.profile?.clinifyId,
        hospitalId: mutator.hospitalId,
        profileId: record.profile.id,
      },
    };
  }

  private personalInformationComposer(
    mutator: ProfileModel,
    details: IDetails,
  ): IComposerReturn {
    const { action, item } = details;
    if (item.dependent) {
      const record = item.dependent as DependentModel;
      const clinicDetails = this.formatHospitalNameAndAddress(mutator.hospital);
      const profileName = record.profile?.fullName || '';
      return {
        description: `${
          mutator.fullName
        } ${action.toLowerCase()} a Dependent for ${profileName} ${clinicDetails}.`,
        hospitalId: mutator.hospital.id,
        profileId: record.profileId,
        metaData: {
          id: record.id,
          recordType: DashboardIcon.PersonalInformation,
          recordId: record.id,
          clinifyId: record?.profile?.clinifyId,
          hospitalId: mutator.hospitalId,
          profileId: record.profileId,
        },
      };
    }
    const record: ProfileModel = item;
    const clinicDetails = this.formatHospitalNameAndAddress(mutator.hospital);
    const profileName = record?.fullName || '';
    return {
      description: `${
        mutator.fullName
      } ${action.toLowerCase()} ${profileName} Personal information detail ${clinicDetails}.`,
      hospitalId: mutator.hospitalId,
      profileId: record.id,
      metaData: {
        id: record.id,
        recordType: DashboardIcon.BackgroundInformation,
        recordId: record.id,
        clinifyId: record?.clinifyId,
        hospitalId: mutator.hospitalId,
        profileId: record.id,
      },
    };
  }

  private backgroundInformationComposer(
    mutator: ProfileModel,
    details: IDetails,
  ): IComposerReturn {
    const { action, item } = details;
    const record: ProfileModel = item;
    const clinicDetails = this.formatHospitalNameAndAddress(mutator.hospital);
    const profileName = record?.fullName || '';
    return {
      description: `${
        mutator.fullName
      } ${action.toLowerCase()} ${profileName} Background information detail ${clinicDetails}.`,
      hospitalId: mutator.hospitalId,
      profileId: record.id,
      metaData: {
        id: record.id,
      },
    };
  }

  private patientProfileComposer(
    mutator: ProfileModel,
    details: IDetails,
  ): IComposerReturn {
    const {
      action,
      item: { item, modelName },
    } = details;
    const record:
      | NextOfKinModel
      | DisabilityModel
      | FamilyHistoryModel
      | DependentModel
      | PreExistingConditionModel
      | PastSurgeryModel
      | ObstetricHistoryModel
      | FamilyHistoryModel
      | OncologyHistoryModel
      | HabitModel
      | PhysicalActivityModel
      | DisabilityModel
      | PastEncounterModel
      | GynecologicHistoryModel = item;
    const clinicDetails = this.formatHospitalNameAndAddress(mutator.hospital);
    const profileName = record?.profile?.fullName || '';

    return {
      description: `${mutator.fullName} ${action.toLowerCase()} ${
        startsWithVowel(modelName) ? 'an' : 'a'
      } ${modelName} for ${profileName} ${clinicDetails}.`,
      hospitalId: mutator.hospital?.id,
      profileId: record.profile?.id,
      metaData: {
        id: record.id,
        recordType: DashboardIcon.PatientProfile,
        recordId: record.id,
        clinifyId: record?.profile?.clinifyId,
        hospitalId: mutator.hospitalId,
        profileId: record.profile?.id,
      },
      modelName,
    };
  }

  private async patientTransfersComposer(
    mutator: ProfileModel,
    details: IDetails,
  ): Promise<IComposerReturn> {
    const { action, item } = details;
    const _item: {
      profileId: string;
      originatedHospitalId: string;
      destinationHospitalId: string;
      payloadDestination: 'originated' | 'destination';
    } = item;
    if (_item.payloadDestination === 'originated') {
      const destinationHospital = await this.repository.manager.findOne(
        HospitalModel,
        {
          where: { id: _item.destinationHospitalId },
        },
      );
      const profile = await this.repository.manager.findOne(ProfileModel, {
        where: { id: _item.profileId },
      });

      const profileName = profile?.fullName || '';
      return {
        description: `${
          mutator.fullName
        } ${action.toLowerCase()} ${profileName} to ${
          destinationHospital?.name || ''
        } located in ${destinationHospital?.address || ''}.`,
        hospitalId: _item.originatedHospitalId,
        profileId: _item.profileId,
        metaData: {
          id: _item.profileId,
          recordType: DashboardIcon.PatientTransfers,
          recordId: _item.profileId,
          clinifyId: profile?.clinifyId,
          hospitalId: _item.originatedHospitalId,
          profileId: _item.profileId,
        },
      };
    }

    const originatedHospital = await this.repository.manager.findOne(
      HospitalModel,
      {
        where: { id: _item.originatedHospitalId },
      },
    );
    const profile = await this.repository.manager.findOne(ProfileModel, {
      where: { id: _item.profileId },
    });
    const profileName = profile?.fullName || '';
    return {
      description: `${
        mutator.fullName
      } ${action.toLowerCase()} ${profileName} from ${
        originatedHospital?.name || ''
      } located in ${originatedHospital?.address || ''}.`,
      hospitalId: _item.destinationHospitalId,
      profileId: _item.profileId,
      metaData: {
        id: _item.profileId,
        recordType: DashboardIcon.PatientTransfers,
        recordId: _item.profileId,
        clinifyId: profile?.clinifyId,
        hospitalId: _item.destinationHospitalId,
        profileId: _item.profileId,
      },
    };
  }

  private async appointmentComposer(
    mutator: ProfileModel,
    details: IDetails,
  ): Promise<IComposerReturn> {
    const action = details.action;
    let item = details.item;
    let deletedCount = 0;
    let deletedIds: string[] = [];
    if (action === NotificationTag.Deleted) {
      deletedCount = item.length;
      deletedIds = item.map((i) => i.id);
      item = item[0];
    }
    const record: OrganisationAppointmentModel =
      await this.ensureHospitalAndProfile(item);
    let specialistName = record?.specialist?.fullName || '';
    if (specialistName === '' && record?.specialistId) {
      const specialist = await this.repository.manager.findOne(ProfileModel, {
        where: { id: record.specialistId },
      });
      specialistName = specialist?.fullName || '';
    }
    const clinicDetails = this.formatHospitalNameAndAddress(mutator.hospital);
    const profileName = record?.patientInformation?.fullName || '';
    let description = `${
      mutator.fullName
    } ${action.toLowerCase()} an appointment for ${profileName}${
      specialistName ? ' with ' + specialistName + ' ' : ''
    } ${clinicDetails}.`;
    if (action === NotificationTag.Deleted) {
      description = `${mutator.fullName} deleted ${deletedCount} appointments for ${profileName} ${clinicDetails}.`;
    }
    return {
      description,
      hospitalId: mutator.hospitalId,
      profileId: record.profileId,
      metaData: {
        id: record.id,
        ids: deletedIds,
        recordType: DashboardIcon.Appointment,
        recordId: record.id,
        clinifyId: record?.patientInformation?.clinifyId,
        hospitalId: record.hospitalId,
        profileId: record.profileId,
      },
    };
  }

  private async inventorynComposer(
    mutator: ProfileModel,
    details: IDetails,
  ): Promise<IComposerReturn> {
    const { action, item } = details;
    const record: InventoryModel = await this.ensureHospitalAndProfile(item);
    const clinicDetails = this.formatHospitalNameAndAddress(record.hospital);
    return {
      description: `${
        mutator.fullName
      } ${action.toLowerCase()} an Inventory item ${clinicDetails}.`,
      hospitalId: record.hospitalId,
      profileId: record.creatorId,
      metaData: {
        id: record.id,
        recordType: DashboardIcon.Inventory,
        recordId: record.id,
        hospitalId: record.hospitalId,
        profileId: record.creatorId,
      },
    };
  }

  private async priceComposer(
    mutator: ProfileModel,
    details: IDetails,
  ): Promise<IComposerReturn> {
    const { action, item } = details;
    const record: PricesModel = await this.ensureHospitalAndProfile(item);
    const clinicDetails = this.formatHospitalNameAndAddress(record.hospital);
    return {
      description: `${
        mutator.fullName
      } ${action.toLowerCase()} a tarrif item ${clinicDetails}.`,
      hospitalId: record.hospitalId,
      profileId: record.creatorId,
      metaData: {
        id: record.id,
        recordType: DashboardIcon.Price,
        recordId: record.id,
        hospitalId: record.hospitalId,
        profileId: record.creatorId,
      },
    };
  }
  private async investigationComposer(
    mutator: ProfileModel,
    details: IDetails,
  ): Promise<IComposerReturn> {
    const { action, item } = details;
    const record = await this.repository.manager.findOne(InvestigationModel, {
      where: { id: item.id },
      relations: ['profile', 'hospital'],
    });

    if (record.requestType === InvestigationRequestType.Laboratory)
      return this.laboratoryComposer(mutator, {
        action,
        item: record,
        modelName: DashboardIcon.Laboratory,
      });
    return this.radiologyComposer(mutator, {
      action,
      item: record,
      modelName: DashboardIcon.Radiology,
    });
  }
  private async nursingServiceComposer(
    mutator: ProfileModel,
    details: IDetails,
  ): Promise<IComposerReturn> {
    const action = details.action;
    let item = details.item;
    let deletedCount = 0;
    let deletedIds: string[] = [];
    if (action === NotificationTag.Deleted) {
      deletedCount = item.length;
      deletedIds = item.map((i) => i.id);
      item = item[0];
    }
    const record: NursingServiceDetailModel =
      await this.ensureHospitalAndProfile(item);
    const creator = await this.repository.manager.findOne(ProfileModel, {
      where: { id: record.creatorId },
      relations: ['hospital'],
    });
    const clinicDetails = this.formatHospitalNameAndAddress(creator.hospital);
    const profileName = record?.profile?.fullName || '';

    let description = `${
      mutator.fullName
    } ${action.toLowerCase()} a Nursing Services${
      record.procedureType ? ` (${record.procedureType})` : ''
    } for ${profileName} ${clinicDetails}.`;

    if (action === NotificationTag.Updated) {
      description = `${
        mutator.fullName
      } ${action.toLowerCase()} a Nursing Services for ${profileName} ${clinicDetails}.`;
    }

    if (action === NotificationTag.Deleted) {
      description = `${
        mutator.fullName
      } deleted ${deletedCount} Nursing Services${
        deletedCount > 1 ? 's' : ''
      } for ${profileName} ${clinicDetails}.`;
    }

    return {
      description,
      hospitalId: creator.hospital.id,
      profileId: record.profileId,
      metaData: {
        id: record.id,
        ids: deletedIds,
        recordType: DashboardIcon.NursingService,
        recordId: record.id,
        clinifyId: record?.profile?.clinifyId,
        hospitalId: creator.hospital.id,
        profileId: record.profileId,
      },
    };
  }
  private async postOperationComposer(
    mutator: ProfileModel,
    details: IDetails,
  ): Promise<IComposerReturn> {
    const action = details.action;
    let item = details.item;
    let deletedCount = 0;
    let deletedIds: string[] = [];
    if (action === NotificationTag.Deleted) {
      deletedCount = item.length;
      deletedIds = item.map((i) => i.id);
      item = item[0];
    }
    const record: PostOperationModel = await this.ensureHospitalAndProfile(
      item,
    );
    const creator = await this.repository.manager.findOne(ProfileModel, {
      where: { id: record.creatorId },
      relations: ['hospital'],
    });
    const clinicDetails = this.formatHospitalNameAndAddress(creator.hospital);
    const profileName = record?.profile?.fullName || '';

    const chartTypeUsed = record.chartType
      ? ` (${
          record.chartType === 'patientObservationChart'
            ? 'Patient Observation Chart - Recovery Room'
            : 'Post-Caesarean Section Clinical Orders Sheet'
        })`
      : '';

    let description = `${
      mutator.fullName
    } ${action.toLowerCase()} a Post Operation Chart${chartTypeUsed} for ${profileName} ${clinicDetails}.`;

    if (action === NotificationTag.Deleted) {
      description = `${
        mutator.fullName
      } deleted ${deletedCount} Post Operation Chart${
        deletedCount > 1 ? 's' : ''
      } for ${profileName} ${clinicDetails}.`;
    }

    return {
      description,
      hospitalId: creator.hospital.id,
      profileId: record.profileId,
      metaData: {
        id: record.id,
        ids: deletedIds,
        recordType: DashboardIcon.PostOperation,
        recordId: record.id,
        clinifyId: record?.profile?.clinifyId,
        hospitalId: creator.hospital.id,
        profileId: record.profileId,
      },
    };
  }
  private async postnatalComposer(
    mutator: ProfileModel,
    details: IDetails,
  ): Promise<IComposerReturn> {
    const action = details.action;
    let item = details.item;
    let deletedCount = 0;
    let deletedIds: string[] = [];
    if (action === NotificationTag.Deleted) {
      deletedCount = item.length;
      deletedIds = item.map((i) => i.id);
      item = item[0];
    }
    const record: PostnatalModel = await this.ensureHospitalAndProfile(item);
    const creator = await this.repository.manager.findOne(ProfileModel, {
      where: { id: record.creatorId },
      relations: ['hospital'],
    });
    const clinicDetails = this.formatHospitalNameAndAddress(creator.hospital);
    const profileName = record?.profile?.fullName || '';
    let description = `${
      mutator.fullName
    } ${action.toLowerCase()} a Postnatal for ${profileName} ${clinicDetails}.`;
    if (action === NotificationTag.Deleted) {
      description = `${mutator.fullName} deleted ${deletedCount} Postnatal Records for ${profileName} ${clinicDetails}.`;
    }
    return {
      description,
      hospitalId: creator.hospital.id,
      profileId: record.profileId,
      metaData: {
        id: record.id,
        ids: deletedIds,
        recordType: DashboardIcon.Postnatal,
        recordId: record.id,
        clinifyId: record?.profile?.clinifyId,
        hospitalId: creator.hospital.id,
        profileId: record.profileId,
      },
    };
  }
  private async labourAndDeliveryComposer(
    mutator: ProfileModel,
    details: IDetails,
  ): Promise<IComposerReturn> {
    const action = details.action;
    let item = details.item;
    let deletedCount = 0;
    let deletedIds: string[] = [];
    if (action === NotificationTag.Deleted) {
      deletedCount = item.length;
      deletedIds = item.map((i) => i.id);
      item = item[0];
    }
    const record: LabourDeliveryModel = await this.ensureHospitalAndProfile(
      item,
    );
    const creator = await this.repository.manager.findOne(ProfileModel, {
      where: { id: record.creatorId },
      relations: ['hospital'],
    });
    const clinicDetails = this.formatHospitalNameAndAddress(creator.hospital);
    const profileName = record?.profile?.fullName || '';
    let description = `${
      mutator.fullName
    } ${action.toLowerCase()} a Labour And Delivery Record for ${profileName} ${clinicDetails}.`;
    if (action === NotificationTag.Deleted) {
      description = `${mutator.fullName} deleted ${deletedCount} Labour And Delivery Records for ${profileName} ${clinicDetails}.`;
    }
    return {
      description,
      hospitalId: creator.hospital.id,
      profileId: record.profileId,
      metaData: {
        id: record.id,
        ids: deletedIds,
        recordType: DashboardIcon.LabourDelivery,
        recordId: record.id,
        clinifyId: record?.profile?.clinifyId,
        hospitalId: creator.hospital.id,
        profileId: record.profileId,
      },
    };
  }

  private async handoverComposer(
    mutator: ProfileModel,
    details: IDetails,
  ): Promise<IComposerReturn> {
    const { action } = details;
    let description: string;
    let item = details.item;
    let deletedCount = 0;
    let deletedIds: string[] = [];
    if (action === NotificationTag.Deleted) {
      deletedCount = item.length;
      deletedIds = item.map(({ id }) => id);
      item = item[0];
    }
    const [record, patientCount] = await this.ensureHandoverParties(item);
    const facilityDetails = this.formatHospitalNameAndAddress(record.hospital);
    const handoverBy = record?.handoverBy?.fullName || '';
    if (action === NotificationTag.Added) {
      description = `${handoverBy} handed over ${patientCount} patient(s) ${facilityDetails}`;
    }
    if (action === NotificationTag.Updated) {
      description = `Handover note by ${handoverBy} was updated by ${mutator.fullName} ${facilityDetails}`;
    }
    if (action === NotificationTag.Deleted) {
      // eslint-disable-next-line max-len
      description = `${mutator.fullName} deleted ${deletedCount} handover note(s) by ${handoverBy} ${facilityDetails}`;
    }

    return {
      description,
      hospitalId: mutator.hospitalId,
      profileId: mutator.id,
      metaData: {
        id: record.id,
        ids: deletedIds,
        recordType: DashboardIcon.HandoverNote,
        recordId: record.id,
        clinifyId: record?.handoverBy?.clinifyId,
        hospitalId: record.hospitalId,
        profileId: mutator.id,
      },
    };
  }

  private async oncologyConsultationComposer(
    mutator: ProfileModel,
    details: IDetails,
  ): Promise<IComposerReturn> {
    let item = details.item;
    const action = details.action;
    let deletedCount = 0;
    let deletedIds: string[] = [];
    if (action === NotificationTag.Deleted) {
      deletedCount = item.length;
      deletedIds = item.map((i) => i.id);
      item = item[0];
    }
    const record: OncologyConsultationHistoryModel =
      await this.ensureHospitalAndProfile(item);
    const clinicDetails = this.formatHospitalNameAndAddress(record.hospital);
    const profileName = record?.profile?.fullName || '';
    let description = `${
      mutator.fullName
    } ${action.toLowerCase()} an Oncology Consultation for ${profileName} ${clinicDetails}.`;
    if (action === NotificationTag.Deleted) {
      description = `${mutator.fullName} deleted ${deletedCount} oncology consultations for ${profileName} ${clinicDetails}`;
    }

    return {
      description,
      hospitalId: record.hospitalId,
      profileId: record.profileId,
      metaData: {
        id: record.id,
        ids: deletedIds,
        recordType: DashboardIcon.OncologyConsultationHistory,
        recordId: record.id,
        clinifyId: record?.profile?.clinifyId,
        hospitalId: record.hospitalId,
        profileId: record.profileId,
      },
    };
  }

  private async requestProcedureComposer(
    mutator: ProfileModel,
    details: IDetails,
  ): Promise<IComposerReturn> {
    let item = details.item;
    const action = details.action;
    let deletedCount = 0;
    let deletedIds: string[] = [];
    if (action === NotificationTag.Deleted) {
      deletedCount = item.length;
      deletedIds = item.map((i) => i.id);
      item = item[0];
    }
    const record: SurgeryModel = await this.ensureHospitalAndProfile(item);
    const clinicDetails = this.formatHospitalNameAndAddress(record.hospital);
    const profileName = record?.profile?.fullName || '';
    const testName =
      joinWithAnd(
        record?.procedureType?.map((test) => test.type).filter(Boolean) || [],
      ) || '';
    const preposition = NotificationTag.Emailed === action ? 'to' : 'for';
    let description = `
      ${
        mutator.fullName
      } ${action.toLowerCase()} a Procedure (${testName}) ${preposition} ${profileName} ${clinicDetails}.`;
    if (action === NotificationTag.Deleted) {
      description = `${mutator.fullName} deleted ${deletedCount} procedures for ${profileName} ${clinicDetails}`;
    }
    return {
      description,
      hospitalId: record.hospitalId,
      profileId: record.profileId,
      metaData: {
        id: record.id,
        ids: deletedIds,
        recordType: DashboardIcon.RequestProcedure,
        recordId: record.id,
        clinifyId: record?.profile?.clinifyId,
        hospitalId: record.hospitalId,
        profileId: record.profileId,
      },
    };
  }
  private async preChemoEducationComposer(
    mutator: ProfileModel,
    details: IDetails,
  ): Promise<IComposerReturn> {
    const action = details.action;
    let item = details.item;
    let deletedCount = 0;
    let deletedIds: string[] = [];
    if (action === NotificationTag.Deleted) {
      deletedCount = item.length;
      deletedIds = item.map((i) => i.id);
      item = item[0];
    }
    const record: PreChemoEducationModel = await this.ensureHospitalAndProfile(
      item,
    );
    const creator = await this.repository.manager.findOne(ProfileModel, {
      where: { id: record.creatorId },
      relations: ['hospital'],
    });
    const clinicDetails = this.formatHospitalNameAndAddress(creator.hospital);
    const profileName = record?.profile?.fullName || '';

    let description = `${
      mutator.fullName
    } ${action.toLowerCase()} a Pre-Chemo Education Checklist for ${profileName} ${clinicDetails}.`;

    if (action === NotificationTag.Deleted) {
      description = `${
        mutator.fullName
      } deleted ${deletedCount} Pre-Chemo Education Checklist${
        deletedCount > 1 ? 's' : ''
      } for ${profileName} ${clinicDetails}.`;
    }

    return {
      description,
      hospitalId: creator.hospital.id,
      profileId: record.profileId,
      metaData: {
        id: record.id,
        ids: deletedIds,
        recordType: DashboardIcon.PreChemoEducation,
        recordId: record.id,
        clinifyId: record?.profile?.clinifyId,
        hospitalId: creator.hospital.id,
        profileId: record.profileId,
      },
    };
  }
  private async cancerScreeningComposer(
    mutator: ProfileModel,
    details: IDetails,
  ): Promise<IComposerReturn> {
    const action = details.action;
    let item = details.item;
    let deletedCount = 0;
    let deletedIds: string[] = [];
    if (action === NotificationTag.Deleted) {
      deletedCount = item.length;
      deletedIds = item.map((i) => i.id);
      item = item[0];
    }
    const record: CancerScreeningModel = await this.ensureHospitalAndProfile(
      item,
    );
    const creator = await this.repository.manager.findOne(ProfileModel, {
      where: { id: record.creatorId },
      relations: ['hospital'],
    });
    const clinicDetails = this.formatHospitalNameAndAddress(creator.hospital);
    const profileName = record?.profile?.fullName || '';

    let description = `${
      mutator.fullName
    } ${action.toLowerCase()} a Cancer Screening Checklist for ${profileName} ${clinicDetails}.`;

    if (action === NotificationTag.Deleted) {
      description = `${
        mutator.fullName
      } deleted ${deletedCount} Cancer Screening Checklist${
        deletedCount > 1 ? 's' : ''
      } for ${profileName} ${clinicDetails}.`;
    }

    return {
      description,
      hospitalId: creator.hospital.id,
      profileId: record.profileId,
      metaData: {
        id: record.id,
        ids: deletedIds,
        recordType: DashboardIcon.CancerScreening,
        recordId: record.id,
        clinifyId: record?.profile?.clinifyId,
        hospitalId: creator.hospital.id,
        profileId: record.profileId,
      },
    };
  }
  private async preAuthorizationReferralComposer(
    _mutator: ProfileModel,
    details: IDetails,
  ): Promise<IComposerReturn> {
    const { item, action } = details;
    const record: PreauthorisationReferralModel =
      await this.ensureHospitalAndProfile(item);
    let referredProvider = record.referredProvider;
    if (!referredProvider) {
      referredProvider = await this.repository.manager.findOne(HospitalModel, {
        where: { id: record.referredProviderId },
      });
    }
    const hmoProviderHospital = await this.repository.manager.findOne(
      HospitalModel,
      {
        where: { hmoId: record.providerId },
      },
    );
    if (!hmoProviderHospital) {
      throw new Error('HMO Provider Hospital Not Found');
    }
    const clinicDetails = this.formatHospitalNameAndAddress(
      referredProvider,
    ).slice(2, -1);
    const profileName = record?.profile?.fullName || '';
    const referredFromName = record.facilityName;
    const enrolleeNumber = record.enrolleeNumber;
    const referralCode = record.utilizations?.[0]?.paCode;

    // eslint-disable-next-line max-len
    const description = `${referredFromName} referred ${profileName} (${enrolleeNumber}) with Authorization Code (${referralCode}) to ${clinicDetails}`;

    return {
      description,
      ...(action === NotificationTag.PreAuthorizationReferral
        ? {
            hospitalId: record.hospitalId,
          }
        : {
            hospitalId: referredProvider.id,
            hmoProviderId: record.providerId,
          }),
      profileId: record.profileId,
      metaData: {
        id: record.id,
        recordType: DashboardIcon.PreAuthorizationReferral,
        recordId: record.id,
        clinifyId: record?.profile?.clinifyId,
        hospitalId: record.hospitalId,
        profileId: record.profileId,
      },
    };
  }
  private async consultationReferralComposer(
    mutator: ProfileModel,
    details: IDetails,
  ): Promise<IComposerReturn> {
    const { item, action } = details;
    const record: ConsultationModel = await this.ensureHospitalAndProfile(item);
    const patientName = record?.profile?.fullName || '';
    let description = '';
    let referredToHospital = record.referredTo?.hospital;
    if (!referredToHospital) {
      referredToHospital = await this.repository.manager.findOne(
        HospitalModel,
        {
          where: { id: record.referredTo?.hospitalId },
        },
      );
    }
    let referringHospital = record.hospital;
    if (!referringHospital) {
      referringHospital = await this.repository.manager.findOne(HospitalModel, {
        where: { id: record.hospitalId },
      });
    }
    if (record.externalReferral) {
      const clinicDetailsTo =
        this.formatHospitalNameAndAddress(referredToHospital);
      if (action === NotificationTag.ReferralExternal) {
        description = `${
          referringHospital.name
        } Referred ${patientName} to ${clinicDetailsTo.slice(2, -1)}`;
      } else {
        description = `${
          record.creatorName
        } Referred ${patientName} to ${clinicDetailsTo.slice(2, -1)}`;
      }
    } else {
      const clinicDetails = this.formatHospitalNameAndAddress(record.hospital);
      description = `${record.creatorName} Referred ${patientName} to ${record.referredTo?.fullName} ${clinicDetails}`;
    }
    return {
      description,
      ...(record.externalReferral && action === NotificationTag.ReferralExternal
        ? {
            hospitalId: referredToHospital.id,
          }
        : {
            hospitalId: record.hospitalId,
          }),
      profileId: record.profileId,
      metaData: {
        id: record.id,
        recordType: DashboardIcon.ConsultationReferral,
        recordId: record.id,
        clinifyId: record?.profile?.clinifyId,
        hospitalId: record.hospitalId,
        profileId: record.profileId,
      },
    };
  }
}
