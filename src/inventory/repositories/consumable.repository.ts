import { NotFoundException } from '@nestjs/common';
import moment from 'moment';
import { In, Repository } from 'typeorm';
import { ConsumableItemModel } from '../models/consumable-item.model';
import { ConsumableModel } from '../models/consumable.model';
import { ConsumableResponse } from '../responses/consumable.response';
import { ConsumableFilterInput } from '../validators/consumable.filter.input';
import {
  ConsumableItemInput,
  ConsumableStatus,
  FulfillConsumableItemInput,
  RequestConsumableInput,
  UpdateConsumableInput,
} from '../validators/consumable.input';
import { ProfileModel } from '@clinify/users/models/profile.model';
import { takePaginatedResponses } from '@clinify/utils/pagination';

export interface IConsumableRepository extends Repository<ConsumableModel> {
  this: Repository<ConsumableModel>;
  requestConsumable(
    mutator: ProfileModel,
    input: RequestConsumableInput,
  ): Promise<ConsumableModel>;
  findByProfile(
    profile: ProfileModel,
    filter: ConsumableFilterInput,
  ): Promise<ConsumableResponse>;
  getConsumable(profile: ProfileModel, id: string): Promise<ConsumableModel>;
  updateConsumable(
    mutator: ProfileModel,
    id: string,
    input: UpdateConsumableInput,
  ): Promise<ConsumableModel>;
  updateConsumableStatus(
    mutator: ProfileModel,
    id: string,
    status: ConsumableStatus,
  ): Promise<ConsumableModel>;
  deleteConsumables(
    mutator: ProfileModel,
    ids: string[],
  ): Promise<ConsumableModel[]>;
  archiveConsumables(
    mutator: ProfileModel,
    ids: string[],
    archive: boolean,
  ): Promise<ConsumableModel[]>;
  addConsumableItem(
    mutator: ProfileModel,
    consumableId: string,
    input: ConsumableItemInput,
  ): Promise<ConsumableItemModel>;
  updateConsumableItem(
    mutator: ProfileModel,
    id: string,
    input: ConsumableItemInput,
  ): Promise<ConsumableItemModel>;
  deleteConsumableItem(
    mutator: ProfileModel,
    id: string,
  ): Promise<ConsumableItemModel>;
  fulfillConsumableItem(
    mutator: ProfileModel,
    id: string,
    input: FulfillConsumableItemInput,
  ): Promise<ConsumableItemModel>;
}

export const CustomConsumableRepoMethods: Pick<
  IConsumableRepository,
  | 'requestConsumable'
  | 'findByProfile'
  | 'getConsumable'
  | 'updateConsumable'
  | 'updateConsumableStatus'
  | 'deleteConsumables'
  | 'archiveConsumables'
  | 'addConsumableItem'
  | 'updateConsumableItem'
  | 'deleteConsumableItem'
  | 'fulfillConsumableItem'
> = {
  async requestConsumable(
    this: IConsumableRepository,
    mutator: ProfileModel,
    input: RequestConsumableInput,
  ): Promise<ConsumableModel> {
    const profile = await this.manager
      .findOneOrFail(ProfileModel, {
        where: {
          clinifyId: input?.clinifyId,
        },
      })
      .catch(() => {
        throw new NotFoundException('Patient Not Found');
      });
    const items = input?.items?.map(
      (item) =>
        new ConsumableItemModel({
          ...item,
          createdBy: mutator,
          creatorName: mutator?.fullName,
        }),
    );
    return this.save({
      ...input,
      items,
      createdBy: mutator,
      profile,
      creatorName: mutator?.fullName,
    });
  },

  async findByProfile(
    this: IConsumableRepository,
    profile: ProfileModel,
    filter: ConsumableFilterInput,
  ): Promise<ConsumableResponse> {
    const { status, skip, take, dateRange, keyword, archive } = filter;
    let query = this.createQueryBuilder('consumable')
      .leftJoinAndSelect('consumable.items', 'items')
      .where('(consumable.profile = :profileId)', { profileId: profile?.id })
      .andWhere('(consumable.archived = :archive)', { archive });

    if (status)
      query = query.andWhere('(consumable.status = :status)', { status });

    if (dateRange?.from) {
      const from = moment(dateRange.from).startOf('day').toDate();
      query = query.andWhere('(consumable.requestDateTime >= :from)', { from });
    }

    if (dateRange?.to) {
      const to = moment(dateRange.to).endOf('day').toDate();
      query = query.andWhere('(consumable.requestDateTime < :to)', { to });
    }

    if (keyword)
      query = query.andWhere(
        `
          (
            consumable.patient_type ILIKE :keyword OR consumable.payment_type ILIKE :keyword OR
            items.quantity_required ILIKE :keyword OR consumable.requested_by ILIKE :keyword
          )
        `,
        { keyword },
      );

    const response = await query
      .orderBy('consumable.created_date', 'DESC')
      .offset(skip)
      .limit(take)
      .getManyAndCount();

    return new ConsumableResponse(...takePaginatedResponses(response, take));
  },

  async getConsumable(
    this: IConsumableRepository,
    profile: ProfileModel,
    id: string,
  ): Promise<ConsumableModel> {
    const consumable = await this.findOneOrFail({ where: { id } }).catch(() => {
      throw new NotFoundException('Consumable Not Found');
    });
    return consumable;
  },

  async updateConsumable(
    this: IConsumableRepository,
    mutator: ProfileModel,
    id: string,
    input: UpdateConsumableInput,
  ): Promise<ConsumableModel> {
    const consumable = await this.findOneOrFail({ where: { id } }).catch(() => {
      throw new NotFoundException('Consumable Not Found');
    });
    return this.save({
      ...consumable,
      updatedBy: mutator,
      ...input,
      lastModifierName: mutator?.fullName,
    });
  },

  async updateConsumableStatus(
    this: IConsumableRepository,
    mutator: ProfileModel,
    id: string,
    status: ConsumableStatus,
  ): Promise<ConsumableModel> {
    const consumable = await this.findOneOrFail({ where: { id } }).catch(() => {
      throw new NotFoundException('Consumable Not Found');
    });
    return this.save({
      ...consumable,
      status,
      updatedDate: consumable?.updatedDate,
      lastModifierName: mutator?.fullName,
    });
  },

  async deleteConsumables(
    this: IConsumableRepository,
    mutator: ProfileModel,
    ids: string[],
  ): Promise<ConsumableModel[]> {
    const consumables = await this.find({ where: { id: In(ids) } });
    await this.delete(consumables.map(({ id }) => id));
    return consumables;
  },

  async archiveConsumables(
    this: IConsumableRepository,
    mutator: ProfileModel,
    ids: string[],
    archive: boolean,
  ): Promise<ConsumableModel[]> {
    const consumables = await this.find({ where: { id: In(ids) } });
    return this.save(
      consumables.map((item) => ({ ...item, archived: archive })),
    );
  },

  async addConsumableItem(
    this: IConsumableRepository,
    mutator: ProfileModel,
    consumableId: string,
    input: ConsumableItemInput,
  ): Promise<ConsumableItemModel> {
    const consumable = await this.findOneOrFail({
      where: { id: consumableId },
    }).catch(() => {
      throw new NotFoundException('Consumable Not Found');
    });
    return this.manager.save(ConsumableItemModel, {
      ...input,
      consumable,
      createdBy: mutator,
      creatorName: mutator?.fullName,
    });
  },

  async updateConsumableItem(
    this: IConsumableRepository,
    mutator: ProfileModel,
    id: string,
    input: ConsumableItemInput,
  ): Promise<ConsumableItemModel> {
    const item = await this.manager
      .findOneOrFail(ConsumableItemModel, { where: { id } })
      .catch(() => {
        throw new NotFoundException('Consumable Item Not Found');
      });
    return this.manager.save(ConsumableItemModel, {
      ...item,
      ...input,
      updatedBy: mutator,
      lastModifierName: mutator?.fullName,
    });
  },

  async deleteConsumableItem(
    this: IConsumableRepository,
    mutator: ProfileModel,
    id: string,
  ): Promise<ConsumableItemModel> {
    const item = await this.manager
      .findOneOrFail(ConsumableItemModel, { where: { id } })
      .catch(() => {
        throw new NotFoundException('Consumable Item Not Found');
      });
    await this.manager.delete(ConsumableItemModel, id);
    return item;
  },

  async fulfillConsumableItem(
    this: IConsumableRepository,
    mutator: ProfileModel,
    id: string,
    input: FulfillConsumableItemInput,
  ): Promise<ConsumableItemModel> {
    const item = await this.manager
      .findOneOrFail(ConsumableItemModel, { where: { id } })
      .catch(() => {
        throw new NotFoundException('Consumable Item Not Found');
      });
    return this.manager.save(ConsumableItemModel, {
      ...item,
      ...input,
      updatedBy: mutator,
      lastModifierName: mutator?.fullName,
    });
  },
};
