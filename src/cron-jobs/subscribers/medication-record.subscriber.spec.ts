import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Chance } from 'chance';
import MockDate from 'mockdate';
import { DataSource } from 'typeorm';
import { MedicationRecordSubscriber } from './medication-record.subscriber';
import { MailActiveListModel } from '../models/mailing-active-list.model';
import { DispenseDetailsModel } from '@clinify/medications/models/dispense-details.model';

const chance = new Chance();

const commonManagerMethods = {
  findOne: jest.fn(),
  save: jest.fn(),
  delete: jest.fn(),
};

const hospitalId = chance.guid({ version: 4 });
const profileId = chance.guid({ version: 4 });
const entityId = chance.guid({ version: 4 });
const medicationName = chance.name();
const medicationDetailSub = {};

const eventMock: any = {
  entity: {
    id: entityId,
    medicationName,
    endDate: new Date('2022-01-06'),
    medicationDetail: {
      endDate: new Date('2022-01-06'),
    },
    medication: {
      hospitalId,
    },
    createdBy: {
      hospitalId,
    },
  },
  metadata: {
    targetName: 'DispenseDetailsEntit',
  },
  manager: {
    getCustomRepository: jest.fn().mockReturnValue(commonManagerMethods),
    withRepository: jest.fn().mockReturnValue(commonManagerMethods),
  },
};

const MailActiveListRepositoryMock = {
  addAuditHistory: jest.fn(() => medicationDetailSub),
  fetchHistory: jest.fn(() => medicationDetailSub),
};

const dsMock = {
  subscribers: {
    push: jest.fn(),
  },
};

beforeAll(() => {
  MockDate.set('2022-01-05');
});

afterAll(() => {
  MockDate.reset();
});

describe('Medication Record Subscriber', () => {
  let subscriber: MedicationRecordSubscriber;
  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        MedicationRecordSubscriber,
        {
          provide: getRepositoryToken(MailActiveListModel),
          useValue: MailActiveListRepositoryMock,
        },
        {
          provide: DataSource,
          useValue: dsMock,
        },
      ],
    }).compile();

    subscriber = module.get<MedicationRecordSubscriber>(
      MedicationRecordSubscriber,
    );
    jest.clearAllMocks();
  });

  it('should return User class', () => {
    expect(subscriber.listenTo()).toBe(DispenseDetailsModel);
  });

  it('afterInsert(): calls the medication afterInsert', async () => {
    await subscriber.afterInsert({ ...eventMock, entity: {} });
    expect(eventMock.manager.getCustomRepository).not.toHaveBeenCalled();
  });

  it('afterInsert(): calls the medication afterInsert', async () => {
    await subscriber.afterInsert({
      ...eventMock,
      entity: {
        ...eventMock.entity,
        medicationDetail: {
          endDate: null,
        },
      },
    });
    expect(eventMock.manager.withRepository).toHaveBeenLastCalledWith(
      MailActiveListRepositoryMock,
    );
    expect(eventMock.manager.getCustomRepository().save).not.toHaveBeenCalled();
  });

  it('afterInsert(): calls the medication afterInsert', async () => {
    await subscriber.afterInsert({
      ...eventMock,
      medicationDetail: {
        endDate: null,
      },
    });
    expect(eventMock.manager.withRepository).toHaveBeenLastCalledWith(
      MailActiveListRepositoryMock,
    );
    expect(
      eventMock.manager.getCustomRepository().save,
    ).toHaveBeenLastCalledWith({
      description: 'undefined',
      hospitalId,
      hospitalRecipients: '',
      nextDate: new Date('2022-01-06T00:00:00.000Z'),
      patientEmail: null,
      patientName: null,
      patientProfileId: undefined,
      recordId: entityId,
      recordType: 'MEDICATION',
    });
  });

  it('afterUpdate(): calls the medication afterUpdate', async () => {
    await subscriber.afterUpdate({ ...eventMock, entity: {} });
    expect(eventMock.manager.withRepository).not.toHaveBeenCalled();
  });

  it('afterUpdate(): should not save', async () => {
    eventMock.manager.getCustomRepository().findOne = jest.fn(() => false);
    await subscriber.afterUpdate(eventMock);
    expect(eventMock.manager.withRepository).toHaveBeenLastCalledWith(
      MailActiveListRepositoryMock,
    );
    expect(
      eventMock.manager.getCustomRepository().findOne,
    ).toHaveBeenLastCalledWith({
      where: {
        recordId: entityId,
        recordType: 'MEDICATION',
      },
    });
    expect(eventMock.manager.getCustomRepository().save).not.toHaveBeenCalled();
  });

  it('afterUpdate(): should not save', async () => {
    eventMock.manager.getCustomRepository().findOne = jest.fn(() => ({}));
    await subscriber.afterUpdate(eventMock);
    expect(eventMock.manager.withRepository).toHaveBeenLastCalledWith(
      MailActiveListRepositoryMock,
    );
    expect(
      eventMock.manager.getCustomRepository().findOne,
    ).toHaveBeenLastCalledWith({
      where: {
        recordId: entityId,
        recordType: 'MEDICATION',
      },
    });
    expect(
      eventMock.manager.getCustomRepository().save,
    ).toHaveBeenLastCalledWith({
      description: 'undefined',
      metaData: undefined,
      nextDate: new Date('2022-01-06T00:00:00.000Z'),
    });
  });
});
