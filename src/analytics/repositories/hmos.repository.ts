/* eslint-disable max-len */
/* eslint-disable max-lines */
/* eslint-disable prefer-arrow/prefer-arrow-functions */
/* eslint-disable @typescript-eslint/quotes */
/* eslint-disable @typescript-eslint/restrict-plus-operands */
import { DataSource, Repository } from 'typeorm';
import {
  extractPeriod,
  hmoDeathDemographicQuery,
  momentTz as moment,
} from '../../shared/helper/analytics';
import { seperateDiagnosisCode } from '../utils';
import { HmosAnalyticsFilter } from '@clinify/analytics/inputs/hmos.input';
import { formatDate } from '@clinify/analytics/inputs/services.input';
import {
  ActuarialReportResponse,
  AutomaticAndManualHmoClaimsData,
  FinanceReportData,
  GroupedHmoPreauthorizationSummary,
  HmoClaimDataResponse,
  HmoDetailedData,
  HmoPreauthorizationDataResponse,
  HmoPreauthorizationDetailedData,
  HmoPreauthorizationSummary,
  HmoReferralDetailedData,
  HmosSummary,
  MedicationReportData,
  OperationReportData,
  ProviderReportData,
  TopProceduresByCostReportData,
} from '@clinify/analytics/responses/hmos.response';
import { applyFilterForHmoAndPartnersOnHmoRecords } from '@clinify/analytics/utils/apply-filter-for-hmo-and-partners';
import { queryDSWithSlave } from '@clinify/database';
import { HmoClaimModel } from '@clinify/hmo-claims/models/hmo-claim.model';
import { CLINIFY_HMO_AGENCY_CODES } from '@clinify/hmo-providers/constants/constants';
import { UserType } from '@clinify/shared/enums/users';
import { getRequiredRole } from '@clinify/shared/helper';
import { EmploymentMemberStatus } from '@clinify/users/models/employment-member-status.enum';
import { ProfileModel } from '@clinify/users/models/profile.model';

const vettingGroups = [
  UserType.ClaimAccount,
  UserType.ClaimFinance,
  UserType.ClaimAdmin,
  UserType.ClaimAuditHOD,
  UserType.ClaimAudit,
  UserType.ClaimReviewerHOD,
  UserType.ClaimReviewer,
  UserType.ClaimOfficerHOD,
  UserType.ClaimOfficer,
  UserType.ClaimConfirmation,
];
export interface IHmosAnalyticsRepository extends Repository<HmoClaimModel> {
  this: Repository<HmoClaimModel>;
  getHmosData(
    dataSource: DataSource,
    profile: ProfileModel,
    filter: HmosAnalyticsFilter,
  ): Promise<HmoClaimDataResponse>;
  getHmosSummary(
    dataSource: DataSource,
    profile: ProfileModel,
    filter?: HmosAnalyticsFilter,
  ): Promise<HmosSummary>;

  getHmoPreauthorizationsData(
    dataSource: DataSource,
    profile: ProfileModel,
    filter: HmosAnalyticsFilter,
  ): Promise<HmoPreauthorizationDataResponse>;

  getHmoPreauthorizationsSummary(
    dataSource: DataSource,
    profile: ProfileModel,
    filter?: HmosAnalyticsFilter,
  ): Promise<HmoPreauthorizationSummary>;

  getHmoFinanceData(
    dataSource: DataSource,
    profile: ProfileModel,
    filter: HmosAnalyticsFilter,
  ): Promise<FinanceReportData[]>;
  getHmoMedicationData(
    dataSource: DataSource,
    profile: ProfileModel,
    filter: HmosAnalyticsFilter,
  ): Promise<MedicationReportData[]>;
  getHmoOperationData(
    dataSource: DataSource,
    profile: ProfileModel,
    filter: HmosAnalyticsFilter,
  ): Promise<OperationReportData>;
  getActuarialData(
    dataSource: DataSource,
    profile: ProfileModel,
    filter: HmosAnalyticsFilter,
  ): Promise<ActuarialReportResponse>;
}

const LASHMA_APPROVAL_ROLE = 'ClaimOfficerHOD';
const OTHERS_APPROVAL_ROLE = 'ClaimAdmin';
const calculateAmountSubmitted = `CAST(COALESCE(NULLIF(util.quantity, ''), '0') AS DECIMAL) * util.price::DECIMAL`;
const calculateAmount = `CAST(COALESCE(util.amount_covered, ${calculateAmountSubmitted}) AS DECIMAL)`;
const APPROVAL_ROLE = `
  CASE 
      WHEN (hmo_providers.provider_code = '107' OR hmo_providers.provider_code = '20') THEN '${LASHMA_APPROVAL_ROLE}'
      ELSE '${OTHERS_APPROVAL_ROLE}'
  END
`;

export const CustomHmosAnalyticsRepoMethods = {
  async getHmosData(
    dataSource: DataSource,
    profile: ProfileModel,
    filter: HmosAnalyticsFilter,
  ): Promise<HmoClaimDataResponse> {
    const { duration, startDate, endDate, orderBy } = filter;
    const { start, end } = formatDate(startDate, endDate);
    const hospitalId = !!profile.hmoId ? filter.hospitalId : profile.hospitalId;
    const isHmoOfficial = !!profile.hmoId;
    const result = await queryDSWithSlave(
      dataSource,
      `SELECT 
        COUNT(DISTINCT hmo_claims.id) AS "totalClaims",
        COUNT(DISTINCT hmo_claims.id) FILTER(WHERE hmo_claims.status ILIKE 'approved' OR hmo_claims.status ILIKE 'paid') AS "totalApprovedClaims",
        COUNT(DISTINCT hmo_claims.id) FILTER(WHERE hmo_claims.status ILIKE 'rejected') AS "totalRejectedClaims",
        COUNT(DISTINCT hmo_claims.id) FILTER(WHERE hmo_claims.status ILIKE 'draft') AS "totalDraftClaims",
        COUNT(DISTINCT hmo_claims.id) FILTER(WHERE hmo_claims.status ILIKE 'submitted' OR hmo_claims.status ILIKE 'Unconfirmed') AS "totalSubmittedClaims",
        COUNT(DISTINCT hmo_claims.id) FILTER(WHERE hmo_claims.status ILIKE 'paid') AS "totalPaidClaims",
        COUNT(DISTINCT hmo_claims.id) FILTER(WHERE hmo_claims.flags IS NOT NULL) AS "totalFlaggedClaims",
        SUM(CAST(hmo_claims."total_tariffFee" AS DECIMAL)) AS "totalClaimsAmount",
        SUM(CAST(hmo_claims."total_tariffFee" AS DECIMAL)) FILTER(WHERE hmo_claims.status ILIKE 'draft') AS "totalDraftClaimsAmount",
        SUM(CAST(hmo_claims."total_tariffFee" AS DECIMAL)) FILTER(WHERE hmo_claims.status ILIKE 'submitted' OR hmo_claims.status ILIKE 'Unconfirmed') AS "totalSubmittedClaimsAmount",
        SUM(amount_calc.amount_approved) AS "totalApprovedClaimsAmount",
        SUM(amount_calc.amount_rejected) AS "totalRejectedClaimsAmount",
        SUM(
        (SELECT SUM(
                        CASE WHEN EXISTS(SELECT
                                         1 FROM jsonb_array_elements(pau.utilisation_status) util
                                           WHERE util->>'status' = 'Rejected')
                            THEN 0
                            ELSE COALESCE(pau.amount_covered, pau.price::decimal * pau.quantity::decimal)
                            END
                )
         FROM pre_auth_utilisations pau
         WHERE pau.hmo_claim = hmo_claims.id)
           ) FILTER (WHERE hmo_claims.status ILIKE 'paid') AS "totalPaidClaimsAmount",
        SUM(CAST(hmo_claims."total_tariffFee" AS DECIMAL)) FILTER(WHERE hmo_claims.flags IS NOT NULL) AS "totalFlaggedClaimsAmount",
        COUNT(DISTINCT hmo_claims.id) FILTER ( WHERE LOWER(hmo_claims.status) NOT IN ('paid', 'rejected', 'approved', 'draft') ) AS "totalPendingClaims",
        SUM(CAST(hmo_claims."total_tariffFee" AS DECIMAL)) FILTER ( WHERE lower(hmo_claims.status) NOT IN ('paid', 'rejected', 'approved', 'draft') ) AS "totalPendingClaimsAmount",
        hmo_providers.name, hosp.name AS "hospitalName",
        ${vettingGroups
          .map(
            (group) =>
              `COUNT(DISTINCT hmo_claims.id) FILTER ( WHERE hmo_claims.flags IS NOT NULL AND EXISTS(
                  SELECT 1 FROM jsonb_array_elements(hmo_claims.flags) flag
                  WHERE flag->>'flaggedByRole' = '${group}')
              ) AS "total${group}FlaggedClaims",
            SUM(CAST(hmo_claims."total_tariffFee" AS DECIMAL)) FILTER ( WHERE hmo_claims.flags IS NOT NULL AND EXISTS(
                  SELECT 1 FROM jsonb_array_elements(hmo_claims.flags) flag
                  WHERE flag->>'flaggedByRole' = '${group}')
            ) AS "total${group}FlaggedClaimsAmount"`,
          )
          .join('\n,')}
        FROM hmo_claims
        INNER JOIN hospitals hosp ON hmo_claims.hospital_id = hosp.id
        INNER JOIN hmo_providers ON hmo_providers.id = hmo_claims.provider_id
        INNER JOIN LATERAL (
          SELECT
            SUM(${calculateAmount}) FILTER (
              WHERE
                status_element ->> 'vettingGroup' = ${APPROVAL_ROLE}
                AND status_element ->> 'status' = 'Approved'
            ) AS amount_approved,
            SUM(${calculateAmount}) FILTER (
              WHERE
                status_element ->> 'status' = 'Rejected'
            ) AS amount_rejected
          FROM
            pre_auth_utilisations util
            LEFT JOIN LATERAL jsonb_array_elements(util.utilisation_status) status_element ON TRUE
          WHERE
            util.hmo_claim = hmo_claims.id
        ) amount_calc ON TRUE
        INNER JOIN profiles created_by ON created_by.id = hmo_claims.created_by
        INNER JOIN "profiles" "profile" ON "profile"."id" = "hmo_claims"."profile_id"
            AND ("profile"."deleted_date" IS NULL)
        ${applyFilterForHmoAndPartnersOnHmoRecords({
          profile,
          filter,
          table: 'hmo_claims',
        })}
        AND created_by.type != 'Patient'
        AND date(hmo_claims.claim_date) BETWEEN '${start}' AND '${end}'
          ${
            !orderBy || orderBy?.toLowerCase() === 'all'
              ? ''
              : isHmoOfficial
              ? `AND hosp.id = '${orderBy}'`
              : `AND hmo_providers.id = '${orderBy}' `
          }
          GROUP BY hmo_providers.name, hosp.name
    `,
    );
    const visitations: Array<{
      name: string;
      total: number;
      hospitalName: string;
    }> = await queryDSWithSlave(
      dataSource,
      `SELECT name, COUNT(*) AS total, "hospitalName"
        FROM
          (
            SELECT
              DATE (hmo_claims.created_date) AS created_date,
              hmo_providers.name,
              hosp.name AS "hospitalName"
            FROM
              hmo_claims
              INNER JOIN hospitals hosp ON hmo_claims.hospital_id = hosp.id
              INNER JOIN hmo_providers ON hmo_providers.id = hmo_claims.provider_id
            ${applyFilterForHmoAndPartnersOnHmoRecords({
              profile,
              filter,
              table: 'hmo_claims',
            })}
              AND DATE (hmo_claims.created_date) BETWEEN '${start}' AND '${end}'
                ${
                  !orderBy || orderBy?.toLowerCase() === 'all'
                    ? ''
                    : isHmoOfficial
                    ? `AND hosp.id = '${orderBy}'`
                    : `AND hmo_providers.id = '${orderBy}' `
                }
            GROUP BY
              DATE (hmo_claims.created_date),
              hmo_providers.id,
              hmo_claims.profile_id,
              hosp.name
          ) as sub
        GROUP BY name, "hospitalName"
    `,
    );
    const clinifyEnrolleeStats: Array<{
      total_enrollees: string;
      total_active_enrollees: string;
      name: string;
      hospitalName: string;
    }> = await queryDSWithSlave(
      dataSource,
      `SELECT
        COUNT(*) AS total_enrollees,
        ${
          isHmoOfficial
            ? `COUNT(*) FILTER (WHERE hp.member_status ILIKE '${EmploymentMemberStatus.ACTIVE}') AS "total_active_enrollees"`
            : `COUNT(*) FILTER (WHERE hpe.hmo_profile_id IS NOT NULL AND hp.member_status ILIKE '${EmploymentMemberStatus.ACTIVE}') AS "total_active_enrollees"`
        },
            
        hmo_providers.name,
        hosp.name AS "hospitalName"
      FROM
        hmo_profiles AS hp
        INNER JOIN hmo_providers ON hmo_providers.id = hp.provider_id
        INNER JOIN profiles ON profiles.id = hp.profile_id
        INNER JOIN coverage_information ci ON ci.id = hp.coverage_information_id
        LEFT JOIN hospitals hosp ON hosp.id = hp.primary_provider_id
        LEFT JOIN hmo_profiles_eligibility hpe ON hpe.hmo_profile_id = hp.id AND hpe.hospital_id = hosp.id
      ${applyHospitalAndHmoIdFilterForEnrolleeStats(
        profile.hmoId,
        hospitalId,
        'hosp',
        'id',
      )} 
      AND hmo_providers.provider_code IN ('${CLINIFY_HMO_AGENCY_CODES.join(
        "','",
      )}')
      ${profile.hmoId ? `AND hp.provider_id = '${profile.hmoId}'` : ''}
        AND date(ci.created_date) BETWEEN '${start}' AND '${end}'
          ${
            !orderBy || orderBy?.toLowerCase() === 'all'
              ? ''
              : isHmoOfficial
              ? `AND hosp.id = '${orderBy}'`
              : `AND hp.provider_id = '${orderBy}' `
          }
      GROUP BY hmo_providers.name, hosp.name
      `,
    );

    const enrolleeStats: Array<{
      total_enrollees: string;
      total_active_enrollees: string;
      name: string;
      hospitalName: string;
    }> = await queryDSWithSlave(
      dataSource,
      `SELECT
        COUNT(*) AS total_enrollees,
        ${
          isHmoOfficial
            ? `COUNT(*) FILTER (WHERE hp.member_status ILIKE '${EmploymentMemberStatus.ACTIVE}') AS "total_active_enrollees"`
            : `COUNT(*) FILTER (WHERE hpe.hmo_profile_id IS NOT NULL AND hp.member_status ILIKE '${EmploymentMemberStatus.ACTIVE}') AS "total_active_enrollees"`
        },
            
        hmo_providers.name,
        hosp.name AS "hospitalName"
      FROM
        hmo_profiles AS hp
        INNER JOIN hmo_providers ON hmo_providers.id = hp.provider_id
        INNER JOIN profiles ON profiles.id = hp.profile_id
        INNER JOIN coverage_information ci ON ci.id = hp.coverage_information_id
        LEFT JOIN profiles cb ON cb.id = ci.created_by
        LEFT JOIN hospitals hosp ON hosp.id = cb."hospitalId" 
        LEFT JOIN hmo_profiles_eligibility hpe ON hpe.hospital_id = cb."hospitalId" AND hpe.hmo_profile_id = hp.id
      ${applyHospitalAndHmoIdFilterForEnrolleeStats(
        profile.hmoId,
        hospitalId,
        'hosp',
        'id',
      )} 
      AND hmo_providers.provider_code NOT IN ('${CLINIFY_HMO_AGENCY_CODES.join(
        "','",
      )}')
      ${profile.hmoId ? `AND hp.provider_id = '${profile.hmoId}'` : ''}
        AND date(ci.created_date) BETWEEN '${start}' AND '${end}'
          ${
            !orderBy || orderBy?.toLowerCase() === 'all'
              ? ''
              : isHmoOfficial
              ? `AND hosp.id = '${orderBy}'`
              : `AND hp.provider_id = '${orderBy}' `
          }
      GROUP BY hmo_providers.name, hosp.name
      `,
    );

    if (isHmoOfficial) {
      let resultByVettingGroup = [];
      let staffs = [];
      if (vettingGroups.length) {
        resultByVettingGroup = await queryDSWithSlave(
          dataSource,
          `SELECT
           ${vettingGroups
             .map(
               (group) => `
          COUNT(DISTINCT hc.id) FILTER (
            WHERE
              ap ->> 'vettingGroup' = '${group}'
              AND ap ->> 'status' = 'Approved'
          )::int AS "total${group}ApprovedClaims",
          SUM(${calculateAmount}) FILTER (WHERE ap ->> 'vettingGroup' = '${group}' AND ap ->> 'status' = 'Approved') AS "total${group}ApprovedClaimsAmount"
          `,
             )
             .join('\n,')},
            ${vettingGroups
              .map(
                (group) => `
          COUNT(DISTINCT hc.id) FILTER (
            WHERE
              ap ->> 'vettingGroup' = '${group}'
              AND ap ->> 'status' = 'Rejected'
          )::int AS "total${group}RejectedClaims",
          SUM(${calculateAmount}) FILTER (WHERE ap ->> 'vettingGroup' = '${group}' AND ap ->> 'status' = 'Rejected') AS "total${group}RejectedClaimsAmount"
          `,
              )
              .join('\n,')},
            COUNT(DISTINCT hc.id) FILTER (WHERE ap ->> 'status' = 'Approved') AS "totalApprovedClaims",
            SUM(${calculateAmount}) FILTER (WHERE ap ->> 'vettingGroup' = ${APPROVAL_ROLE}
                AND ap ->> 'status' = 'Approved') AS "totalApprovedClaimsAmount",
            COUNT(DISTINCT hc.id) FILTER (WHERE ap ->> 'status' = 'Rejected') AS "totalRejectedClaims",
            SUM(${calculateAmount}) FILTER (WHERE ap ->> 'status' = 'Rejected') AS "totalRejectedClaimsAmount",
          hosp.name AS "hospitalName",
          hmo_providers.name AS "hmoProvider"
        FROM
          hmo_claims hc
              JOIN (
              SELECT DISTINCT
                  id,
                  price,
                  quantity,
                  amount_covered,
                  utilisation_status,
                  hmo_claim
              FROM pre_auth_utilisations
          ) util ON util.hmo_claim = hc.id
          INNER JOIN hospitals hosp ON hc.hospital_id = hosp.id
          INNER JOIN hmo_providers ON hmo_providers.id = hc.provider_id
          CROSS JOIN LATERAL jsonb_array_elements(util.utilisation_status) as ap
        WHERE hc.provider_id = '${profile.hmoId}'
          AND date(hc.claim_date) BETWEEN '${start}' AND '${end}'
          AND util.utilisation_status IS NOT NULL
          ${hospitalId ? `AND hc.hospital_id = '${hospitalId}'` : ''}
          ${
            !orderBy || orderBy?.toLowerCase() === 'all'
              ? ''
              : `AND hc.hospital_id = '${orderBy}'`
          }
        GROUP BY hosp.name, hmo_providers.name
      `,
        );
        staffs = await queryDSWithSlave(
          dataSource,
          `SELECT 
          ${vettingGroups
            .map(
              (group) =>
                `(COUNT(*) FILTER (WHERE profiles.type = '${group}'))::int AS "total${group}StaffCount"`,
            )
            .join('\n,')},
          hosp.name AS "hospitalName"
           FROM profiles 
           INNER JOIN hospitals hosp ON hosp.id = profiles."hospitalId"
           WHERE profiles.hmo_id = '${profile.hmoId}'
           AND profiles.deleted_date IS NULL
           GROUP BY hosp.name`,
        );
      }

      const hmoProviderRoles = (
        await queryDSWithSlave(
          dataSource,
          `
            SELECT type FROM profiles
            WHERE hmo_id = '${profile.hmoId}'
              AND profiles.deleted_date IS NULL
            GROUP BY type
          `,
        )
      ).map((v) => v.type);
      const vettingRoles = vettingGroups.filter(
        (group) =>
          ![UserType.ClaimOfficer, UserType.ClaimAccount].includes(group) &&
          hmoProviderRoles.includes(group),
      );

      let pendingClaims = [];
      let claimAccountPending = [];
      if (vettingRoles.length) {
        pendingClaims = await queryDSWithSlave(
          dataSource,
          `SELECT
            COUNT(DISTINCT hc.id) FILTER (WHERE util.utilisation_status IS NULL) AS "totalClaimOfficerPendingClaims",
            SUM(${calculateAmount}) FILTER (WHERE util.utilisation_status IS NULL) AS "totalClaimOfficerPendingClaimsAmount",
            ${vettingRoles
              .map((group) => {
                const requiredRole = getRequiredRole(hmoProviderRoles, group);
                if (
                  hmoProviderRoles.includes(UserType.ClaimConfirmation) &&
                  group === UserType.ClaimConfirmation
                ) {
                  return `COUNT (DISTINCT hc.id) FILTER (WHERE util.confirmation IS NULL OR util.confirmation = FALSE) AS "totalClaimConfirmationPendingClaims",
                  SUM(${calculateAmount}) FILTER (WHERE util.confirmation IS NULL OR util.confirmation = FALSE) AS "totalClaimConfirmationPendingClaimsAmount"`;
                }

                let requiredRoleCondition = `util->>'vettingGroup' = '${requiredRole}'`;
                if (
                  group === UserType.ClaimOfficerHOD &&
                  hmoProviderRoles.includes(UserType.ClaimOfficer)
                ) {
                  requiredRoleCondition = `util->>'vettingGroup' = '${UserType.ClaimOfficer}'`;
                }

                return `
          COUNT(DISTINCT hc.id) FILTER (
            WHERE EXISTS (
              SELECT 1 
              FROM jsonb_array_elements(util.utilisation_status) util
              WHERE ${requiredRoleCondition} 
              AND util->>'status' = 'Approved'
            )
            AND NOT EXISTS (
              SELECT 1 
              FROM jsonb_array_elements(util.utilisation_status) util
              WHERE util->>'vettingGroup' = '${group}'
            )
          )::int AS "total${group}PendingClaims",
          SUM(${calculateAmount}) FILTER (
            WHERE EXISTS (
              SELECT 1 
              FROM jsonb_array_elements(util.utilisation_status) util
              WHERE ${requiredRoleCondition} 
              AND util->>'status' = 'Approved'
            )
            AND NOT EXISTS (
              SELECT 1 
              FROM jsonb_array_elements(util.utilisation_status) util
              WHERE util->>'vettingGroup' = '${group}'
            )
          )::int AS "total${group}PendingClaimsAmount"
          `;
              })
              .join('\n,')},
          hosp.name AS "hospitalName",
          hmo_providers.name AS "hmoProvider"
        FROM
          hmo_claims hc
              JOIN (SELECT DISTINCT id, price, quantity, amount_covered, utilisation_status, hmo_claim, confirmation FROM pre_auth_utilisations) util ON util.hmo_claim = hc.id
          INNER JOIN hospitals hosp ON hc.hospital_id = hosp.id
          INNER JOIN hmo_providers ON hmo_providers.id = hc.provider_id
         
        WHERE hc.provider_id = '${profile.hmoId}'
          AND LOWER(hc.status) NOT IN ('draft', 'approved','paid')
          AND date(hc.claim_date) BETWEEN '${start}' AND '${end}'
          ${hospitalId ? `AND hc.hospital_id = '${hospitalId}'` : ''}
          ${
            !orderBy || orderBy?.toLowerCase() === 'all'
              ? ''
              : `AND hc.hospital_id = '${orderBy}'`
          }
        GROUP BY hosp.name, hmo_providers.name
      `,
        );

        const requiredRole = getRequiredRole(
          hmoProviderRoles,
          UserType.ClaimAccount,
        );
        claimAccountPending = await queryDSWithSlave(
          dataSource,
          `SELECT
            COUNT(DISTINCT hc.id) FILTER (
            WHERE EXISTS (
              SELECT 1 
              FROM jsonb_array_elements(util.utilisation_status) util
              WHERE util->>'vettingGroup' = '${requiredRole}'
              AND util->>'status' = 'Approved'
            )
            AND NOT EXISTS (
              SELECT 1 
              FROM jsonb_array_elements(util.utilisation_status) util
              WHERE util->>'vettingGroup' = '${UserType.ClaimAccount}'
            )
          )::int AS "totalClaimAccountPendingClaims",
            SUM(${calculateAmount}) FILTER (
            WHERE EXISTS (
              SELECT 1 
              FROM jsonb_array_elements(util.utilisation_status) util
              WHERE util->>'vettingGroup' = '${requiredRole}'
              AND util->>'status' = 'Approved'
            )
            AND NOT EXISTS (
              SELECT 1 
              FROM jsonb_array_elements(util.utilisation_status) util
              WHERE util->>'vettingGroup' = '${UserType.ClaimAccount}'
            )
          )::int AS "totalClaimAccountPendingClaimsAmount",
            hosp.name AS "hospitalName",
            hmo_providers.name AS "hmoProvider"
          FROM
            hmo_claims hc
                JOIN (SELECT DISTINCT id, price, quantity, amount_covered, utilisation_status, hmo_claim, confirmation FROM pre_auth_utilisations) util ON util.hmo_claim = hc.id
            INNER JOIN hospitals hosp ON hc.hospital_id = hosp.id
            INNER JOIN hmo_providers ON hmo_providers.id = hc.provider_id
          WHERE hc.provider_id = '${profile.hmoId}'
            AND LOWER(hc.status) NOT IN ('draft','paid')
            AND date(hc.claim_date) BETWEEN '${start}' AND '${end}'
            ${hospitalId ? `AND hc.hospital_id = '${hospitalId}'` : ''}
            ${
              !orderBy || orderBy?.toLowerCase() === 'all'
                ? ''
                : `AND hc.hospital_id = '${orderBy}'`
            }
          GROUP BY hosp.name, hmo_providers.name
      `,
        );
      }

      if (!result.length) {
        result.push(
          ...resultByVettingGroup,
          ...staffs,
          ...pendingClaims,
          ...claimAccountPending,
        );
      }

      [
        ...resultByVettingGroup,
        ...staffs,
        ...pendingClaims,
        ...claimAccountPending,
      ].forEach((item) => {
        const index = result.findIndex(
          (r) => r.hospitalName === item.hospitalName,
        );
        if (index !== -1) {
          result[index] = {
            ...result[index],
            ...item,
          };
        } else {
          result.push({
            ...item,
          });
        }
      });
    }
    const data = result.map((item) => ({
      totalClaims: 0,
      totalEnrollees: 0,
      totalEnrolleeVisitations: 0,
      totalActiveEnrollees: 0,
      totalInactiveEnrollees: 0,
      totalApprovedClaims: 0,
      totalRejectedClaims: 0,
      totalDraftClaims: 0,
      totalSubmittedClaims: 0,
      totalClaimsAmount: 0,
      totalApprovedClaimsAmount: 0,
      totalRejectedClaimsAmount: 0,
      totalDraftClaimsAmount: 0,
      totalSubmittedClaimsAmount: 0,
      totalClaimOfficerApprovedClaims: 0,
      totalClaimReviewerApprovedClaims: 0,
      totalClaimAdminApprovedClaims: 0,
      totalClaimAccountApprovedClaims: 0,
      totalClaimOfficerRejectedClaims: 0,
      totalClaimReviewerRejectedClaims: 0,
      totalClaimAdminRejectedClaims: 0,
      totalClaimAccountRejectedClaims: 0,
      totalClaimOfficerHODApprovedClaims: 0,
      totalClaimOfficerHODRejectedClaims: 0,
      totalClaimReviewerHODApprovedClaims: 0,
      totalClaimReviewerHODRejectedClaims: 0,
      totalClaimOfficerHODPendingClaims: 0,
      totalClaimReviewerHODPendingClaims: 0,
      totalClaimOfficerHODPendingClaimsAmount: 0,
      totalClaimReviewerHODPendingClaimsAmount: 0,
      totalClaimOfficerHODFlaggedClaims: 0,
      totalClaimReviewerHODFlaggedClaims: 0,
      totalClaimOfficerHODFlaggedClaimsAmount: 0,
      totalClaimReviewerHODFlaggedClaimsAmount: 0,
      totalPaidClaims: 0,
      totalFlaggedClaims: 0,
      totalPaidClaimsAmount: 0,
      totalFlaggedClaimsAmount: 0,
      ...item,
      hmoProvider: item.name,
      hospitalName: item.hospitalName,
      name: extractPeriod(new Date(start), duration),
    })) as HmosSummary[];

    for (const item of [...enrolleeStats, ...clinifyEnrolleeStats]) {
      const index = data.findIndex(
        (d) =>
          d.hmoProvider === item.name && d.hospitalName === item.hospitalName,
      );
      if (index !== -1) {
        data[index].totalEnrollees = +item.total_enrollees;
        data[index].totalActiveEnrollees = +item.total_active_enrollees;
        data[index].totalInactiveEnrollees =
          +item.total_enrollees - +item.total_active_enrollees;
      } else {
        data.push({
          name: extractPeriod(new Date(start), duration),
          hmoProvider: item.name,
          totalClaims: 0,
          totalEnrollees: +item.total_enrollees,
          totalEnrolleeVisitations: 0,
          totalActiveEnrollees: +item.total_active_enrollees,
          totalInactiveEnrollees:
            +item.total_enrollees - +item.total_active_enrollees,
          totalApprovedClaims: 0,
          totalRejectedClaims: 0,
          totalDraftClaims: 0,
          totalSubmittedClaims: 0,
          totalClaimsAmount: 0,
          totalApprovedClaimsAmount: 0,
          totalRejectedClaimsAmount: 0,
          totalDraftClaimsAmount: 0,
          totalSubmittedClaimsAmount: 0,
          totalPaidClaims: 0,
          totalFlaggedClaims: 0,
          totalPaidClaimsAmount: 0,
          totalFlaggedClaimsAmount: 0,
          hospitalName: item.hospitalName,
        });
      }
    }

    for (const item of visitations) {
      const index = data.findIndex(
        (d) =>
          d.hmoProvider === item.name && d.hospitalName === item.hospitalName,
      );
      if (index !== -1) {
        data[index].totalEnrolleeVisitations = item.total;
      } else {
        data.push({
          name: extractPeriod(new Date(start), duration),
          hmoProvider: item.name,
          totalClaims: 0,
          totalEnrollees: 0,
          totalEnrolleeVisitations: item.total,
          totalActiveEnrollees: 0,
          totalInactiveEnrollees: 0,
          totalApprovedClaims: 0,
          totalRejectedClaims: 0,
          totalDraftClaims: 0,
          totalSubmittedClaims: 0,
          totalClaimsAmount: 0,
          totalApprovedClaimsAmount: 0,
          totalRejectedClaimsAmount: 0,
          totalDraftClaimsAmount: 0,
          totalSubmittedClaimsAmount: 0,
          totalPaidClaims: 0,
          totalPaidClaimsAmount: 0,
          totalFlaggedClaims: 0,
          totalFlaggedClaimsAmount: 0,
          hospitalName: item.hospitalName,
        });
      }
    }
    let detailedData: HmoDetailedData[] = [];
    let automaticAndManualClaimsData: AutomaticAndManualHmoClaimsData;
    let deathCountByGender: [
      {
        totalMaleDeath: number;
        totalFemaleDeath: number;
        [key: string]: number;
      },
    ] = [
      {
        totalMaleDeath: 0,
        totalFemaleDeath: 0,
      },
    ];
    if (profile.hmoId) {
      const query = `SELECT  hc.id,
        hc.claim_date AS "treatmentDateTime",
        hc.treatment_start_date AS "treatmentStartDate",
        hc.treatment_end_date AS "treatmentEndDate",
        hc.service_type AS "visitationType",
        hc."total_tariffFee" AS "amountSubmitted",
        hc.status AS "claimStatus",
        hc.claim_id AS "claimId",
        hc."enrolleeNumber",
        hc.batch_number AS "batchNumber",
        patient.full_name AS "enrolleeName",
        amount_calc.total_quantity AS "totalQuantity",
        amount_calc.amount_approved AS "amountApproved",
        amount_calc.amount_rejected AS "amountRejected",
        amount_calc.approved_by AS "approvedBy",
        amount_calc.rejected_by AS "rejectedBy",
        amount_calc.reason_for_rejection AS "reasonForRejection",
        amount_calc.service_type_name AS "serviceTypeName",
        amount_calc.amount_paid AS "amountPaid",
        amount_calc.birth_count AS "birthCount",
        amount_calc.delivery_date_time AS "deliveryDateTime",
        hc.submitted_by AS "submittedBy",
        string_agg(
            DISTINCT CASE
                WHEN flags_data ->> 'flaggedByFullname' IS NOT NULL
                         AND flags_data ->> 'flaggedByFullname' <> '' THEN flags_data ->> 'flaggedByFullname'
                END,
            ', '
        ) AS "flaggedBy",
       STRING_AGG(
        DISTINCT CONCAT(
          diagnosis_data->>'diagnosisICD10',
          CASE
            WHEN diagnosis_data->>'diagnosisICD11' IS NOT NULL AND diagnosis_data->>'diagnosisICD11' <> '' THEN CONCAT(', ', diagnosis_data->>'diagnosisICD11')
            ELSE ''
          END,
          CASE
            WHEN diagnosis_data->>'diagnosisSNOMED' IS NOT NULL AND diagnosis_data->>'diagnosisSNOMED' <> '' THEN CONCAT(', ', diagnosis_data->>'diagnosisSNOMED')
            ELSE ''
          END
        ),
        ', '
      ) AS "clinicalDiagnosis",
      hmop.member_plan AS "memberPlan",
      hmop.company_name AS "companyName",
      CASE
        WHEN patient.patient_status = 'Dead' THEN patient.cause_of_death
        ELSE ''
      END AS "causeOfDeath",
      CASE
        WHEN patient.patient_status = 'Dead' THEN patient.death_date_time
        ELSE NULL
      END AS "timeOfDeath",
      hosp.name AS "hospitalName",
      EXTRACT(YEAR FROM AGE(details.date_of_birth)) AS "age",
      patient.gender AS "gender",
      hmop.member_plan_group AS "memberPlanGroup",
      hmop.member_plan_sub_group AS "memberPlanSubGroup",
      details.lga AS "lga",
      details.ward AS "ward",
      jsonb_agg(util.utilisation_status) AS "utilizationStatus"
      FROM
        hmo_claims hc
        INNER JOIN hospitals hosp ON hc.hospital_id = hosp.id
        LEFT JOIN profiles patient ON patient.id = hc.profile_id
        LEFT JOIN details ON details.id = patient.details
        LEFT JOIN hmo_profiles hmop ON patient.id = hmop.profile_id
        LEFT JOIN pre_auth_utilisations util ON util.hmo_claim = hc.id
        LEFT JOIN LATERAL jsonb_array_elements(hc.diagnosis) AS diagnosis_data ON true
        LEFT JOIN LATERAL jsonb_array_elements(hc.flags) AS flags_data ON TRUE
        INNER JOIN LATERAL (
          SELECT
            (SELECT SUM(CAST(COALESCE(NULLIF(auth_util.quantity, ''), '0') AS DECIMAL))
              FROM (SELECT DISTINCT ON (pre_auth_util.id) pre_auth_util.id, pre_auth_util.quantity FROM pre_auth_utilisations pre_auth_util WHERE pre_auth_util.hmo_claim = hc.id) auth_util
              ) AS total_quantity,
            SUM(${calculateAmount}) FILTER (
              WHERE
                status_element ->> 'vettingGroup' = ${APPROVAL_ROLE}
                AND status_element ->> 'status' = 'Approved'
            ) AS amount_approved,
            SUM(${calculateAmount}) FILTER (
              WHERE
                status_element ->> 'status' = 'Rejected'
            ) AS amount_rejected,
            SUM(${calculateAmount}) FILTER (
              WHERE
                status_element ->> 'vettingGroup' = ${APPROVAL_ROLE}
                AND status_element ->> 'status' = 'Approved'
                AND hc.status ILIKE 'paid'
            ) AS amount_paid,
            STRING_AGG(
              DISTINCT CASE 
                WHEN status_element->>'status' = 'Approved' 
                THEN status_element ->> 'creatorName' 
              END, 
              ', '
            ) AS approved_by,
            STRING_AGG(
              DISTINCT CASE 
                WHEN status_element->>'status' = 'Rejected' 
                THEN status_element ->> 'creatorName' 
              END, 
              ', '
            ) AS rejected_by,
            STRING_AGG(
              DISTINCT CASE 
                WHEN status_element->>'status' = 'Rejected' 
                THEN status_element ->> 'statusDescription' 
              END, 
              ', '
            ) AS reason_for_rejection,
             STRING_AGG(DISTINCT(util.category || ' - ' || util.type) || ' ('|| util."utilizationCode" || ')', ', ') AS service_type_name,
             STRING_AGG(DISTINCT util.birth_count, ', ') AS birth_count,
             STRING_AGG(DISTINCT util.delivery_date_time::TEXT, ', ') AS delivery_date_time
          FROM
            pre_auth_utilisations util
            LEFT JOIN hmo_providers ON hmo_providers.id = util.hmo_provider_id
            LEFT JOIN LATERAL jsonb_array_elements(util.utilisation_status) status_element ON TRUE
          WHERE
            util.hmo_claim = hc.id
        ) amount_calc ON TRUE
      WHERE
        hc.status != 'draft'
        AND hc.provider_id = '${profile.hmoId}'
        AND date(hc.claim_date) BETWEEN '${start}' AND '${end}'
        ${hospitalId ? `AND hc.hospital_id = '${hospitalId}'` : ''}
        ${
          !orderBy || orderBy?.toLowerCase() === 'all'
            ? ''
            : `AND hc.hospital_id = '${orderBy}'`
        }
      GROUP BY
          hc.id, patient.id, hosp.name, hc.claim_date, hmop.member_plan,
          hmop.company_name, amount_calc.total_quantity, amount_calc.amount_approved,
          amount_calc.amount_rejected, amount_calc.approved_by, amount_calc.rejected_by,
          amount_calc.reason_for_rejection, amount_calc.service_type_name, amount_calc.amount_paid,
          amount_calc.birth_count, amount_calc.delivery_date_time, hmop.member_plan_group, hmop.member_plan_sub_group,details.date_of_birth,
          patient.gender, details.lga, details.ward
      ORDER BY hc.claim_date DESC`;
      detailedData = await queryDSWithSlave(dataSource, query);

      automaticAndManualClaimsData = (
        await queryDSWithSlave(
          dataSource,
          `
      WITH categorized_claims AS (
          SELECT
              c.id AS "id",
              c.status AS "status",
              CASE
                  WHEN p.hmo_id IS NOT NULL THEN p.type::text
                      ELSE NULL
              END AS "type",
              p.full_name::text AS "created_by_full_name",
              CASE
                  WHEN p.hmo_id IS NULL THEN 'automatic'
                  ELSE 'manual'
              END AS "claim_type",
              CASE
                  WHEN p.hmo_id IS NOT NULL THEN NULL
                  ELSE array_agg(jsonb_extract_path_text(elem.value, 'vettingGroup')) FILTER (WHERE jsonb_extract_path_text(elem.value, 'vettingGroup') IS NOT NULL)
              END AS "vetting_groups",
              CASE
                  WHEN p.hmo_id IS NOT NULL THEN NULL
                  ELSE array_agg(jsonb_extract_path_text(elem.value, 'creatorName')) FILTER (WHERE jsonb_extract_path_text(elem.value, 'creatorName') IS NOT NULL)
              END AS "vetter_names"
          FROM
              hmo_claims c
          LEFT JOIN pre_auth_utilisations util ON c.id = util.hmo_claim
          LEFT JOIN LATERAL jsonb_array_elements(util.utilisation_status) AS elem(value) ON TRUE
          JOIN
                  profiles p ON c.created_by = p.id
          WHERE c.provider_id = $1 AND c.status != 'draft' AND c.claim_date::DATE BETWEEN $2 AND $3
           ${hospitalId ? `AND c.hospital_id = '${hospitalId}'` : ''}
          GROUP BY c.id, c.status, p.type, p.full_name, p.hmo_id
      )
      SELECT
          (SELECT COUNT(DISTINCT id) FROM categorized_claims WHERE claim_type = 'automatic') AS "totalAutomated",
          (SELECT COUNT(DISTINCT id) FROM categorized_claims WHERE claim_type = 'manual') AS "totalManual",
          (
              SELECT json_agg(role_counts)
              FROM (
                  SELECT unnest(
                                 CASE
                                     WHEN categorized_claims.type IS NOT NULL
                                         THEN array_append(vetting_groups, categorized_claims.type)
                                     ELSE vetting_groups
                                 END
                         ) AS name,
                         COUNT(DISTINCT id) FILTER ( WHERE categorized_claims.claim_type = 'manual' ) AS "manualCount",
                         COUNT(DISTINCT id) FILTER(WHERE categorized_claims.vetting_groups IS NOT NULL AND array_length(categorized_claims.vetting_groups, 1) > 0) AS count
                  FROM categorized_claims
                  GROUP BY
                      unnest(
                              CASE
                                  WHEN categorized_claims.type IS NOT NULL
                                      THEN array_append(vetting_groups, categorized_claims.type)
                                  ELSE vetting_groups
                                  END
                      )
                   ) role_counts
          ) AS "byRoles",
          (
              SELECT json_agg(staff_counts)
              FROM (
                  SELECT unnest(
                                 CASE 
                                     WHEN categorized_claims.type IS NOT NULL 
                                        THEN array_append(vetter_names, categorized_claims.created_by_full_name)
                                     ELSE vetter_names
                                 END
                         ) AS name,
                         unnest(
                                 CASE
                                     WHEN categorized_claims.type IS NOT NULL
                                         THEN array_append(vetting_groups, categorized_claims.type)
                                     ELSE vetting_groups
                                     END
                         ) AS type,
                         COUNT(DISTINCT id) FILTER (WHERE categorized_claims.claim_type = 'manual') AS "manualCount",
                         COUNT(DISTINCT id) FILTER (WHERE categorized_claims.vetter_names IS NOT NULL AND array_length(categorized_claims.vetter_names, 1) > 0) AS count
                  FROM categorized_claims
                  GROUP BY
                      unnest(
                              CASE
                                  WHEN categorized_claims.type IS NOT NULL
                                      THEN array_append(vetter_names, categorized_claims.created_by_full_name)
                                  ELSE vetter_names
                                  END
                      ),
                      unnest(
                              CASE
                                  WHEN categorized_claims.type IS NOT NULL
                                      THEN array_append(vetting_groups, categorized_claims.type)
                                  ELSE vetting_groups
                                  END
                      )
                   ) staff_counts
          ) AS "byStaffs"
      `,
          [profile.hmoId, start, end],
        )
      )[0];

      deathCountByGender = await queryDSWithSlave(
        dataSource,
        `SELECT
          COUNT(DISTINCT profiles.id) FILTER (WHERE profiles.gender = 'Male') AS "totalMaleDeath",
          COUNT(DISTINCT profiles.id) FILTER (WHERE profiles.gender = 'Female') AS "totalFemaleDeath",
          COUNT(DISTINCT profiles.id) FILTER (WHERE ${
            hmoDeathDemographicQuery['0-28 Days']
          }) AS "0-28 Days",
          COUNT(DISTINCT profiles.id) FILTER (WHERE ${
            hmoDeathDemographicQuery['1-11 Months']
          }) AS "1-11 Months",
          COUNT(DISTINCT profiles.id) FILTER (WHERE ${
            hmoDeathDemographicQuery['12-59 Months']
          }) AS "12-59 Months",
          COUNT(DISTINCT profiles.id) FILTER (WHERE ${
            hmoDeathDemographicQuery['5-9 Years']
          }) AS "5-9 Years",
          COUNT(DISTINCT profiles.id) FILTER (WHERE ${
            hmoDeathDemographicQuery['10-19 Years']
          }) AS "10-19 Years",
          COUNT(DISTINCT profiles.id) FILTER (WHERE ${
            hmoDeathDemographicQuery['20-40 Years']
          }) AS "20-40 Years",
          COUNT(DISTINCT profiles.id) FILTER (WHERE ${
            hmoDeathDemographicQuery['> 40 Years']
          }) AS "> 40 Years"
        FROM
          hmo_claims AS hc
          INNER JOIN profiles ON profiles.id = hc.profile_id
          INNER JOIN details ON details.id = profiles.details
        WHERE
          hc.provider_id = '${profile.hmoId}'
          AND profiles.patient_status = 'Dead'
          AND date(hc.claim_date) BETWEEN '${start}' AND '${end}'
          ${hospitalId ? `AND hc.hospital_id = '${hospitalId}'` : ''}
          ${
            !orderBy || orderBy?.toLowerCase() === 'all'
              ? ''
              : `AND hc.hospital_id = '${orderBy}'`
          }
        `,
      );
    }

    const { totalMaleDeath, totalFemaleDeath, ...others } =
      deathCountByGender[0];
    const detailedDataArray = detailedData.map((item) => {
      const deliveryDateTimeSplit = (item.deliveryDateTime || '').split(',');
      if (deliveryDateTimeSplit.length <= 1) {
        item.deliveryDateTime = item.deliveryDateTime
          ? moment(item.deliveryDateTime).format('DD-MMM-YYYY HH:mm A')
          : null;
      } else {
        item.deliveryDateTime = deliveryDateTimeSplit
          .map(
            (_deliveryDateTime) =>
              (_deliveryDateTime = _deliveryDateTime
                ? moment(_deliveryDateTime).format('DD-MMM-YYYY HH:mm A')
                : null),
          )
          .join(', ');
      }
      return {
        ...item,
        utilizationStatus: item.utilizationStatus?.filter((v) => v)?.flat(),
        treatmentDateTime: item.treatmentDateTime
          ? moment(item.treatmentDateTime).format('DD-MMM-YYYY HH:mm A')
          : null,
        treatmentStartDate: item.treatmentStartDate
          ? moment(item.treatmentStartDate).format('DD-MMM-YYYY HH:mm A')
          : null,
        treatmentEndDate: item.treatmentEndDate
          ? moment(item.treatmentEndDate).format('DD-MMM-YYYY HH:mm A')
          : null,
        timeOfDeath: item.timeOfDeath
          ? moment(item.timeOfDeath).format('DD-MMM-YYYY HH:mm A')
          : null,
      };
    });
    return {
      summary: data,
      detailedData: detailedDataArray,
      automaticAndManualData: automaticAndManualClaimsData,
      totalMaleDeath,
      totalFemaleDeath,
      deathAgeRanges: Object.entries(others).map(([key, value]) => ({
        category: key,
        count: Number(value),
      })),
    };
  },

  async getHmosSummary(
    dataSource: DataSource,
    profile: ProfileModel,
    filter?: HmosAnalyticsFilter,
  ): Promise<HmosSummary> {
    const { start, end } = filter
      ? formatDate(filter.startDate, filter.endDate)
      : { start: '', end: '' };
    const result = await queryDSWithSlave(
      dataSource,
      `SELECT  COUNT(DISTINCT hmo_claims.id) AS "totalClaims",
        COUNT(DISTINCT hmo_claims.id) FILTER(WHERE hmo_claims.status ILIKE 'approved' OR hmo_claims.status ILIKE 'paid') AS "totalApprovedClaims",
        COUNT(hmo_claims.id) FILTER(WHERE hmo_claims.status ILIKE 'rejected') AS "totalRejectedClaims",
        COUNT(hmo_claims.id) FILTER(WHERE hmo_claims.status ILIKE 'draft') AS "totalDraftClaims",
        COUNT(hmo_claims.id) FILTER(WHERE hmo_claims.status ILIKE 'submitted' OR hmo_claims.status ILIKE 'Unconfirmed') AS "totalSubmittedClaims",
        COUNT(hmo_claims.id) FILTER(WHERE hmo_claims.status ILIKE 'paid') AS "totalPaidClaims",
        COUNT(hmo_claims.id) FILTER(WHERE hmo_claims.flags IS NOT NULL) AS "totalFlaggedClaims",
        SUM(CAST(hmo_claims."total_tariffFee" AS DECIMAL)) AS "totalClaimsAmount",
        SUM(CAST(hmo_claims."total_tariffFee" AS DECIMAL)) FILTER(WHERE hmo_claims.status ILIKE 'draft') AS "totalDraftClaimsAmount",
        SUM(CAST(hmo_claims."total_tariffFee" AS DECIMAL)) FILTER(WHERE hmo_claims.status ILIKE 'submitted' OR hmo_claims.status ILIKE 'Unconfirmed') AS "totalSubmittedClaimsAmount",
        SUM(amount_calc.amount_approved) AS "totalApprovedClaimsAmount",
        SUM(amount_calc.amount_rejected) AS "totalRejectedClaimsAmount",
        SUM(
       (SELECT SUM(
                       CASE WHEN EXISTS(SELECT
                                        1 FROM jsonb_array_elements(pau.utilisation_status) util_status
                                    WHERE util_status->>'status' = 'Rejected')
                           THEN 0
                           ELSE COALESCE(pau.amount_covered, pau.price::decimal * pau.quantity::decimal)
                           END
               )
        FROM pre_auth_utilisations pau
        WHERE pau.hmo_claim = hmo_claims.id)
          ) FILTER (WHERE hmo_claims.status ILIKE 'paid') AS "totalPaidClaimsAmount",
        SUM(CAST(hmo_claims."total_tariffFee" AS DECIMAL)) FILTER(WHERE hmo_claims.flags IS NOT NULL) AS "totalFlaggedClaimsAmount",
        COUNT(DISTINCT hmo_claims.id) FILTER ( WHERE LOWER(hmo_claims.status) NOT IN ('paid', 'rejected', 'approved', 'draft') ) AS "totalPendingClaims",
        SUM(CAST(hmo_claims."total_tariffFee" AS DECIMAL)) FILTER ( WHERE lower(hmo_claims.status) NOT IN ('paid', 'rejected', 'approved', 'draft') ) AS "totalPendingClaimsAmount",
        hmo_claims.facility_name AS "hospitalName",
        hmo_providers."name" AS "hmoProvider",
        ${vettingGroups
          .map(
            (group) =>
              `COUNT(DISTINCT hmo_claims.id) FILTER ( WHERE hmo_claims.flags IS NOT NULL AND EXISTS(
                  SELECT 1 FROM jsonb_array_elements(hmo_claims.flags) flag
                  WHERE flag->>'flaggedByRole' = '${group}')
              ) AS "total${group}FlaggedClaims",
            SUM(CAST(hmo_claims."total_tariffFee" AS DECIMAL)) FILTER ( WHERE hmo_claims.flags IS NOT NULL AND EXISTS(
                  SELECT 1 FROM jsonb_array_elements(hmo_claims.flags) flag
                  WHERE flag->>'flaggedByRole' = '${group}')
            ) AS "total${group}FlaggedClaimsAmount"`,
          )
          .join('\n,')}
         FROM hmo_claims
          INNER JOIN profiles created_by ON created_by.id = hmo_claims.created_by
          INNER JOIN "profiles" "profile" ON "profile"."id" = "hmo_claims"."profile_id"
            AND ("profile"."deleted_date" IS NULL)
          INNER JOIN hmo_providers ON hmo_providers.id = hmo_claims.provider_id
          INNER JOIN LATERAL (
          SELECT
            SUM(${calculateAmount}) FILTER (
              WHERE
                status_element ->> 'vettingGroup' = ${APPROVAL_ROLE}
                AND status_element ->> 'status' = 'Approved'
            ) AS amount_approved,
            SUM(${calculateAmount}) FILTER (
              WHERE
                status_element ->> 'status' = 'Rejected'
            ) AS amount_rejected
          FROM
            pre_auth_utilisations util
            LEFT JOIN LATERAL jsonb_array_elements(util.utilisation_status) status_element ON TRUE
          WHERE
            util.hmo_claim = hmo_claims.id
        ) amount_calc ON TRUE
          ${applyFilterForHmoAndPartnersOnHmoRecords({
            profile,
            filter,
            table: 'hmo_claims',
          })}
           AND created_by.type != 'Patient'
         ${
           filter?.startDate && filter?.endDate
             ? `AND date(hmo_claims.claim_date) BETWEEN '${start}' AND '${end}'`
             : ''
         }
    GROUP BY hmo_claims.facility_name, hmo_providers."name"`,
    );
    const isHmoOfficial = !!profile.hmoId;
    const clinifyEnrolleeStats: {
      totalEnrollees: string;
      totalActiveEnrollees: string;
      name: string;
      hospitalName: string;
    }[] = await queryDSWithSlave(
      dataSource,
      `SELECT
            COUNT(*) AS "totalEnrollees",
            ${
              isHmoOfficial
                ? `COUNT(*) FILTER (WHERE hp.member_status ILIKE '${EmploymentMemberStatus.ACTIVE}') AS "totalActiveEnrollees"`
                : `COUNT(*) FILTER (WHERE hpe.hmo_profile_id IS NOT NULL AND hp.member_status ILIKE '${EmploymentMemberStatus.ACTIVE}') AS "totalActiveEnrollees"`
            },
            
            hmo_providers.name,
            hosp.name AS "hospitalName"
          FROM
            hmo_profiles AS hp
            INNER JOIN hmo_providers ON hmo_providers.id = hp.provider_id
            INNER JOIN profiles ON profiles.id = hp.profile_id
            INNER JOIN coverage_information ci ON ci.id = hp.coverage_information_id
            LEFT JOIN hospitals hosp ON hosp.id = hp.primary_provider_id
            LEFT JOIN hmo_profiles_eligibility hpe ON hpe.hmo_profile_id = hp.id AND hpe.hospital_id = hosp.id
          ${applyHospitalAndHmoIdFilterForEnrolleeStats(
            profile.hmoId,
            profile.hmoId ? null : profile.hospitalId,
            'hosp',
            'id',
          )} 
          ${profile.hmoId ? `AND hp.provider_id = '${profile.hmoId}'` : ''}
          AND hmo_providers.provider_code IN ('${CLINIFY_HMO_AGENCY_CODES.join(
            "','",
          )}')
          GROUP BY hmo_providers.name, hosp.name
          `,
    );
    const enrolleeStats: {
      totalEnrollees: string;
      totalActiveEnrollees: string;
      name: string;
      hospitalName: string;
    }[] = await queryDSWithSlave(
      dataSource,
      `SELECT
            COUNT(*) AS "totalEnrollees",
            ${
              isHmoOfficial
                ? `COUNT(*) FILTER (WHERE hp.member_status ILIKE '${EmploymentMemberStatus.ACTIVE}') AS "totalActiveEnrollees"`
                : `COUNT(*) FILTER (WHERE hpe.hmo_profile_id IS NOT NULL AND hp.member_status ILIKE '${EmploymentMemberStatus.ACTIVE}') AS "totalActiveEnrollees"`
            },
            
            hmo_providers.name,
            hosp.name AS "hospitalName"
          FROM
            hmo_profiles AS hp
            INNER JOIN hmo_providers ON hmo_providers.id = hp.provider_id
            INNER JOIN profiles ON profiles.id = hp.profile_id
            INNER JOIN coverage_information ci ON ci.id = hp.coverage_information_id
            LEFT JOIN profiles cb ON cb.id = ci.created_by
            LEFT JOIN hospitals hosp ON cb."hospitalId" = hosp.id
            LEFT JOIN hmo_profiles_eligibility hpe ON hpe.hmo_profile_id = hp.id AND hpe.hospital_id = cb."hospitalId"
          ${applyHospitalAndHmoIdFilterForEnrolleeStats(
            profile.hmoId,
            profile.hmoId ? null : profile.hospitalId,
            'hosp',
            'id',
          )} 
          ${profile.hmoId ? `AND hp.provider_id = '${profile.hmoId}'` : ''}
          AND hmo_providers.provider_code NOT IN ('${CLINIFY_HMO_AGENCY_CODES.join(
            "','",
          )}')
          GROUP BY hmo_providers.name, hosp.name
          `,
    );

    const visitations: Array<{
      name: string;
      total: number;
      hospitalName: string;
    }> = await queryDSWithSlave(
      dataSource,
      `SELECT 
        COUNT(*) AS total, "name", "hospitalName"
      FROM
        (
          SELECT
            DATE (pa.created_date) AS created_date,
            hmo_providers.name AS "name",
            hosp.name AS "hospitalName",
            pa.profile_id AS "ProfileId"
          FROM
            pre_authorizations pa
            INNER JOIN hospitals hosp ON pa.hospital_id = hosp.id
            INNER JOIN hmo_providers ON hmo_providers.id = pa.provider_id
          ${applyFilterForHmoAndPartnersOnHmoRecords({
            profile,
            filter,
            table: 'pa',
          })}
          GROUP BY DATE (pa.created_date), hmo_providers.name, "hospitalName", "ProfileId"
          UNION ALL
          SELECT
            DATE (hc.created_date) AS created_date,
            hmo_providers.name AS "name",
            hosp.name AS "hospitalName",
            hc.profile_id AS "ProfileId"
          FROM
            hmo_claims hc
            INNER JOIN hospitals hosp ON hc.hospital_id = hosp.id
            INNER JOIN hmo_providers ON hmo_providers.id = hc.provider_id
          ${applyFilterForHmoAndPartnersOnHmoRecords({
            profile,
            filter,
            table: 'hc',
          })}
          GROUP BY DATE (hc.created_date), hmo_providers.name, "hospitalName", "ProfileId"
        ) as sub
      GROUP BY "name", "hospitalName"
      `,
    );

    let automaticAndManualClaimsData: AutomaticAndManualHmoClaimsData;
    if (profile.hmoId) {
      let resultByVettingGroup = [];
      let staffs = [];
      if (vettingGroups.length) {
        resultByVettingGroup = await queryDSWithSlave(
          dataSource,
          `SELECT
            ${vettingGroups
              .map(
                (group) => `
          COUNT(DISTINCT hc.id) FILTER (  WHERE  ap ->> 'vettingGroup' = '${group}' AND ap ->> 'status' = 'Approved')::int AS "total${group}ApprovedClaims",
          SUM(${calculateAmount}) FILTER (WHERE ap ->> 'vettingGroup' = '${group}' AND ap ->> 'status' = 'Approved') AS "total${group}ApprovedClaimsAmount"
          `,
              )
              .join('\n,')},
            ${vettingGroups
              .map(
                (group) => `
          COUNT(DISTINCT hc.id) FILTER (
            WHERE
              ap ->> 'vettingGroup' = '${group}'
              AND ap ->> 'status' = 'Rejected'
          )::int AS "total${group}RejectedClaims",
          SUM(${calculateAmount}) FILTER (WHERE ap ->> 'vettingGroup' = '${group}' AND ap ->> 'status' = 'Rejected') AS "total${group}RejectedClaimsAmount"
          `,
              )
              .join('\n,')},
            COUNT(DISTINCT hc.id) FILTER (WHERE ap ->> 'status' = 'Approved') AS "totalApprovedClaims",
            SUM(${calculateAmount}) FILTER (WHERE ap ->> 'vettingGroup' = ${APPROVAL_ROLE} AND ap ->> 'status' = 'Approved') AS "totalApprovedClaimsAmount",
            COUNT(DISTINCT hc.id) FILTER (WHERE ap ->> 'status' = 'Rejected') AS "totalRejectedClaims",
            SUM(${calculateAmount}) FILTER (WHERE ap ->> 'status' = 'Rejected') AS "totalRejectedClaimsAmount",
            hosp.name AS "hospitalName",
            hmo_providers.name AS "hmoProvider"
        FROM
          hmo_claims hc
              JOIN (
              SELECT DISTINCT
                  id,
                  price,
                  quantity,
                  amount_covered,
                  utilisation_status,
                  hmo_claim
              FROM pre_auth_utilisations
          ) util ON util.hmo_claim = hc.id
          INNER JOIN hospitals hosp ON hc.hospital_id = hosp.id
          INNER JOIN hmo_providers ON hmo_providers.id = hc.provider_id
          CROSS JOIN LATERAL jsonb_array_elements(util.utilisation_status) as ap
        WHERE hc.provider_id = '${profile.hmoId}'
          AND util.utilisation_status IS NOT NULL
        GROUP BY hosp.name, hmo_providers.name
      `,
        );
        staffs = await queryDSWithSlave(
          dataSource,
          `SELECT 
          ${vettingGroups
            .map(
              (group) =>
                `(COUNT(*) FILTER (WHERE profiles.type = '${group}'))::int AS "total${group}StaffCount"`,
            )
            .join('\n,')},
          hosp.name AS "hospitalName"
           FROM profiles 
           INNER JOIN hospitals hosp ON hosp.id = profiles."hospitalId"
           WHERE profiles.hmo_id = '${profile.hmoId}'
           AND profiles.deleted_date IS NULL
           GROUP BY hosp.name`,
        );
      }
      const hmoProviderRoles = (
        await queryDSWithSlave(
          dataSource,
          `
            SELECT type FROM profiles
            WHERE hmo_id = '${profile.hmoId}'
              AND profiles.deleted_date IS NULL
            GROUP BY type
          `,
        )
      ).map((v) => v.type);
      const vettingRoles = vettingGroups.filter(
        (group) =>
          ![UserType.ClaimOfficer, UserType.ClaimAccount].includes(group) &&
          hmoProviderRoles.includes(group),
      );
      let pendingClaims = [];
      let claimAccountPending = [];

      if (vettingRoles.length) {
        pendingClaims = await queryDSWithSlave(
          dataSource,
          `SELECT
            COUNT(DISTINCT hc.id) FILTER (WHERE util.utilisation_status IS NULL) AS "totalClaimOfficerPendingClaims",
            SUM(${calculateAmount}) FILTER (WHERE util.utilisation_status IS NULL) AS "totalClaimOfficerPendingClaimsAmount",
            ${vettingRoles
              .map((group) => {
                const requiredRole = getRequiredRole(hmoProviderRoles, group);
                if (
                  hmoProviderRoles.includes(UserType.ClaimConfirmation) &&
                  group === UserType.ClaimConfirmation
                ) {
                  return `COUNT (DISTINCT hc.id) FILTER (WHERE util.confirmation IS NULL OR util.confirmation = FALSE) AS "totalClaimConfirmationPendingClaims",
                  SUM(${calculateAmount}) FILTER (WHERE util.confirmation IS NULL OR util.confirmation = FALSE) AS "totalClaimConfirmationPendingClaimsAmount"`;
                }

                let requiredRoleCondition = `util->>'vettingGroup' = '${requiredRole}'`;
                if (
                  group === UserType.ClaimOfficerHOD &&
                  hmoProviderRoles.includes(UserType.ClaimOfficer)
                ) {
                  requiredRoleCondition = `util->>'vettingGroup' = '${UserType.ClaimOfficer}'`;
                }
                return `
          COUNT(DISTINCT hc.id) FILTER (
            WHERE EXISTS (
              SELECT 1 
              FROM jsonb_array_elements(util.utilisation_status) util
              WHERE ${requiredRoleCondition}
              AND util->>'status' = 'Approved'
            )
            AND NOT EXISTS (
              SELECT 1 
              FROM jsonb_array_elements(util.utilisation_status) util
              WHERE util->>'vettingGroup' = '${group}'
            )
          )::int AS "total${group}PendingClaims",
          SUM(${calculateAmount}) FILTER (
            WHERE EXISTS (
              SELECT 1 
              FROM jsonb_array_elements(util.utilisation_status) util
              WHERE ${requiredRoleCondition}
              AND util->>'status' = 'Approved'
            )
            AND NOT EXISTS (
              SELECT 1 
              FROM jsonb_array_elements(util.utilisation_status) util
              WHERE util->>'vettingGroup' = '${group}'
            )
          )::int AS "total${group}PendingClaimsAmount"
          `;
              })
              .join('\n,')},
          hosp.name AS "hospitalName",
          hmo_providers.name AS "hmoProvider"
        FROM
          hmo_claims hc
              JOIN (SELECT DISTINCT id, price, quantity, amount_covered, utilisation_status, hmo_claim, confirmation FROM pre_auth_utilisations) util ON util.hmo_claim = hc.id
          INNER JOIN hospitals hosp ON hc.hospital_id = hosp.id
          INNER JOIN hmo_providers ON hmo_providers.id = hc.provider_id
        WHERE hc.provider_id = '${
          profile.hmoId
        }' AND LOWER(hc.status) NOT IN ('draft', 'approved','paid')
        GROUP BY hosp.name, hmo_providers.name
      `,
        );

        const requiredRole = getRequiredRole(
          hmoProviderRoles,
          UserType.ClaimAccount,
        );
        claimAccountPending = await queryDSWithSlave(
          dataSource,
          `SELECT
            COUNT(DISTINCT hc.id) FILTER (
            WHERE EXISTS (
              SELECT 1 
              FROM jsonb_array_elements(util.utilisation_status) util
              WHERE util->>'vettingGroup' = '${requiredRole}'
              AND util->>'status' = 'Approved'
            )
            AND NOT EXISTS (
              SELECT 1 
              FROM jsonb_array_elements(util.utilisation_status) util
              WHERE util->>'vettingGroup' = '${UserType.ClaimAccount}'
            )
          )::int AS "totalClaimAccountPendingClaims",
            SUM(${calculateAmount}) FILTER (
            WHERE EXISTS (
              SELECT 1 
              FROM jsonb_array_elements(util.utilisation_status) util
              WHERE util->>'vettingGroup' = '${requiredRole}'
              AND util->>'status' = 'Approved'
            )
            AND NOT EXISTS (
              SELECT 1 
              FROM jsonb_array_elements(util.utilisation_status) util
              WHERE util->>'vettingGroup' = '${UserType.ClaimAccount}'
            )
          )::int AS "totalClaimAccountPendingClaimsAmount",
            hosp.name AS "hospitalName",
            hmo_providers.name AS "hmoProvider"
          FROM
            hmo_claims hc
                JOIN (SELECT DISTINCT id, price, quantity, amount_covered, utilisation_status, hmo_claim, confirmation FROM pre_auth_utilisations) util ON util.hmo_claim = hc.id
            INNER JOIN hospitals hosp ON hc.hospital_id = hosp.id
            INNER JOIN hmo_providers ON hmo_providers.id = hc.provider_id
        WHERE hc.provider_id = '${profile.hmoId}' AND LOWER(hc.status) NOT IN ('draft', 'paid')
        GROUP BY hosp.name, hmo_providers.name
      `,
        );
      }

      if (!result.length) {
        result.push(
          ...resultByVettingGroup,
          ...pendingClaims,
          ...staffs,
          ...claimAccountPending,
        );
      }
      [
        ...resultByVettingGroup,
        ...staffs,
        ...pendingClaims,
        ...claimAccountPending,
      ].forEach((item) => {
        const index = result.findIndex(
          (r) => r.hospitalName === item.hospitalName,
        );
        if (index !== -1) {
          result[index] = {
            ...result[index],
            ...item,
          };
        } else {
          result.push({
            ...item,
          });
        }
      });

      automaticAndManualClaimsData = (
        await queryDSWithSlave(
          dataSource,
          `
      WITH categorized_claims AS (
          SELECT
              c.id AS "id",
              c.status AS "status",
              CASE 
                  WHEN p.hmo_id IS NOT NULL THEN p.type::text
                      ELSE NULL
              END AS "type",
              p.full_name::text AS "created_by_full_name",
              CASE
                  WHEN p.hmo_id IS NULL THEN 'automatic'
                  ELSE 'manual'
              END AS "claim_type",
              CASE
                  WHEN p.hmo_id IS NOT NULL THEN NULL
                  ELSE array_agg(jsonb_extract_path_text(elem.value, 'vettingGroup')) FILTER (WHERE jsonb_extract_path_text(elem.value, 'vettingGroup') IS NOT NULL)
              END AS "vetting_groups",
              CASE
                  WHEN p.hmo_id IS NOT NULL THEN NULL
                  ELSE array_agg(jsonb_extract_path_text(elem.value, 'creatorName')) FILTER (WHERE jsonb_extract_path_text(elem.value, 'creatorName') IS NOT NULL)
              END AS "vetter_names"
          FROM
              hmo_claims c
          LEFT JOIN pre_auth_utilisations util ON c.id = util.hmo_claim
          LEFT JOIN LATERAL jsonb_array_elements(util.utilisation_status) AS elem(value) ON TRUE
          JOIN
                  profiles p ON c.created_by = p.id
          WHERE c.provider_id = $1 AND c.status != 'draft'
          GROUP BY c.id, c.status, p.type, p.full_name, p.hmo_id
      )
      SELECT
          (SELECT COUNT(DISTINCT id) FROM categorized_claims WHERE claim_type = 'automatic') AS "totalAutomated",
          (SELECT COUNT(DISTINCT id) FROM categorized_claims WHERE claim_type = 'manual') AS "totalManual",
          (
              SELECT json_agg(role_counts)
              FROM (
                  SELECT unnest(
                                 CASE
                                     WHEN categorized_claims.type IS NOT NULL
                                        THEN array_append(vetting_groups, categorized_claims.type)
                                    ELSE vetting_groups
                                 END
                         ) AS name,
                         COUNT(DISTINCT id) FILTER (WHERE categorized_claims.claim_type = 'manual') AS "manualCount",
                         COUNT(DISTINCT id) FILTER(WHERE categorized_claims.vetting_groups IS NOT NULL AND array_length(categorized_claims.vetting_groups, 1) > 0) AS count
                  FROM categorized_claims
                  GROUP BY
                      unnest(
                              CASE
                                  WHEN categorized_claims.type IS NOT NULL
                                      THEN array_append(vetting_groups, categorized_claims.type)
                                  ELSE vetting_groups
                                  END
                      )
                   ) role_counts
          ) AS "byRoles",
          (
              SELECT json_agg(staff_counts)
              FROM (
                  SELECT unnest(
                                 CASE
                                     WHEN categorized_claims.type IS NOT NULL
                                        THEN array_append(vetter_names, categorized_claims.created_by_full_name)
                                    ELSE vetter_names
                                 END
                         ) AS name,
                         unnest(
                                 CASE 
                                     WHEN categorized_claims.type IS NOT NULL
                                         THEN array_append(vetting_groups, categorized_claims.type) 
                                     ELSE vetting_groups
                                 END
                         ) AS type,
                         COUNT(DISTINCT id) FILTER (WHERE categorized_claims.claim_type = 'manual') AS "manualCount",
                         COUNT(DISTINCT id) FILTER(WHERE categorized_claims.vetter_names IS NOT NULL AND array_length(categorized_claims.vetter_names, 1) > 0) AS count
                  FROM categorized_claims
                  GROUP BY
                      unnest(
                              CASE
                                  WHEN categorized_claims.type IS NOT NULL
                                      THEN array_append(vetter_names, categorized_claims.created_by_full_name)
                                  ELSE vetter_names
                                  END
                      ),
                      unnest(
                              CASE
                                  WHEN categorized_claims.type IS NOT NULL
                                      THEN array_append(vetting_groups, categorized_claims.type)
                                  ELSE vetting_groups
                                  END
                      )
                   ) staff_counts
          ) AS "byStaffs"
      `,
          [profile.hmoId],
        )
      )[0];
    }

    for (const item of [...enrolleeStats, ...clinifyEnrolleeStats]) {
      const index = result.findIndex(
        (d) =>
          d.hmoProvider === item.name && d.hospitalName === item.hospitalName,
      );
      if (index !== -1) {
        result[index].totalEnrollees = +item.totalEnrollees;
        result[index].totalActiveEnrollees = +item.totalActiveEnrollees;
        result[index].totalInactiveEnrollees =
          +item.totalEnrollees - +item.totalActiveEnrollees;
      } else {
        result.push({
          hmoProvider: item.name,
          totalClaims: 0,
          totalEnrollees: +item.totalEnrollees,
          totalEnrolleeVisitations: 0,
          totalActiveEnrollees: +item.totalActiveEnrollees,
          totalInactiveEnrollees:
            +item.totalEnrollees - +item.totalActiveEnrollees,
          totalApprovedClaims: 0,
          totalRejectedClaims: 0,
          totalDraftClaims: 0,
          totalSubmittedClaims: 0,
          totalClaimsAmount: '0',
          totalApprovedClaimsAmount: '0',
          totalRejectedClaimsAmount: '0',
          totalDraftClaimsAmount: '0',
          totalSubmittedClaimsAmount: '0',
          totalPaidClaims: 0,
          totalPaidClaimsAmount: '0',
          totalFlaggedClaims: 0,
          totalFlaggedClaimsAmount: '0',
          hospitalName: item.hospitalName,
        });
      }
    }
    for (const item of visitations) {
      const index = result.findIndex(
        (d) => d.hospitalName === item.hospitalName,
      );
      if (index !== -1) {
        result[index].totalEnrolleeVisitations = +item.total;
      } else {
        result.push({
          hospitalName: item.hospitalName,
          totalEnrolleeVisitations: +item.total,
        });
      }
    }
    return {
      groupedData: result,
      automaticAndManualData: automaticAndManualClaimsData,
    };
  },

  async getHmoPreauthorizationsData(
    dataSource: DataSource,
    profile: ProfileModel,
    filter: HmosAnalyticsFilter,
  ): Promise<HmoPreauthorizationDataResponse> {
    const { duration, startDate, endDate, orderBy = 'all' } = filter;
    const { start, end } = formatDate(startDate, endDate);
    const hospitalId = profile.hmoId ? filter.hospitalId : profile.hospitalId;
    const isHmoOfficial = !!profile.hmoId;

    const result = await queryDSWithSlave(
      dataSource,
      `SELECT
        COUNT(DISTINCT preauth.id) AS "totalPreauthorizations",
        COUNT(DISTINCT preauth.id) FILTER(WHERE utilisation.status ILIKE 'pending') AS "totalPendingPreauthorizations",
        COUNT(DISTINCT preauth.id) FILTER(WHERE utilisation.status ILIKE 'approved') AS "totalApprovedPreauthorizations",
        COUNT(DISTINCT preauth.id) FILTER(WHERE utilisation.status ILIKE 'rejected') AS "totalRejectedPreauthorizations",
        SUM(CAST(utilisation.price AS DECIMAL) * CAST(utilisation.quantity AS DECIMAL)) AS "totalPreauthorizationAmount",
        SUM(CAST(utilisation.price AS DECIMAL) * CAST(utilisation.quantity AS DECIMAL)) FILTER(WHERE utilisation.status ILIKE 'pending') AS "totalPendingPreauthorizationAmount",
        SUM(CAST(utilisation.price AS DECIMAL) * CAST(utilisation.quantity AS DECIMAL)) FILTER(WHERE utilisation.status ILIKE 'approved') AS "totalApprovedPreauthorizationAmount",
        SUM(CAST(utilisation.price AS DECIMAL) * CAST(utilisation.quantity AS DECIMAL)) FILTER(WHERE utilisation.status ILIKE 'rejected') AS "totalRejectedPreauthorizationAmount",
        COUNT(DISTINCT preauth.id) FILTER (WHERE preauth.flags IS NOT NULL) AS "totalFlaggedPreauthorizations",
        SUM(utilisation.quantity::decimal * utilisation.price::decimal) FILTER (WHERE preauth.flags IS NOT NULL ) AS "totalFlaggedPreauthorizationsAmount",
        hmo_providers.name,
        hosp.name AS "hospitalName"${profile.hmoId ? ',' : ''}
        ${generatePreauthQueryStringForVettingGroupApprovalStatus(
          profile,
          'utilisation',
        )}${profile.hmoId ? ',' : ''}
        ${
          profile.hmoId
            ? vettingGroups
                .map(
                  (group) =>
                    `COUNT(DISTINCT preauth.id) FILTER ( WHERE preauth.flags IS NOT NULL AND EXISTS(
                          SELECT 1 FROM jsonb_array_elements(preauth.flags) flag
                          WHERE flag->>'flaggedByRole' = '${group}')
                      ) AS "total${group}FlaggedPreauthorizations",
                    SUM(CAST(utilisation.price AS DECIMAL) * CAST(utilisation.quantity AS DECIMAL)) FILTER ( WHERE preauth.flags IS NOT NULL AND EXISTS(
                          SELECT 1 FROM jsonb_array_elements(preauth.flags) flag
                          WHERE flag->>'flaggedByRole' = '${group}')
                    ) AS "total${group}FlaggedPreauthorizationsAmount"`,
                )
                .join('\n,')
            : ''
        }
        FROM pre_authorizations AS preauth
        INNER JOIN hospitals hosp ON preauth.hospital_id = hosp.id
        INNER JOIN pre_auth_utilisations AS utilisation ON utilisation.pre_auth = preauth.id
        INNER JOIN hmo_providers ON hmo_providers.id = preauth.provider_id 
        ${applyFilterForHmoAndPartnersOnHmoRecords({
          profile,
          filter,
          table: 'preauth',
        })}
        AND date(preauth.request_date_time) BETWEEN '${start}' AND '${end}'
          ${
            !orderBy || orderBy.toLowerCase() === 'all'
              ? ''
              : isHmoOfficial
              ? `AND hosp.id = '${orderBy}'`
              : `AND hmo_providers.id = '${orderBy}' `
          }
          GROUP BY hmo_providers.name, hosp.name
    `,
    );
    const clinifyEnrolleeStats: Array<{
      total_enrollees: string;
      total_active_enrollees: string;
      name: string;
      hospitalName: string;
    }> = await queryDSWithSlave(
      dataSource,
      `SELECT
        COUNT(*) AS total_enrollees,
        ${
          isHmoOfficial
            ? `COUNT(*) FILTER (WHERE hp.member_status ILIKE '${EmploymentMemberStatus.ACTIVE}') AS "total_active_enrollees"`
            : `COUNT(*) FILTER (WHERE hpe.hmo_profile_id IS NOT NULL AND hp.member_status ILIKE '${EmploymentMemberStatus.ACTIVE}') AS "total_active_enrollees"`
        },
        hmo_providers.name,
        hosp.name AS "hospitalName"
      FROM
        hmo_profiles AS hp
        INNER JOIN hmo_providers ON hmo_providers.id = hp.provider_id
        INNER JOIN profiles ON profiles.id = hp.profile_id
        INNER JOIN coverage_information ci ON ci.id = hp.coverage_information_id
        LEFT JOIN hospitals hosp ON hosp.id = hp.primary_provider_id
        LEFT JOIN hmo_profiles_eligibility hpe ON hpe.hmo_profile_id = hp.id AND hpe.hospital_id = hosp.id
      ${applyHospitalAndHmoIdFilterForEnrolleeStats(
        profile.hmoId,
        hospitalId,
        'hosp',
        'id',
      )}
      ${profile.hmoId ? `AND hp.provider_id = '${profile.hmoId}'` : ''}
      AND hmo_providers.provider_code IN ('${CLINIFY_HMO_AGENCY_CODES.join(
        "','",
      )}')
        AND date(ci.created_date) BETWEEN '${start}' AND '${end}'
          ${
            !orderBy || orderBy.toLowerCase() === 'all'
              ? ''
              : isHmoOfficial
              ? `AND hosp.id = '${orderBy}'`
              : `AND hp.provider_id = '${orderBy}' `
          }
      GROUP BY hmo_providers.name, hosp.name
      `,
    );
    const enrolleeStats: Array<{
      total_enrollees: string;
      total_active_enrollees: string;
      name: string;
      hospitalName: string;
    }> = await queryDSWithSlave(
      dataSource,
      `SELECT
        COUNT(*) AS total_enrollees,
        ${
          isHmoOfficial
            ? `COUNT(*) FILTER (WHERE hp.member_status ILIKE '${EmploymentMemberStatus.ACTIVE}') AS "total_active_enrollees"`
            : `COUNT(*) FILTER (WHERE hpe.hmo_profile_id IS NOT NULL AND hp.member_status ILIKE '${EmploymentMemberStatus.ACTIVE}') AS "total_active_enrollees"`
        },
        hmo_providers.name,
        hosp.name AS "hospitalName"
      FROM
        hmo_profiles AS hp
        INNER JOIN hmo_providers ON hmo_providers.id = hp.provider_id
        INNER JOIN profiles ON profiles.id = hp.profile_id
        INNER JOIN coverage_information ci ON ci.id = hp.coverage_information_id
        LEFT JOIN profiles cb ON cb.id = ci.created_by
        LEFT JOIN hospitals hosp ON cb."hospitalId" = hosp.id
        LEFT JOIN hmo_profiles_eligibility hpe ON hpe.hmo_profile_id = hp.id AND hpe.hospital_id = cb."hospitalId"
      ${applyHospitalAndHmoIdFilterForEnrolleeStats(
        profile.hmoId,
        hospitalId,
        'hosp',
        'id',
      )}
      ${profile.hmoId ? `AND hp.provider_id = '${profile.hmoId}'` : ''}
      AND hmo_providers.provider_code NOT IN ('${CLINIFY_HMO_AGENCY_CODES.join(
        "','",
      )}')
        AND date(ci.created_date) BETWEEN '${start}' AND '${end}'
          ${
            !orderBy || orderBy.toLowerCase() === 'all'
              ? ''
              : isHmoOfficial
              ? `AND hosp.id = '${orderBy}'`
              : `AND hp.provider_id = '${orderBy}' `
          }
      GROUP BY hmo_providers.name, hosp.name
      `,
    );
    const visitations: Array<{
      name: string;
      total: number;
      hospitalName: string;
    }> = await queryDSWithSlave(
      dataSource,
      `SELECT 
        COUNT(*) AS total, "name", "hospitalName"
      FROM
        (
          SELECT
            DATE (pa.created_date) AS created_date,
            hmo_providers.name AS "name",
            hosp.name AS "hospitalName",
            pa.profile_id AS "ProfileId"
          FROM
            pre_authorizations pa
            INNER JOIN hospitals hosp ON pa.hospital_id = hosp.id
            INNER JOIN hmo_providers ON hmo_providers.id = pa.provider_id
          ${applyFilterForHmoAndPartnersOnHmoRecords({
            profile,
            filter,
            table: 'pa',
          })}
          GROUP BY DATE (pa.created_date), hmo_providers.name, "hospitalName", "ProfileId"
          UNION ALL
          SELECT
            DATE (hc.created_date) AS created_date,
            hmo_providers.name AS "name",
            hosp.name AS "hospitalName",
            hc.profile_id AS "ProfileId"
          FROM
            hmo_claims hc
            INNER JOIN hospitals hosp ON hc.hospital_id = hosp.id
            INNER JOIN hmo_providers ON hmo_providers.id = hc.provider_id
          ${applyFilterForHmoAndPartnersOnHmoRecords({
            profile,
            filter,
            table: 'hc',
          })}
          GROUP BY DATE (hc.created_date), hmo_providers.name, "hospitalName", "ProfileId"
        ) as sub
      GROUP BY "name", "hospitalName"
    `,
    );
    const referrals: Array<{
      name: string;
      total: number;
      hospitalName: string;
      totalReferralsAmount: string;
      totalPendingReferralsAmount: string;
      totalApprovedReferralsAmount: string;
      totalRejectedReferralsAmount: string;
      totalPendingReferrals: string;
      totalApprovedReferrals: string;
      totalRejectedReferrals: string;
    }> = await queryDSWithSlave(
      dataSource,
      `SELECT
        COUNT(*) AS total,
        hmo_providers."name" AS "name",
        hospitals."name" AS "hospitalName",
        SUM(CAST(pre_auth_referral_utilisations.quantity AS numeric) * CAST(pre_auth_referral_utilisations.price AS numeric)) AS "totalReferralsAmount",
        SUM(CAST(pre_auth_referral_utilisations.quantity AS numeric) * CAST(pre_auth_referral_utilisations.price AS numeric)) FILTER (WHERE pre_auth_referral_utilisations.status ilike 'Pending') AS "totalPendingReferralsAmount",
        SUM(CAST(pre_auth_referral_utilisations.quantity AS numeric) * CAST(pre_auth_referral_utilisations.price AS numeric)) FILTER (WHERE pre_auth_referral_utilisations.status ilike 'Approved') AS "totalApprovedReferralsAmount",
        SUM(CAST(pre_auth_referral_utilisations.quantity AS numeric) * CAST(pre_auth_referral_utilisations.price AS numeric)) FILTER (WHERE pre_auth_referral_utilisations.status ilike 'Rejected') AS "totalRejectedReferralsAmount",
        COUNT(DISTINCT par.id) FILTER (WHERE pre_auth_referral_utilisations.status ilike 'Pending') AS "totalPendingReferrals",
        COUNT(DISTINCT par.id) FILTER (WHERE pre_auth_referral_utilisations.status ilike 'Approved') AS "totalApprovedReferrals",
        COUNT(DISTINCT par.id) FILTER (WHERE pre_auth_referral_utilisations.status ilike 'Rejected') AS "totalRejectedReferrals"
      FROM
        pre_authorizations_referral as par
        INNER JOIN pre_auth_referral_utilisations ON par.id = pre_auth_referral_utilisations.pre_auth_referral_id
        INNER JOIN hospitals ON par.hospital_id = hospitals.id
        INNER JOIN hmo_providers ON par.provider_id = hmo_providers.id
      ${applyFilterForHmoAndPartnersOnHmoRecords({
        profile,
        filter,
        table: 'par',
      })}
        AND DATE (par.created_date) BETWEEN '${start}' AND '${end}'
          ${
            !orderBy || orderBy.toLowerCase() === 'all'
              ? ''
              : isHmoOfficial
              ? `AND hospitals.id = '${orderBy}'`
              : `AND hmo_providers.id = '${orderBy}' `
          }
      GROUP BY hmo_providers."name", hospitals."name"
    `,
    );

    const data = result.map((item) => ({
      totalPreauthorizations: 0,
      totalPendingPreauthorizations: 0,
      totalApprovedPreauthorizations: 0,
      totalRejectedPreauthorizations: 0,
      totalEnrollees: 0,
      totalEnrolleeVisitations: 0,
      totalActiveEnrollees: 0,
      totalInactiveEnrollees: 0,
      totalPreauthorizationAmount: '0',
      totalApprovedPreauthorizationAmount: '0',
      totalRejectedPreauthorizationAmount: '0',
      totalPendingPreauthorizationAmount: '0',
      ...item,
      hmoProvider: item.name,
      hospitalName: item.hospitalName,
      name: extractPeriod(new Date(start), duration),
    })) as GroupedHmoPreauthorizationSummary[];

    for (const item of [...enrolleeStats, ...clinifyEnrolleeStats]) {
      const index = data.findIndex(
        (d) =>
          d.hmoProvider === item.name && d.hospitalName === item.hospitalName,
      );
      if (index !== -1) {
        data[index].totalEnrollees = +item.total_enrollees;
        data[index].totalActiveEnrollees = +item.total_active_enrollees;
        data[index].totalInactiveEnrollees =
          +item.total_enrollees - +item.total_active_enrollees;
      } else {
        data.push({
          totalPreauthorizations: 0,
          totalPendingPreauthorizations: 0,
          totalApprovedPreauthorizations: 0,
          totalRejectedPreauthorizations: 0,
          totalEnrollees: 0,
          totalEnrolleeVisitations: 0,
          totalActiveEnrollees: 0,
          totalInactiveEnrollees: 0,
          totalPreauthorizationAmount: '0',
          totalApprovedPreauthorizationAmount: '0',
          totalRejectedPreauthorizationAmount: '0',
          totalPendingPreauthorizationAmount: '0',
          hmoProvider: item.name,
          hospitalName: item.hospitalName,
        });
      }
    }

    for (const item of visitations) {
      const index = data.findIndex(
        (d) =>
          d.hmoProvider === item.name && d.hospitalName === item.hospitalName,
      );
      if (index !== -1) {
        data[index].totalEnrolleeVisitations = item.total;
      } else {
        data.push({
          totalPreauthorizations: 0,
          totalPendingPreauthorizations: 0,
          totalApprovedPreauthorizations: 0,
          totalRejectedPreauthorizations: 0,
          totalEnrollees: 0,
          totalEnrolleeVisitations: 0,
          totalActiveEnrollees: 0,
          totalInactiveEnrollees: 0,
          totalPreauthorizationAmount: '0',
          totalApprovedPreauthorizationAmount: '0',
          totalRejectedPreauthorizationAmount: '0',
          totalPendingPreauthorizationAmount: '0',
          hmoProvider: item.name,
          hospitalName: item.hospitalName,
        });
      }
    }
    for (const item of referrals) {
      const index = data.findIndex(
        (d) =>
          d.hmoProvider === item.name && d.hospitalName === item.hospitalName,
      );
      if (index !== -1) {
        data[index].totalReferrals = +item.total;
        data[index].totalReferralsAmount = +item.totalReferralsAmount;
        data[index].totalPendingReferrals = +item.totalPendingReferrals;
        data[index].totalApprovedReferrals = +item.totalApprovedReferrals;
        data[index].totalRejectedReferrals = +item.totalRejectedReferrals;
        data[index].totalPendingReferralsAmount =
          +item.totalPendingReferralsAmount;
        data[index].totalApprovedReferralsAmount =
          +item.totalApprovedReferralsAmount;
        data[index].totalRejectedReferralsAmount =
          +item.totalRejectedReferralsAmount;
      } else {
        data.push({
          totalPreauthorizations: 0,
          totalPendingPreauthorizations: 0,
          totalApprovedPreauthorizations: 0,
          totalRejectedPreauthorizations: 0,
          totalEnrollees: 0,
          totalEnrolleeVisitations: 0,
          totalActiveEnrollees: 0,
          totalInactiveEnrollees: 0,
          totalPreauthorizationAmount: '0',
          totalApprovedPreauthorizationAmount: '0',
          totalRejectedPreauthorizationAmount: '0',
          totalPendingPreauthorizationAmount: '0',
          totalReferrals: +item.total,
          totalReferralsAmount: +item.totalReferralsAmount,
          totalPendingReferrals: +item.totalPendingReferrals,
          totalApprovedReferrals: +item.totalApprovedReferrals,
          totalRejectedReferrals: +item.totalRejectedReferrals,
          totalPendingReferralsAmount: +item.totalPendingReferralsAmount,
          totalApprovedReferralsAmount: +item.totalApprovedReferralsAmount,
          totalRejectedReferralsAmount: +item.totalRejectedReferralsAmount,
          hmoProvider: item.name,
          hospitalName: item.hospitalName,
        });
      }
    }
    let detailedData: HmoPreauthorizationDetailedData[] = [];
    let detailedReferrals: Array<HmoReferralDetailedData> = [];
    let staffs = [];
    let automaticAndManualPreauthData: AutomaticAndManualHmoClaimsData;
    let deathCount: [
      {
        totalMaleDeath: number;
        totalFemaleDeath: number;
        [key: string]: number;
      },
    ] = [
      {
        totalMaleDeath: 0,
        totalFemaleDeath: 0,
      },
    ];
    if (profile.hmoId) {
      detailedData = await queryDSWithSlave(
        dataSource,
        `SELECT  preauth.id,
        preauth.request_date_time AS "treatmentDateTime",
        preauth.treatment_start_date AS "treatmentStartDate",
        preauth.treatment_end_date AS "treatmentEndDate",
        preauth.service_type AS "visitationType",
        preauth.enrollee_number AS "enrolleeNumber",
        patient.full_name AS "enrolleeName",
        STRING_AGG(DISTINCT(util.category || ' - ' || util.type) || ' ('|| util."utilizationCode" || ')', ', ') AS "serviceTypeName",
        string_agg(util.status, ', ') AS "preauthorizationStatus",
        string_agg(util.pa_code, ', ') AS "preauthorizationCode",
        SUM(CAST(COALESCE(NULLIF(util.quantity, ''), '0') AS DECIMAL)) AS "totalQuantity",
        SUM(CAST(COALESCE(NULLIF(util.quantity, ''), '0') AS DECIMAL) * CAST(util.price AS DECIMAL)) AS "amountRequested",
        preauth.requested_by AS "requestedBy",
        SUM(CAST(COALESCE(NULLIF(util.quantity, ''), '0') AS DECIMAL) * CAST(util.price AS DECIMAL)) FILTER (WHERE status_element->>'status' = 'Approved') AS "amountApproved",
        SUM(CAST(COALESCE(NULLIF(util.quantity, ''), '0') AS DECIMAL) * CAST(util.price AS DECIMAL)) FILTER (WHERE status_element->>'status' = 'Rejected') AS "amountRejected",
           string_agg(
             DISTINCT CASE
                WHEN flags_data ->> 'flaggedByFullname' IS NOT NULL
                    AND flags_data ->> 'flaggedByFullname' <> '' THEN flags_data ->> 'flaggedByFullname'
            END,
             ', '
           ) AS "flaggedBy",
        STRING_AGG(
          CASE 
            WHEN status_element->>'status' = 'Approved' 
            THEN status_element ->> 'creatorName' 
          END, 
          ', '
        ) AS "approvedBy",
        STRING_AGG(
          CASE 
            WHEN status_element->>'status' = 'Rejected' 
            THEN status_element ->> 'creatorName' 
          END, 
          ', '
        ) AS "rejectedBy",
       STRING_AGG(
        DISTINCT CONCAT(
          diagnosis_data->>'diagnosisICD10',
          CASE
            WHEN diagnosis_data->>'diagnosisICD11' IS NOT NULL AND diagnosis_data->>'diagnosisICD11' <> '' THEN CONCAT(', ', diagnosis_data->>'diagnosisICD11')
            ELSE ''
          END,
          CASE
            WHEN diagnosis_data->>'diagnosisSNOMED' IS NOT NULL AND diagnosis_data->>'diagnosisSNOMED' <> '' THEN CONCAT(', ', diagnosis_data->>'diagnosisSNOMED')
            ELSE ''
          END
        ),
        ', '
      ) AS "clinicalDiagnosis",
      CASE
        WHEN patient.patient_status = 'Dead' THEN patient.cause_of_death
        ELSE ''
      END AS "causeOfDeath",
      CASE
        WHEN patient.patient_status = 'Dead' THEN patient.death_date_time
        ELSE NULL
      END AS "timeOfDeath",
      STRING_AGG(DISTINCT util.birth_count, ', ') AS "birthCount",
      STRING_AGG(DISTINCT util.delivery_date_time::TEXT, ', ') AS "deliveryDateTime",
      hosp.name AS "hospitalName",
      hmo_providers."name" AS "hmoProvider",
      EXTRACT(YEAR FROM AGE(details.date_of_birth)) AS "age",
      patient.gender AS "gender",
      hmop.member_plan_group AS "memberPlanGroup",
      hmop.member_plan_sub_group AS "memberPlanSubGroup",
      hmop.member_plan AS "memberPlan",
      details.lga AS "lga",
      details.ward AS "ward",
      preauth.response_date_time AS "responseDateTime",
      preauth.created_date AS "createdDate"
        FROM
        pre_authorizations AS preauth
        INNER JOIN hospitals hosp ON preauth.hospital_id = hosp.id
        LEFT JOIN pre_auth_utilisations util ON util.pre_auth = preauth.id
        LEFT JOIN profiles patient ON patient.id = preauth.profile_id
        LEFT JOIN details ON details.id = patient.details
        LEFT JOIN hmo_providers ON hmo_providers.id = preauth.provider_id
        LEFT JOIN LATERAL jsonb_array_elements(util.utilisation_status) AS status_element ON true
        LEFT JOIN LATERAL jsonb_array_elements(preauth.diagnosis) AS diagnosis_data ON true
        LEFT JOIN LATERAL jsonb_array_elements(preauth.flags) AS flags_data ON TRUE
        LEFT JOIN LATERAL (
          SELECT
            id, member_plan, member_plan_group, member_plan_sub_group
            FROM
            hmo_profiles
            WHERE
            provider_id = preauth.provider_id
            AND profile_id = patient.id
          ) hmop ON TRUE
      WHERE
        preauth.provider_id = '${profile.hmoId}'
        AND date(preauth.request_date_time) BETWEEN '${start}' AND '${end}'
       
        ${hospitalId ? `AND preauth.hospital_id = '${hospitalId}'` : ''}
        ${
          !orderBy || orderBy?.toLowerCase() === 'all'
            ? ''
            : `AND preauth.hospital_id = '${orderBy}'`
        }
      GROUP BY preauth.id, patient.id, hosp.name, hmo_providers."name", preauth.request_date_time,details.date_of_birth, hmop.member_plan, hmop.member_plan_group, hmop.member_plan_sub_group, details.lga, details.ward
        ORDER BY preauth.request_date_time DESC`,
      );

      detailedReferrals = await queryDSWithSlave(
        dataSource,
        `SELECT  par.id,
          par.request_date_time AS "treatmentDateTime",
          par.enrollee_number AS "enrolleeNumber",
          patient.full_name AS "enrolleeName",
          par.facility_name AS "referringFacility",
          par.referred_provider_name AS "referredToFacility",
          par.priority AS "priority",
          par.referred_by AS "referredBy",
          (
            SELECT STRING_AGG( 
              CONCAT(
                elem->>'diagnosisICD10',
                CASE
                  WHEN elem->>'diagnosisICD11' IS NOT NULL AND elem->>'diagnosisICD11' <> '' THEN CONCAT(', ', elem->>'diagnosisICD11')
                  ELSE ''
                END,
                CASE
                  WHEN elem->>'diagnosisSNOMED' IS NOT NULL AND elem->>'diagnosisSNOMED' <> '' THEN CONCAT(', ', elem->>'diagnosisSNOMED')
                  ELSE ''
                END
              ), ', '
            )
            FROM jsonb_array_elements(par.diagnosis) AS elem
          ) AS "clinicalDiagnosis",
        par.service_type AS "visitType",
        EXTRACT(YEAR FROM AGE(details.date_of_birth)) AS "age",
        patient.gender AS "gender",
        hmop.member_plan_group AS "memberPlanGroup",
        hmop.member_plan_sub_group AS "memberPlanSubGroup",
        hmop.member_plan AS "memberPlan",
        par.provider_refferal_remarks AS "providerReferralRemarks",
        details.lga AS "lga",
        details.ward AS "ward",
        util.*

      FROM pre_authorizations_referral par
        INNER JOIN hospitals hosp ON par.hospital_id = hosp.id
        LEFT JOIN profiles patient ON patient.id = par.profile_id
        LEFT JOIN details ON details.id = patient.details
        LEFT JOIN LATERAL (
          SELECT
          id, member_plan, member_plan_group, member_plan_sub_group
          FROM
          hmo_profiles
          WHERE
          provider_id = par.provider_id
          AND profile_id = patient.id
        ) hmop ON TRUE
        LEFT JOIN LATERAL (
          SELECT
             util.pa_code AS "referralCode",
             string_agg(util.status, ', ') AS "referralStatus",
            STRING_AGG((
              SELECT STRING_AGG( elem->>'creatorName', ', ')
              FROM jsonb_array_elements(util.status_history) AS elem
              WHERE elem->>'status' = 'Approved'
            ), ', ') AS "approvedBy",
            STRING_AGG((
              SELECT STRING_AGG( elem->>'creatorName', ', ')
              FROM jsonb_array_elements(util.status_history) AS elem
              WHERE elem->>'status' = 'Rejected'
            ), ', ') AS "rejectedBy"
          FROM
            pre_auth_referral_utilisations util
          WHERE
            util.pre_auth_referral_id = par.id
            GROUP BY util.pa_code
        ) util ON TRUE
      WHERE par.provider_id = '${profile.hmoId}'
        AND date(par.created_date) BETWEEN '${start}' AND '${end}'
        ${hospitalId ? `AND par.hospital_id = '${hospitalId}'` : ''}
        ${
          !orderBy || orderBy?.toLowerCase() === 'all'
            ? ''
            : `AND par.hospital_id = '${orderBy}'`
        }
    
        ORDER BY par.request_date_time DESC
        `,
      );
      staffs = await queryDSWithSlave(
        dataSource,
        `
    SELECT COUNT(profiles.id) AS "totalStaffCount", profiles.type 
        FROM profiles
            WHERE profiles.hmo_id = $1 AND profiles.deleted_date IS NULL
        GROUP BY profiles.type 
    `,
        [profile.hmoId],
      );

      automaticAndManualPreauthData = (
        await queryDSWithSlave(
          dataSource,
          `WITH categorized_preauths AS (
          SELECT
              pa.id AS "id",
              pa.status AS "status",
              CASE
                  WHEN p.hmo_id IS NOT NULL THEN p.type::text
                      ELSE NULL
              END AS "type",
              p.full_name::text AS "created_by_full_name",
              CASE WHEN p.hmo_id IS NULL THEN 'automatic' ELSE 'manual' END AS "pa_type",
              CASE
                  WHEN p.hmo_id IS NOT NULL THEN NULL
                  ELSE array_agg(jsonb_extract_path_text(elem.value, 'vettingGroup')) FILTER (WHERE jsonb_extract_path_text(elem.value, 'vettingGroup') IS NOT NULL)
              END AS "vetting_groups",
              CASE
                  WHEN p.hmo_id IS NOT NULL THEN NULL
                  ELSE array_agg(jsonb_extract_path_text(elem.value, 'creatorName')) FILTER (WHERE jsonb_extract_path_text(elem.value, 'creatorName') IS NOT NULL)
              END AS "vetter_names"
          FROM
              pre_authorizations pa
          LEFT JOIN pre_auth_utilisations util ON pa.id = util.pre_auth
          LEFT JOIN LATERAL jsonb_array_elements(util.utilisation_status) AS elem(value) ON TRUE
          JOIN profiles p ON pa.created_by = p.id
          WHERE pa.provider_id = $1 AND pa.request_date_time::DATE BETWEEN $2 AND $3
           ${hospitalId ? `AND pa.hospital_id = '${hospitalId}'` : ''}
          GROUP BY pa.id, pa.status, p.full_name, p.type, p.full_name, p.hmo_id
      )
      SELECT
          (SELECT COUNT(DISTINCT id) FROM categorized_preauths WHERE pa_type = 'automatic') AS "totalAutomated",
          (SELECT COUNT(DISTINCT id) FROM categorized_preauths WHERE pa_type = 'manual') AS "totalManual",
          (
              SELECT json_agg(role_counts)
              FROM (
                  SELECT unnest(
                            CASE
                                WHEN categorized_preauths.type IS NOT NULL
                                    THEN array_append(vetting_groups, categorized_preauths.type)
                                ELSE vetting_groups
                            END
                         ) AS name,
                         COUNT(DISTINCT id) FILTER (WHERE categorized_preauths.pa_type = 'manual') AS "manualCount",
                         COUNT(DISTINCT id) FILTER (WHERE categorized_preauths.vetting_groups IS NOT NULL AND array_length(categorized_preauths.vetting_groups, 1) > 0) As count
                  FROM categorized_preauths
                  GROUP BY
                      unnest(
                              CASE
                                  WHEN categorized_preauths.type IS NOT NULL
                                      THEN array_append(vetting_groups, categorized_preauths.type)
                                  ELSE vetting_groups
                                  END
                      )
                   ) role_counts
          ) AS "byRoles",
          (
              SELECT json_agg(staff_counts)
              FROM (
                  SELECT unnest(
                                 CASE
                                     WHEN categorized_preauths.type IS NOT NULL
                                         THEN array_append(vetter_names, categorized_preauths.created_by_full_name)
                                     ELSE vetter_names
                                 END
                         ) AS name,
                         unnest(
                                 CASE
                                     WHEN categorized_preauths.type IS NOT NULL
                                         THEN array_append(vetting_groups, categorized_preauths.type)
                                     ELSE vetting_groups
                                 END
                         ) AS type,
                         COUNT(DISTINCT id) FILTER (WHERE categorized_preauths.pa_type = 'manual') AS "manualCount",
                         COUNT(DISTINCT id) FILTER(WHERE categorized_preauths.vetter_names IS NOT NULL AND array_length(categorized_preauths.vetter_names, 1) > 0) AS count
                  FROM categorized_preauths
                  GROUP BY
                      unnest(
                              CASE
                                  WHEN categorized_preauths.type IS NOT NULL
                                      THEN array_append(vetter_names, categorized_preauths.created_by_full_name)
                                  ELSE vetter_names
                                  END
                      ),
                      unnest(
                              CASE
                                  WHEN categorized_preauths.type IS NOT NULL
                                      THEN array_append(vetting_groups, categorized_preauths.type)
                                  ELSE vetting_groups
                                  END
                      )
                   ) staff_counts
              ) AS "byStaffs"
      `,
          [profile.hmoId, start, end],
        )
      )[0];

      deathCount = await queryDSWithSlave(
        dataSource,
        `SELECT
          COUNT(DISTINCT profiles.id) FILTER (WHERE profiles.gender = 'Male') AS "totalMaleDeath",
          COUNT(DISTINCT profiles.id) FILTER (WHERE profiles.gender = 'Female') AS "totalFemaleDeath",
          COUNT(DISTINCT profiles.id) FILTER (WHERE ${
            hmoDeathDemographicQuery['0-28 Days']
          }) AS "0-28 Days",
          COUNT(DISTINCT profiles.id) FILTER (WHERE ${
            hmoDeathDemographicQuery['1-11 Months']
          }) AS "1-11 Months",
          COUNT(DISTINCT profiles.id) FILTER (WHERE ${
            hmoDeathDemographicQuery['12-59 Months']
          }) AS "12-59 Months",
          COUNT(DISTINCT profiles.id) FILTER (WHERE ${
            hmoDeathDemographicQuery['5-9 Years']
          }) AS "5-9 Years",
          COUNT(DISTINCT profiles.id) FILTER (WHERE ${
            hmoDeathDemographicQuery['10-19 Years']
          }) AS "10-19 Years",
          COUNT(DISTINCT profiles.id) FILTER (WHERE ${
            hmoDeathDemographicQuery['20-40 Years']
          }) AS "20-40 Years",
          COUNT(DISTINCT profiles.id) FILTER (WHERE ${
            hmoDeathDemographicQuery['> 40 Years']
          }) AS "> 40 Years"
        FROM
          pre_authorizations AS preauth
          INNER JOIN profiles ON profiles.id = preauth.profile_id
          INNER JOIN details ON details.id = profiles.details
        WHERE
          preauth.provider_id = '${profile.hmoId}'
          AND profiles.patient_status = 'Dead'
          AND date(preauth.request_date_time) BETWEEN '${start}' AND '${end}'
          ${hospitalId ? `AND preauth.hospital_id = '${hospitalId}'` : ''}
          ${
            !orderBy || orderBy?.toLowerCase() === 'all'
              ? ''
              : `AND preauth.hospital_id = '${orderBy}'`
          }
        `,
      );
    }

    const { totalMaleDeath, totalFemaleDeath, ...others } = deathCount[0];

    return {
      summary: data,
      automaticAndManualData: automaticAndManualPreauthData,
      detailedData: detailedData.map((item) => {
        const deliveryDateTimeSplit = (item.deliveryDateTime || '').split(',');
        if (deliveryDateTimeSplit.length <= 1) {
          item.deliveryDateTime = item.deliveryDateTime
            ? moment(item.deliveryDateTime).format('DD-MMM-YYYY HH:mm A')
            : null;
        } else {
          item.deliveryDateTime = deliveryDateTimeSplit
            .map(
              (_deliveryDateTime) =>
                (_deliveryDateTime = _deliveryDateTime
                  ? moment(_deliveryDateTime).format('DD-MMM-YYYY HH:mm A')
                  : null),
            )
            .join(', ');
        }

        return {
          ...item,
          treatmentDateTime: item.treatmentDateTime
            ? moment(item.treatmentDateTime).format('DD-MMM-YYYY HH:mm A')
            : null,
          treatmentStartDate: item.treatmentStartDate
            ? moment(item.treatmentStartDate).format('DD-MMM-YYYY HH:mm A')
            : null,
          treatmentEndDate: item.treatmentEndDate
            ? moment(item.treatmentEndDate).format('DD-MMM-YYYY HH:mm A')
            : null,
          timeOfDeath: item.timeOfDeath
            ? moment(item.timeOfDeath).format('DD-MMM-YYYY HH:mm A')
            : null,
        };
      }),
      referralData: detailedReferrals.map((item) => ({
        ...item,
        treatmentDateTime: item.treatmentDateTime
          ? moment(item.treatmentDateTime).format('DD-MMM-YYYY HH:mm A')
          : null,
      })),
      ...(staffs.length
        ? staffs.reduce((acc, curr) => {
            return {
              ...acc,
              [`total${curr.type}StaffCount`]: Number(curr.totalStaffCount),
            };
          }, {})
        : {}),
      totalMaleDeath,
      totalFemaleDeath,
      deathAgeRanges: Object.entries(others).map(([key, value]) => ({
        category: key,
        count: Number(value),
      })),
    };
  },
  async getHmoPreauthorizationsSummary(
    dataSource: DataSource,
    profile: ProfileModel,
    filter?: HmosAnalyticsFilter,
  ): Promise<HmoPreauthorizationSummary> {
    const { start, end } = filter
      ? formatDate(filter.startDate, filter.endDate)
      : { start: '', end: '' };
    const isHmoOfficial = !!profile.hmoId;

    const data: GroupedHmoPreauthorizationSummary[] = await queryDSWithSlave(
      dataSource,
      `SELECT COUNT(DISTINCT preauth.id) AS "totalPreauthorizations",
          SUM(CAST(utilizations.quantity AS DECIMAL) * CAST(utilizations.price AS DECIMAL)) AS "totalPreauthorizationAmount",
          COUNT(DISTINCT preauth.id) FILTER(WHERE utilizations.status ilike 'approved') AS "totalApprovedPreauthorizations",
          COUNT(DISTINCT preauth.id) FILTER(WHERE utilizations.status ilike 'rejected') AS "totalRejectedPreauthorizations",
          COUNT(DISTINCT preauth.id) FILTER(WHERE utilizations.status ilike 'pending') AS "totalPendingPreauthorizations",
          SUM(CAST(utilizations.quantity AS DECIMAL) * CAST(utilizations.price AS DECIMAL)) FILTER(WHERE utilizations.status ilike 'approved') AS "totalApprovedPreauthorizationAmount",
          SUM(CAST(utilizations.quantity AS DECIMAL) * CAST(utilizations.price AS DECIMAL)) FILTER(WHERE utilizations.status ilike 'rejected') AS "totalRejectedPreauthorizationAmount",
          SUM(CAST(utilizations.quantity AS DECIMAL) * CAST(utilizations.price AS DECIMAL)) FILTER(WHERE utilizations.status ilike 'pending') AS "totalPendingPreauthorizationAmount",
          COUNT(DISTINCT preauth.id) FILTER (WHERE preauth.flags IS NOT NULL ) AS "totalFlaggedPreauthorizations",
          SUM(utilizations.quantity::decimal * utilizations.price::decimal) FILTER (WHERE preauth.flags IS NOT NULL ) AS "totalFlaggedPreauthorizationsAmount",
          preauth.facility_name AS "hospitalName",
          hmo_providers.name AS "hmoProvider"${profile.hmoId ? ',' : ''}
          ${generatePreauthQueryStringForVettingGroupApprovalStatus(profile)}${
        profile.hmoId ? ',' : ''
      }
          ${
            profile.hmoId
              ? vettingGroups
                  .map(
                    (group) =>
                      `COUNT(DISTINCT preauth.id) FILTER ( WHERE preauth.flags IS NOT NULL AND EXISTS(
                          SELECT 1 FROM jsonb_array_elements(preauth.flags) flag
                          WHERE flag->>'flaggedByRole' = '${group}')
                      ) AS "total${group}FlaggedPreauthorizations",
                    SUM(CAST(utilizations.price AS DECIMAL) * CAST(utilizations.quantity AS DECIMAL)) FILTER ( WHERE preauth.flags IS NOT NULL AND EXISTS(
                          SELECT 1 FROM jsonb_array_elements(preauth.flags) flag
                          WHERE flag->>'flaggedByRole' = '${group}')
                    ) AS "total${group}FlaggedPreauthorizationsAmount"`,
                  )
                  .join('\n,')
              : ''
          }
          FROM pre_authorizations preauth
          INNER JOIN pre_auth_utilisations utilizations ON utilizations.pre_auth = preauth.id
          INNER JOIN hmo_providers ON hmo_providers.id = preauth.provider_id
          ${applyFilterForHmoAndPartnersOnHmoRecords({
            profile,
            filter,
            table: 'preauth',
          })}
          ${
            start && end
              ? `AND date(preauth.created_date) BETWEEN '${start}' AND '${end}'`
              : ''
          }
          GROUP BY preauth.facility_name, hmo_providers.name
    `,
    );

    const clinifyEnrolleeStats: Array<{
      total_enrollees: string;
      total_active_enrollees: string;
      name: string;
      hospitalName: string;
    }> = await queryDSWithSlave(
      dataSource,
      `SELECT
        COUNT(*) AS total_enrollees,
        ${
          isHmoOfficial
            ? `COUNT(*) FILTER (WHERE hp.member_status ILIKE '${EmploymentMemberStatus.ACTIVE}') AS "total_active_enrollees"`
            : `COUNT(*) FILTER (WHERE hpe.hmo_profile_id IS NOT NULL AND hp.member_status ILIKE '${EmploymentMemberStatus.ACTIVE}') AS "total_active_enrollees"`
        },
        hmo_providers.name,
        hosp.name AS "hospitalName"
      FROM
        hmo_profiles AS hp
        INNER JOIN hmo_providers ON hmo_providers.id = hp.provider_id
        INNER JOIN profiles ON profiles.id = hp.profile_id
        INNER JOIN coverage_information ci ON ci.id = hp.coverage_information_id
        LEFT JOIN hospitals hosp ON hosp.id = hp.primary_provider_id
        LEFT JOIN hmo_profiles_eligibility hpe ON hpe.hmo_profile_id = hp.id AND hpe.hospital_id = hosp.id
      ${applyHospitalAndHmoIdFilterForEnrolleeStats(
        profile.hmoId,
        profile.hmoId ? null : profile.hospitalId,
        'hosp',
        'id',
      )}
      ${profile.hmoId ? `AND hp.provider_id = '${profile.hmoId}'` : ''}
      AND hmo_providers.provider_code IN ('${CLINIFY_HMO_AGENCY_CODES.join(
        "','",
      )}')
      GROUP BY hmo_providers.name, hosp.name
      `,
    );
    const enrolleeStats: Array<{
      total_enrollees: string;
      total_active_enrollees: string;
      name: string;
      hospitalName: string;
    }> = await queryDSWithSlave(
      dataSource,
      `SELECT
        COUNT(*) AS total_enrollees,
        ${
          isHmoOfficial
            ? `COUNT(*) FILTER (WHERE hp.member_status ILIKE '${EmploymentMemberStatus.ACTIVE}') AS "total_active_enrollees"`
            : `COUNT(*) FILTER (WHERE hpe.hmo_profile_id IS NOT NULL AND hp.member_status ILIKE '${EmploymentMemberStatus.ACTIVE}') AS "total_active_enrollees"`
        },
        hmo_providers.name,
        hosp.name AS "hospitalName"
      FROM
        hmo_profiles AS hp
        INNER JOIN hmo_providers ON hmo_providers.id = hp.provider_id
        INNER JOIN profiles ON profiles.id = hp.profile_id
        INNER JOIN coverage_information ci ON ci.id = hp.coverage_information_id
        LEFT JOIN profiles cb ON cb.id = ci.created_by
        LEFT JOIN hospitals hosp ON cb."hospitalId" = hosp.id
        LEFT JOIN hmo_profiles_eligibility hpe ON hpe.hmo_profile_id = hp.id AND hpe.hospital_id = cb."hospitalId"
      ${applyHospitalAndHmoIdFilterForEnrolleeStats(
        profile.hmoId,
        profile.hmoId ? null : profile.hospitalId,
        'hosp',
        'id',
      )}
      ${profile.hmoId ? `AND hp.provider_id = '${profile.hmoId}'` : ''}
      AND hmo_providers.provider_code NOT IN ('${CLINIFY_HMO_AGENCY_CODES.join(
        "','",
      )}')
      GROUP BY hmo_providers.name, hosp.name
      `,
    );
    const visitations: Array<{
      name: string;
      total: number;
      hospitalName: string;
    }> = await queryDSWithSlave(
      dataSource,
      `SELECT
        COUNT(*) AS total, "name", "hospitalName"
      FROM
        (
          SELECT
            DATE (pa.created_date) AS created_date,
            hmo_providers.name AS "name",
            hosp.name AS "hospitalName",
            pa.profile_id AS "ProfileId"
          FROM
            pre_authorizations pa
            INNER JOIN hospitals hosp ON pa.hospital_id = hosp.id
            INNER JOIN hmo_providers ON hmo_providers.id = pa.provider_id
          ${applyFilterForHmoAndPartnersOnHmoRecords({
            profile,
            filter,
            table: 'pa',
          })}
          GROUP BY DATE (pa.created_date), hmo_providers.name, "hospitalName", "ProfileId"
          UNION ALL
          SELECT
            DATE (hc.created_date) AS created_date,
            hmo_providers.name AS "name",
            hosp.name AS "hospitalName",
            hc.profile_id AS "ProfileId"
          FROM
            hmo_claims hc
            INNER JOIN hospitals hosp ON hc.hospital_id = hosp.id
            INNER JOIN hmo_providers ON hmo_providers.id = hc.provider_id
          ${applyFilterForHmoAndPartnersOnHmoRecords({
            profile,
            filter,
            table: 'hc',
          })}
          GROUP BY DATE (hc.created_date), hmo_providers.name, "hospitalName", "ProfileId"
        ) as sub
      GROUP BY "name", "hospitalName"
    `,
    );
    const referrals: Array<{
      name: string;
      total: number;
      hospitalName: string;
      totalReferralsAmount: string;
      totalPendingReferralsAmount: string;
      totalApprovedReferralsAmount: string;
      totalRejectedReferralsAmount: string;
      totalPendingReferrals: string;
      totalApprovedReferrals: string;
      totalRejectedReferrals: string;
    }> = await queryDSWithSlave(
      dataSource,
      `SELECT
        COUNT(DISTINCT pre_authorizations_referral.id) AS total,
        hmo_providers."name" AS "name",
        hospitals."name" AS "hospitalName",
        SUM(CAST(pre_auth_referral_utilisations.quantity AS numeric) * CAST(pre_auth_referral_utilisations.price AS numeric)) AS "totalReferralsAmount",
        SUM(CAST(pre_auth_referral_utilisations.quantity AS numeric) * CAST(pre_auth_referral_utilisations.price AS numeric)) FILTER (WHERE pre_auth_referral_utilisations.status ilike 'Pending') AS "totalPendingReferralsAmount",
        SUM(CAST(pre_auth_referral_utilisations.quantity AS numeric) * CAST(pre_auth_referral_utilisations.price AS numeric)) FILTER (WHERE pre_auth_referral_utilisations.status ilike 'Approved') AS "totalApprovedReferralsAmount",
        SUM(CAST(pre_auth_referral_utilisations.quantity AS numeric) * CAST(pre_auth_referral_utilisations.price AS numeric)) FILTER (WHERE pre_auth_referral_utilisations.status ilike 'Rejected') AS "totalRejectedReferralsAmount",
        COUNT(DISTINCT pre_authorizations_referral.id) FILTER (WHERE pre_auth_referral_utilisations.status ilike 'Pending') AS "totalPendingReferrals",
        COUNT(DISTINCT pre_authorizations_referral.id) FILTER (WHERE pre_auth_referral_utilisations.status ilike 'Approved') AS "totalApprovedReferrals",
        COUNT(DISTINCT pre_authorizations_referral.id) FILTER (WHERE pre_auth_referral_utilisations.status ilike 'Rejected') AS "totalRejectedReferrals"
      FROM
        pre_authorizations_referral
        INNER JOIN pre_auth_referral_utilisations ON pre_authorizations_referral.id = pre_auth_referral_utilisations.pre_auth_referral_id
        INNER JOIN hospitals ON pre_authorizations_referral.hospital_id = hospitals.id
        INNER JOIN hmo_providers ON pre_authorizations_referral.provider_id = hmo_providers.id
        ${applyFilterForHmoAndPartnersOnHmoRecords({
          profile,
          filter,
          table: 'pre_authorizations_referral',
        })}
      GROUP BY hmo_providers."name", hospitals."name"
    `,
    );

    let staffs = [];
    let automaticAndManualPreauthData: AutomaticAndManualHmoClaimsData;
    if (profile.hmoId) {
      staffs = await queryDSWithSlave(
        dataSource,
        `
    SELECT COUNT(profiles.id) AS "totalStaffCount", profiles.type 
        FROM profiles
            WHERE profiles.hmo_id = $1 AND profiles.deleted_date IS NULL
        GROUP BY profiles.type 
    `,
        [profile.hmoId],
      );

      automaticAndManualPreauthData = (
        await queryDSWithSlave(
          dataSource,
          `
        WITH categorized_preauths AS (
            SELECT
                pa.id AS "id",
                pa.status AS "status",
                CASE
                    WHEN p.hmo_id IS NOT NULL THEN p.type::text
                        ELSE NULL
                END AS "type",
                p.full_name::text AS "created_by_full_name",
                CASE
                    WHEN p.hmo_id IS NULL THEN 'automatic'
                    ELSE 'manual'
                END AS "preauth_type",
                CASE
                    WHEN p.hmo_id IS NOT NULL THEN NULL
                    ELSE array_agg(jsonb_extract_path_text(elem.value, 'vettingGroup')) FILTER (WHERE jsonb_extract_path_text(elem.value, 'vettingGroup') IS NOT NULL)
                END AS "vetting_groups",
                CASE
                    WHEN p.hmo_id IS NOT NULL THEN NULL
                    ELSE array_agg(jsonb_extract_path_text(elem.value, 'creatorName')) FILTER (WHERE jsonb_extract_path_text(elem.value, 'creatorName') IS NOT NULL)
                END AS "vetter_names"
            FROM
                pre_authorizations pa
            LEFT JOIN pre_auth_utilisations util ON pa.id = util.pre_auth
            LEFT JOIN LATERAL jsonb_array_elements(util.utilisation_status) AS elem(value) ON TRUE
            JOIN
                    profiles p ON pa.created_by = p.id
            WHERE pa.provider_id = $1
            GROUP BY pa.id, pa.status, p.type, p.full_name, p.hmo_id
        )
        SELECT
            (SELECT COUNT(DISTINCT id) FROM categorized_preauths WHERE preauth_type = 'automatic') AS "totalAutomated",
            (SELECT COUNT(DISTINCT id) FROM categorized_preauths WHERE preauth_type = 'manual') AS "totalManual",
            (
                SELECT json_agg(role_counts)
                FROM (
                    SELECT unnest(
                            CASE
                                WHEN categorized_preauths.type IS NOT NULL
                                    THEN array_append(vetting_groups, categorized_preauths.type)
                                ELSE vetting_groups
                            END
                           ) AS name,
                           COUNT(DISTINCT id) FILTER (WHERE categorized_preauths.preauth_type = 'manual') AS "manualCount",
                           COUNT(DISTINCT id) FILTER ( WHERE categorized_preauths.vetting_groups IS NOT NULL AND array_length(categorized_preauths.vetting_groups, 1) > 0 ) AS "count"
                    FROM categorized_preauths
                    GROUP BY
                        unnest(
                                CASE
                                    WHEN categorized_preauths.type IS NOT NULL
                                        THEN array_append(vetting_groups, categorized_preauths.type)
                                    ELSE vetting_groups
                                    END
                        )
                     ) role_counts
            ) AS "byRoles",
            (
                SELECT json_agg(staff_counts)
                FROM (
                    SELECT unnest(
                                   CASE
                                       WHEN categorized_preauths.type IS NOT NULL
                                           THEN array_append(vetter_names, categorized_preauths.created_by_full_name)
                                       ELSE vetter_names
                                   END
                           ) AS name,
                           unnest(
                                   CASE
                                       WHEN categorized_preauths.type IS NOT NULL
                                           THEN array_append(vetting_groups, categorized_preauths.type)
                                       ELSE vetting_groups
                                   END
                           ) AS type,
                           COUNT(DISTINCT id) FILTER (WHERE categorized_preauths.preauth_type = 'manual') AS "manualCount",
                           COUNT(DISTINCT id) FILTER (WHERE categorized_preauths.vetter_names IS NOT NULL AND array_length(categorized_preauths.vetter_names, 1) > 0) AS "count"
                    FROM categorized_preauths
                    GROUP BY
                        unnest(
                                CASE
                                    WHEN categorized_preauths.type IS NOT NULL
                                        THEN array_append(vetter_names, categorized_preauths.created_by_full_name)
                                    ELSE vetter_names
                                    END
                        ),
                        unnest(
                                CASE
                                    WHEN categorized_preauths.type IS NOT NULL
                                        THEN array_append(vetting_groups, categorized_preauths.type)
                                    ELSE vetting_groups
                                    END
                        )
                     ) staff_counts
            ) AS "byStaffs"
        `,
          [profile.hmoId],
        )
      )[0];
    }

    for (const item of [...enrolleeStats, ...clinifyEnrolleeStats]) {
      const index = data.findIndex(
        (d) =>
          d.hmoProvider === item.name && d.hospitalName === item.hospitalName,
      );
      if (index !== -1) {
        data[index].totalEnrollees = +item.total_enrollees;
        data[index].totalActiveEnrollees = +item.total_active_enrollees;
        data[index].totalInactiveEnrollees =
          +item.total_enrollees - +item.total_active_enrollees;
      } else {
        data.push({
          hmoProvider: item.name,
          totalPreauthorizations: 0,
          totalEnrollees: +item.total_enrollees,
          totalEnrolleeVisitations: 0,
          totalActiveEnrollees: +item.total_active_enrollees,
          totalInactiveEnrollees:
            +item.total_enrollees - +item.total_active_enrollees,
          totalApprovedPreauthorizations: 0,
          totalRejectedPreauthorizations: 0,
          totalPendingPreauthorizations: 0,
          totalPreauthorizationAmount: '0',
          totalApprovedPreauthorizationAmount: '0',
          totalRejectedPreauthorizationAmount: '0',
          totalPendingPreauthorizationAmount: '0',
          hospitalName: item.hospitalName,
        });
      }
    }
    for (const item of visitations) {
      const index = data.findIndex(
        (d) =>
          d.hmoProvider === item.name && d.hospitalName === item.hospitalName,
      );
      if (index !== -1) {
        data[index].totalEnrolleeVisitations = +item.total;
      } else {
        data.push({
          hospitalName: item.hospitalName,
          totalEnrolleeVisitations: +item.total,
        });
      }
    }
    for (const item of referrals) {
      const index = data.findIndex(
        (d) =>
          d.hmoProvider === item.name && d.hospitalName === item.hospitalName,
      );
      if (index !== -1) {
        data[index].totalReferrals = +item.total;
        data[index].totalReferralsAmount = +item.totalReferralsAmount;
        data[index].totalPendingReferrals = +item.totalPendingReferrals;
        data[index].totalApprovedReferrals = +item.totalApprovedReferrals;
        data[index].totalRejectedReferrals = +item.totalRejectedReferrals;
        data[index].totalPendingReferralsAmount =
          +item.totalPendingReferralsAmount;
        data[index].totalApprovedReferralsAmount =
          +item.totalApprovedReferralsAmount;
        data[index].totalRejectedReferralsAmount =
          +item.totalRejectedReferralsAmount;
      } else {
        data.push({
          hospitalName: item.hospitalName,
          totalReferrals: +item.total,
          totalReferralsAmount: +item.totalReferralsAmount,
          totalPendingReferrals: +item.totalPendingReferrals,
          totalApprovedReferrals: +item.totalApprovedReferrals,
          totalRejectedReferrals: +item.totalRejectedReferrals,
          totalPendingReferralsAmount: +item.totalPendingReferralsAmount,
          totalApprovedReferralsAmount: +item.totalApprovedReferralsAmount,
          totalRejectedReferralsAmount: +item.totalRejectedReferralsAmount,
        });
      }
    }

    return {
      groupedData: data,
      automaticAndManualData: automaticAndManualPreauthData,
      ...(staffs.length
        ? staffs.reduce((acc, curr) => {
            return {
              ...acc,
              [`total${curr.type}StaffCount`]: Number(curr.totalStaffCount),
            };
          }, {})
        : {}),
    };
  },

  async getHmoFinanceData(
    dataSource: DataSource,
    profile: ProfileModel,
    filter: HmosAnalyticsFilter,
  ): Promise<FinanceReportData[]> {
    const { startDate, endDate, orderBy } = filter;
    const { start, end } = formatDate(startDate, endDate);
    const hospitalId = !!profile.hmoId ? filter.hospitalId : profile.hospitalId;
    const isHmoOfficial = !!profile.hmoId;

    if (!isHmoOfficial) return [];

    const financeReport = await queryDSWithSlave(
      dataSource,
      `
        SELECT
          pre_auth.code AS "code",
          CASE
            WHEN hc.claim_date < DATE_TRUNC('month', CURRENT_DATE) THEN 'Submitted'
            ELSE 'Pending'
          END AS "batchStatus",
          hc.status AS "claimStatus",
          hospitals."name" AS "providerName",
          hmo_hospitals.hmo_provider_id AS "providerCode",
          hospitals.plan AS "providerType",
          hospitals.lga AS "providerRegion",
          hospitals.political_ward AS "providerSubregion",
          CASE
            WHEN hc.status ILIKE 'paid' THEN hc.paid_by
            ELSE ''
          END AS "payerName",
          hmo_profiles.company_name AS "employerName",
          hc.claim_id AS "claimId",
          profiles.full_name AS "enrolleeName",
          hmo_plan_types."name" AS "programPackage",
          hmo_profiles.plan_category AS "programCover",
          hmo_profiles.membership_number AS "membershipNumber",
          hmo_profiles.member_number AS "memberNumber",
          details.date_of_birth AS "dateOfBirth",
          EXTRACT(YEAR FROM AGE(details.date_of_birth)) AS "enrolleeAge",
          profiles.gender AS "gender",
          hc.claim_date AS "treatmentDate",
          util_status.approved_date AS "claimFinalizedDate",
          util_status.approved_by AS "claimFinalizedBy",
          pre_auth.description AS "itemDescription",
          pre_auth.price AS "tariffAmount",
          users.phone_number AS "phoneNumber",
          pre_auth.quantity AS "treatmentQuantity",
          pre_auth.total_treatment_amount AS "totalTreatmentAmount",
          hc.diagnosis AS "diagnosis",
          hc.claim_date AS "visitDate",
          hc.presenting_complain AS "doctorNote",
          util_status.reason_for_rejection AS "rejectionReason",
          util_status.comments AS "overridingComments",
          util_status.operator AS "operator"
        FROM hmo_claims hc
        INNER JOIN LATERAL (
          SELECT
            STRING_AGG(util.pa_code, ', ') AS "code",
            STRING_AGG((util.category || ' - ' || util."type"), ', ') AS "description",
            SUM(COALESCE(NULLIF(util.quantity, '0')::DECIMAL, 0)) AS "quantity",
            SUM(COALESCE(NULLIF(util.price, '0')::DECIMAL, 0)) AS "price",
            SUM(
                COALESCE(
                    util.amount_covered::DECIMAL,
                    COALESCE(NULLIF(util.quantity, '0')::DECIMAL, 0) * COALESCE(NULLIF(util.price, '0')::DECIMAL, 0)
                )
            ) AS "total_treatment_amount"
          FROM pre_auth_utilisations util
          WHERE util.hmo_claim IS NOT NULL AND util.hmo_claim = hc.id
        ) pre_auth ON TRUE
        INNER JOIN LATERAL (
          SELECT
            STRING_AGG(
                DISTINCT CASE 
                  WHEN status_element ->> 'vettingGroup' = 'ClaimOfficerHOD'
                    AND status_element->>'status' = 'Approved'
                  THEN status_element ->> 'creatorName' 
                END, 
                ', '
              ) AS "approved_by",
            STRING_AGG(
                DISTINCT CASE 
                  WHEN status_element ->> 'vettingGroup' = 'ClaimOfficerHOD'
                    AND status_element->>'status' = 'Approved'
                  THEN status_element ->> 'createdDate' 
                END, 
                ', '
              ) AS "approved_date",
            STRING_AGG(
                DISTINCT CASE 
                  WHEN status_element->>'status' = 'Rejected' THEN status_element ->> 'statusDescription' 
                END, 
                ', '
              ) AS "reason_for_rejection",
            STRING_AGG(status_element ->> 'comment', ', ') AS "comments",
            STRING_AGG(
                DISTINCT CASE 
                  WHEN status_element ->> 'vettingGroup' = 'ClaimOfficer'
                  THEN status_element ->> 'creatorName' 
                END, 
                ', '
              ) AS "operator"
          FROM pre_auth_utilisations util
          LEFT JOIN LATERAL jsonb_array_elements(util.utilisation_status) status_element ON TRUE
          WHERE util.hmo_claim IS NOT NULL AND util.hmo_claim = hc.id
        ) util_status ON TRUE
        INNER JOIN profiles ON hc.profile_id = profiles.id
        INNER JOIN details ON profiles.details = details.id
        INNER JOIN users ON profiles."user" = users.id
        INNER JOIN hmo_profiles ON hmo_profiles.profile_id = profiles.id AND hmo_profiles.provider_id = '${
          profile.hmoId
        }'
        INNER JOIN hmo_plan_types ON hmo_profiles.member_plan_id::TEXT = hmo_plan_types.id::TEXT
        INNER JOIN hospitals ON hc.hospital_id = hospitals.id
        INNER JOIN hmo_hospitals ON hmo_hospitals.hospital_id = hospitals.id  AND hmo_hospitals.provider_id = '${
          profile.hmoId
        }'
        WHERE hc.provider_id = '${profile.hmoId}'
        AND LOWER(hc.status) <> 'draft'
        AND date(hc.claim_date) BETWEEN '${start}' AND '${end}'
        ${hospitalId ? `AND hc.hospital_id = '${hospitalId}'` : ''}
        ${
          !orderBy || orderBy?.toLowerCase() === 'all'
            ? ''
            : `AND hc.hospital_id = '${orderBy}'`
        }
        ORDER BY hc.claim_date DESC
      `,
    );

    return financeReport.map((item) => {
      const diagnosisICD10 = item.diagnosis.map(
        (_item) => _item.diagnosisICD10,
      );
      const diagnosisICD11 = item.diagnosis.map(
        (_item) => _item.diagnosisICD11,
      );
      const diagnosisSNOMED = item.diagnosis.map(
        (_item) => _item.diagnosisSNOMED,
      );

      const diagnosis = [
        ...diagnosisICD10,
        ...diagnosisICD11,
        ...diagnosisSNOMED,
      ]?.filter(Boolean);

      return {
        ...item,
        diagnosis: diagnosis?.length ? diagnosis.join(', ') : '',
        treatmentDate: item.treatmentDate
          ? moment(item.treatmentDate).format('DD-MMM-YYYY HH:mm A')
          : null,
        visitDate: item.visitDate
          ? moment(item.visitDate).format('DD-MMM-YYYY HH:mm A')
          : null,
        dateOfBirth: item.dateOfBirth
          ? moment(item.dateOfBirth).format('DD-MMM-YYYY')
          : null,
        claimFinalizedDate: item.claimFinalizedDate
          ? moment(item.claimFinalizedDate).format('DD-MMM-YYYY HH:mm A')
          : null,
      };
    });
  },
  async getHmoMedicationData(
    dataSource: DataSource,
    profile: ProfileModel,
    filter: HmosAnalyticsFilter,
  ): Promise<MedicationReportData[]> {
    const { startDate, endDate, orderBy } = filter;
    const { start, end } = formatDate(startDate, endDate);
    const hospitalId = !!profile.hmoId ? filter.hospitalId : profile.hospitalId;
    const isHmoOfficial = !!profile.hmoId;

    if (!isHmoOfficial) return [];

    const medicationReport = await queryDSWithSlave(
      dataSource,
      `
        SELECT
          pres_hsp."name" AS "prescribingHospital",
          desp_hsp."name" AS "dispensingPharmacy",
          hmo_profiles.member_number AS "enrolleeID",
          profiles.full_name AS "enrolleeName",
          medication_details.date_prescribed AS "datePrescribed",
          medication_details.medication_name AS "medicationName",
          medication_details.category AS "medicationCategory",
          bill_details.quantity AS "medicationQuantity",
          bill_details.unit_price AS "unitPrice",
          COALESCE(NULLIF(bill_details.quantity, '0')::DECIMAL, 0) * COALESCE(NULLIF(bill_details.unit_price, '0')::DECIMAL, 0) AS "totalAmount",
          medication_details.dosage AS "dosage",
          medication_details.dosage_unit AS "dosageUnit",
          medication_details.frequency AS "frequency",
          medication_details.duration AS "duration",
          medication_details.prescription_note AS "prescriptionNote",
          dispense_details.dispense_date AS "dispenseDate",
          CASE
            WHEN dispense_details.id IS NOT NULL
              THEN 'Dispensed'
            WHEN medications.verification_code IS NOT NULL AND EXTRACT(EPOCH FROM (NOW() - medication_details.date_prescribed)) > 48 * 3600
              THEN 'Elapsed'
            ELSE 'Prescriped'
          END AS "status"
        FROM medication_details
        INNER JOIN medications ON medication_details.medication = medications.id
        INNER JOIN profiles ON medications.profile = profiles.id
        INNER JOIN hmo_profiles ON hmo_profiles.profile_id = profiles.id AND hmo_profiles.provider_id = '${
          profile.hmoId
        }'
        LEFT JOIN dispense_details ON dispense_details.medication_detail = medication_details.id
        INNER JOIN hospitals pres_hsp ON medication_details.hospital_id::TEXT = pres_hsp.id::TEXT
        LEFT JOIN hospitals desp_hsp ON dispense_details.hospital_id::TEXT = desp_hsp.id::TEXT
        LEFT JOIN bill_details ON dispense_details.billing = bill_details.id
        WHERE medication_details."option" = 'M'
        AND date(medication_details.date_prescribed) BETWEEN '${start}' AND '${end}'
        ${
          hospitalId
            ? `AND medication_details.hospital_id = '${hospitalId}'`
            : ''
        }
        ${
          !orderBy || orderBy?.toLowerCase() === 'all'
            ? ''
            : `AND medication_details.hospital_id = '${orderBy}'`
        }
        ORDER BY medication_details.date_prescribed DESC
      `,
    );

    return medicationReport.map((item) => ({
      ...item,
      datePrescribed: item.datePrescribed
        ? moment(item.datePrescribed).format('DD-MMM-YYYY HH:mm A')
        : null,
      dispenseDate: item.dispenseDate
        ? moment(item.dispenseDate).format('DD-MMM-YYYY')
        : null,
      dispenseTime: item.dispenseDate
        ? moment(item.dispenseDate).format('HH:mm A')
        : null,
    }));
  },
  async getHmoOperationData(
    dataSource: DataSource,
    profile: ProfileModel,
    filter: HmosAnalyticsFilter,
  ): Promise<OperationReportData> {
    const { startDate, endDate } = filter;
    const { start, end } = formatDate(startDate, endDate);
    const isHmoOfficial = !!profile.hmoId;

    if (!isHmoOfficial) return {};

    const providerReports: ProviderReportData[] = await dataSource.query(`
      WITH
        claim_totals AS (
          SELECT
            pau.hmo_claim,
            SUM(
              CAST(COALESCE(
                pau.amount_covered,
                CAST(COALESCE(NULLIF(pau.quantity, ''), '0') AS DECIMAL) * pau.price::DECIMAL) AS DECIMAL
              )
            ) AS claim_amount,
            SUM(
               CASE WHEN EXISTS(SELECT
                  1 FROM jsonb_array_elements(pau.utilisation_status) util
                    WHERE util->>'status' = 'Rejected')
                THEN 0
                ELSE CAST(COALESCE(
                  pau.amount_covered,
                  CAST(COALESCE(NULLIF(pau.quantity, ''), '0') AS DECIMAL) * pau.price::DECIMAL) AS DECIMAL
                )
              END
            ) AS amount_approved
          FROM
            pre_auth_utilisations pau
          WHERE
            pau.hmo_provider_id = '${profile.hmoId}'
            AND pau.hmo_claim IS NOT NULL
          GROUP BY
            pau.hmo_claim
        )
      SELECT
        h.name AS "providerName",
        COUNT(*) AS "numberOfClaims",
        SUM(ct.claim_amount) AS "totalSubmittedAmount",
        SUM(
          CASE
            WHEN hc.status ILIKE 'paid' THEN ct.amount_approved
            ELSE 0
          END
        ) AS "totalPaidAmount"
      FROM
        hmo_claims hc
        INNER JOIN hospitals h ON hc.hospital_id = h.id
        LEFT JOIN claim_totals ct ON ct.hmo_claim = hc.id
      WHERE
        hc.provider_id = '${profile.hmoId}'
        AND LOWER(hc.status) <> 'draft'
        AND date(hc.claim_date) BETWEEN '${start}' AND '${end}'
      GROUP BY h.name
      ORDER BY "numberOfClaims" DESC
    `);

    const topProceduresByCost: TopProceduresByCostReportData[] =
      await dataSource.query(`
      WITH
        claim_totals AS (
          SELECT
            pau.hmo_claim,
            SUM(
              CASE
                WHEN EXISTS (
                  SELECT
                    1
                  FROM
                    jsonb_array_elements(pau.utilisation_status) util
                  WHERE
                    util ->> 'status' = 'Rejected'
                ) THEN 0
                ELSE COALESCE(
                  pau.amount_covered,
                  pau.price::decimal * pau.quantity::decimal
                )
              END
            ) AS claim_amount,
            pau.type
          FROM
            pre_auth_utilisations pau
          WHERE
            pau.hmo_provider_id = '${profile.hmoId}'
            AND pau.hmo_claim IS NOT NULL
            AND (pau.category ILIKE 'procedure' OR pau.category ILIKE 'surgery')
          GROUP BY
            pau.hmo_claim,
            pau.type
        )
      SELECT
        ct.type AS "procedureName",
        COUNT(*) AS "numberOfClaims",
        SUM(ct.claim_amount) AS "totalSubmittedAmount"
      FROM
        hmo_claims hc
        LEFT JOIN claim_totals ct ON ct.hmo_claim = hc.id
      WHERE
        hc.provider_id = '${profile.hmoId}'
        AND LOWER(hc.status) <> 'draft'
        AND date(hc.claim_date) BETWEEN '${start}' AND '${end}'
      GROUP BY
        ct.type
      ORDER BY
        "totalSubmittedAmount" DESC
      LIMIT
        10
  `);
    const topDiagnosis = await dataSource.query(`
      WITH
        claim_totals AS (
          SELECT
            pau.hmo_claim,
            SUM(
              CASE
                WHEN EXISTS (
                  SELECT
                    1
                  FROM
                    jsonb_array_elements(pau.utilisation_status) util
                  WHERE
                    util ->> 'status' = 'Rejected'
                ) THEN 0
                ELSE COALESCE(
                  pau.amount_covered,
                  pau.price::decimal * pau.quantity::decimal
                )
              END
            ) AS claim_amount
          FROM
            pre_auth_utilisations pau
          WHERE
            pau.hmo_provider_id = '${profile.hmoId}'
            AND pau.hmo_claim IS NOT NULL
          GROUP BY
            pau.hmo_claim
        ),
        diagnosis_expanded AS (
          SELECT
            hc.id AS claim_id,
            diag ->> 'diagnosisICD10' AS diagnosis_icd10,
            diag ->> 'diagnosisICD11' AS diagnosis_icd11,
            diag ->> 'diagnosisSNOMED' AS diagnosis_snomed,
            ct.claim_amount
          FROM
            hmo_claims hc
            LEFT JOIN claim_totals ct ON ct.hmo_claim = hc.id
            CROSS JOIN jsonb_array_elements(hc.diagnosis) diag
          WHERE
            hc.provider_id = '${profile.hmoId}'
            AND LOWER(hc.status) <> 'draft'
           AND date(hc.claim_date) BETWEEN '${start}' AND '${end}'
        )
      SELECT
        COALESCE(
          diagnosis_icd10,
          diagnosis_icd11,
          diagnosis_snomed
        ) AS "diagnosis",
        COUNT(*) AS "numberOfClaims",
        SUM(claim_amount) AS "totalSubmittedAmount"
      FROM
        diagnosis_expanded
      WHERE
        COALESCE(
          diagnosis_icd10,
          diagnosis_icd11,
          diagnosis_snomed
        ) IS NOT NULL
      GROUP BY
        COALESCE(
          diagnosis_icd10,
          diagnosis_icd11,
          diagnosis_snomed
        )
      ORDER BY
        "numberOfClaims" DESC
      LIMIT
        10
    `);
    const topFeeForServiceProviders = await dataSource.query(`
      WITH
        benefit_categories AS (
          SELECT
            hpb.id,
            ut.code,ut.category
          FROM
            hmo_plan_benefits hpb,
            LATERAL (
              SELECT
                jsonb_array_elements(hpb.utilisation_types) ->> 'benefitCategory' AS category,
              jsonb_array_elements(hpb.utilisation_types) ->> 'code' AS code
            ) ut
          WHERE
            ut.category = 'FeeForService'
            AND hpb.hmo_provider_id ='${profile.hmoId}'
        ),
        claim_totals AS (
          SELECT
            pau.hmo_claim,
            SUM(
              CASE
                WHEN EXISTS (
                  SELECT
                    1
                  FROM
                    jsonb_array_elements(pau.utilisation_status) util
                  WHERE
                    util ->> 'status' = 'Rejected'
                ) THEN 0
                ELSE COALESCE(
                  pau.amount_covered,
                  pau.price::decimal * pau.quantity::decimal
                )
              END
            ) AS claim_amount
          FROM
            pre_auth_utilisations pau
          INNER JOIN benefit_categories bc ON bc.id = pau."utilizationId"::uuid AND pau."utilizationCode" = bc.code
          WHERE
            pau.hmo_provider_id = '${profile.hmoId}'
            AND pau.hmo_claim IS NOT NULL
          GROUP BY
            pau.hmo_claim
        )
      SELECT
        COUNT(*) AS "numberOfClaims",
        SUM(ct.claim_amount) AS "totalSubmittedAmount",
        h.name AS "providerName"
      FROM
        hmo_claims hc
        INNER JOIN hospitals h ON hc.hospital_id = h.id
        LEFT JOIN claim_totals ct ON ct.hmo_claim = hc.id
      WHERE
        hc.provider_id = '${profile.hmoId}'
        AND LOWER(hc.status) <> 'draft'
          AND date(hc.claim_date) BETWEEN '${start}' AND '${end}'
      GROUP BY
        h.name
      ORDER BY
        "totalSubmittedAmount" DESC
      LIMIT
        10
    `);
    const claimVettedData = await dataSource.query(`
      WITH
        claim_totals AS (
          SELECT
            pau.hmo_claim,
            SUM(
              COALESCE(
                pau.amount_covered,
                pau.price::decimal * pau.quantity::decimal
              )
            ) AS claim_amount,
            SUM(
              CASE WHEN EXISTS(SELECT
                1 FROM jsonb_array_elements(pau.utilisation_status) util
                  WHERE util->>'status' = 'Rejected')
              THEN 0
              ELSE CAST(COALESCE(
                pau.amount_covered,
                CAST(COALESCE(NULLIF(pau.quantity, ''), '0') AS DECIMAL) * pau.price::DECIMAL) AS DECIMAL
              )
            END
          ) AS amount_approved
          FROM
            pre_auth_utilisations pau
          WHERE
            pau.hmo_provider_id = '${profile.hmoId}'
            AND pau.hmo_claim IS NOT NULL
            AND pau.utilisation_status IS NOT NULL
          GROUP BY
            pau.hmo_claim
        )
      SELECT
        h.name AS "providerName",
        COUNT(*) AS "numberOfClaims",
        SUM(ct.claim_amount) AS "totalSubmittedAmount",
        SUM(
          CASE
            WHEN hc.status ILIKE 'paid' THEN ct.amount_approved
            ELSE 0
          END
        ) AS "totalPaidAmount"
      FROM
        hmo_claims hc
        INNER JOIN hospitals h ON hc.hospital_id = h.id
        INNER JOIN claim_totals ct ON ct.hmo_claim = hc.id
      WHERE
        hc.provider_id = '${profile.hmoId}'
        AND LOWER(hc.status) <> 'draft'
          AND date(hc.claim_date) BETWEEN '${start}' AND '${end}'
      GROUP BY
        h.name;
    `);
    const totalProvidersSubmittedClaims = [
      ...new Set([...(providerReports || []).map((data) => data.providerName)]),
    ].length;
    const summary = providerReports.reduce(
      (acc, report) => {
        acc.totalSubmittedClaimsAmount += Number(report.totalSubmittedAmount);
        acc.totalPaidClaimsAmount += Number(report.totalPaidAmount);
        acc.totalNumberOfClaims += Number(report.numberOfClaims);
        return acc;
      },
      {
        totalSubmittedClaimsAmount: 0,
        totalPaidClaimsAmount: 0,
        totalNumberOfClaims: 0,
      },
    );
    return {
      totalProvidersSubmittedClaims,
      totalNumberOfClaims: summary.totalNumberOfClaims || 0,
      totalSubmittedClaimsAmount: summary.totalSubmittedClaimsAmount || 0,
      totalPaidClaimsAmount: summary.totalPaidClaimsAmount || 0,
      providerReports,
      topProceduresByCost,
      topDiagnosis,
      topFeeForServiceProviders,
      claimVettedData,
    };
  },
  async getActuarialData(
    dataSource: DataSource,
    profile: ProfileModel,
    filter: HmosAnalyticsFilter,
  ): Promise<ActuarialReportResponse> {
    const { startDate, endDate, orderBy } = filter;
    const { start, end } = formatDate(startDate, endDate);
    const hospitalId = !!profile.hmoId ? filter.hospitalId : profile.hospitalId;
    const isHmoOfficial = !!profile.hmoId;

    if (!isHmoOfficial) return {};

    const membershipData = await queryDSWithSlave(
      dataSource,
      `
        SELECT
          hp.member_number AS "enrolleeId",
          profiles.gender AS "gender",
          hp.plan_category AS "planCategory",
          hpt."name" AS "planName",
          details.date_of_birth AS "dateOfBirth",
          hp.payment_date_time AS "paymentDate",
          hp.member_start_date AS "planStartDate",
          hp.member_due_date AS "planEndDate",
          hp.member_status AS "memberStatus",
          details.ward AS "enrolleeWard",
          hsp."name" AS "providerName",
          hsp.lga AS "providerLga",
          hp.member_plan_group AS "groupName",
          hp.member_plan_sub_group AS "subGroupName",
          hp.company_name AS "companyName",
          details.lga AS "enrolleeLga",
          hpt.premium_country AS "planPremiumFlag",
          hp.premium_collected AS "planPremiumAmount",
          hp.payment_frequency AS "paymentFrequency",
          hp.premium_collected AS "premiumCollected",
          hp.premium_outstanding AS "premiumOutstanding",
          hp.termination_date AS "terminationDate",
          CASE
            WHEN dependents.id IS NOT NULL THEN dependents.relationship
            WHEN hpp.id IS NOT NULL AND hpp.profile_id = profiles.id THEN 'Main Member'
            ELSE NULL
          END AS "beneficiaryDescription"
        FROM hmo_profiles hp
        INNER JOIN profiles ON hp.profile_id = profiles.id
        INNER JOIN details ON profiles.details = details.id
        LEFT JOIN hospitals hsp ON hp.primary_provider_id = hsp.id
        LEFT JOIN hmo_plan_types hpt ON hpt.id::TEXT = hp.member_plan_id
        LEFT JOIN dependents ON dependents.hmo_profile_id = hp.id
        LEFT JOIN LATERAL (
          SELECT id, membership_number, profile_id FROM hmo_profile_plan
          WHERE membership_number = hp.membership_number
          ORDER BY created_date DESC
          LIMIT 1
        ) hpp ON TRUE
        WHERE hp.provider_id = '${profile.hmoId}'
        AND date(hp.created_date) BETWEEN '${start}' AND '${end}'
        ${hospitalId ? `AND hp.primary_provider_id = '${hospitalId}'` : ''}
        ${
          !orderBy || orderBy?.toLowerCase() === 'all'
            ? ''
            : `AND hp.primary_provider_id = '${orderBy}'`
        }
        ORDER BY hp.created_date DESC
      `,
    );

    const summaryClaimsData = await queryDSWithSlave(
      dataSource,
      `
          WITH
            claim_totals AS (
              SELECT
                pau.id,
                SUM(
                  CASE WHEN EXISTS(SELECT
                      1 FROM jsonb_array_elements(pau.utilisation_status) util
                        WHERE util->>'status' = 'Rejected')
                    THEN 0
                    ELSE CAST(COALESCE(
                      pau.amount_covered,
                      CAST(COALESCE(NULLIF(pau.quantity, ''), '0') AS DECIMAL) * pau.price::DECIMAL) AS DECIMAL
                    )
                  END
                ) AS amount_approved
              FROM
                pre_auth_utilisations pau
              WHERE
                pau.hmo_provider_id = '${profile.hmoId}'
                AND pau.hmo_claim IS NOT NULL
              GROUP BY
                pau.id
            )
          SELECT
            hc."enrolleeNumber" AS "enrolleeId",
            hhp.hmo_provider_id AS "providerCode",
            hosp."name" AS "providerName",
            hosp.lga AS "providerLga",
            details.date_of_birth AS "dateOfBirth",
            hp.member_plan_group AS "groupName",
            hp.member_plan_sub_group AS "subGroupName",
            hp.company_name AS "companyName",
            hpt."name" AS "planName",
            hp.plan_category AS "planCategory",
            profiles.gender AS "gender",
            hc.treatment_start_date AS "treatmentStartDate",
            hc.treatment_end_date AS "treatmentEndDate",
            hc.service_type AS "treatmentType",
            hc.claim_id AS "claimId",
            hc.diagnosis AS "diagnosis",
            hc."total_tariffFee" AS "amountSubmitted",
            hosp.ownership AS "providerOwnership",
            hosp."level" AS "providerLevel",
            MAX(hc.treatment_end_date::date - hc.treatment_start_date::date) FILTER (WHERE util.category ILIKE 'admission') AS "admissionDays",
            STRING_AGG(util."utilizationCode", ', ') FILTER (WHERE util.category ILIKE 'procedure' OR util.category ILIKE 'surgery') AS "procedureCodes",
            STRING_AGG(util."utilizationCode", ', ') FILTER (WHERE util.category ILIKE 'drug') AS "drugCodes",
            STRING_AGG(DISTINCT util.category, ', ') AS "utilizationCategories",
            STRING_AGG(DISTINCT util.type, ', ') AS "utilizationTypes",
            STRING_AGG(DISTINCT util.payment_model, ', ') AS "paymentModels",
            SUM(${calculateAmountSubmitted}) AS "tariffCharge",
            SUM(${calculateAmount}) AS "amountCharged",
            CASE
              WHEN hc.status ILIKE 'paid' THEN SUM(ct.amount_approved)
            END AS "amountPaid",
            CASE WHEN hc.status ILIKE 'paid'
              THEN hc.updated_date
              ELSE NULL
            END AS "paidDate"
          FROM
            hmo_claims hc
            INNER JOIN pre_auth_utilisations util ON util.hmo_claim = hc.id
            INNER JOIN profiles ON hc.profile_id = profiles.id
            INNER JOIN details ON profiles.details = details.id
            INNER JOIN hmo_profiles hp ON hp.member_number = hc."enrolleeNumber"
            INNER JOIN hospitals hosp ON hc.hospital_id = hosp.id
            LEFT JOIN claim_totals ct ON ct.id = util.id
            LEFT JOIN hmo_hospitals hhp ON hhp.hospital_id = hosp.id AND hhp.provider_id = '${
              profile.hmoId
            }'
            LEFT JOIN hmo_plan_types hpt ON hpt.id::TEXT = hp.member_plan_id
          WHERE
            hc.provider_id = '${profile.hmoId}'
            AND hc.status <> 'draft'
            AND date(hc.created_date) BETWEEN '${start}' AND '${end}'
            ${hospitalId ? `AND hc.hospital_id = '${hospitalId}'` : ''}
            ${
              !orderBy || orderBy?.toLowerCase() === 'all'
                ? ''
                : `AND hc.hospital_id = '${orderBy}'`
            }
          GROUP BY
            hc.id,
            hhp.hmo_provider_id,
            hosp.name,
            hosp.lga,
            details.date_of_birth,
            hp.member_plan_group,
            hp.member_plan_sub_group,
            hp.company_name,
            hpt.name,
            hp.plan_category,
            profiles.gender,
            hosp.ownership,
            hosp.level
          ORDER BY hc.created_date DESC
        `,
    );

    const proceduresClaimsData = await queryDSWithSlave(
      dataSource,
      `
        SELECT
          hc."enrolleeNumber" AS "enrolleeId",
          hhp.hmo_provider_id AS "providerCode",
          hosp."name" AS "providerName",
          hosp.lga AS "providerLga",
          details.date_of_birth AS "dateOfBirth",
          hp.member_plan_group AS "groupName",
          hp.member_plan_sub_group AS "subGroupName",
          hp.company_name AS "companyName",
          hpt."name" AS "planName",
          hp.plan_category AS "planCategory",
          profiles.gender AS "gender",
          hc.treatment_start_date AS "treatmentStartDate",
          hc.treatment_end_date AS "treatmentEndDate",
          hc.service_type AS "treatmentType",
          hc.claim_id AS "claimId",
          hc.diagnosis AS "diagnosis",
          hc."total_tariffFee" AS "amountSubmitted",
          hosp.ownership AS "providerOwnership",
          hosp."level" AS "providerLevel",
          util."type" AS "procedureNames",
          util."utilizationCode" AS "procedureCodes",
          util.category AS "utilizationCategories",
          (SELECT (hmo_claims.treatment_end_date::date - hmo_claims.treatment_start_date::date)
              FROM pre_auth_utilisations util_sub
              INNER JOIN hmo_claims ON util_sub.hmo_claim = hmo_claims.id
              WHERE util_sub.hmo_claim = util.hmo_claim
              AND util_sub.category ILIKE 'admission') AS "admissionDays",
          util.payment_model AS "paymentModels",
          ${calculateAmountSubmitted} AS "tariffCharge",
          ${calculateAmount} AS "amountCharged",
          CASE WHEN hc.status ILIKE 'paid'
            THEN
              CASE WHEN EXISTS(SELECT
                1 FROM jsonb_array_elements(util.utilisation_status) util
                  WHERE util->>'status' = 'Rejected')
                THEN 0
                ELSE ${calculateAmount}
              END
            ELSE NULL
          END AS "amountPaid",
          CASE WHEN hc.status ILIKE 'paid'
            THEN hc.updated_date
            ELSE NULL
          END AS "paidDate"
        FROM
          hmo_claims hc
          INNER JOIN pre_auth_utilisations util ON util.hmo_claim = hc.id
          INNER JOIN profiles ON hc.profile_id = profiles.id
          INNER JOIN details ON profiles.details = details.id
          INNER JOIN hmo_profiles hp ON hp.member_number = hc."enrolleeNumber"
          INNER JOIN hospitals hosp ON hc.hospital_id = hosp.id
          LEFT JOIN hmo_hospitals hhp ON hhp.hospital_id = hosp.id AND hhp.provider_id = '${
            profile.hmoId
          }'
          LEFT JOIN hmo_plan_types hpt ON hpt.id::TEXT = hp.member_plan_id
        WHERE
          hc.provider_id = '${profile.hmoId}'
          AND hc.status <> 'draft'
          AND (util.category ILIKE 'procedure' OR util.category ILIKE 'surgery')
          AND date(hc.created_date) BETWEEN '${start}' AND '${end}'
          ${hospitalId ? `AND hc.hospital_id = '${hospitalId}'` : ''}
          ${
            !orderBy || orderBy?.toLowerCase() === 'all'
              ? ''
              : `AND hc.hospital_id = '${orderBy}'`
          }
        ORDER BY hc.created_date DESC
      `,
    );

    const drugsClaimsData = await queryDSWithSlave(
      dataSource,
      `
        SELECT
          hc."enrolleeNumber" AS "enrolleeId",
          hhp.hmo_provider_id AS "providerCode",
          hosp."name" AS "providerName",
          hosp.lga AS "providerLga",
          details.date_of_birth AS "dateOfBirth",
          hp.member_plan_group AS "groupName",
          hp.member_plan_sub_group AS "subGroupName",
          hp.company_name AS "companyName",
          hpt."name" AS "planName",
          hp.plan_category AS "planCategory",
          profiles.gender AS "gender",
          hc.treatment_start_date AS "treatmentStartDate",
          hc.treatment_end_date AS "treatmentEndDate",
          hc.service_type AS "treatmentType",
          hc.claim_id AS "claimId",
          hc.diagnosis AS "diagnosis",
          hc."total_tariffFee" AS "amountSubmitted",
          hosp.ownership AS "providerOwnership",
          hosp."level" AS "providerLevel",
          util."type" AS "drugNames",
          util."utilizationCode" AS "drugCodes",
          util.category AS "utilizationCategories",
          (SELECT (hmo_claims.treatment_end_date::date - hmo_claims.treatment_start_date::date)
              FROM pre_auth_utilisations util_sub
              INNER JOIN hmo_claims ON util_sub.hmo_claim = hmo_claims.id
              WHERE util_sub.hmo_claim = util.hmo_claim
              AND util_sub.category ILIKE 'admission') AS "admissionDays",
          util.payment_model AS "paymentModels",
          ${calculateAmountSubmitted} AS "tariffCharge",
          ${calculateAmount} AS "amountCharged",
          CASE WHEN hc.status ILIKE 'paid'
            THEN
              CASE WHEN EXISTS(SELECT
                1 FROM jsonb_array_elements(util.utilisation_status) util
                  WHERE util->>'status' = 'Rejected')
                THEN 0
                ELSE ${calculateAmount}
              END
            ELSE NULL
          END AS "amountPaid",
          CASE WHEN hc.status ILIKE 'paid'
            THEN hc.updated_date
            ELSE NULL
          END AS "paidDate"
        FROM
          hmo_claims hc
          INNER JOIN pre_auth_utilisations util ON util.hmo_claim = hc.id
          INNER JOIN profiles ON hc.profile_id = profiles.id
          INNER JOIN details ON profiles.details = details.id
          INNER JOIN hmo_profiles hp ON hp.member_number = hc."enrolleeNumber"
          INNER JOIN hospitals hosp ON hc.hospital_id = hosp.id
          LEFT JOIN hmo_hospitals hhp ON hhp.hospital_id = hosp.id AND hhp.provider_id = '${
            profile.hmoId
          }'
          LEFT JOIN hmo_plan_types hpt ON hpt.id::TEXT = hp.member_plan_id
        WHERE
          hc.provider_id = '${profile.hmoId}'
          AND hc.status <> 'draft'
          AND util.category ILIKE 'drug'
          AND date(hc.created_date) BETWEEN '${start}' AND '${end}'
          ${hospitalId ? `AND hc.hospital_id = '${hospitalId}'` : ''}
          ${
            !orderBy || orderBy?.toLowerCase() === 'all'
              ? ''
              : `AND hc.hospital_id = '${orderBy}'`
          }
        ORDER BY hc.created_date DESC
      `,
    );

    const capitationData = await queryDSWithSlave(
      dataSource,
      `
      SELECT
        profiles.full_name AS "enrolleeName",
        hmo_profiles.member_number AS "enrolleeId",
        hospitals."name" AS "providerName",
        hmo_hospitals.hmo_provider_id AS "providerCode",
        facility_preferences.enrollee_capitation_amount AS "capitationAmount"
      FROM hmo_profiles
      INNER JOIN profiles ON hmo_profiles.profile_id = profiles.id
      LEFT JOIN hospitals ON hmo_profiles.primary_provider_id = hospitals.id
      LEFT JOIN hmo_hospitals ON hmo_hospitals.hospital_id = hospitals.id
        AND hmo_hospitals.provider_id = '${profile.hmoId}'
      LEFT JOIN facility_preferences ON facility_preferences.hospital_id = '${
        profile.hospitalId
      }'
      WHERE hmo_profiles.provider_id = '${profile.hmoId}'
        AND LOWER(member_status) = 'active'
        AND date(hmo_profiles.created_date) BETWEEN '${start}' AND '${end}'
        ${
          hospitalId
            ? `AND hmo_profiles.primary_provider_id = '${hospitalId}'`
            : ''
        }
        ${
          !orderBy || orderBy?.toLowerCase() === 'all'
            ? ''
            : `AND hmo_profiles.primary_provider_id = '${orderBy}'`
        }
      ORDER BY hmo_profiles.created_date DESC
    `,
    );

    return {
      membershipData: membershipData.map((item) => ({
        ...item,
        dateOfBirth: item.dateOfBirth
          ? moment(item.dateOfBirth).format('DD-MMM-YYYY')
          : null,
        paymentDate: item.paymentDate
          ? moment(item.paymentDate).format('DD-MMM-YYYY')
          : null,
        planStartDate: item.planStartDate
          ? moment(item.planStartDate).format('DD-MMM-YYYY')
          : null,
        planEndDate: item.planEndDate
          ? moment(item.planEndDate).format('DD-MMM-YYYY')
          : null,
        terminationDate: item.terminationDate
          ? moment(item.terminationDate).format('DD-MMM-YYYY')
          : null,
      })),
      summaryClaimsData: summaryClaimsData.map((item) => {
        const { diagnosisCodes, diagnosisNames } = seperateDiagnosisCode(
          item?.diagnosis || [],
        );

        return {
          ...item,
          diagnosisCodes,
          diagnosisNames,
          dateOfBirth: item.dateOfBirth
            ? moment(item.dateOfBirth).format('DD-MMM-YYYY')
            : null,
          treatmentStartDate: item.treatmentStartDate
            ? moment(item.treatmentStartDate).format('DD-MMM-YYYY')
            : null,
          treatmentEndDate: item.treatmentEndDate
            ? moment(item.treatmentEndDate).format('DD-MMM-YYYY')
            : null,
          paidDate: item.paidDate
            ? moment(item.paidDate).format('DD-MMM-YYYY')
            : null,
        };
      }),
      proceduresClaimsData: proceduresClaimsData.map((item) => {
        const { diagnosisCodes, diagnosisNames } = seperateDiagnosisCode(
          item?.diagnosis || [],
        );

        return {
          ...item,
          diagnosisCodes,
          diagnosisNames,
          dateOfBirth: item.dateOfBirth
            ? moment(item.dateOfBirth).format('DD-MMM-YYYY')
            : null,
          treatmentStartDate: item.treatmentStartDate
            ? moment(item.treatmentStartDate).format('DD-MMM-YYYY')
            : null,
          treatmentEndDate: item.treatmentEndDate
            ? moment(item.treatmentEndDate).format('DD-MMM-YYYY')
            : null,
          paidDate: item.paidDate
            ? moment(item.paidDate).format('DD-MMM-YYYY')
            : null,
        };
      }),
      drugsClaimsData: drugsClaimsData.map((item) => {
        const { diagnosisCodes, diagnosisNames } = seperateDiagnosisCode(
          item?.diagnosis || [],
        );

        return {
          ...item,
          diagnosisCodes,
          diagnosisNames,
          dateOfBirth: item.dateOfBirth
            ? moment(item.dateOfBirth).format('DD-MMM-YYYY')
            : null,
          treatmentStartDate: item.treatmentStartDate
            ? moment(item.treatmentStartDate).format('DD-MMM-YYYY')
            : null,
          treatmentEndDate: item.treatmentEndDate
            ? moment(item.treatmentEndDate).format('DD-MMM-YYYY')
            : null,
          paidDate: item.paidDate
            ? moment(item.paidDate).format('DD-MMM-YYYY')
            : null,
        };
      }),
      capitationData,
    };
  },
} as IHmosAnalyticsRepository;

function applyHospitalAndHmoIdFilterForEnrolleeStats(
  profileHmoId: string,
  hospitalId: string,
  table: string,
  hospitalCol = 'hospital_id',
) {
  const isHmoProviderProfile = !!profileHmoId;
  return isHmoProviderProfile
    ? `WHERE hp.provider_id = '${profileHmoId}'
    ${hospitalId ? `AND "${table}"."${hospitalCol}" = '${hospitalId}'` : ''}`
    : `WHERE "${table}"."${hospitalCol}" = '${hospitalId}'`;
}

function generatePreauthQueryStringForVettingGroupApprovalStatus(
  profile: ProfileModel,
  utilizationsTableName = 'utilizations',
): string {
  return profile.hmoId
    ? [...vettingGroups, UserType.ClaimAgent, UserType.ClaimAgentHOD]
        .map(
          (group) =>
            `
              COUNT(DISTINCT CASE
                   WHEN EXISTS (SELECT 1
                                FROM jsonb_array_elements("${utilizationsTableName}".utilisation_status) AS elem
                                WHERE jsonb_extract_path_text(elem, 'status') = 'Approved'
                                AND jsonb_extract_path_text(elem, 'vettingGroup') = '${group}'
                                )
                       THEN preauth.id
              END) AS "total${group}ApprovedPreauthorizations",
              COUNT(DISTINCT CASE
                   WHEN EXISTS (SELECT 1
                                FROM jsonb_array_elements("${utilizationsTableName}".utilisation_status) AS elem
                                WHERE jsonb_extract_path_text(elem, 'status') = 'Rejected'
                                AND jsonb_extract_path_text(elem, 'vettingGroup') = '${group}'
                                )
                       THEN preauth.id
              END) AS "total${group}RejectedPreauthorizations",
              SUM(CASE
              WHEN EXISTS (SELECT 1
                           FROM jsonb_array_elements("${utilizationsTableName}".utilisation_status) AS elem
                           WHERE jsonb_extract_path_text(elem, 'status') = 'Approved'
                             AND jsonb_extract_path_text(elem, 'vettingGroup') = '${group}'
                           ) THEN ("${utilizationsTableName}".quantity::decimal * "${utilizationsTableName}".price::decimal)
                    ELSE 0
              END
          ) AS "total${group}ApprovedPreauthorizationsAmount",
          SUM(CASE
              WHEN EXISTS (SELECT 1
                           FROM jsonb_array_elements("${utilizationsTableName}".utilisation_status) AS elem
                           WHERE jsonb_extract_path_text(elem, 'status') = 'Rejected'
                             AND jsonb_extract_path_text(elem, 'vettingGroup') = '${group}'
                           ) THEN ("${utilizationsTableName}".quantity::decimal * "${utilizationsTableName}".price::decimal)
                    ELSE 0
              END
          ) AS "total${group}RejectedPreauthorizationsAmount"`,
        )
        .join(',\n')
    : '';
}
