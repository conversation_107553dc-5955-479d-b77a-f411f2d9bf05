/* eslint-disable prefer-arrow/prefer-arrow-functions */
/* eslint-disable max-lines,max-len */
/* eslint-disable prefer-spread */
/* eslint-disable @typescript-eslint/restrict-plus-operands */
/* eslint-disable  @typescript-eslint/quotes */

import { DataSource, Repository } from 'typeorm';
import { InvestigationTestInfoInput } from '../../investigation/validator/investigation.input';
import { LabTestDetail } from '../../investigation/validator/lab-result.input';
import { RadiologyExamDetail } from '../../investigation/validator/radiology-result-input';
import { MedicationConsumable } from '../../medications/validators/medication-details.input';
import { DispenseConsumable } from '../../medications/validators/medication-dispense-details.input';
import {
  ageRangeQueryString,
  demographicQuery,
  extractPeriod,
  momentTz as moment,
} from '../../shared/helper/analytics';
import { DiagnosisInput } from '../../shared/validators/service-detail.input';
import {
  DiagnosisGroup,
  DiagnosisType,
  formatDate,
  GenderOption,
  investigationColumnMapper,
  investigationMapper,
  prescribedMapper,
  processedMapper,
  QueryType,
  requestedMapper,
  ServicesAnalyticsFilter,
  tableMapper,
  TableType,
} from '../inputs/services.input';
import {
  AdmissionDiagnosisData,
  BirthData,
  BirthGenderData,
  ChemoDiagnosisListByConsultationDateAndDoctorName,
  ListByChemoDiagnosis,
  ListByConsultationDateAndDoctorName,
  ListByExaminationDate,
  ListByImmunizationAdmistrationDateAndAdmistratorName,
  ListByLabTestDate,
  ListByMedicationDispensedDateAndDispenserName,
  ListByMedicationPrescriptionDateAndPrescriberName,
  ListsByAdmittedDateAndAdmittedBy,
  MedicalReportSummeryResponse,
  NursingServiceSummaryResponse,
  RequestedToFacilityResponse,
  ServiceDurationData,
  ServicesSummary,
  ServiceSummaryCount,
  ServiceSummaryWithList,
  TopServicesSummary,
} from '../responses/services.response';
import { IAdmissionDiagnosisAnalytics } from '../types';
import {
  applyFilterForHmoAndPartners,
  applyFilterForHmoAndPartnersOnMedicationDispense,
  applyFilterForHmoAndPartnersOnPatients,
} from '@clinify/analytics/utils/apply-filter-for-hmo-and-partners';
import { queryDSWithSlave } from '@clinify/database';
import { InvestigationRequestType } from '@clinify/shared/enums/investigation';
import { MedicationOptionType } from '@clinify/shared/enums/medication';
import { UserType } from '@clinify/shared/enums/users';
import { truncateDate } from '@clinify/shared/helper';
import { ProfileModel } from '@clinify/users/models/profile.model';
import { groupDataByCategoryWithMetrics } from '@clinify/utils/helpers';

type ExamResults = {
  examType: string;
  patientId: string;
  gender: string;
  amount: string;
  patientName: string;
  clinifyId: string;
  paymentType: string;
  examinationDate: string;
  performedBy: string;
  details: RadiologyExamDetail[];
  external: string;
  investigationId: string;
};
type ResultsByPatient = {
  testName: string;
  patientId: string;
  gender: string;
  amount: string;
  patientName: string;
  clinifyId: string;
  paymentType: string;
  investigationId: string;
  details: LabTestDetail[];
  testDate: string;
  performedBy: string;
  external: boolean;
};

export class AnalyticsServicesModel extends ProfileModel {}

export interface IServicesAnalyticsRepository
  extends Repository<AnalyticsServicesModel> {
  this: Repository<ProfileModel>;
  getServicesSummary(
    dataSource: DataSource,
    profile: ProfileModel,
    filter: ServicesAnalyticsFilter,
  ): Promise<ServicesSummary>;
  getServicesData(
    dataSource: DataSource,
    profile: ProfileModel,
    filter: ServicesAnalyticsFilter,
  ): Promise<ServicesSummary[]>;
  getTopServicesData(
    dataSource: DataSource,
    profile: ProfileModel,
    filter: ServicesAnalyticsFilter,
  ): Promise<TopServicesSummary>;
  getConsultationData(
    dataSource: DataSource,
    profile: ProfileModel,
    filter: ServicesAnalyticsFilter,
  ): Promise<ServiceDurationData>;
  getProcedureData(
    dataSource: DataSource,
    profile: ProfileModel,
    filter: ServicesAnalyticsFilter,
  ): Promise<ServiceDurationData>;
  getRadiologyInvestigationData(
    dataSource: DataSource,
    profile: ProfileModel,
    filter: ServicesAnalyticsFilter,
  ): Promise<ServiceSummaryWithList>;
  getLaboratoryInvestigationData(
    dataSource: DataSource,
    profile: ProfileModel,
    filter: ServicesAnalyticsFilter,
  ): Promise<ServiceSummaryWithList>;
  getImmunizationData(
    dataSource: DataSource,
    profile: ProfileModel,
    filter: ServicesAnalyticsFilter,
  ): Promise<ServiceSummaryWithList>;
  getServiceAdmissionData(
    dataSource: DataSource,
    profile: ProfileModel,
    filter: ServicesAnalyticsFilter,
  ): Promise<ServiceSummaryWithList>;
  getMedicationPrescibedData(
    dataSource: DataSource,
    profile: ProfileModel,
    filter: ServicesAnalyticsFilter,
  ): Promise<ServiceSummaryWithList>;
  getConsumablePrescribedData(
    dataSource: DataSource,
    profile: ProfileModel,
    filter: ServicesAnalyticsFilter,
  ): Promise<ServiceSummaryWithList>;
  getMedicationDispenseData(
    dataSource: DataSource,
    profile: ProfileModel,
    filter: ServicesAnalyticsFilter,
  ): Promise<ServiceSummaryWithList>;
  getConsumableDispenseData(
    dataSource: DataSource,
    profile: ProfileModel,
    filter: ServicesAnalyticsFilter,
  ): Promise<ServiceSummaryWithList>;
  getInvestigationStatusData(
    dataSource: DataSource,
    profile: ProfileModel,
    filter: ServicesAnalyticsFilter,
  ): Promise<ServicesSummary>;
  getBirthData(
    dataSource: DataSource,
    profile: ProfileModel,
    filter: ServicesAnalyticsFilter,
  ): Promise<BirthGenderData>;
  getDemographicData(
    dataSource: DataSource,
    profile: ProfileModel,
    filter: ServicesAnalyticsFilter,
  ): Promise<BirthData[]>;
  getAdverseEffectsFollowingImmunizationsData(
    dataSource: DataSource,
    profile: ProfileModel,
    filter: ServicesAnalyticsFilter,
  ): Promise<ServicesSummary>;
  getServicesByCoverageType(
    dataSource: DataSource,
    profile: ProfileModel,
    filter: ServicesAnalyticsFilter,
  ): Promise<ServiceSummaryWithList>;
  getInternalAndExternalInvestigation(
    dataSource: DataSource,
    profile: ProfileModel,
    filter: ServicesAnalyticsFilter,
  ): Promise<ServiceSummaryWithList>;

  getNursingService(
    dataSource: DataSource,
    profile: ProfileModel,
    filter: ServicesAnalyticsFilter,
  ): Promise<NursingServiceSummaryResponse>;

  getRequestedToFacility(
    dataSource: DataSource,
    profile: ProfileModel,
    filter: ServicesAnalyticsFilter,
  ): Promise<RequestedToFacilityResponse[]>;

  getMedicalReport(
    dataSource: DataSource,
    profile: ProfileModel,
    filter: ServicesAnalyticsFilter,
  ): Promise<MedicalReportSummeryResponse>;
}
const checkForValidFloat = (columnKey: string) => {
  return `COALESCE(NULLIF(TRIM(${columnKey}), ''), '0')::float`;
};

const inPatientQueryString = (
  tableName: string,
  inPatient?: boolean,
): string => {
  switch (tableName) {
    case 'admissions':
      return '';
    case 'consultations':
      return inPatient
        ? `
        AND treatment_plans.patient_admitted = 'Yes'
        `
        : `AND (treatment_plans.patient_admitted <> 'Yes' OR treatment_plans IS NULL)`;
    case 'medication_details':
      return inPatient
        ? `
        AND medication_details.price_details :: jsonb @> '{ "patientType": "InPatient" }'
        `
        : `
          AND (NOT(medication_details.price_details :: jsonb @> '{ "patientType": "InPatient" }') OR medication_details.price_details IS NULL)
          `;
    case 'dispense_details':
      return inPatient
        ? `
        AND dispense_details.dispense_service_details::jsonb @> '[{ "patientType": "InPatient" }]'
        `
        : `
          AND (NOT(dispense_details.dispense_service_details :: jsonb @> '[{ "patientType": "InPatient" }]') OR dispense_details.dispense_service_details IS NULL)
          `;
    default:
      return inPatient
        ? `
        AND ${tableName}.service_details :: jsonb @> '[{ "patientType": "InPatient" }]'
        `
        : `
          AND (NOT(${tableName}.service_details :: jsonb @> '[{ "patientType": "InPatient" }]') OR ${tableName}.service_details IS NULL)
          `;
  }
};

const getHospitalIdQueryResult = async (
  dataSource: DataSource,
  type: string,
  duration: string,
  tableName: string,
  hospitalId: string,
  startDate: string,
  endDate: string,
  diagnosisColumn?: string,
  diagnosisType?: DiagnosisType,
  gender?: GenderOption,
  inPatient?: boolean,
  ageRange?: string,
): Promise<any[]> => {
  const query = `
      SELECT
        COUNT(distinct ${tableName}.id) AS "${tableMapper[tableName]}"
      FROM ${tableName}
      LEFT JOIN profiles ON ${tableName}.profile = profiles.id
      ${
        tableName === 'consultations'
          ? `
      LEFT JOIN treatment_plans ON consultations.id = treatment_plans.consultation
      `
          : ''
      }    
      LEFT JOIN details ON profiles.details = details.id${
        diagnosisColumn ? ',' : ''
      }
      ${
        diagnosisColumn
          ? `
        jsonb_to_recordset(${tableName}.${diagnosisColumn}) 
        AS diag("diagnosisICD10" text, "diagnosisICD11" text, "diagnosisSNOMED" text)
        WHERE ${
          diagnosisType
            ? `diag."${diagnosisType}" IS NOT NULL`
            : '(diag."diagnosisICD10" IS NOT NULL OR diag."diagnosisICD11" IS NOT NULL OR diag."diagnosisSNOMED" IS NOT NULL)'
        } AND
      `
          : ''
      }
      ${!diagnosisColumn ? 'WHERE' : ''} ${tableName}.${type} = '${hospitalId}'
        AND date(${tableName}.created_date) BETWEEN '${startDate}' AND '${endDate}'
        ${
          !!gender && gender !== GenderOption.All
            ? `AND profiles.gender = '${gender}'`
            : ''
        }
      ${inPatientQueryString(tableName, inPatient)}
      ${ageRangeQueryString(ageRange)}
      `;
  const result = await queryDSWithSlave(dataSource, query);
  return result.map((item) => ({
    ...item,
    name: extractPeriod(new Date(startDate), duration),
  }));
};

const serializeDiagnosis = (
  key: string,
  diagnosis: IAdmissionDiagnosisAnalytics[],
  subKey?: string,
) => {
  return diagnosis.reduce(
    (acc, item) => {
      const gender = item.gender.toLowerCase();
      if (item[key]) {
        item[key].forEach((diag) => {
          Object.entries(diag).forEach(([k, value]) => {
            if (subKey) {
              if (subKey === k && value) {
                acc[k][gender].push(value);
              }
            } else {
              if (value) {
                acc[k][gender].push(value);
              }
            }
          });
        });
      }
      return acc;
    },
    {
      diagnosisICD10: {
        male: [],
        female: [],
        others: [],
      },
      diagnosisICD11: {
        male: [],
        female: [],
        others: [],
      },
      diagnosisSNOMED: {
        male: [],
        female: [],
        others: [],
      },
    } as AdmissionDiagnosisData,
  );
};
const groupByGender = (key: string, data: any, subKey?: string) => {
  return data.reduce(
    (acc, curr) => {
      const gender = curr.gender.toLowerCase();
      if (subKey) {
        const data = curr[key];
        if (Array.isArray(data)) {
          data.forEach((item) => {
            if (item[subKey]) {
              acc[gender].push(item[subKey]);
            }
          });
        } else {
          const value = curr[key][subKey];
          acc[gender].push(...(Array.isArray(value) ? value : [value]));
        }
      } else {
        const value = curr[key];
        acc[gender].push(...(Array.isArray(value) ? value : [value]));
      }
      return acc;
    },
    {
      male: [],
      female: [],
      others: [],
    },
  ) as {
    male: string[];
    female: string[];
    others: string[];
  };
};
const groupByGenderWithQuantity = (key: string | string[], data: any) => {
  const pickValue = (item: any) => {
    if (key) {
      if (Array.isArray(key)) {
        const obj = {} as {
          name: string;
          quantity: number;
          totalAmount: number;
        };
        key.forEach((k, index) => {
          const value = item[k];
          if (Array.isArray(value)) {
            if (index === 0) {
              obj.name = value[0];
            } else {
              obj[k] = value[0];
            }
          } else {
            if (index === 0) {
              obj.name = value;
            } else {
              obj[k] = value;
            }
          }
        });
        return obj;
      }
      return item[key];
    }
    return item;
  };
  return data.reduce(
    (acc, curr) => {
      const gender = (curr.gender || 'others').toLowerCase();
      const value = pickValue(curr);
      acc[gender].push(value);

      return acc;
    },
    {
      male: [],
      female: [],
      others: [],
    },
  );
};
const groupByGenderConsumables = (key: string, data: any, subKey: string[]) => {
  const pickValue = (item: any) => {
    if (subKey) {
      if (Array.isArray(subKey)) {
        const obj = {} as {
          name: string;
          quantity: number;
          totalAmount: number;
        };
        subKey.forEach((k) => {
          if (k === 'quantityConsumed') {
            obj.quantity = +item.quantityConsumed;
          } else {
            if (k === 'quantity') {
              obj.quantity = +(item.quantity || '0');
            } else {
              const value = item[k];
              obj[k] = value;
            }
          }
        });
        return obj;
      }
      return item[subKey];
    }
    return item;
  };
  return data.reduce(
    (acc, curr) => {
      const gender = curr.gender.toLowerCase();
      const els = curr[key];
      if (Array.isArray(els)) {
        els.forEach((el) => {
          el.totalAmount = (el.quantityConsumed || 0) * (el.unitPrice || 0);
          const value = pickValue(el);
          if (value?.name) {
            acc[gender].push(value);
          }
        });
      } else {
        const el = curr[key];

        el.totalAmount = (el.quantityConsumed || 0) * (el.unitPrice || 0);
        const value = pickValue(el);
        if (value?.name) {
          acc[gender].push(value);
        }
      }
      return acc;
    },
    {
      male: [],
      female: [],
      others: [],
    },
  );
};
const prescribedSummaryQuery = (
  dataSource: DataSource,
  start: string,
  end: string,
  option: string,
  hospitalId: string, // hospitalId on filter
  profile: ProfileModel,
  overall?: boolean,
) => {
  return queryDSWithSlave(
    dataSource,
    `SELECT
    COUNT(medication_details.id) AS "count",
    medications.hospital_name AS "hospitalName"
    FROM medication_details
    LEFT JOIN medications ON medication_details.medication = medications.id
    LEFT JOIN profiles ON medications.profile = profiles.id
  ${applyFilterForHmoAndPartnersOnMedicationDispense({
    profile,
    filter: { hospitalId },
    table: 'medication_details',
  })}
  AND option = '${option}' AND option IS NOT NULL
  ${
    overall
      ? ''
      : `AND date(medication_details.created_date) BETWEEN '${start}' AND '${end}'`
  }
  GROUP BY medications.hospital_name
    `,
  );
};
const prescribedQuery = async (
  dataSource: DataSource,
  start: string,
  end: string,
  option: string,
  hospitalId: string,
  groupBy: boolean,
  overall?: boolean,
  duration?: string,
  gender?: GenderOption,
  inPatient?: boolean,
  ageRange?: string,
) => {
  const result = await queryDSWithSlave(
    dataSource,
    `SELECT 
  COUNT(medication_details.id) AS "${prescribedMapper[option]}"
  FROM medication_details
    LEFT JOIN medications ON medication_details.medication = medications.id
    LEFT JOIN profiles ON medications.profile = profiles.id
  WHERE medication_details.hospital_id = '${hospitalId}'
  AND medication_details.option = '${option}' AND medication_details.option IS NOT NULL
  ${
    overall
      ? ''
      : `AND date(medication_details.created_date) BETWEEN '${start}' AND '${end}'`
  }
     ${
       !!gender && gender !== GenderOption.All
         ? `AND profiles.gender = '${gender}'`
         : ''
     }
  ${inPatientQueryString('medication_details', inPatient)} 
  ${ageRangeQueryString(ageRange)}
`,
  );
  return result.map((item) => ({
    ...item,
    name: extractPeriod(new Date(start), duration),
  }));
};

const medicationDispensedQuery = async (
  dataSource: DataSource,
  start: string,
  end: string,
  duration: string,
  hospitalId: string,
  gender?: GenderOption,
): Promise<any[]> => {
  const query = `SELECT SUM(ARRAY_LENGTH(dd.medication_name, 1)) AS "totalMedicationsDispensed",
    SUM(bill_details.amount_due) AS "totalAmount"
  FROM
    "dispense_details" "dd"
    INNER JOIN medications ON medications.id = dd.medication
    LEFT JOIN profiles ON profiles."id" = "medications"."profile"
    LEFT JOIN "profiles" "createdBy" ON "createdBy"."id" = "medications"."created_by"
    LEFT JOIN "bill_details" ON "bill_details"."id" = "dd"."billing"
  WHERE
    "medications"."hospital" = '${hospitalId}'
    AND "createdBy"."type" != 'Patient'
    AND "dd"."medication" IS NOT NULL
    AND date ("dd"."dispense_date") BETWEEN  '${start}' AND '${end}'
  ${
    !!gender && gender !== GenderOption.All
      ? `AND profiles.gender = '${gender}'`
      : ''
  }
`;
  const result = await queryDSWithSlave(dataSource, query);
  return result.map((item) => ({
    ...item,
    name: extractPeriod(new Date(start), duration),
  }));
};

const consultationDiagnosisObjStatement = (
  objKey: string,
  diagnosisType: DiagnosisType,
) => {
  const diagnosisICD10 = ` 'diagnosisICD10',
  jsonb_build_object(
    'female', ARRAY_REMOVE(ARRAY_AGG(CASE WHEN profiles.gender = 'Female' THEN ${objKey}->>'diagnosisICD10' END), NULL),
    'male', ARRAY_REMOVE(ARRAY_AGG(CASE WHEN profiles.gender = 'Male' THEN ${objKey}->>'diagnosisICD10' END), NULL)
  )`;

  const diagnosisICD11 = `'diagnosisICD11',
  jsonb_build_object(
    'female', ARRAY_REMOVE(ARRAY_AGG(CASE WHEN profiles.gender = 'Female' THEN ${objKey}->>'diagnosisICD11' END), NULL),
    'male', ARRAY_REMOVE(ARRAY_AGG(CASE WHEN profiles.gender = 'Male' THEN ${objKey}->>'diagnosisICD11' END), NULL)
  )`;

  const diagnosisSNOMED = `'diagnosisSNOMED',
  jsonb_build_object(
    'female', ARRAY_REMOVE(ARRAY_AGG(CASE WHEN profiles.gender = 'Female' THEN ${objKey}->>'diagnosisSNOMED' END), NULL),
    'male', ARRAY_REMOVE(ARRAY_AGG(CASE WHEN profiles.gender = 'Male' THEN ${objKey}->>'diagnosisSNOMED' END), NULL)
  )`;
  let list = [diagnosisICD10, diagnosisICD11, diagnosisSNOMED];
  if (diagnosisType === DiagnosisType.ICD10) {
    list = [diagnosisICD10];
  } else if (diagnosisType === DiagnosisType.ICD11) {
    list = [diagnosisICD11];
  } else if (diagnosisType === DiagnosisType.SNOMED) {
    list = [diagnosisSNOMED];
  }
  return `jsonb_build_object(${list.join(',')})`;
};

const consumableDispensedQuery = async (
  dataSource: DataSource,
  start: string,
  end: string,
  duration: string,
  hospitalId: string,
): Promise<any[]> => {
  const consResult = await queryDSWithSlave(
    dataSource,
    `SELECT json_agg(dispense_consumables) AS "totalConsumablesDispensed"
    FROM
        dispense_details    
    WHERE dispense_details.hospital_id = '${hospitalId}'
      AND dispense_consumables IS NOT NULL AND dispense_consumables != '[]'
      AND option = 'C'
      AND date(dispense_details.created_date) BETWEEN '${start}' AND '${end}'
`,
  );
  const consumableResult = (consResult || [])?.map((items) => {
    const result = items.totalConsumablesDispensed?.map((data) => {
      const newResult = data?.map((result) => ({
        quantityConsumed: result?.quantityConsumed[0],
      }));
      return {
        quantityConsumed: newResult[0]?.quantityConsumed,
      };
    });

    return {
      name: extractPeriod(new Date(start), duration),
      totalConsumablesDispensed: result
        ?.map((item) => Number(item.quantityConsumed))
        .reduce((prev: number, curr: number) => prev + curr, 0),
    };
  });
  return consumableResult;
};

const investigationQuery = async (
  dataSource: DataSource,
  start: string,
  end: string,
  duration: string,
  hospitalId: string,
  type: string,
  overall?: boolean,
  isSummary?: boolean,
  gender?: GenderOption,
  inPatient?: boolean,
  ageRange?: string,
): Promise<any> => {
  const result = await queryDSWithSlave(
    dataSource,
    `SELECT 
    COUNT(request_type) AS "${investigationMapper[type]}"
  FROM investigations
  LEFT JOIN profiles ON investigations.profile = profiles.id
  LEFT JOIN details ON profiles.details = details.id
  WHERE investigations.hospital = '${hospitalId}'
  AND request_type = '${type}'
  ${
    overall
      ? ''
      : `AND date(investigations.created_date) BETWEEN '${start}' AND '${end}'`
  }
  ${
    !!gender && gender !== GenderOption.All
      ? `AND profiles.gender = '${gender}'`
      : ''
  }
  ${inPatientQueryString('investigations', inPatient)}
  ${ageRangeQueryString(ageRange)}
`,
  );
  return result.map((item) => ({
    ...item,
    name: extractPeriod(new Date(start), duration),
  }));
};

const getDiagnosisNames = (diag: DiagnosisInput[]) =>
  Object.values(diag).reduce((acc, cur) => {
    const items = Object.values(cur).filter((item) => item);
    return [...acc, ...items];
  }, [] as string[]);

const groupSameNames = (diag: string[]) =>
  Object.entries(
    diag.reduce((acc, cur) => {
      if (!acc[cur]) {
        acc[cur] = 0;
      }
      acc[cur] += 1;
      return acc;
    }, {} as { [key: string]: number }),
  ).map(([key, value]) => `${key} (${value})`);

export const CustomServicesAnalyticsRepoMethods = {
  async getServicesSummary(
    dataSource: DataSource,
    profile: ProfileModel,
    filter: ServicesAnalyticsFilter,
  ): Promise<ServicesSummary> {
    const { startDate, endDate, overall, duration } = filter;
    const { start, end } = formatDate(startDate, endDate);

    const totalConsultationsQuery = queryDSWithSlave(
      dataSource,
      `SELECT COUNT(consultations.id) AS "count",
      SUM(bill_details.amount_due) AS "totalAmount",
      consultations.clinic_name AS "hospitalName"
    FROM consultations
    LEFT JOIN bill_details ON consultations.bill = bill_details.bill
      AND bill_details.reference = consultations.id::TEXT
    ${applyFilterForHmoAndPartners({
      profile,
      filter,
      table: 'consultations',
    })}
     ${
       overall
         ? ''
         : `AND date(consultations.created_date) BETWEEN '${startDate}' AND '${endDate}'`
     }
     GROUP BY consultations.clinic_name
    `,
    );
    const totalImmunizationsQuery = await queryDSWithSlave(
      dataSource,
      `SELECT COUNT(*) as "count",
      SUM(bd.amount_due) AS "totalAmount",
      im.hospital_name AS "hospitalName"
      FROM immunization_details imd
      LEFT JOIN immunizations im ON imd.immunization_id = im.id
      LEFT JOIN bill_details bd ON imd.billing = bd.id
      ${applyFilterForHmoAndPartners({
        table: 'im',
        profile,
        filter,
      })}
      ${
        overall
          ? ''
          : `AND date(imd.created_date) BETWEEN '${start}' AND '${end}'`
      }
      GROUP BY im.hospital_name
    `,
    );
    const totalProceduresQuery = await queryDSWithSlave(
      dataSource,
      `SELECT
      COUNT(pt.type) AS "count",
      SUM(bd.amount_due) AS "totalAmount",
      surgeries.facility_name AS "hospitalName"
    FROM surgeries
    LEFT JOIN jsonb_to_recordset(procedure_type) AS pt("type" text, "ref" text) ON TRUE
    LEFT JOIN bill_details bd ON bd.reference = pt.ref
    ${applyFilterForHmoAndPartners({ profile, filter, table: 'surgeries' })}
     ${
       overall
         ? ''
         : `AND date(surgeries.created_date) BETWEEN '${startDate}' AND '${endDate}'`
     }
      GROUP BY surgeries.facility_name, pt.type
    `,
    );

    const totalAntenatalsQuery = await queryDSWithSlave(
      dataSource,
      `SELECT 
        SUM(a.antenatal_count + p.postnatal_count + ld.labour_delivery_count) AS "totalAntenatals",
        SUM(a.total_amount + p.total_amount + ld.total_amount) AS "totalAmount",
        a."antenatalHospitalName", p."postnatalHospitalName", ld."labourDeliveryHospitalName"
      FROM 
        (SELECT COUNT(*) AS antenatal_count, SUM(bd.amount_due) AS total_amount, a.hospital_name AS "antenatalHospitalName" FROM antenatals a LEFT JOIN antenatal_details ad ON a.id = ad.antenatal LEFT JOIN bill_details bd ON ad.billing = bd.id 
        ${applyFilterForHmoAndPartners({ profile, table: 'a', filter })}  ${
        overall
          ? ''
          : `AND date(a.created_date) BETWEEN '${startDate}' AND '${endDate}'`
      }
        GROUP BY a.hospital_name) a
        FULL OUTER JOIN (
          SELECT COUNT(*) AS postnatal_count,
          SUM(bd.amount_due) AS total_amount,
          p.facility_name AS "postnatalHospitalName"
          FROM postnatal p LEFT JOIN bill_details bd ON p.bill = bd.bill AND bd.reference = p.id::TEXT
        ${applyFilterForHmoAndPartners({ table: 'p', profile, filter })}  ${
        overall
          ? ''
          : `AND date(p.created_date) BETWEEN '${startDate}' AND '${endDate}'`
      }
        GROUP BY p.facility_name) p ON a."antenatalHospitalName" = p."postnatalHospitalName"
        FULL OUTER JOIN (
          SELECT COUNT(*) AS labour_delivery_count,
          SUM(bd.amount_due) AS total_amount,
          ld.facility_name AS "labourDeliveryHospitalName"
          FROM labour_delivery ld LEFT JOIN bill_details bd ON ld.bill = bd.bill AND bd.reference = ld.id::TEXT 
        ${applyFilterForHmoAndPartners({ table: 'ld', profile, filter })} ${
        overall
          ? ''
          : `AND date(ld.created_date) BETWEEN '${startDate}' AND '${endDate}'`
      }
        GROUP BY ld.facility_name) ld ON p."postnatalHospitalName" = ld."labourDeliveryHospitalName"
    GROUP BY a."antenatalHospitalName", p."postnatalHospitalName", ld."labourDeliveryHospitalName"`,
    );

    const totalAdmissionQuery = queryDSWithSlave(
      dataSource,
      `SELECT COUNT(admissions.id) AS "count",
      SUM(bill_details.amount_due) AS "totalAmount",
      admissions.clinic_name AS "hospitalName"
    FROM admissions
    LEFT JOIN bill_details ON admissions.bill = bill_details.bill
      AND bill_details.reference = admissions.id::TEXT
    ${applyFilterForHmoAndPartners({ table: 'admissions', profile, filter })}
     ${
       overall
         ? ''
         : `AND date(admissions.created_date) BETWEEN '${startDate}' AND '${endDate}'`
     }
     GROUP BY admissions.clinic_name
    `,
    );
    const totalLaboratoryQuery = queryDSWithSlave(
      dataSource,
      `SELECT  SUM(total_laboratory) AS "count",
  SUM(total_amount) AS "totalAmount",
  facility_name AS "hospitalName"
FROM
(
    SELECT
      COUNT(test_name) AS total_laboratory,
      SUM(sum_amounts.total_amount_due) AS total_amount,
      facility_name
    FROM
      (
        SELECT DISTINCT ON (ref)
          jsonb_array_elements(test_info) ->> 'testName' AS test_name,
          jsonb_array_elements(test_info) ->> 'ref' AS ref,
          investigations.bill,
          investigations.facility_name
        FROM investigations
          ${applyFilterForHmoAndPartners({
            table: 'investigations',
            profile,
            filter,
          })}
          AND request_type = 'Laboratory'
           ${
             overall
               ? ''
               : `AND date(investigations.created_date) BETWEEN '${start}' AND '${end}'`
           }
      ) inv
      LEFT JOIN (
        SELECT  id, bill, amount_due AS total_amount_due, reference
        FROM  bill_details
      ) sum_amounts ON inv.bill = sum_amounts.bill AND inv.ref = sum_amounts.reference
    GROUP BY inv.facility_name
  ) a
  GROUP BY facility_name
`,
    );
    const totalRadiologyQuery = queryDSWithSlave(
      dataSource,
      `SELECT  SUM(total_radioloy) AS "count",
  SUM(total_amount) AS "totalAmount",
  facility_name AS "hospitalName"
FROM
(
    SELECT
      COUNT(exam_type) AS total_radioloy,
      SUM(sum_amounts.total_amount_due) AS total_amount,
      facility_name
    FROM
      (
        SELECT DISTINCT ON (ref)
          jsonb_array_elements(examination_type)->>'examType' as exam_type,
          jsonb_array_elements(examination_type) ->> 'ref' AS ref,
          investigations.bill,
          investigations.facility_name
        FROM investigations
          ${applyFilterForHmoAndPartners({
            profile,
            table: 'investigations',
            filter,
          })}
          AND request_type = 'Radiology'
           ${
             overall
               ? ''
               : `AND date(investigations.created_date) BETWEEN '${start}' AND '${end}'`
           }
      ) inv
      LEFT JOIN (
        SELECT  id, bill, amount_due AS total_amount_due, reference
        FROM  bill_details
      ) sum_amounts ON inv.bill = sum_amounts.bill AND inv.ref = sum_amounts.reference
    GROUP BY inv.facility_name
  ) a
  GROUP BY facility_name
`,
    );
    const totalConsumablesPrescribedQuery = prescribedSummaryQuery(
      dataSource,
      start,
      end,
      MedicationOptionType.C,
      filter.hospitalId,
      profile,
      overall,
    );
    const totalMedicationsPrescribedQuery = prescribedSummaryQuery(
      dataSource,
      start,
      end,
      MedicationOptionType.M,
      filter.hospitalId,
      profile,
      overall,
    );

    const totalMedicationsDispensedQuery = queryDSWithSlave(
      dataSource,
      `SELECT SUM(ARRAY_LENGTH(dd.medication_name, 1)) AS "count",
        SUM(bill_details.amount_due) AS "totalAmount",
        medications.hospital_name AS "hospitalName"
      FROM
        "dispense_details" "dd"
        INNER JOIN medications ON medications.id = dd.medication
        LEFT JOIN profiles ON profiles."id" = "medications"."profile"
        LEFT JOIN "profiles" "createdBy" ON "createdBy"."id" = "medications"."created_by"
        LEFT JOIN "bill_details" ON "bill_details"."id" = "dd"."billing"
        ${applyFilterForHmoAndPartners({
          profile,
          table: 'medications',
          filter,
        })}
        AND "dd".option = '${MedicationOptionType.M}'
        AND "createdBy"."type" != 'Patient'
        AND "dd"."medication" IS NOT NULL
      ${
        overall
          ? ''
          : `AND date ("dd"."dispense_date") BETWEEN  '${start}' AND '${end}'`
      }
      GROUP BY medications.hospital_name
    `,
    );
    const consumableDispensedQuery = queryDSWithSlave(
      dataSource,
      `SELECT SUM(jsonb_array_length(dd.dispense_consumables)) AS "count",
        SUM(bill_details.amount_due) AS "totalAmount",
        medications.hospital_name AS "hospitalName"
      FROM
        "dispense_details" "dd"
        INNER JOIN medications ON medications.id = dd.medication
        LEFT JOIN profiles ON profiles."id" = "medications"."profile"
        LEFT JOIN "profiles" "createdBy" ON "createdBy"."id" = "medications"."created_by"
        LEFT JOIN "bill_details" ON "bill_details"."id" = "dd"."billing"
        ${applyFilterForHmoAndPartners({
          profile,
          table: 'medications',
          filter,
        })}
        AND "dd".option = '${MedicationOptionType.C}'
        AND "createdBy"."type" != 'Patient'
        AND "dd"."dispense_consumables" IS NOT NULL

      ${
        overall
          ? ''
          : `AND date ("dd"."dispense_date") BETWEEN  '${start}' AND '${end}'`
      }
      GROUP BY medications.hospital_name
    `,
    );
    const investigationStatusBaseQuery = (type: string): string => {
      return `
      SELECT
        COUNT(DISTINCT inv.id) AS "${requestedMapper[type]}",
        COUNT(DISTINCT inv.id) FILTER(WHERE inv.status = 'Approved') AS "${
          processedMapper[type]
        }",
        SUM(sum_amounts.total_amount_due) AS "totalAmount",
        SUM(sum_amounts.total_amount_due) FILTER(WHERE inv.status = 'Approved') AS "totalAmountProcessed",
        inv.facility_name AS "hospitalName"
      FROM (
        SELECT DISTINCT ON (ref) investigations.id, bill, facility_name, "status",
          jsonb_array_elements(${
            investigationColumnMapper[type]
          }) ->> 'ref' AS ref
        FROM investigations
	      ${applyFilterForHmoAndPartners({
          profile,
          table: 'investigations',
          filter,
        })}
        AND investigations.archived = false
        AND request_type = '${type}'
        ${
          overall
            ? ''
            : `AND date(investigations.created_date) BETWEEN '${start}' AND '${end}'`
        }
      ) inv
      LEFT JOIN (
        SELECT id, bill, amount_due AS total_amount_due, reference
        FROM  bill_details
      ) sum_amounts ON inv.bill = sum_amounts.bill AND inv.ref = sum_amounts.reference
      GROUP BY inv.facility_name`;
    };
    const radiologyStatusQuery = await queryDSWithSlave(
      dataSource,
      `${investigationStatusBaseQuery('Radiology')}`,
    );
    const laboratoryStatusQuery = await queryDSWithSlave(
      dataSource,
      `${investigationStatusBaseQuery('Laboratory')}`,
    );
    const nursingServicesQuery = await queryDSWithSlave(
      dataSource,
      `SELECT  COUNT(*) as "count",
      SUM(bd.amount_due) AS "totalAmount",
      ns.facility_name AS "hospitalName"
      FROM nursing_service_details nsd
      LEFT JOIN nursing_service ns ON nsd.nursing_service_id = ns.id
      LEFT JOIN bill_details bd ON nsd.billing = bd.id
      ${applyFilterForHmoAndPartners({
        profile,
        table: 'ns',
        filter,
        hospitalCol: 'hospital_id',
      })}
      ${
        overall
          ? ''
          : `AND date(nsd.created_date) BETWEEN '${start}' AND '${end}'`
      }
      GROUP BY ns.facility_name
    `,
    );

    const medicalReportQuery = await queryDSWithSlave(
      dataSource,
      `SELECT COUNT(medical_report.id) AS "count",
      SUM(bd.amount_due) AS "totalAmount",
      medical_report.facility_name AS "hospitalName"
      FROM medical_report
      LEFT JOIN jsonb_to_recordset(report_type) AS pt("ref" text) ON TRUE
      LEFT JOIN bill_details bd ON bd.reference = pt.ref
      ${applyFilterForHmoAndPartners({
        profile,
        table: 'medical_report',
        filter,
        hospitalCol: 'hospital_id',
        profileCol: 'profile_id',
      })}
      ${
        overall
          ? ''
          : `AND date(medical_report.created_date) BETWEEN '${start}' AND '${end}'`
      }
      GROUP BY medical_report.facility_name
    `,
    );
    const [
      totalConsultations,
      totalImmunizations,
      totalProcedures,
      totalAntenatals,
      totalAdmissions,
      totalLaboratory,
      totalRadiology,
      totalMedicationsPrescribedResult,
      totalConsumablesPrescribedResult,
      totalMedicationsDispensed,
      consumableDispensed,
      radiologyStatus,
      laboratoryStatus,
      nursingServices,
      medicalReport,
    ] = await Promise.all([
      totalConsultationsQuery,
      totalImmunizationsQuery,
      totalProceduresQuery,
      totalAntenatalsQuery,
      totalAdmissionQuery,
      totalLaboratoryQuery,
      totalRadiologyQuery,
      totalMedicationsPrescribedQuery,
      totalConsumablesPrescribedQuery,
      totalMedicationsDispensedQuery,
      consumableDispensedQuery,
      radiologyStatusQuery,
      laboratoryStatusQuery,
      nursingServicesQuery,
      medicalReportQuery,
    ]);

    const result = {
      name: extractPeriod(new Date(start), duration),
      proceduresSummary: totalProcedures,
      consultationsSummary: totalConsultations,
      immunizationsSummary: totalImmunizations,
      antenatalsSummary: totalAntenatals.map(
        (item) =>
          new ServiceSummaryCount(
            item.totalAntenatals,
            item.antenatalHospitalName,
            item.totalAmount,
          ),
      ),
      admissionsSummary: totalAdmissions,
      medicationsPrescribedSummary: totalMedicationsPrescribedResult,
      consumablesPrescribedSummary: totalConsumablesPrescribedResult,
      medicationsDispensedSummary: totalMedicationsDispensed,
      laboratorySummary: totalLaboratory,
      radiologySummary: totalRadiology,
      consumablesDispensedSummary: consumableDispensed,
      requestedLaboratorySummary: laboratoryStatus.map(
        (item) =>
          new ServiceSummaryCount(
            item.totalRequestedLaboratory,
            item.hospitalName,
            item.totalAmount,
          ),
      ),
      processedLaboratorySummary: laboratoryStatus.map(
        (item) =>
          new ServiceSummaryCount(
            item.totalProcessedLaboratory,
            item.hospitalName,
            item.totalAmountProcessed,
          ),
      ),
      requestedRadiologySummary: radiologyStatus.map(
        (item) =>
          new ServiceSummaryCount(
            item.totalRequestedRadiology,
            item.hospitalName,
            item.totalAmount,
          ),
      ),
      processedRadiologySummary: radiologyStatus.map(
        (item) =>
          new ServiceSummaryCount(
            item.totalProcessedRadiology,
            item.hospitalName,
            item.totalAmountProcessed,
          ),
      ),
      nursingServicesSummary: nursingServices,
      medicalReportsSummary: medicalReport,
    };
    return result;
  },

  async getServicesData(
    dataSource: DataSource,
    profile: ProfileModel,
    filter: ServicesAnalyticsFilter,
  ): Promise<ServicesSummary[]> {
    const map = new Map();
    const { duration, startDate, endDate } = filter;

    const { start, end } = formatDate(startDate, endDate);

    const consultationQuery = queryDSWithSlave(
      dataSource,
      `SELECT  COUNT(consultations.id) AS "totalConsultations",
      SUM(bills.amount_due) AS "totalAmount"
    FROM consultations
    LEFT JOIN bills ON consultations.bill = bills.id
    WHERE consultations.hospital = '${profile.hospitalId}'
    AND consultations.archived = false
    AND date(consultations.created_date) BETWEEN '${startDate}' AND '${endDate}'
    `,
    );

    const investigationQueryData = getHospitalIdQueryResult(
      dataSource,
      QueryType.HOSPITAL,
      duration,
      TableType.Investigation,
      profile.hospitalId,
      start,
      end,
    );

    const laboratoryQuery = await investigationQuery(
      dataSource,
      start,
      end,
      duration,
      profile.hospitalId,
      'Laboratory',
    );

    const radiologyQuery = await investigationQuery(
      dataSource,
      start,
      end,
      duration,
      profile.hospitalId,
      'Radiology',
    );

    const immunizationQuery = queryDSWithSlave(
      dataSource,
      `SELECT COUNT(*) as "totalImmunizations",
      SUM(bd.amount_due) AS "totalAmount"
      FROM immunization_details imd
      LEFT JOIN immunizations im ON imd.immunization_id = im.id
      LEFT JOIN bill_details bd ON imd.billing = bd.id
      WHERE im.hospital = '${profile.hospitalId}'
      AND date(im.created_date) BETWEEN '${start}' AND '${end}'
    `,
    );

    const admissionQuery = getHospitalIdQueryResult(
      dataSource,
      QueryType.HOSPITAL,
      duration,
      TableType.Admission,
      profile.hospitalId,
      start,
      end,
    );

    const antenatalQuery = getHospitalIdQueryResult(
      dataSource,
      QueryType.HOSPITAL,
      duration,
      TableType.Antenatal,
      profile.hospitalId,
      start,
      end,
    );

    const procedureQuery = getHospitalIdQueryResult(
      dataSource,
      QueryType.HOSPITAL,
      duration,
      TableType.Procedure,
      profile.hospitalId,
      start,
      end,
    );

    const totalConsumablesPrescribedQuery = prescribedQuery(
      dataSource,
      start,
      end,
      MedicationOptionType.C,
      profile.hospitalId,
      true,
      false,
      duration,
    );

    const totalMedicationsPrescribedQuery = prescribedQuery(
      dataSource,
      start,
      end,
      MedicationOptionType.M,
      profile.hospitalId,
      true,
      false,
      duration,
    );

    const totalMedicationsDispensedQuery = medicationDispensedQuery(
      dataSource,
      start,
      end,
      duration,
      profile.hospitalId,
    );

    const totalConsumableDispensedQuery = consumableDispensedQuery(
      dataSource,
      start,
      end,
      duration,
      profile.hospitalId,
    );
    const nursingServicesQuery = queryDSWithSlave(
      dataSource,
      `SELECT  COUNT(*) as "totalNursingServices",
      SUM(bd.amount_due) AS "totalAmount"
     FROM nursing_service_details nsd
      LEFT JOIN nursing_service ns ON nsd.nursing_service_id = ns.id
      LEFT JOIN bill_details bd ON nsd.billing = bd.id
      WHERE ns.hospital_id = '${profile.hospitalId}'
      AND date(ns.created_date) BETWEEN '${start}' AND '${end}'
    `,
    );

    const medicalReportQuery = queryDSWithSlave(
      dataSource,
      `SELECT COUNT(medical_report.id) AS "totalMedicalReports",
      SUM(bills.amount_due) AS "totalAmount"
      FROM medical_report
      LEFT JOIN bills ON medical_report.bill = bills.id
      WHERE medical_report.hospital_id = '${profile.hospitalId}'
      AND date(medical_report.created_date) BETWEEN '${startDate}' AND '${endDate}'
    `,
    );

    const [
      admissionResult,
      consultationResult,
      investigationResult,
      radiologyResult,
      laboratoryResult,
      immunizationResult,
      antenatalResult,
      procedureResult,
      totalConsumablesPrescribedResult,
      totalMedicationsPrescribedResult,
      totalMedicationsDispensed,
      consumableDispensedResult,
      nursingServicesResult,
      medicalReportResult,
    ] = await Promise.all([
      admissionQuery,
      consultationQuery,
      investigationQueryData,
      radiologyQuery,
      laboratoryQuery,
      immunizationQuery,
      antenatalQuery,
      procedureQuery,
      totalConsumablesPrescribedQuery,
      totalMedicationsPrescribedQuery,
      totalMedicationsDispensedQuery,
      totalConsumableDispensedQuery,
      nursingServicesQuery,
      medicalReportQuery,
    ]);
    const name = truncateDate(new Date(startDate), duration.toLowerCase());
    consultationResult.forEach((item) => map.set(name, { ...item }));
    investigationResult.forEach((item) =>
      map.set(name, { ...map.get(name), ...item }),
    );
    radiologyResult.forEach((item) =>
      map.set(name, { ...map.get(name), ...item }),
    );
    procedureResult.forEach((item) =>
      map.set(name, { ...map.get(name), ...item }),
    );
    immunizationResult.forEach((item) =>
      map.set(name, { ...map.get(name), ...item }),
    );
    laboratoryResult.forEach((item) =>
      map.set(name, { ...map.get(name), ...item }),
    );
    totalMedicationsDispensed.forEach((item) =>
      map.set(name, { ...map.get(name), ...item }),
    );
    consumableDispensedResult.forEach((item) =>
      map.set(name, { ...map.get(name), ...item }),
    );
    antenatalResult.forEach((item) =>
      map.set(name, { ...map.get(name), ...item }),
    );
    admissionResult.forEach((item) =>
      map.set(name, { ...map.get(name), ...item }),
    );
    totalConsumablesPrescribedResult.forEach((item) =>
      map.set(name, { ...map.get(name), ...item }),
    );
    totalMedicationsPrescribedResult.forEach((item) =>
      map.set(name, { ...map.get(name), ...item }),
    );

    nursingServicesResult.forEach((item) =>
      map.set(name, { ...map.get(name), ...item }),
    );

    medicalReportResult.forEach((item) =>
      map.set(name, { ...map.get(name), ...item }),
    );

    const servicesData = Array.from(map.values());
    if (!servicesData.length) {
      return [
        {
          name: truncateDate(new Date(startDate), duration.toLowerCase()),
          totalAntenatals: 0,
          totalAdmissions: 0,
          totalMedications: 0,
          totalConsultations: 0,
          totalProcedures: 0,
          totalInvestigations: 0,
          totalImmunizations: 0,
          totalRadiology: 0,
          totalLaboratory: 0,
          totalMedicationsDispensed: 0,
          totalMedicationsPrescribed: 0,
          totalConsumablesPrescribed: 0,
          totalConsumablesDispensed: 0,
          totalNursingServices: 0,
          totalMedicalReports: 0,
        },
      ];
    }

    return servicesData;
  },

  async getTopServicesData(
    dataSource: DataSource,
    profile: ProfileModel,
    filter: ServicesAnalyticsFilter,
  ): Promise<TopServicesSummary> {
    const { startDate, endDate } = filter;
    const { start, end } = formatDate(startDate, endDate);

    const immunizationQuery = queryDSWithSlave(
      dataSource,
      `SELECT 
      immunization_name AS "category", 
      COUNT(*) AS "count",
      SUM(bd.quantity) AS "totalQuantity",
      SUM(bd.amount_due) AS "totalAmountDue",
      SUM(bd.amount_paid) AS "totalAmountPaid",
      SUM(bd.amount_outstanding) AS "totalAmountOutstanding",
      SUM(bd.amount) AS "totalAmount"
    FROM immunization_details
    LEFT JOIN immunizations ON immunization_details.immunization_id = immunizations.id
    LEFT JOIN bill_details bd ON immunization_details.billing = bd.id
    ${applyFilterForHmoAndPartners({
      profile,
      filter,
      table: 'immunizations',
    })}
      AND date(immunization_details.created_date) BETWEEN '${start}' AND '${end}'
      AND immunization_name IS NOT NULL
      AND immunization_name != ''
    GROUP BY immunization_name 
    ORDER BY COUNT(*) DESC
    LIMIT 10;
  `,
    );

    const procedureQuery = queryDSWithSlave(
      dataSource,
      `SELECT 
      billing.type AS "category",
      COUNT(billing.type) AS "count",
      SUM(billing.quantity) AS "totalQuantity",
      SUM(billing.amount_due) AS "totalAmountDue",
      SUM(billing.amount_paid) AS "totalAmountPaid",
      SUM(billing.amount_outstanding) AS "totalAmountOutstanding",
      SUM(billing.total_amount) AS "totalAmount"
    FROM (
      SELECT 
        s.id,
        pt.type,
        pt.ref,
        SUM(bd.quantity) AS quantity,
        SUM(bd.amount_due) AS amount_due,
        SUM(bd.amount_paid) AS amount_paid,
        SUM(bd.amount_outstanding) AS amount_outstanding,
        SUM(bd.amount) AS total_amount
      FROM surgeries s
      INNER JOIN jsonb_to_recordset(s.procedure_type) AS pt("type" text, "ref" text) ON TRUE
      LEFT JOIN bill_details bd ON s.bill = bd.bill AND bd.reference::TEXT = pt.ref::TEXT
      ${applyFilterForHmoAndPartners({ profile, filter, table: 's' })}
      AND s.procedure_type IS NOT NULL
      AND date(s.created_date) BETWEEN '${start}' AND '${end}'
      GROUP BY s.id, pt.type, pt.ref
    ) billing
    GROUP BY billing.type 
    ORDER BY count DESC 
    LIMIT 10;
  `,
    );

    const medicationPrescribedQuery = queryDSWithSlave(
      dataSource,
      `SELECT medication_name AS "category", COUNT(*) 
      FROM medication_details
      ${applyFilterForHmoAndPartners({
        profile,
        filter,
        table: 'medication_details',
        hospitalCol: 'hospital_id',
        profileCol: 'ref_profile',
      })}
        AND medication_details.medication_name IS NOT NULL
        AND option = 'M' AND option IS NOT NULL
        AND date(medication_details.created_date) BETWEEN '${start}' AND '${end}'
      GROUP BY medication_name 
      ORDER BY COUNT(*) DESC
      LIMIT 10;
  `,
    );

    const consumablePrescribedQuery = queryDSWithSlave(
      dataSource,
      `SELECT mc.name AS "category", COUNT(*) 
    FROM medication_details
    INNER JOIN jsonb_to_recordset(medication_consumables)  AS mc("name" text) ON TRUE
    ${applyFilterForHmoAndPartners({
      profile,
      filter,
      table: 'medication_details',
      hospitalCol: 'hospital_id',
      profileCol: 'ref_profile',
    })}
    AND medication_details.medication_consumables IS NOT NULL
    AND medication_details.medication_consumables != '[]' 
    AND option = 'C' AND option IS NOT NULL
    AND date(medication_details.created_date) BETWEEN '${start}' AND '${end}'
    AND (NOT(medication_details.price_details :: jsonb @> '{ "patientType": "InPatient" }') OR medication_details.price_details IS NULL)
    GROUP BY mc.name 
    ORDER BY COUNT(*) DESC
    LIMIT 10;
  `,
    );

    const medicationDispensedQuery = queryDSWithSlave(
      dataSource,
      `SELECT 
      med_name AS "category",
      COUNT(med_name) AS "count",
      SUM(billing.quantity) AS "totalQuantity",
      SUM(billing.amount_due) AS "totalAmountDue",
      SUM(billing.amount_paid) AS "totalAmountPaid",
      SUM(billing.amount_outstanding) AS "totalAmountOutstanding",
      SUM(billing.total_amount) AS "totalAmount"
    FROM (
      SELECT 
        dd.id,
        SUM(bd.quantity) AS quantity,
        SUM(bd.amount_due) AS amount_due,
        SUM(bd.amount_paid) AS amount_paid,
        SUM(bd.amount_outstanding) AS amount_outstanding,
        SUM(bd.amount) AS total_amount,
        dd.medication_name
      FROM dispense_details dd
      LEFT JOIN medications m ON dd.medication = m.id
      LEFT JOIN bill_details bd ON bd.id = dd.billing
      ${applyFilterForHmoAndPartnersOnMedicationDispense({
        profile,
        filter,
        table: 'dd',
      })}
      AND dd.medication IS NOT NULL
      AND dd.option = 'M' AND dd.option IS NOT NULL
      AND date(dd.created_date) BETWEEN '${start}' AND '${end}'
      GROUP BY dd.id, dd.medication_name
    ) billing
    INNER JOIN unnest(billing.medication_name) AS med_name ON TRUE
    GROUP BY med_name
    ORDER BY count DESC
    LIMIT 10
  `,
    );

    const consumableDispensedQuery = queryDSWithSlave(
      dataSource,
      `SELECT 
      dc.name AS "category", 
      COUNT(*) AS "count",
      SUM(billing.quantity) AS "totalQuantity",
      SUM(billing.amount_due) AS "totalAmountDue",
      SUM(billing.amount_paid) AS "totalAmountPaid",
      SUM(billing.amount_outstanding) AS "totalAmountOutstanding",
      SUM(billing.total_amount) AS "totalAmount"
    FROM (
      SELECT 
        dd.id,
        SUM(bd.quantity) AS quantity,
        SUM(bd.amount_due) AS amount_due,
        SUM(bd.amount_paid) AS amount_paid,
        SUM(bd.amount_outstanding) AS amount_outstanding,
        SUM(bd.amount) AS total_amount,
        dd.dispense_consumables
      FROM dispense_details dd
      LEFT JOIN bill_details bd ON bd.id = dd.billing
      ${applyFilterForHmoAndPartnersOnMedicationDispense({
        profile,
        filter,
        table: 'dd',
      })}
      AND dd.dispense_consumables IS NOT NULL
      AND dd.dispense_consumables != '[]' 
      AND option = 'C' AND option IS NOT NULL
      AND date(dd.created_date) BETWEEN '${start}' AND '${end}'
      GROUP BY dd.id, dd.dispense_consumables
    ) billing
    INNER JOIN jsonb_to_recordset(billing.dispense_consumables) AS dc("name" text, "quantity_consumed" int) ON TRUE
    GROUP BY dc.name 
    ORDER BY COUNT(*) DESC
    LIMIT 10;
  `,
    );

    const investigationLabQuery = queryDSWithSlave(
      dataSource,
      `SELECT 
      billing."testName" AS "category",
      COUNT(billing."testName") AS "count",
      SUM(billing.quantity) AS "totalQuantity",
      SUM(billing.amount_due) AS "totalAmountDue",
      SUM(billing.amount_paid) AS "totalAmountPaid",
      SUM(billing.amount_outstanding) AS "totalAmountOutstanding",
      SUM(billing.total_amount) AS "totalAmount"
    FROM (
      SELECT 
        i.id,
        inv."testName",
        inv.ref,
        SUM(bd.quantity) AS quantity,
        SUM(bd.amount_due) AS amount_due,
        SUM(bd.amount_paid) AS amount_paid,
        SUM(bd.amount_outstanding) AS amount_outstanding,
        SUM(bd.amount) AS total_amount,
        i.test_info
      FROM investigations i
      INNER JOIN jsonb_to_recordset(i.test_info) AS inv("testName" text, "ref" text) ON TRUE
      LEFT JOIN bill_details bd ON i.bill = bd.bill AND bd.reference::TEXT = inv.ref::TEXT
      ${applyFilterForHmoAndPartners({
        profile,
        filter,
        table: 'i',
      })}
      AND i.test_info IS NOT NULL
      AND date(i.created_date) BETWEEN '${start}' AND '${end}'
      GROUP BY i.id, i.test_info, inv."testName", inv.ref
    ) billing
     WHERE billing."testName" <> ''
    GROUP BY billing."testName"
    ORDER BY count DESC
    LIMIT 10;
  `,
    );

    const investigationRadQuery = queryDSWithSlave(
      dataSource,
      `SELECT 
      billing."examType" AS "category",
      COUNT(billing."examType") AS "count",
      SUM(billing.quantity) AS "totalQuantity",
      SUM(billing.amount_due) AS "totalAmountDue",
      SUM(billing.amount_paid) AS "totalAmountPaid",
      SUM(billing.amount_outstanding) AS "totalAmountOutstanding",
      SUM(billing.total_amount) AS "totalAmount"
    FROM (
      SELECT 
        i.id,
        inv."examType",
        inv.ref,
        SUM(bd.quantity) AS quantity,
        SUM(bd.amount_due) AS amount_due,
        SUM(bd.amount_paid) AS amount_paid,
        SUM(bd.amount_outstanding) AS amount_outstanding,
        SUM(bd.amount) AS total_amount,
        i.examination_type
      FROM investigations i
      INNER JOIN jsonb_to_recordset(i.examination_type) AS inv("examType" text, "ref" text) ON TRUE
      LEFT JOIN bill_details bd ON i.bill = bd.bill AND bd.reference::TEXT = inv.ref::TEXT
      ${applyFilterForHmoAndPartners({
        profile,
        filter,
        table: 'i',
      })} 
      AND i.examination_type IS NOT NULL
      AND date(i.created_date) BETWEEN '${start}' AND '${end}'
      GROUP BY i.id, i.examination_type, inv."examType", inv.ref
    ) billing
    WHERE billing."examType" <> ''
    GROUP BY billing."examType"
    ORDER BY count DESC
    LIMIT 10;
  `,
    );

    const admissionQuery = queryDSWithSlave(
      dataSource,
      `SELECT 
      dia AS "category",
      COUNT(dia) AS "count",
      SUM(billing.quantity) AS "totalQuantity",
      SUM(billing.amount_due) AS "totalAmountDue",
      SUM(billing.amount_paid) AS "totalAmountPaid",
      SUM(billing.amount_outstanding) AS "totalAmountOutstanding",
      SUM(billing.total_amount) AS "totalAmount"
    FROM (
      SELECT 
        a.id,
        SUM(b.quantity) AS quantity,
        SUM(b.amount_due) AS amount_due,
        SUM(b.amount_paid) AS amount_paid,
        SUM(b.amount_outstanding) AS amount_outstanding,
        SUM(b.amount) AS total_amount,
        a.admission_diagnosis
      FROM admissions a
      LEFT JOIN bill_details b ON a.bill = b.bill AND b.reference::TEXT = a.id::TEXT
      ${applyFilterForHmoAndPartners({
        profile,
        filter,
        table: 'a',
      })}
      AND a.admission_diagnosis IS NOT NULL
      AND a.admission_diagnosis != '[]' 
      AND date(a.created_date) BETWEEN '${start}' AND '${end}'
      GROUP BY a.id, a.admission_diagnosis
    ) billing,
    jsonb_to_recordset(billing.admission_diagnosis) AS ad(
      "diagnosisICD10" text,
      "diagnosisICD11" text,
      "diagnosisSNOMED" text
    ),
    UNNEST(ARRAY [ad."diagnosisICD10", ad."diagnosisICD11", ad."diagnosisSNOMED"]) AS dia 
    WHERE dia IS NOT NULL AND dia != ''
    GROUP BY dia
    ORDER BY "count" DESC
    LIMIT 10
  `,
    );

    const consultationQuery = queryDSWithSlave(
      dataSource,
      `SELECT 
      dia AS "category",
      COUNT(dia) AS "count",
      SUM(billing.quantity) AS "totalQuantity",
      SUM(billing.amount_due) AS "totalAmountDue",
      SUM(billing.amount_paid) AS "totalAmountPaid",
      SUM(billing.amount_outstanding) AS "totalAmountOutstanding",
      SUM(billing.total_amount) AS "totalAmount"
    FROM (
      SELECT 
        c.id,
        SUM(b.quantity) AS quantity,
        SUM(b.amount_due) AS amount_due,
        SUM(b.amount_paid) AS amount_paid,
        SUM(b.amount_outstanding) AS amount_outstanding,
        SUM(b.amount) AS total_amount,
        c.provisional_diagnosis
      FROM consultations c
      LEFT JOIN bill_details b ON c.bill = b.bill AND b.reference::TEXT = c.id::TEXT
      ${applyFilterForHmoAndPartners({
        profile,
        filter,
        table: 'c',
      })}
      AND c.provisional_diagnosis IS NOT NULL
      AND c.provisional_diagnosis != '[]' 
      AND date(c.created_date) BETWEEN '${start}' AND '${end}'
      GROUP BY c.id, c.provisional_diagnosis
    ) billing,
    jsonb_to_recordset(billing.provisional_diagnosis) AS cons(
      "diagnosisICD10" text,
      "diagnosisICD11" text,
      "diagnosisSNOMED" text
    ),
    UNNEST(ARRAY [cons."diagnosisICD10", cons."diagnosisICD11", cons."diagnosisSNOMED"]) AS dia 
    WHERE dia IS NOT NULL AND dia != ''
    GROUP BY dia
    ORDER BY "count" DESC
    LIMIT 10
  `,
    );

    const nursingServicesQuery = queryDSWithSlave(
      dataSource,
      `SELECT 
      procedure_type AS "category", 
      COUNT(*) AS "count",
      SUM(bd.quantity) AS "totalQuantity",
      SUM(bd.amount_due) AS "totalAmountDue",
      SUM(bd.amount_paid) AS "totalAmountPaid",
      SUM(bd.amount_outstanding) AS "totalAmountOutstanding",
      SUM(bd.amount) AS "totalAmount"
    FROM nursing_service_details
    LEFT JOIN nursing_service ON nursing_service_details.nursing_service_id = nursing_service.id
    LEFT JOIN bill_details bd ON nursing_service_details.billing = bd.id
    ${applyFilterForHmoAndPartners({
      profile,
      filter,
      table: 'nursing_service',
      hospitalCol: 'hospital_id',
    })}
    AND date(nursing_service_details.created_date) BETWEEN '${start}' AND '${end}'
    AND nursing_service_details.procedure_type IS NOT NULL 
    AND nursing_service_details.procedure_type != ''
    GROUP BY procedure_type
    ORDER BY count DESC
    LIMIT 10;
  `,
    );

    const medicalReportQuery = queryDSWithSlave(
      dataSource,
      `SELECT 
      billing."name" AS "category",
      COUNT(billing."name") AS "count",
      SUM(billing.quantity) AS "totalQuantity",
      SUM(billing.amount_due) AS "totalAmountDue",
      SUM(billing.amount_paid) AS "totalAmountPaid",
      SUM(billing.amount_outstanding) AS "totalAmountOutstanding",
      SUM(billing.total_amount) AS "totalAmount"
    FROM (
      SELECT 
        mr.id,
        rt."name",
        rt.ref,
        SUM(bd.quantity) AS quantity,
        SUM(bd.amount_due) AS amount_due,
        SUM(bd.amount_paid) AS amount_paid,
        SUM(bd.amount_outstanding) AS amount_outstanding,
        SUM(bd.amount) AS total_amount
      FROM medical_report mr
      INNER JOIN jsonb_to_recordset(mr.report_type) AS rt("name" text, "ref" text) ON TRUE
      LEFT JOIN bill_details bd ON mr.bill = bd.bill AND bd.reference::TEXT = rt.ref::TEXT
      ${applyFilterForHmoAndPartners({
        profile,
        filter,
        table: 'mr',
        hospitalCol: 'hospital_id',
        profileCol: 'profile_id',
      })}
      AND rt."name" IS NOT NULL AND rt."name" <> ''
      AND date(mr.created_date) BETWEEN '${start}' AND '${end}'
      GROUP BY mr.id, rt."name", rt.ref
    ) billing
    GROUP BY billing."name"
    ORDER BY count DESC
    LIMIT 10;
  `,
    );

    const investigationResult =
      await CustomServicesAnalyticsRepoMethods.getInvestigationStatusData(
        dataSource,
        profile,
        filter,
      );

    const [
      immunizationResult,
      procedureResult,
      investigationLabResult,
      investigationRadResult,
      admissionResult,
      consultationResult,
      medicationPrescribedResult,
      medicationDispenseResult,
      consumablePrescribedResult,
      consumableDispensedResult,
      nursingServiceResult,
      medicalReportResult,
    ] = await Promise.all([
      immunizationQuery,
      procedureQuery,
      investigationLabQuery,
      investigationRadQuery,
      admissionQuery,
      consultationQuery,
      medicationPrescribedQuery,
      medicationDispensedQuery,
      consumablePrescribedQuery,
      consumableDispensedQuery,
      nursingServicesQuery,
      medicalReportQuery,
    ]);

    return {
      immunizationResult,
      procedureResult,
      medicationPrescribedResult,
      medicationDispenseResult,
      consumablePrescribedResult,
      consumableDispensedResult,
      investigationLabResult,
      investigationRadResult,
      admissionResult,
      consultationResult,
      investigationResult,
      nursingServiceResult,
      medicalReportResult,
    };
  },

  async getConsultationData(
    dataSource: DataSource,
    profile: ProfileModel,
    filter: ServicesAnalyticsFilter,
  ): Promise<ServiceDurationData> {
    const {
      duration,
      startDate,
      endDate,
      diagnosisType,
      gender,
      inPatient,
      ageRange,
      orderBy = 'GP',
      diagnosisGroup,
    } = filter;
    type ListByConsultationDateAndDoctorNameResult = {
      consultationDate: string;
      doctorName: string;
      paymentType: string;
      patientName: string;
      clinifyId: string;
      priority: string;
      provisionalDiagnosis: {
        diagnosisICD10: {
          male: string[];
          female: string[];
        };
        diagnosisICD11: {
          male: string[];
          female: string[];
        };
        diagnosisSNOMED: {
          male: string[];
          female: string[];
        };
      };
      finalDiagnosis: ListByConsultationDateAndDoctorNameResult['provisionalDiagnosis'];
    };
    const emptyDiag = {
      diagnosisICD10: {
        male: [],
        female: [],
      },
      diagnosisICD11: {
        male: [],
        female: [],
      },
      diagnosisSNOMED: {
        male: [],
        female: [],
      },
    };

    const groupByDoctorNameAndConsultationDate = (
      list: ListByConsultationDateAndDoctorNameResult[],
    ) => {
      const result = {} as {
        [key: string]: ListByConsultationDateAndDoctorName;
      };
      list.forEach((item) => {
        const { consultationDate, doctorName, paymentType, clinifyId } = item;
        const dateKey = moment(consultationDate).format('DD/MMM/YYYY hh:mm A');
        const key = `${doctorName}-${dateKey}-${paymentType}-${clinifyId}`;
        if (!result[key]) {
          result[key] = {
            doctorName,
            consultationDate: dateKey,
            finalDiagnosis: [],
            provisionalDiagnosis: [],
            paymentType: item.paymentType,
            patientName: item.patientName,
            clinifyId: item.clinifyId,
            priority: item.priority,
          };
        }
        if (item.finalDiagnosis) {
          result[key].finalDiagnosis.push(
            ...Object.values(item.finalDiagnosis).reduce((acc, curr) => {
              acc.push(...Object.values(curr).flat());
              return acc;
            }, [] as string[]),
          );
        }
        if (item.provisionalDiagnosis) {
          result[key].provisionalDiagnosis.push(
            ...Object.values(item.provisionalDiagnosis).reduce((acc, curr) => {
              acc.push(...Object.values(curr).flat());
              return acc;
            }, [] as string[]),
          );
        }
      });

      let items = Object.values(result);
      items = items.map((item) => {
        item.finalDiagnosis = groupSameNames(item.finalDiagnosis);
        item.provisionalDiagnosis = groupSameNames(item.provisionalDiagnosis);
        return item;
      });
      return items;
    };
    const removeEmptyDiagnosis = (
      list: ListByConsultationDateAndDoctorNameResult['finalDiagnosis'][] = [],
    ) => {
      const newlist: typeof list = [];
      for (const item of list) {
        const values = Object.values(item).reduce((acc, curr) => {
          acc.push(...Object.values(curr).flat());
          return acc;
        }, [] as string[]);
        if (values.length) {
          newlist.push(item);
        }
      }
      return newlist;
    };
    const combineDiagnosis = (
      list: ListByConsultationDateAndDoctorNameResult['finalDiagnosis'][],
    ) => {
      if (!list?.length) {
        return emptyDiag;
      }
      const result =
        {} as ListByConsultationDateAndDoctorNameResult['finalDiagnosis'];
      list.forEach((item) => {
        Object.keys(item).forEach((key) => {
          if (!result[key]) {
            result[key] = {
              male: item[key].male,
              female: item[key].female,
            };
          } else {
            result[key] = {
              male: [...(result[key].male || []), ...(item[key].male || [])],
              female: [
                ...(result[key].female || []),
                ...(item[key].female || []),
              ],
            };
          }
        });
      });
      return result;
    };
    const diagSelected = [];
    if (!diagnosisGroup || diagnosisGroup === DiagnosisGroup.FINAL) {
      diagSelected.push('final');
    }
    if (!diagnosisGroup || diagnosisGroup === DiagnosisGroup.PROVISIONAL) {
      diagSelected.push('initial');
    }

    const { start, end } = formatDate(startDate, endDate);
    if (orderBy === 'GP') {
      let diagnosisSource = [];
      if (diagnosisGroup === DiagnosisGroup.FINAL) {
        diagnosisSource.push('consultations.final_diagnosis');
      } else if (diagnosisGroup === DiagnosisGroup.PROVISIONAL) {
        diagnosisSource.push('consultations.provisional_diagnosis');
      } else {
        diagnosisSource = [
          'consultations.final_diagnosis',
          'consultations.provisional_diagnosis',
        ];
      }

      const totalConsultationQuery = await queryDSWithSlave(
        dataSource,
        `SELECT COUNT(*) AS "totalConsultations"
      FROM consultations
        LEFT JOIN profiles ON consultations.profile = profiles.id  
      LEFT JOIN details ON profiles.details = details.id
      LEFT JOIN treatment_plans ON consultations.id = treatment_plans.consultation
      ${applyFilterForHmoAndPartners({
        profile,
        filter,
        table: 'consultations',
      })}
      AND date(consultations.created_date) BETWEEN '${start}' AND '${end}'
       ${
         !!gender && gender !== GenderOption.All
           ? `AND profiles.gender = '${gender}'`
           : ''
       }
      ${inPatientQueryString('consultations', inPatient)}
      ${ageRangeQueryString(ageRange)}
      ${
        diagnosisSource.length === 1 && !diagnosisType
          ? `AND EXISTS (
        SELECT 1
           FROM jsonb_array_elements(${diagnosisSource[0]}) AS diag(elem)
        WHERE   (elem->>'diagnosisICD10' IS NOT NULL
              OR elem->>'diagnosisICD11' IS NOT NULL
              OR elem->>'diagnosisSNOMED' IS NOT NULL)
          )`
          : ''
      }
      ${
        diagnosisType
          ? `AND EXISTS (
        SELECT 1
           FROM jsonb_array_elements(${diagnosisSource.join(
             ' || ',
           )}) AS diag(elem)
        WHERE  elem->>'${diagnosisType}' IS NOT NULL 
          )
          `
          : ''
      }
      `,
      );
      const averageNumberOfConsultationsResult = await queryDSWithSlave(
        dataSource,
        `SELECT AVG(consultationperday) AS "averageNumberOfConsultations"
      FROM ( SELECT 
          COUNT(*) AS "consultationperday"
        FROM consultations
          ${applyFilterForHmoAndPartners({
            profile,
            filter,
            table: 'consultations',
          })}
          AND date(consultations.created_date) BETWEEN '${start}' AND '${end}'
        GROUP BY DATE(consultations.created_date)
      ) AS a;
    `,
      );
      const diagnosisFiltersFinal = [];
      const diagnosisFiltersProvisional = [];
      if (diagnosisType) {
        if (diagnosisGroup === DiagnosisGroup.FINAL) {
          diagnosisFiltersFinal.push(
            `final_elem->>'${diagnosisType}' IS NOT NULL`,
          );
        } else if (diagnosisGroup === DiagnosisGroup.PROVISIONAL) {
          diagnosisFiltersProvisional.push(
            `provi_elem->>'${diagnosisType}' IS NOT NULL`,
          );
        } else {
          diagnosisFiltersFinal.push(
            `final_elem->>'${diagnosisType}' IS NOT NULL`,
          );
          diagnosisFiltersProvisional.push(
            `provi_elem->>'${diagnosisType}' IS NOT NULL`,
          );
        }
      } else {
        if (diagnosisGroup === DiagnosisGroup.FINAL) {
          diagnosisFiltersFinal.push(
            `final_elem->>'diagnosisICD10' IS NOT NULL
          OR final_elem->>'diagnosisICD11' IS NOT NULL
          OR final_elem->>'diagnosisSNOMED' IS NOT NULL`,
          );
        } else if (diagnosisGroup === DiagnosisGroup.PROVISIONAL) {
          diagnosisFiltersProvisional.push(
            `provi_elem->>'diagnosisICD10' IS NOT NULL
          OR provi_elem->>'diagnosisICD11' IS NOT NULL
          OR provi_elem->>'diagnosisSNOMED' IS NOT NULL`,
          );
        }
      }
      const result: ListByConsultationDateAndDoctorNameResult[] =
        await queryDSWithSlave(
          dataSource,
          `SELECT 
           ${
             diagSelected.includes('final')
               ? `final_diag_json."finalDiagnosis",`
               : ''
           }
           ${
             diagSelected.includes('initial')
               ? `provi_diag_json."provisionalDiagnosis",`
               : ''
           }
           consultations.consultation_date AS "consultationDate",
           consultations.doctor_name AS "doctorName",
           consultations.priority AS "priority",
           profiles.id AS "profileId",
           profiles.clinify_id AS "clinifyId",
           profiles.full_name AS "patientName",
           bill_details."paymentType" AS "paymentType"
        FROM consultations
          LEFT JOIN bills ON consultations.bill = bills.id
          LEFT JOIN bill_details ON bills.id = bill_details.bill  AND bill_details."bill_type" = 'Consultation'
          LEFT JOIN profiles ON consultations.profile = profiles.id
          LEFT JOIN details ON profiles.details = details.id
          LEFT JOIN treatment_plans ON consultations.id = treatment_plans.consultation
          ${
            diagSelected.includes('initial')
              ? `LEFT JOIN LATERAL (
                    SELECT ${consultationDiagnosisObjStatement(
                      'provi_elem',
                      diagnosisType,
                    )} AS "provisionalDiagnosis"
                    FROM jsonb_array_elements(consultations.provisional_diagnosis) AS provi_diag(provi_elem)
                    ${
                      diagnosisFiltersProvisional.length
                        ? `WHERE (${diagnosisFiltersProvisional.join(' OR ')})`
                        : ''
                    }
                ) provi_diag_json ON TRUE`
              : ''
          }
          ${
            diagSelected.includes('final')
              ? `LEFT JOIN LATERAL (
                    SELECT ${consultationDiagnosisObjStatement(
                      'final_elem',
                      diagnosisType,
                    )} AS "finalDiagnosis"
                    FROM jsonb_array_elements(consultations.final_diagnosis) AS final_diag(final_elem) 
                    ${
                      diagnosisFiltersFinal.length
                        ? `WHERE (${diagnosisFiltersFinal.join(' OR ')})`
                        : ''
                    }
                ) final_diag_json ON TRUE`
              : ''
          }
        ${applyFilterForHmoAndPartners({
          profile,
          filter,
          table: 'consultations',
        })}
           
            
            
            ${
              !!gender && gender !== GenderOption.All
                ? `AND profiles.gender = '${gender}'`
                : ''
            }
            ${inPatientQueryString('consultations', inPatient)}
            ${ageRangeQueryString(ageRange)}
            AND date(consultations.created_date) BETWEEN '${start}' AND '${end}'
            GROUP BY   ${
              diagSelected.includes('initial')
                ? 'provi_diag_json."provisionalDiagnosis",'
                : ''
            }
            ${
              diagSelected.includes('final')
                ? 'final_diag_json."finalDiagnosis",'
                : ''
            }
            consultations.doctor_name, "consultationDate", profiles.id, consultations.id, bill_details."paymentType"
          
    `,
        );
      return {
        averageNumber:
          averageNumberOfConsultationsResult[0]?.averageNumberOfConsultations,
        data: [
          {
            name: extractPeriod(new Date(start), duration),
            totalConsultations:
              totalConsultationQuery[0]?.totalConsultations || 0,
          },
        ],
        finalDiagnosis: combineDiagnosis(
          removeEmptyDiagnosis(
            result.map((v) => v.finalDiagnosis || emptyDiag).flat(),
          ),
        ),
        provisionalDiagnosis: combineDiagnosis(
          removeEmptyDiagnosis(
            result.map((v) => v.provisionalDiagnosis || emptyDiag).flat(),
          ),
        ),
        byConsultationDateAndDoctorName:
          groupByDoctorNameAndConsultationDate(result),
      };
    }
    const diagnosisSource = diagSelected[0];
    const code = (diagnosisType || '').replace('diagnosis', '').toLowerCase();
    const hasICD = [DiagnosisGroup.FINAL, DiagnosisGroup.PROVISIONAL].includes(
      diagnosisGroup,
    );
    const diagnosisClause =
      [
        hasICD && !diagnosisType
          ? `AND (onco.${diagnosisSource}_diagnosis_icd10 IS NOT NULL OR 
           onco.${diagnosisSource}_diagnosis_icd11 IS NOT NULL OR 
           onco.${diagnosisSource}_diagnosis_snomed IS NOT NULL)`
          : '',
        diagnosisType && hasICD
          ? `AND onco.${diagnosisSource}_diagnosis_${code} IS NOT NULL`
          : '',
        diagnosisType && !hasICD
          ? `AND (onco.final_diagnosis_${code} IS NOT NULL OR 
          onco.initial_diagnosis_${code} IS NOT NULL)`
          : '',
      ].filter((v) => !!v)[0] || '';
    const totalConsultationQuery = await queryDSWithSlave(
      dataSource,
      `SELECT COUNT(DISTINCT onco.id) AS "totalConsultations"
      FROM
       oncology_consultation_histories onco
        LEFT JOIN profiles ON onco.profile = profiles.id  
        LEFT JOIN details ON profiles.details = details.id
      ${applyFilterForHmoAndPartners({ profile, filter, table: 'onco' })}
      AND date(onco.created_date) BETWEEN '${start}' AND '${end}'
       ${
         !!gender && gender !== GenderOption.All
           ? `AND profiles.gender = '${gender}'`
           : ''
       }
       ${diagnosisClause}
      ${ageRangeQueryString(ageRange)}
        ${inPatientQueryString('onco', inPatient)}
      `,
    );

    const averageNumberOfConsultationsResult = await queryDSWithSlave(
      dataSource,
      `SELECT AVG(consultationperday) AS "averageNumberOfConsultations"
      FROM ( SELECT 
          COUNT(*)::int AS "consultationperday"
        FROM oncology_consultation_histories
        ${applyFilterForHmoAndPartners({
          profile,
          filter,
          table: 'oncology_consultation_histories',
        })}
          AND date(oncology_consultation_histories.created_date) BETWEEN '${start}' AND '${end}'
        GROUP BY DATE(oncology_consultation_histories.created_date)
      ) AS a;
    `,
    );
    let byChemoDiagnosis: ListByChemoDiagnosis[] = [];

    let diagnosisResult: ListByConsultationDateAndDoctorNameResult[] = [];
    let byChemoDiagnosisAndDoctorName: ChemoDiagnosisListByConsultationDateAndDoctorName[] =
      [];

    if (!diagnosisGroup || diagnosisGroup === DiagnosisGroup.CHEMO_DIAGNOSIS) {
      byChemoDiagnosis = await queryDSWithSlave(
        dataSource,
        `SELECT 
        SUM(CASE WHEN profiles.gender = 'Male' THEN 1 ELSE 0 END)::int AS "male",
        SUM(CASE WHEN profiles.gender = 'Female' THEN 1 ELSE 0 END)::int AS "female",
        onco_drugs.chemo_diagnosis AS "chemoDiagnosis"
      FROM
       oncology_consultation_histories onco
        LEFT JOIN profiles ON onco.profile = profiles.id  
        LEFT JOIN details ON profiles.details = details.id
        INNER JOIN oncology_chemo_drug onco_drugs ON onco_drugs.oncology_consultation_history_id = onco.id  AND onco_drugs.chemo_diagnosis IS NOT NULL
      ${applyFilterForHmoAndPartners({ profile, filter, table: 'onco' })}
      AND date(onco.created_date) BETWEEN '${start}' AND '${end}'
       ${
         !!gender && gender !== GenderOption.All
           ? `AND profiles.gender = '${gender}'`
           : ''
       }
      ${ageRangeQueryString(ageRange)}
      ${inPatientQueryString('onco', inPatient)}
      GROUP BY onco_drugs.chemo_diagnosis
      `,
      );
      byChemoDiagnosisAndDoctorName = await queryDSWithSlave(
        dataSource,
        `SELECT 
        TO_CHAR(onco.consultation_date, 'DD/Mon/YYYY') AS "consultationDate",
        onco.doctor_name AS "doctorName",
        ARRAY_AGG(onco_drugs.chemo_diagnosis) AS "chemoDiagnosis"
      FROM
       oncology_consultation_histories onco
        LEFT JOIN profiles ON onco.profile = profiles.id  
        LEFT JOIN details ON profiles.details = details.id
        INNER JOIN oncology_chemo_drug onco_drugs ON onco_drugs.oncology_consultation_history_id = onco.id  AND onco_drugs.chemo_diagnosis IS NOT NULL
      ${applyFilterForHmoAndPartners({ profile, filter, table: 'onco' })}
      AND date(onco.created_date) BETWEEN '${start}' AND '${end}'
       ${
         !!gender && gender !== GenderOption.All
           ? `AND profiles.gender = '${gender}'`
           : ''
       }
      ${ageRangeQueryString(ageRange)}
      ${inPatientQueryString('onco', inPatient)}
      GROUP BY "consultationDate", onco.doctor_name
      `,
      );
    }

    const oncologyDiagnosisStatement = (
      diagnosisKey: string,
      diagnosisType: DiagnosisType,
    ) => {
      const diagnosisICD10 = `'diagnosisICD10',jsonb_build_object(
        'female', ARRAY_REMOVE(ARRAY_AGG(CASE WHEN profiles.gender = 'Female' THEN onco.${diagnosisKey}_diagnosis_icd10 END), NULL),
        'male', ARRAY_REMOVE(ARRAY_AGG(CASE WHEN profiles.gender = 'Male' THEN onco.${diagnosisKey}_diagnosis_icd10 END), NULL)
      )`;
      const diagnosisICD11 = `'diagnosisICD11',
      jsonb_build_object(
        'female', ARRAY_REMOVE(ARRAY_AGG(CASE WHEN profiles.gender = 'Female' THEN onco.${diagnosisKey}_diagnosis_icd11 END), NULL),
        'male', ARRAY_REMOVE(ARRAY_AGG(CASE WHEN profiles.gender = 'Male' THEN onco.${diagnosisKey}_diagnosis_icd11 END), NULL)
      )`;
      const diagnosisSNOMED = `'diagnosisSNOMED',
      jsonb_build_object(
        'female', ARRAY_REMOVE(ARRAY_AGG(CASE WHEN profiles.gender = 'Female' THEN onco.${diagnosisKey}_diagnosis_snomed END), NULL),
        'male', ARRAY_REMOVE(ARRAY_AGG(CASE WHEN profiles.gender = 'Male' THEN onco.${diagnosisKey}_diagnosis_snomed END), NULL)
      )`;
      let list = [diagnosisICD10, diagnosisICD11, diagnosisSNOMED];
      if (diagnosisType === DiagnosisType.ICD10) {
        list = [diagnosisICD10];
      } else if (diagnosisType === DiagnosisType.ICD11) {
        list = [diagnosisICD11];
      } else if (diagnosisType === DiagnosisType.SNOMED) {
        list = [diagnosisSNOMED];
      }
      return `jsonb_build_object(${list.join(',')})`;
    };

    diagnosisResult = await queryDSWithSlave(
      dataSource,
      `SELECT 
       ${
         diagSelected.includes('final')
           ? `${oncologyDiagnosisStatement(
               'final',
               diagnosisType,
             )} AS "finalDiagnosis",`
           : ''
       }
        ${
          diagSelected.includes('initial')
            ? `${oncologyDiagnosisStatement(
                'initial',
                diagnosisType,
              )} AS "provisionalDiagnosis",`
            : ''
        }
        onco.consultation_date AS "consultationDate",
        onco.doctor_name AS "doctorName",
        onco.priority AS "priority",
        bill_details."paymentType" AS "paymentType",
        profiles.full_name AS "patientName",
        profiles.clinify_id AS "clinifyId",
        profiles.id AS "profileId"
      FROM
        oncology_consultation_histories onco
        LEFT JOIN bills ON onco.bill = bills.id
        LEFT JOIN bill_details ON bills.id = bill_details.bill
        LEFT JOIN profiles ON onco.profile = profiles.id  
        LEFT JOIN details ON profiles.details = details.id
      ${applyFilterForHmoAndPartners({ profile, filter, table: 'onco' })}
     ${diagnosisClause}
      
      AND date(onco.created_date) BETWEEN '${start}' AND '${end}'
       ${
         !!gender && gender !== GenderOption.All
           ? `AND profiles.gender = '${gender}'`
           : ''
       }
      ${ageRangeQueryString(ageRange)}
      ${inPatientQueryString('onco', inPatient)}
      GROUP BY "consultationDate", onco.doctor_name, bill_details."paymentType", profiles.id, onco.priority
      `,
    );

    const finalDiagnosis = combineDiagnosis(
      removeEmptyDiagnosis(
        diagnosisResult.map((v) => v.finalDiagnosis || emptyDiag).flat(),
      ),
    );
    const provisionalDiagnosis = combineDiagnosis(
      removeEmptyDiagnosis(
        diagnosisResult.map((v) => v.provisionalDiagnosis || emptyDiag).flat(),
      ),
    );

    return {
      averageNumber:
        averageNumberOfConsultationsResult[0]?.averageNumberOfConsultations,
      data: [
        {
          name: extractPeriod(new Date(start), duration),
          totalConsultations:
            totalConsultationQuery[0]?.totalConsultations || 0,
        },
      ],
      byChemoDiagnosis,
      finalDiagnosis,
      provisionalDiagnosis,
      byConsultationDateAndDoctorName:
        groupByDoctorNameAndConsultationDate(diagnosisResult),
      byChemoDiagnosisAndDoctorName,
    };
  },

  async getProcedureData(
    dataSource: DataSource,
    profile: ProfileModel,
    filter: ServicesAnalyticsFilter,
  ): Promise<ServiceDurationData> {
    const { duration, startDate, endDate, gender, inPatient, ageRange } =
      filter;

    const { start, end } = formatDate(startDate, endDate);

    const averageDuration = await queryDSWithSlave(
      dataSource,
      `SELECT AVG(procedureperduration) AS "averageDurationOfProcedures"
      FROM ( SELECT 
          COUNT(duration) AS "procedureperduration"
        FROM surgeries
        WHERE date(created_date) BETWEEN '${start}' AND '${end}'
        GROUP BY DATE(created_date)
      ) AS a;
  `,
    );

    type ProcedureNames = {
      name: string;
      amount: string;
      paymentType: string;
      procedureId: string;
      patientName: string;
      clinifyId: string;
      gender: string;
      operationDate: string;
      operatedBy: string;
      quantity: string;
      priority: string;
    };
    let procedureNames: ProcedureNames[] = await queryDSWithSlave(
      dataSource,
      `SELECT pt ->> 'type' AS "name",
        bd.amount_due AS "amount",
        "bd"."paymentType" AS "paymentType",
        surgeries.id AS "procedureId",
        profiles.full_name AS "patientName",
        profiles.clinify_id AS "clinifyId",
        profiles.gender AS "gender",
        surgeries.surgery_date AS "operationDate",
        surgeries.operated_by AS "operatedBy",
        1 AS "quantity",
        pt ->> 'priority' AS "priority"
      FROM
        surgeries 
        LEFT JOIN LATERAL jsonb_array_elements(surgeries.procedure_type) pt ON TRUE
        LEFT JOIN bill_details bd ON bd.reference = pt ->> 'ref'
        LEFT JOIN profiles ON surgeries.profile = profiles.id
        LEFT JOIN details ON profiles.details = details.id
      ${applyFilterForHmoAndPartners({ profile, filter, table: 'surgeries' })}
      AND date(surgeries.created_date) BETWEEN '${start}' AND '${end}'
      ${
        !!gender && gender !== GenderOption.All
          ? `AND profiles.gender = '${gender}'`
          : ''
      }
      ${inPatientQueryString('surgeries', inPatient)}
      ${ageRangeQueryString(ageRange)}
      ORDER BY surgeries.surgery_date DESC
    `,
    );

    const _procedureNames = procedureNames.reduce((acc, curr) => {
      const date = moment(curr.operationDate).format('DD/MMM/YYYY hh:mm A');
      const key = `${curr.name}-${date}-${curr.operatedBy}-${curr.paymentType}-${curr.clinifyId}`;
      if (!acc[key]) {
        acc[key] = curr;
      } else {
        acc[key].quantity = (
          parseFloat(acc[key].quantity || '0') +
          parseFloat(curr.quantity || '0')
        ).toString();
        acc[key].amount = (
          parseFloat(acc[key].amount || '0') + parseFloat(curr.amount || '0')
        ).toString();
      }

      return acc;
    }, {} as { [key: string]: ProcedureNames });
    procedureNames = Object.values(_procedureNames);

    return {
      averageNumber: averageDuration[0]?.averageDurationOfProcedures,
      data: [
        {
          name: extractPeriod(new Date(start), duration),
          totalProcedures: procedureNames.length,
        },
      ],
      byPatientName: procedureNames.map((v) => ({
        name: v.name,
        clinifyId: v.clinifyId,
        gender: v.gender,
        operationDate: moment(v.operationDate).format('DD/MMM/YYYY hh:mm A'),
        operatedBy: v.operatedBy,
        patientName: v.patientName,
        totalAmount: parseFloat(v.amount || '0'),
        quantity: v.quantity,
        paymentType: v.paymentType,
        priority: v.priority,
      })),
    };
  },

  async getRadiologyInvestigationData(
    dataSource: DataSource,
    profile: ProfileModel,
    filter: ServicesAnalyticsFilter,
  ): Promise<ServiceSummaryWithList> {
    const { duration, startDate, endDate, gender, inPatient, ageRange } =
      filter;
    const { start, end } = formatDate(startDate, endDate);

    type ExamResults = {
      examType: string;
      patientId: string;
      gender: string;
      amount: string;
      patientName: string;
      clinifyId: string;
      paymentType: string;
      examinationDate: string;
      details: RadiologyExamDetail[];
      radiographerName: string;
    }[];
    let examResults: ExamResults = await queryDSWithSlave(
      dataSource,
      `SELECT 	exam->>'examType' as "examType",
        profiles.id,
        profiles.gender as "gender",
        bill_details.amount_due as "amount",
        profiles.full_name as "patientName",
        profiles.clinify_id as "clinifyId",
        bill_details."paymentType",
        radiology_results.details
      FROM
        investigations
        LEFT JOIN radiology_results ON investigations.radiology_result = radiology_results.id
        LEFT JOIN profiles ON investigations.profile = profiles.id
        LEFT JOIN details ON profiles.details = details.id
        LEFT JOIN LATERAL jsonb_array_elements(examination_type) AS exam ON TRUE
        LEFT JOIN bill_details ON exam ->> 'ref' = bill_details.reference
      ${applyFilterForHmoAndPartners({
        profile,
        filter,
        table: 'investigations',
      })}
      AND request_type = 'Radiology'
      AND investigations.status = 'Approved'
      AND date(investigations.created_date) BETWEEN '${start}' AND '${end}'
      ${
        !!gender && gender !== GenderOption.All
          ? `AND profiles.gender = '${gender}'`
          : ''
      }
      ${inPatientQueryString('investigations', inPatient)}
      ${ageRangeQueryString(ageRange)}
    `,
    );
    examResults = examResults.map((item) => ({
      ...item,
      examinationDate: item.details?.find((v) => v.examType === item.examType)
        ?.examinationDate
        ? moment(
            item.details.find((v) => v.examType === item.examType)
              ?.examinationDate,
          ).format('DD/MMM/YYYY hh:mm A')
        : '',
      radiographerName: item.details?.find((v) => v.examType === item.examType)
        ?.radiographerName,
    }));
    const listByPatient = groupDataByCategoryWithMetrics(
      examResults,
      ['examType', 'patientId', 'testDate', 'performedBy'],
      {
        name: (items) => items[0].examType,
        count: (items) => items.length,
        totalAmount: (items) =>
          items.reduce((acc, curr) => acc + parseFloat(curr.amount || '0'), 0),
        quantity: (items) =>
          items.reduce(
            (acc, curr) => acc + parseFloat(curr.quantity || '1'),
            0,
          ),
        patientName: (items) => items[0].patientName,
        clinifyId: (items) => items[0].clinifyId,
        paymentType: (items) => items[0].paymentType,
        examinationDate: (items) => items[0].examinationDate,
        radiographerName: (items) => items[0].radiographerName,
      },
    );

    function groupExamsByRadiographerAndDate(
      data: ExamResults,
    ): ListByExaminationDate[] {
      const groupedResults: { [key: string]: ListByExaminationDate } = {};

      data.forEach((exam) => {
        const dateOnly = exam.examinationDate
          ? moment(new Date(exam.examinationDate)).format('DD/MMM/YYYY')
          : '';

        const key = `${exam.radiographerName}-${dateOnly}`;

        if (!groupedResults[key]) {
          groupedResults[key] = {
            radiographerName: exam.radiographerName,
            examinationDate: dateOnly,
            investigationNames: [],
          };
        }

        const existingExams = groupedResults[key].investigationNames;
        const examCount = existingExams.filter(
          (name) => name === exam.examType,
        ).length;

        existingExams.push(`${exam.examType} (${examCount + 1})`);
      });

      return Object.values(groupedResults);
    }

    const byExaminationDate = groupExamsByRadiographerAndDate(examResults);

    const sum =
      (listByPatient.female?.length || 0) + (listByPatient.male?.length || 0);
    return {
      servicesSummary: [
        {
          name: extractPeriod(new Date(startDate), duration),
          totalRadiology: sum,
        },
      ],
      list: {
        examTypes: listByPatient as any,
        byExaminationDate,
        byPatientName: listByPatient.female
          .concat(listByPatient.male)
          .sort(
            (a, b) =>
              a.examinationDate?.localeCompare(b.examinationDate || '') || 0,
          )
          .map((item) => ({
            ...item,
            quantity: item.quantity.toString(),
          })),
      },
    };
  },

  async getLaboratoryInvestigationData(
    dataSource: DataSource,
    profile: ProfileModel,
    filter: ServicesAnalyticsFilter,
  ): Promise<ServiceSummaryWithList> {
    const { duration, startDate, endDate, gender, inPatient, ageRange } =
      filter;
    const { start, end } = formatDate(startDate, endDate);
    type LaboratoryResponse = {
      testInfo: InvestigationTestInfoInput[];
      details: LabTestDetail[];
      gender: string;
    }[];
    type ResultsByPatient = {
      testName: string;
      patientId: string;
      gender: string;
      amount: string;
      patientName: string;
      clinifyId: string;
      paymentType: string;
      investigationId: string;
      details: LabTestDetail[];
      testDate: string;
      performedBy: string;
    }[];
    let resultsByPatient: ResultsByPatient = await queryDSWithSlave(
      dataSource,
      `SELECT    lab_info ->> 'testName' as "testName",
        profiles.id,
        profiles.gender as "gender",
        bill_details.amount_due as "amount",
        profiles.full_name as "patientName",
        profiles.clinify_id as "clinifyId",
        bill_details."paymentType", 
        lab_results.details,
        investigations.id as "investigationId"
      FROM
        investigations
        LEFT JOIN lab_results ON investigations.lab_result = lab_results.id
        LEFT JOIN profiles ON investigations.profile = profiles.id
        LEFT JOIN details ON profiles.details = details.id
        LEFT JOIN LATERAL jsonb_array_elements(test_info) AS lab_info ON TRUE
        LEFT JOIN bill_details ON lab_info->> 'ref' = bill_details.reference
      ${applyFilterForHmoAndPartners({
        profile,
        filter,
        table: 'investigations',
      })}
      AND request_type = 'Laboratory'
      AND investigations.status = 'Approved'
      AND date(investigations.created_date) BETWEEN '${start}' AND '${end}'
      ${
        !!gender && gender !== GenderOption.All
          ? `AND profiles.gender = '${gender}'`
          : ''
      }
      ${inPatientQueryString('investigations', inPatient)}
      ${ageRangeQueryString(ageRange)}
    `,
    );
    const testInfos: LaboratoryResponse = await queryDSWithSlave(
      dataSource,
      `SELECT test_info as "testInfo", 
      profiles.gender as "gender",
      investigations."id",
      investigations.request_date as "requestDate",
      investigations.ordered_by as "requestedBy",
      lab_results.details,
      jsonb_array_length(investigations.test_info) as "quantity",
      bill_details."paymentType",
      bill_details.amount_due as "amount"
       FROM investigations
       LEFT JOIN lab_results ON investigations.lab_result = lab_results.id
      LEFT JOIN profiles ON investigations.profile = profiles.id
      LEFT JOIN details ON profiles.details = details.id
      LEFT JOIN bill_details ON investigations.bill = bill_details.id
      ${applyFilterForHmoAndPartners({
        profile,
        filter,
        table: 'investigations',
      })}
      AND request_type = 'Laboratory'
      AND investigations.status = 'Approved'
      AND date(investigations.created_date) BETWEEN '${start}' AND '${end}'
      ${
        !!gender && gender !== GenderOption.All
          ? `AND profiles.gender = '${gender}'`
          : ''
      }
      ${inPatientQueryString('investigations', inPatient)}
      ${ageRangeQueryString(ageRange)}
    `,
    );

    const groupByLabTestDate = (list: LaboratoryResponse) => {
      const result = {} as {
        [key: string]: ListByLabTestDate;
      };
      list
        .filter((v) => v.details)
        .forEach((item) => {
          const { details } = item;
          details.forEach((detail) => {
            const { testDate, testName, verifiedBy } = detail;
            const dateKey = new Date(testDate).toISOString().split('T')[0];
            const key = `${verifiedBy}-${dateKey}`;
            if (!result[key]) {
              result[key] = {
                testVerifiedBy: verifiedBy,
                testDate: moment(testDate).format('DD/MMM/YYYY'),
                investigationNames: [],
              };
            }
            result[key].investigationNames.push(testName);
          });
        });

      let items = Object.values(result);
      items = items.map((item) => {
        item.investigationNames = groupSameNames(item.investigationNames);
        return item;
      });
      return items;
    };
    resultsByPatient = resultsByPatient.map((item) => ({
      ...item,
      testDate: item.details?.find((v) => v.testName === item.testName)
        ?.testDate as unknown as string,
      performedBy: item.details?.find((v) => v.testName === item.testName)
        ?.performedBy,
    }));

    const listByPatient = groupDataByCategoryWithMetrics(
      resultsByPatient,
      ['testName', 'patientId', 'testDate', 'performedBy'],
      {
        name: (items) => items[0].testName,
        count: (items) => items.length,
        totalAmount: (items) =>
          items.reduce((acc, curr) => acc + parseFloat(curr.amount || '0'), 0),
        quantity: (items) =>
          items.reduce(
            (acc, curr) => acc + parseFloat(curr.quantity || '1'),
            0,
          ),
        patientName: (items) => items[0].patientName,
        clinifyId: (items) => items[0].clinifyId,
        paymentType: (items) => items[0].paymentType,
        testDate: (items) => items[0].testDate,
        performedBy: (items) => items[0].performedBy,
      },
    );
    const byLabTestDate = groupByLabTestDate(testInfos);
    const list = groupByGender('testInfo', testInfos, 'testName');
    const sum = (list.female?.length || 0) + (list.male?.length || 0);
    return {
      servicesSummary: [
        {
          name: extractPeriod(new Date(startDate), duration),
          totalLaboratory: sum,
        },
      ],
      list: {
        testInfos: listByPatient as any,
        byLabTestDate,
        byPatientName: listByPatient.female
          .concat(listByPatient.male)
          .sort((a, b) => a.testDate?.localeCompare(b.testDate || '') || 0)
          .map((item) => ({
            ...item,
            quantity: item.quantity.toString(),
            testDate: moment(item.testDate).format('DD/MMM/YYYY hh:mm A'),
          })),
      },
    };
  },

  async getImmunizationData(
    dataSource: DataSource,
    profile: ProfileModel,
    filter: ServicesAnalyticsFilter,
  ): Promise<ServiceSummaryWithList> {
    const { duration, startDate, endDate, gender, inPatient, ageRange } =
      filter;

    const { start, end } = formatDate(startDate, endDate);

    type ImmunizationsResponse = {
      immunizationName: string;
      gender: string;
      admistratorName: string;
      administrationDate: string;
      routeOfAdministration: string;
      amount: string;
      quantity: string;
    }[];
    const result: ImmunizationsResponse = await queryDSWithSlave(
      dataSource,
      `SELECT
        immunization_name AS "immunizationName",
        profiles."gender" AS gender ,
        administered_date AS "administrationDate",
        "method" AS "routeOfAdministration",
        "administrator_name" AS "admistratorName",
        bill_details.amount_due as "amount",
        immunization_details.quantity as "quantity"
      FROM immunization_details
        LEFT JOIN immunizations ON immunization_details.immunization_id = immunizations.id
        LEFT JOIN profiles ON immunizations.profile = profiles.id  
        LEFT JOIN details ON profiles.details = details.id
        LEFT JOIN bill_details ON immunization_details.billing = bill_details.id
        ${applyFilterForHmoAndPartners({
          profile,
          filter,
          table: 'immunizations',
        })}
      
      ${
        !!gender && gender !== GenderOption.All
          ? `AND profiles.gender = '${gender}'`
          : ''
      }
      ${inPatientQueryString('immunizations', inPatient)}
      ${ageRangeQueryString(ageRange)}
      AND date(immunization_details.created_date) BETWEEN '${start}' AND '${end}'
    `,
    );
    const groupByImmunizationAdmistrationDateAndAdmistratorName = (
      list: ImmunizationsResponse,
    ) => {
      const result = {} as {
        [key: string]: ListByImmunizationAdmistrationDateAndAdmistratorName;
      };
      list.forEach((item) => {
        const {
          routeOfAdministration,
          administrationDate,
          admistratorName,
          immunizationName,
          amount,
          quantity,
        } = item;
        const dateKey = new Date(administrationDate)
          .toISOString()
          .split('T')[0];
        const key = `${admistratorName}-${dateKey}`;
        if (!result[key]) {
          const totalAmount = parseFloat(amount || '0');
          const totalQuantity = parseFloat(quantity || '0');
          result[key] = {
            admistratorName,
            administrationDate:
              moment(administrationDate).format('DD/MMM/YYYY'),
            immunizationNames: [],
            routeOfAdministration,
            totalAmount: (result[key]?.totalAmount || 0) + totalAmount,
            totalQuantity: (result[key]?.totalQuantity || 0) + totalQuantity,
          };
        }
        result[key].immunizationNames.push(immunizationName);
      });

      let items = Object.values(result);
      items = items.map((item) => {
        item.immunizationNames = groupSameNames(item.immunizationNames);
        return item;
      });
      return items;
    };
    type ImmunizationNamesByPatientName = {
      name: string;
      administrationDate: string;
      admistratorName: string;
      patientName: string;
      patientId: string;
      gender: string;
      clinifyId: string;
      paymentType: string;
      totalAmount: number;
      quantity: string;
      priority: string;
    };
    const immunizationNamesByPatientName: ImmunizationNamesByPatientName[] =
      await queryDSWithSlave(
        dataSource,
        `SELECT immunization_name as "name",
       administered_date as "administrationDate",
       "administrator_name" as "admistratorName",
       profiles.full_name as "patientName",
       profiles.id as "patientId",
       profiles.gender as "gender",
       profiles.clinify_id as "clinifyId",
       bill_details."paymentType" as "paymentType",
       SUM(bill_details.amount_due) as "totalAmount",
       SUM(CASE 
           WHEN immunization_details.quantity = '' THEN 0
           WHEN immunization_details.quantity IS NULL THEN 0 
           ELSE immunization_details.quantity::float 
       END) as "quantity",
        immunization_details.priority as "priority"
       from immunization_details
      LEFT JOIN immunizations ON immunization_details.immunization_id = immunizations.id
      LEFT JOIN profiles ON immunizations.profile = profiles.id
      LEFT JOIN details ON profiles.details = details.id
      LEFT JOIN bill_details ON immunization_details.billing = bill_details.id
      ${applyFilterForHmoAndPartners({
        profile,
        filter,
        table: 'immunizations',
      })}
      AND date(immunization_details.created_date) BETWEEN '${start}' AND '${end}'
      ${inPatientQueryString('immunizations', inPatient)}
      ${ageRangeQueryString(ageRange)}
      ${gender && gender !== 'All' ? `AND profiles.gender = '${gender}'` : ''}
      GROUP BY immunization_name, profiles.id, bill_details.id, "admistratorName", "administrationDate", immunization_details.priority
      ORDER BY administered_date ASC
    `,
      );
    const immunizationsByGenderWithMetrics = groupDataByCategoryWithMetrics(
      result,
      ['immunizationName'],
      {
        name: (items) => items[0].immunizationName,
        count: (items) => items.length,
        totalAmount: (items) =>
          items.reduce((acc, curr) => acc + parseFloat(curr.amount || '0'), 0),
        quantity: (items) =>
          items.reduce(
            (acc, curr) => acc + parseFloat(curr.quantity || '0'),
            0,
          ),
      },
    );

    const sum =
      (immunizationsByGenderWithMetrics.female?.length || 0) +
      (immunizationsByGenderWithMetrics.male?.length || 0);
    return {
      servicesSummary: [
        {
          name: extractPeriod(new Date(startDate), duration),
          totalImmunizations: sum,
        },
      ],
      list: {
        immunizationNames: immunizationsByGenderWithMetrics,
        byImmunizationAdmistrationDateAndAdmistratorName:
          groupByImmunizationAdmistrationDateAndAdmistratorName(result),
        byPatientName: immunizationNamesByPatientName.map((item) => ({
          ...item,
          administrationDate: moment(item.administrationDate).format(
            'DD/MMM/YYYY hh:mm A',
          ),
        })),
      },
    };
  },

  async getServiceAdmissionData(
    dataSource: DataSource,
    profile: ProfileModel,
    filter: ServicesAnalyticsFilter,
  ): Promise<ServiceSummaryWithList> {
    const {
      duration,
      startDate,
      endDate,
      diagnosisType,
      gender,
      inPatient,
      ageRange,
    } = filter;

    const { start, end } = formatDate(startDate, endDate);

    type AdmissionResponse = {
      admissionDiagnosis: DiagnosisInput[];
      gender: string;
      admittedBy: string;
      admissionDate: string;
      patientName: string;
      clinifyId: string;
      paymentType: string;
      status: string;
      priority: string;
      totalLengthOfStay: string;
    }[];
    const result: AdmissionResponse = await queryDSWithSlave(
      dataSource,
      `SELECT admission_diagnosis AS "admissionDiagnosis",
       profiles."gender" AS gender ,
       profiles.clinify_id as "clinifyId",
       profiles.full_name as "patientName",
       admissions.admitted_by AS "admittedBy",
       admissions.admission_date AS "admissionDate",
       bill_details."paymentType" AS "paymentType",
       EXTRACT(DAY FROM (discharge_patient.discharge_date - admissions.admission_date)) AS "totalLengthOfStay",
       admissions.priority AS "priority",
       CASE
        WHEN discharge_patient.id IS NOT NULL THEN 'Discharged'
        ELSE 'Admitted'
       END AS "status"
  FROM admissions
      LEFT JOIN discharge_patient ON discharge_patient.admission = admissions.id
      LEFT JOIN profiles ON admissions.profile = profiles.id  
      LEFT JOIN details ON profiles.details = details.id
      LEFT JOIN bill_details ON admissions.bill = bill_details.bill
      ${applyFilterForHmoAndPartners({ profile, filter, table: 'admissions' })}
        AND EXISTS (
        SELECT
          1
        FROM
          jsonb_array_elements(admissions.admission_diagnosis) AS diag(elem)
        WHERE
          ${
            diagnosisType
              ? `elem ->> '${diagnosisType}' IS NOT NULL`
              : "elem ->> 'diagnosisICD10' IS NOT NULL OR elem ->> 'diagnosisICD11' IS NOT NULL OR elem ->> 'diagnosisSNOMED' IS NOT NULL"
          }
      )
      ${
        !!gender && gender !== GenderOption.All
          ? `AND profiles.gender = '${gender}'`
          : ''
      }
      ${inPatientQueryString('admissions', inPatient)}
      ${ageRangeQueryString(ageRange)}
      AND date(admissions.created_date) BETWEEN '${start}' AND '${end}'
      GROUP BY admissions.id, profiles.id, profiles.full_name, profiles.gender,
        admissions.admitted_by, admissions.admission_date,  bill_details."paymentType",
        discharge_patient.discharge_date, discharge_patient.id
    `,
    );
    const groupByAddmittedByAndAdmissionDate = (list: AdmissionResponse) => {
      const result = {} as {
        [key: string]: ListsByAdmittedDateAndAdmittedBy;
      };
      list.forEach((item) => {
        const { admittedBy, admissionDate, paymentType, clinifyId } = item;
        const dateKey = moment(admissionDate).format('DD/MMM/YYYY hh:mm A');
        const key = `${admittedBy}-${dateKey}-${paymentType}-${clinifyId}`;
        if (!result[key]) {
          result[key] = {
            admittedBy,
            admissionDate: dateKey,
            diagnosis: [],
            paymentType: item.paymentType,
            patientName: item.patientName,
            clinifyId: item.clinifyId,
            status: item.status,
            priority: item.priority,
            totalLengthOfStay: item.totalLengthOfStay,
          };
        }
        result[key].diagnosis.push(
          ...getDiagnosisNames(item.admissionDiagnosis),
        );
      });

      let items = Object.values(result);
      items = items.map((item) => {
        item.diagnosis = groupSameNames(item.diagnosis);
        return item;
      });
      return items;
    };
    const list = serializeDiagnosis(
      'admissionDiagnosis',
      result as any,
      diagnosisType,
    );
    return {
      servicesSummary: [
        {
          name: extractPeriod(new Date(startDate), duration),
          totalAdmissions: result.length,
        },
      ],
      list: {
        admissionDiagnosis: {
          ...list,
          byAdmittedDateAndAdmittedBy:
            groupByAddmittedByAndAdmissionDate(result),
        },
      },
    };
  },

  async getMedicationPrescibedData(
    dataSource: DataSource,
    profile: ProfileModel,
    filter: ServicesAnalyticsFilter,
  ): Promise<ServiceSummaryWithList> {
    const {
      duration,
      startDate,
      endDate,
      gender,
      inPatient,
      ageRange,
      orderBy: ob = 'all',
    } = filter;
    const orderBy = ob.toLowerCase();
    const { start, end } = formatDate(startDate, endDate);
    type MedicationResponse = {
      medicationName: string;
      gender: string;
      quantity: number;
      prescribedDate: string;
      prescribedBy: string;
    }[];
    const medicationNames: MedicationResponse = await queryDSWithSlave(
      dataSource,
      `SELECT medication_name as "medicationName",
       profiles.gender as "gender", 
       quantity, 
       date_prescribed as "prescribedDate",
       medications.prescribed_by as "prescribedBy"
       from medication_details
      LEFT JOIN medications ON medication_details.medication = medications.id
      LEFT JOIN profiles ON medications.profile = profiles.id
      LEFT JOIN details ON profiles.details = details.id
      ${applyFilterForHmoAndPartnersOnMedicationDispense({
        profile,
        filter,
        table: 'medication_details',
      })}
      AND option = 'M'
      AND medication_details.medication_name IS NOT NULL
      AND date(medication_details.created_date) BETWEEN '${start}' AND '${end}'
        ${
          !!gender && gender !== GenderOption.All
            ? `AND profiles.gender = '${gender}'`
            : ''
        }
      ${inPatientQueryString('medication_details', inPatient)}
       ${ageRangeQueryString(ageRange)}
    `,
    );

    const medicationNamesByPatientName = await queryDSWithSlave(
      dataSource,
      `SELECT medication_name as "name",
       profiles.gender as "gender", 
      quantity,
      medication_details.date_prescribed as "prescribedDate",
       medications.prescribed_by as "prescribedBy",
       medication_details.dosage as "dosage",
       medication_details.dosage_unit as "dosageUnit",
       medication_details.category as "medicationCategory",
       profiles.full_name as "patientName",
       profiles.id as "patientId"
       from medication_details
      LEFT JOIN medications ON medication_details.medication = medications.id
      LEFT JOIN profiles ON medications.profile = profiles.id
      LEFT JOIN details ON profiles.details = details.id
      ${applyFilterForHmoAndPartnersOnMedicationDispense({
        profile,
        filter,
        table: 'medication_details',
      })}
      AND option = 'M'
      AND medication_details.medication_name IS NOT NULL
      AND date(medication_details.created_date) BETWEEN '${start}' AND '${end}'
        ${
          !!gender && gender !== GenderOption.All
            ? `AND profiles.gender = '${gender}'`
            : ''
        }
      ${inPatientQueryString('medication_details', inPatient)}
       ${ageRangeQueryString(ageRange)}
       ORDER BY medication_details.date_prescribed ASC
    `,
    );
    const byMost: {
      name: string;
      total: number;
    }[] = await queryDSWithSlave(
      dataSource,
      `SELECT 
        medication_name AS "name",
        SUM(${checkForValidFloat('quantity')}) AS "total"
      FROM medication_details
      LEFT JOIN medications ON medication_details.medication = medications.id
      LEFT JOIN profiles ON medications.profile = profiles.id
      LEFT JOIN details ON profiles.details = details.id
      ${applyFilterForHmoAndPartnersOnMedicationDispense({
        profile,
        filter,
        table: 'medication_details',
      })}
      AND option = 'M' 
      AND date(medication_details.created_date) BETWEEN '${start}' AND '${end}'
        ${
          !!gender && gender !== GenderOption.All
            ? `AND profiles.gender = '${gender}'`
            : ''
        }
      ${inPatientQueryString('medication_details', inPatient)}
       ${ageRangeQueryString(ageRange)}
      GROUP BY medication_name
       ORDER BY "total" ${getOrderByKeyword(orderBy)}
    `,
    );
    const groupBy = (list: MedicationResponse) => {
      const result = {} as {
        [key: string]: ListByMedicationPrescriptionDateAndPrescriberName;
      };
      list.forEach((item) => {
        const { prescribedBy, prescribedDate, quantity, medicationName } = item;
        const dateKey = new Date(prescribedDate).toISOString().split('T')[0];
        const key = `${prescribedBy}-${dateKey}`;
        if (!result[key]) {
          result[key] = {
            prescribedBy,
            prescriptionDate: moment(prescribedDate).format('DD/MMM/YYYY'),
            medicationNames: [],
            quantity: 0,
          };
        }
        result[key].quantity += Number(quantity) || 0;
        result[key].medicationNames.push(medicationName);
      });

      let items = Object.values(result);
      items = items.map((item) => {
        item.medicationNames = groupSameNames(item.medicationNames);
        return item;
      });
      return items;
    };
    const list = groupByGenderWithQuantity(
      ['medicationName', 'quantity'],
      medicationNames,
    );

    const sum = (list.female?.length || 0) + (list.male?.length || 0);
    return {
      servicesSummary: [
        {
          name: extractPeriod(new Date(startDate), duration),
          totalMedicationsPrescribed: sum,
        },
      ],
      list: {
        medicationNames: {
          male: list.male?.map((item) => ({
            ...item,
            quantity: parseFloat(item.quantity || '0'),
          })),
          female: list.female?.map((item) => ({
            ...item,
            quantity: parseFloat(item.quantity || '0'),
          })),
        },
        byMedicationPrescriptionDateAndPrescriberName: groupBy(medicationNames),
        byPatientName: medicationNamesByPatientName.map((item) => ({
          ...item,
          prescribedDate: moment(item.prescribedDate).format(
            'DD/MMM/YYYY hh:mm A',
          ),
        })),
        ...(orderBy === 'all'
          ? {
              byMost,
              byLeast: [...byMost].sort((a, b) => a.total - b.total),
            }
          : {}),
        ...(orderBy === 'most'
          ? {
              byMost,
            }
          : {}),
        ...(orderBy === 'least'
          ? {
              byLeast: byMost,
            }
          : {}),
      },
    };
  },
  async getMedicationDispenseData(
    dataSource: DataSource,
    profile: ProfileModel,
    filter: ServicesAnalyticsFilter,
  ): Promise<ServiceSummaryWithList> {
    const {
      duration,
      startDate,
      endDate,
      gender,
      ageRange,
      inPatient,
      orderBy: ob = 'all',
    } = filter;
    const orderBy = ob.toLowerCase();
    const { start, end } = formatDate(startDate, endDate);
    type MedicationResponse = {
      medicationName: string;
      gender: string;
      totalAmount: number;
      quantity: number;
      dispenseDate: string;
      dispensedBy: string;
    }[];
    const medicationNames: MedicationResponse = await queryDSWithSlave(
      dataSource,
      `SELECT 
        dispense_details.medication_name AS "medicationName",
        profiles.gender as "gender", 
        ${checkForValidFloat(
          'dispense_details.quantity_dispensed',
        )} AS quantity,
        SUM(bill_details.amount_due) AS "totalAmount",
        dispense_details.dispense_date AS "dispenseDate",
        dispense_details.dispensed_by AS "dispensedBy"

      FROM dispense_details
      LEFT JOIN medication_details ON medication_details.id = dispense_details.medication_detail
      LEFT JOIN bill_details ON dispense_details.billing = bill_details.id
      LEFT JOIN medications ON dispense_details.medication = medications.id
      LEFT JOIN profiles ON medications.profile = profiles.id
      LEFT JOIN details ON profiles.details = details.id
      ${applyFilterForHmoAndPartnersOnMedicationDispense({
        profile,
        filter,
        table: 'dispense_details',
      })}
      AND dispense_details.option = 'M'
      AND date(dispense_details.created_date) BETWEEN '${start}' AND '${end}'
        ${ageRangeQueryString(ageRange)}
      ${inPatientQueryString('medication_details', inPatient)}
      ${gender && gender !== 'All' ? `AND profiles.gender = '${gender}'` : ''}
        GROUP BY dispense_details.medication_name, profiles.gender, dispense_details.quantity_dispensed, bill_details.amount_due, 
        dispense_details.dispense_date,  dispense_details.dispensed_by
    `,
    );

    const medicationNamesByPatientName = await queryDSWithSlave(
      dataSource,
      `SELECT 
      array_to_string(dispense_details.medication_name, ',') AS "name",
        SUM(${checkForValidFloat(
          'dispense_details.quantity_dispensed',
        )}) AS quantity,
        SUM(${checkForValidFloat(
          'dispense_details.quantity_remaining',
        )}) AS "quantityRemaining",
        SUM(bill_details.amount_due) AS "totalAmount",
        profiles.full_name as "patientName",
        profiles.id as "patientId",
        profiles.gender as "gender",
        date_part('year', age(details.date_of_birth::DATE)) as "patientAge",
        dispense_details.dispense_date AS "dispenseDate",
        dispense_details.dispensed_by AS "dispensedBy",
        medication_details.dosage as "dosage",
        medication_details.dosage_unit as "dosageUnit",
        medication_details.category as "medicationCategory",
        bill_details."paymentType" as "paymentType"
      FROM dispense_details
      LEFT JOIN medication_details ON medication_details.id = dispense_details.medication_detail
      LEFT JOIN bill_details ON dispense_details.billing = bill_details.id
      LEFT JOIN medications ON dispense_details.medication = medications.id
      LEFT JOIN profiles ON medications.profile = profiles.id
      LEFT JOIN details ON profiles.details = details.id
      ${applyFilterForHmoAndPartnersOnMedicationDispense({
        profile,
        filter,
        table: 'dispense_details',
      })}
      AND dispense_details.option = 'M'
      AND date(dispense_details.created_date) BETWEEN '${start}' AND '${end}'
        ${ageRangeQueryString(ageRange)}
      ${inPatientQueryString('medication_details', inPatient)}
      ${gender && gender !== 'All' ? `AND profiles.gender = '${gender}'` : ''}
        GROUP BY dispense_details.medication_name, profiles.id, dispense_details.dispense_date,
          dispense_details.dispensed_by, bill_details."paymentType", details.date_of_birth,
          medication_details.dosage, medication_details.dosage_unit, medication_details.category
        ORDER BY dispense_details.dispense_date ASC
    `,
    );
    const byMost: {
      name: string;
      total: number;
    }[] = await queryDSWithSlave(
      dataSource,
      `SELECT 
        array_to_string(dispense_details.medication_name, ',') AS "name",
         SUM(${checkForValidFloat(
           'dispense_details.quantity_dispensed',
         )}) AS "total"
      FROM dispense_details
      LEFT JOIN medication_details ON medication_details.id = dispense_details.medication_detail
      LEFT JOIN medications ON dispense_details.medication = medications.id
      LEFT JOIN profiles ON medications.profile = profiles.id
      LEFT JOIN details ON profiles.details = details.id
      ${applyFilterForHmoAndPartnersOnMedicationDispense({
        profile,
        filter,
        table: 'dispense_details',
      })}
      AND dispense_details.option = 'M'
      AND date(dispense_details.created_date) BETWEEN '${start}' AND '${end}'
        ${ageRangeQueryString(ageRange)}
      ${inPatientQueryString('medication_details', inPatient)}
      ${gender && gender !== 'All' ? `AND profiles.gender = '${gender}'` : ''}
      GROUP BY array_to_string(dispense_details.medication_name, ',')
       ORDER BY "total" ${getOrderByKeyword(orderBy)}
    `,
    );

    const groupBy = (list: MedicationResponse) => {
      const result = {} as {
        [key: string]: ListByMedicationDispensedDateAndDispenserName;
      };
      list.forEach((item) => {
        const { dispensedBy, dispenseDate, quantity, medicationName } = item;
        const dateKey = new Date(dispenseDate).toISOString().split('T')[0];
        const key = `${dispensedBy}-${dateKey}`;
        if (!result[key]) {
          result[key] = {
            dispensedBy,
            dispenseDate: moment(dispenseDate).format('DD/MMM/YYYY'),
            medicationNames: [],
            quantity: 0,
          };
        }
        result[key].quantity += Number(quantity) || 0;
        result[key].medicationNames.push(medicationName);
      });

      let items = Object.values(result);
      items = items.map((item) => {
        item.medicationNames = groupSameNames(item.medicationNames);
        return item;
      });
      return items;
    };
    const list = groupByGenderWithQuantity(
      ['medicationName', 'quantity', 'totalAmount', 'patientName', 'patientId'],
      medicationNames,
    );
    const sum = (list.male?.length || 0) + (list.female?.length || 0);

    return {
      servicesSummary: [
        {
          name: extractPeriod(new Date(startDate), duration),
          totalMedicationsDispensed: sum,
        },
      ],
      list: {
        medicationNames: list,
        byMedicationDispensedDateAndDispenserName: groupBy(medicationNames),
        byPatientName: medicationNamesByPatientName.map((item) => ({
          ...item,
          dispenseDate: moment(item.dispenseDate).format('DD/MMM/YYYY h:mm A'),
        })),
        ...(orderBy === 'all'
          ? {
              byMost,
              byLeast: [...byMost].sort((a, b) => a.total - b.total),
            }
          : {}),
        ...(orderBy === 'most'
          ? {
              byMost,
            }
          : {}),
        ...(orderBy === 'least'
          ? {
              byLeast: byMost,
            }
          : {}),
      },
    };
  },
  async getConsumablePrescribedData(
    dataSource: DataSource,
    profile: ProfileModel,
    filter: ServicesAnalyticsFilter,
  ): Promise<ServiceSummaryWithList> {
    const {
      duration,
      startDate,
      endDate,
      ageRange,
      gender,
      inPatient,
      orderBy: ob = 'all',
    } = filter;
    const orderBy = ob.toLowerCase();
    const { start, end } = formatDate(startDate, endDate);
    type ConsumablePrescribedResponse = {
      medicationConsumables: MedicationConsumable[];
      gender: string;
      prescribedDate: string;
      prescribedBy: string;
      patientName: string;
      clinifyId: string;
    }[];
    type ConsumableByPatientNames = {
      name: string;
      quantity: number;
      prescribedDate: string;
      prescribedBy: string;
      patientName: string;
      clinifyId: string;
      patientId: string;
    };
    const consumableByPatientNames: ConsumableByPatientNames[] =
      await queryDSWithSlave(
        dataSource,
        `SELECT diag."name" AS "name",
       date_prescribed as "prescribedDate",
       medications.prescribed_by as "prescribedBy",
      profiles.full_name as "patientName",
       profiles.clinify_id as "clinifyId",
       profiles.id as "patientId",
      SUM(CASE 
          WHEN diag."quantity" = '' THEN 1
          WHEN diag."quantity" IS NULL THEN 1
          ELSE COALESCE(diag."quantity"::float, 1)
      END) AS "quantity"
       FROM medication_details
      LEFT JOIN medications ON medication_details.medication = medications.id
      LEFT JOIN profiles ON medications.profile = profiles.id
      LEFT JOIN details ON profiles.details = details.id
      LEFT JOIN LATERAL jsonb_to_recordset(medication_details.medication_consumables) AS diag(
                "name" text,
                "quantity" text
              ) ON TRUE
      ${applyFilterForHmoAndPartnersOnMedicationDispense({
        profile,
        filter,
        table: 'medication_details',
      })}
      AND option = 'C'
      AND medication_consumables IS NOT NULL
      AND date(medication_details.created_date) BETWEEN '${start}' AND '${end}'
      ${ageRangeQueryString(ageRange)}
      ${inPatientQueryString('medication_details', inPatient)}
      ${gender && gender !== 'All' ? `AND profiles.gender = '${gender}'` : ''}
      GROUP BY diag."name", profiles.gender, date_prescribed, medications.prescribed_by, profiles.id
    `,
      );
    const consumableNames = await queryDSWithSlave(
      dataSource,
      `SELECT medication_consumables AS "medicationConsumables",
      profiles.gender as "gender",
       date_prescribed as "prescribedDate",
       medications.prescribed_by as "prescribedBy",
      profiles.full_name as "patientName",
       profiles.clinify_id as "clinifyId"
       FROM medication_details
      LEFT JOIN medications ON medication_details.medication = medications.id
      LEFT JOIN profiles ON medications.profile = profiles.id
      LEFT JOIN details ON profiles.details = details.id
      ${applyFilterForHmoAndPartnersOnMedicationDispense({
        profile,
        filter,
        table: 'medication_details',
      })}
      AND option = 'C'
      AND medication_consumables IS NOT NULL
      AND date(medication_details.created_date) BETWEEN '${start}' AND '${end}'
      ${ageRangeQueryString(ageRange)}
      ${inPatientQueryString('medication_details', inPatient)}
      ${gender && gender !== 'All' ? `AND profiles.gender = '${gender}'` : ''}
    `,
    );
    const byOrders = await queryDSWithSlave(
      dataSource,
      `SELECT SUM(${checkForValidFloat('diag.quantity')}) AS "total",
				diag.name as name
       FROM medication_details
      LEFT JOIN medications ON medication_details.medication = medications.id
      LEFT JOIN profiles ON medications.profile = profiles.id
      LEFT JOIN details ON profiles.details = details.id
      INNER JOIN jsonb_to_recordset(medication_details.medication_consumables) AS diag(
                "name" text,
                "quantity" text
              ) ON TRUE
      ${applyFilterForHmoAndPartnersOnMedicationDispense({
        profile,
        filter,
        table: 'medication_details',
      })}     
      AND option = 'C'
      AND date(medication_details.created_date) BETWEEN '${start}' AND '${end}'
      ${ageRangeQueryString(ageRange)}
      ${inPatientQueryString('medication_details', inPatient)}
      ${gender && gender !== 'All' ? `AND profiles.gender = '${gender}'` : ''}
        GROUP BY diag.name
       ORDER BY "total" ${getOrderByKeyword(orderBy)}
    `,
    );
    const groupBy = (list: ConsumablePrescribedResponse) => {
      const result = {} as {
        [key: string]: ListByMedicationPrescriptionDateAndPrescriberName;
      };
      list.forEach((item) => {
        const { prescribedBy, prescribedDate, medicationConsumables } = item;
        const dateKey = new Date(prescribedDate).toISOString().split('T')[0];
        const key = `${prescribedBy}-${dateKey}`;
        if (!result[key]) {
          result[key] = {
            prescribedBy,
            prescriptionDate: moment(prescribedDate).format('DD/MMM/YYYY'),
            medicationNames: [],
            quantity: 0,
          };
        }
        medicationConsumables.forEach((consumable) => {
          result[key].quantity += +(consumable.quantity || 0);
          result[key].medicationNames.push(consumable.name);
        });
      });

      let items = Object.values(result);
      items = items.map((item) => {
        item.medicationNames = groupSameNames(item.medicationNames);
        return item;
      });
      return items;
    };
    const list = groupByGenderConsumables(
      'medicationConsumables',
      consumableNames,
      ['name', 'quantity'],
    );
    const sum = (list.male?.length || 0) + (list.female?.length || 0);

    return {
      servicesSummary: [
        {
          name: extractPeriod(new Date(startDate), duration),
          totalConsumablesPrescribed: sum,
        },
      ],
      list: {
        consumables: list,
        byConsumablePrescriptionDateAndPrescriberName: groupBy(consumableNames),
        ...(orderBy === 'all'
          ? {
              byMost: byOrders,
              byLeast: [...byOrders].sort((a, b) => a.total - b.total),
            }
          : {}),
        ...(orderBy === 'most'
          ? {
              byMost: byOrders,
            }
          : {}),
        ...(orderBy === 'least'
          ? {
              byLeast: byOrders,
            }
          : {}),
        byPatientName: consumableByPatientNames.map((item) => ({
          ...item,
          quantity: item.quantity.toString(),
          prescribedDate: moment(item.prescribedDate).format(
            'DD/MMM/YYYY h:mm A',
          ),
        })),
      },
    };
  },

  async getConsumableDispenseData(
    dataSource: DataSource,
    profile: ProfileModel,
    filter: ServicesAnalyticsFilter,
  ): Promise<ServiceSummaryWithList> {
    const {
      duration,
      startDate,
      endDate,
      ageRange,
      gender,
      inPatient,
      orderBy: ob = 'all',
    } = filter;
    const orderBy = ob.toLowerCase();
    const { start, end } = formatDate(startDate, endDate);
    type ConsumableDispenseResponse = {
      consumables: DispenseConsumable[];
      dispenseDate: string;
      dispensedBy: string;
      gender: string;
    }[];
    const consumables = await queryDSWithSlave(
      dataSource,
      `SELECT 
        dispense_details.dispense_consumables AS "consumables",
        profiles.gender as "gender",
          dispense_details.dispense_date AS "dispenseDate",
        dispense_details.dispensed_by AS "dispensedBy"
      FROM dispense_details
      LEFT JOIN medications ON dispense_details.medication = medications.id
      LEFT JOIN profiles ON medications.profile = profiles.id
      LEFT JOIN details ON profiles.details = details.id
      ${applyFilterForHmoAndPartnersOnMedicationDispense({
        profile,
        filter,
        table: 'dispense_details',
      })}
      AND dispense_details.option = '${MedicationOptionType.C}'
      AND date(dispense_details.created_date) BETWEEN '${start}' AND '${end}'
        ${ageRangeQueryString(ageRange)}
      ${inPatientQueryString('dispense_details', inPatient)}
      ${gender && gender !== 'All' ? `AND profiles.gender = '${gender}'` : ''}
    `,
    );

    const byOrders = await queryDSWithSlave(
      dataSource,
      `SELECT  SUM(${checkForValidFloat('diag."quantityConsumed"')}) AS "total",
				diag.name as name
      FROM dispense_details
      LEFT JOIN medications ON dispense_details.medication = medications.id
      LEFT JOIN profiles ON medications.profile = profiles.id
      LEFT JOIN details ON profiles.details = details.id
      INNER JOIN jsonb_to_recordset(dispense_details.dispense_consumables) AS diag(
                "name" text,
                "quantityConsumed" text
              ) ON TRUE
      ${applyFilterForHmoAndPartnersOnMedicationDispense({
        profile,
        filter,
        table: 'dispense_details',
      })}
      AND dispense_details.option = '${MedicationOptionType.C}'
      AND date(dispense_details.created_date) BETWEEN '${start}' AND '${end}'
        ${ageRangeQueryString(ageRange)}
      ${inPatientQueryString('dispense_details', inPatient)}
      ${gender && gender !== 'All' ? `AND profiles.gender = '${gender}'` : ''}
        GROUP BY diag.name
        ORDER BY "total" ${getOrderByKeyword(orderBy)}
    `,
    );
    type ByPatientNames = {
      name: string;
      dispenseDate: string;
      dispensedBy: string;
      patientId: string;
      patientName: string;
      clinifyId: string;
      totalAmount: string;
      paymentType: string;
      billId: string;
      quantity: number;
    };
    const byPatientNames: ByPatientNames[] = await queryDSWithSlave(
      dataSource,
      `SELECT 
        diag."name" as "name",
        dispense_details.dispense_date AS "dispenseDate",
        dispense_details.dispensed_by AS "dispensedBy",
        profiles.id as "patientId",
        profiles.full_name as "patientName",
        profiles.clinify_id as "clinifyId",
        SUM(CASE 
          WHEN diag."quantityConsumed" = '' THEN 1
          WHEN diag."quantityConsumed" IS NULL THEN 1
          ELSE COALESCE(diag."quantityConsumed"::float, 1)
      END) AS "quantity",
      bill_details.amount_due as "totalAmount",
      bill_details."paymentType" as "paymentType",
      bill_details.id as "billId"
      FROM dispense_details
      LEFT JOIN medications ON dispense_details.medication = medications.id
      LEFT JOIN profiles ON medications.profile = profiles.id
      LEFT JOIN details ON profiles.details = details.id
      LEFT JOIN LATERAL jsonb_to_recordset(dispense_details.dispense_consumables) AS diag(
                "name" text,
                "quantityConsumed" text
              ) ON TRUE
      LEFT JOIN bill_details ON dispense_details.billing = bill_details.id
      ${applyFilterForHmoAndPartnersOnMedicationDispense({
        profile,
        filter,
        table: 'dispense_details',
      })}
      AND dispense_details.option = '${MedicationOptionType.C}'
      AND date(dispense_details.created_date) BETWEEN '${start}' AND '${end}'
        ${ageRangeQueryString(ageRange)}
      ${inPatientQueryString('dispense_details', inPatient)}
      ${gender && gender !== 'All' ? `AND profiles.gender = '${gender}'` : ''}
      GROUP BY diag."name", dispense_details.dispense_date, dispense_details.dispensed_by, profiles.id, bill_details.id
      `,
    );

    const groupBy = (list: ConsumableDispenseResponse) => {
      const result = {} as {
        [key: string]: ListByMedicationDispensedDateAndDispenserName;
      };
      list.forEach((item) => {
        const { dispensedBy, dispenseDate, consumables } = item;
        const dateKey = new Date(dispenseDate).toISOString().split('T')[0];
        const key = `${dispensedBy}-${dateKey}`;
        if (!result[key]) {
          result[key] = {
            dispensedBy,
            dispenseDate: moment(dispenseDate).format('DD/MMM/YYYY'),
            medicationNames: [],
            quantity: 0,
          };
        }
        consumables.forEach((consumable) => {
          result[key].quantity += +(consumable.quantityConsumed || 0);
          result[key].medicationNames.push(consumable.name);
        });
      });

      let items = Object.values(result);
      items = items.map((item) => {
        item.medicationNames = groupSameNames(item.medicationNames);
        return item;
      });
      return items;
    };
    const list = groupByGenderConsumables('consumables', consumables, [
      'name',
      'quantityConsumed',
      'totalAmount',
    ]);
    const sum = (list.male?.length || 0) + (list.female?.length || 0);
    return {
      servicesSummary: [
        {
          name: extractPeriod(new Date(startDate), duration),
          totalConsumablesDispensed: sum,
        },
      ],
      list: {
        consumables: list,
        byConsumableDispensedDateAndDispenserName: groupBy(consumables),
        ...(orderBy === 'all'
          ? {
              byMost: byOrders,
              byLeast: [...byOrders].sort((a, b) => a.total - b.total),
            }
          : {}),
        ...(orderBy === 'most'
          ? {
              byMost: byOrders,
            }
          : {}),
        ...(orderBy === 'least'
          ? {
              byLeast: byOrders,
            }
          : {}),
        byPatientName: byPatientNames.map((item) => ({
          ...item,
          quantity: item.quantity.toString(),
          totalAmount: parseFloat(item.totalAmount),
          dispenseDate: moment(item.dispenseDate).format('DD/MMM/YYYY h:mm A'),
        })),
      },
    };
  },

  async getInvestigationStatusData(
    dataSource: DataSource,
    profile: ProfileModel,
    filter: ServicesAnalyticsFilter,
  ): Promise<ServicesSummary> {
    const { startDate, endDate } = filter;
    const { start, end } = formatDate(startDate, endDate);

    const investigationStatusBaseQuery = (type: string): string => {
      return `
      SELECT 
        COUNT(request_type) AS "${requestedMapper[type]}",
        COUNT(request_type) FILTER(WHERE investigations.status = 'Approved') AS "${
          processedMapper[type]
        }",
        SUM(1) AS "total${type}Quantity",
        SUM(b.amount_due) AS "total${type}AmountDue",
        SUM(b.amount_paid) AS "total${type}AmountPaid",
        SUM(b.amount_outstanding) AS "total${type}AmountOutstanding",
        SUM(b.total_amount) AS "total${type}Amount",
        investigations.facility_name AS "hospitalName"
      FROM investigations
      LEFT JOIN bills b ON investigations.bill = b.id
      ${applyFilterForHmoAndPartners({
        profile,
        table: 'investigations',
        filter,
      })}
      AND investigations.archived = false
      AND request_type = '${type}'
      AND date(investigations.created_date) BETWEEN '${start}' AND '${end}'
      GROUP BY investigations.facility_name`;
    };

    const radiologyStatusQuery = await queryDSWithSlave(
      dataSource,
      investigationStatusBaseQuery('Radiology'),
    );

    const laboratoryStatusQuery = await queryDSWithSlave(
      dataSource,
      investigationStatusBaseQuery('Laboratory'),
    );
    type ResultsByPatient = {
      testName: string;
      patientId: string;
      gender: string;
      amount: string;
      patientName: string;
      clinifyId: string;
      paymentType: string;
      investigationId: string;
      requestType: string;
      details: LabTestDetail[];
      testDate: string;
      performedBy: string;
      status: string;
    }[];
    let labResultsByPatient: ResultsByPatient = await queryDSWithSlave(
      dataSource,
      `SELECT    lab_info ->> 'testName' as "testName",
        profiles.id,
        profiles.gender as "gender",
        bill_details.amount_due as "amount",
        profiles.full_name as "patientName",
        profiles.clinify_id as "clinifyId",
        bill_details."paymentType", 
        lab_results.details,
        investigations.id as "investigationId",
        investigations.request_type as "requestType",
        investigations.status as "status",
        investigations.request_date as "testDate"
      FROM
        investigations
        LEFT JOIN lab_results ON investigations.lab_result = lab_results.id
        LEFT JOIN profiles ON investigations.profile = profiles.id
        LEFT JOIN details ON profiles.details = details.id
        LEFT JOIN LATERAL jsonb_array_elements(test_info) AS lab_info ON TRUE
        LEFT JOIN bill_details ON lab_info->> 'ref' = bill_details.reference
      ${applyFilterForHmoAndPartners({
        profile,
        filter,
        table: 'investigations',
      })}
      AND request_type = 'Laboratory'
      AND date(investigations.created_date) BETWEEN '${start}' AND '${end}'
    `,
    );

    type ExamResults = {
      examType: string;
      patientId: string;
      gender: string;
      amount: string;
      patientName: string;
      clinifyId: string;
      paymentType: string;
      examinationDate: string;
      performedBy: string;
      requestType: string;
      status: string;
      details: RadiologyExamDetail[];
      testDate: string;
    }[];
    let examResults: ExamResults = await queryDSWithSlave(
      dataSource,
      `SELECT 	exam->>'examType' as "examType",
        profiles.id,
        profiles.gender as "gender",
        bill_details.amount_due as "amount",
        profiles.full_name as "patientName",
        profiles.clinify_id as "clinifyId",
        bill_details."paymentType",
        radiology_results.details,
        investigations.request_type as "requestType",
        investigations.status as "status",
        investigations.request_date as "testDate"
      FROM
        investigations
        LEFT JOIN radiology_results ON investigations.radiology_result = radiology_results.id
        LEFT JOIN profiles ON investigations.profile = profiles.id
        LEFT JOIN details ON profiles.details = details.id
        LEFT JOIN LATERAL jsonb_array_elements(examination_type) AS exam ON TRUE
        LEFT JOIN bill_details ON exam ->> 'ref' = bill_details.reference
      ${applyFilterForHmoAndPartners({
        profile,
        filter,
        table: 'investigations',
      })}
      AND request_type = 'Radiology'
      AND date(investigations.created_date) BETWEEN '${start}' AND '${end}'
    `,
    );

    const totalRequestedRadiologyAmount = examResults.reduce(
      (acc, curr) => acc + parseFloat(curr.amount || '0'),
      0,
    );
    const totalProcessedRadiologyAmount = examResults.reduce(
      (acc, curr) =>
        acc + (curr.status === 'Approved' ? parseFloat(curr.amount || '0') : 0),
      0,
    );

    const totalRequestedLaboratoryAmount = labResultsByPatient.reduce(
      (acc, curr) => acc + parseFloat(curr.amount || '0'),
      0,
    );
    const totalProcessedLaboratoryAmount = labResultsByPatient.reduce(
      (acc, curr) =>
        acc + (curr.status === 'Approved' ? parseFloat(curr.amount || '0') : 0),
      0,
    );
    labResultsByPatient = labResultsByPatient.map((item) => {
      return {
        ...item,
        testDate: moment(
          item.status === 'Approved'
            ? item.details?.find((v) => v.testName === item.testName)?.testDate
            : item.testDate,
        ).format('DD/MMM/YYYY hh:mm A'),
        performedBy: item.details?.find((v) => v.testName === item.testName)
          ?.performedBy,
      };
    });
    examResults = examResults.map((item) => ({
      ...item,
      examinationDate: moment(
        item.status === 'Approved'
          ? item.details?.find((v) => v.examType === item.examType)
              ?.examinationDate
          : item.examinationDate,
      ).format('DD/MMM/YYYY hh:mm A'),
      radiographerName: item.details?.find((v) => v.examType === item.examType)
        ?.radiographerName,
    }));
    const listByPatientRadiologyResult = groupDataByCategoryWithMetrics(
      examResults,
      ['examType', 'patientId', 'testDate', 'performedBy'],
      {
        name: (items) => items[0].examType,
        count: (items) => items.length,
        totalAmount: (items) =>
          items.reduce((acc, curr) => acc + parseFloat(curr.amount || '0'), 0),
        quantity: (items) =>
          items.reduce(
            (acc, curr) => acc + parseFloat(curr.quantity || '1'),
            0,
          ),
        patientName: (items) => items[0].patientName,
        clinifyId: (items) => items[0].clinifyId,
        paymentType: (items) => items[0].paymentType,
        examinationDate: (items) => items[0].examinationDate,
        radiographerName: (items) => items[0].radiographerName,
      },
    );
    const listByPatient = groupDataByCategoryWithMetrics(
      labResultsByPatient,
      ['testName', 'patientId', 'testDate', 'performedBy'],
      {
        name: (items) => items[0].testName,
        count: (items) => items.length,
        totalAmount: (items) =>
          items.reduce((acc, curr) => acc + parseFloat(curr.amount || '0'), 0),
        quantity: (items) =>
          items.reduce(
            (acc, curr) => acc + parseFloat(curr.quantity || '1'),
            0,
          ),
        patientName: (items) => items[0].patientName,
        clinifyId: (items) => items[0].clinifyId,
        paymentType: (items) => items[0].paymentType,
        testDate: (items) => items[0].testDate,
        performedBy: (items) => items[0].performedBy,
      },
    );
    const listRadiologyByPatient = listByPatientRadiologyResult.female
      .concat(listByPatientRadiologyResult.male)
      .sort(
        (a, b) =>
          a.examinationDate?.localeCompare(b.examinationDate || '') || 0,
      );

    const listLabTestByPatient = listByPatient.female
      .concat(listByPatient.male)
      .sort((a, b) => a.testDate?.localeCompare(b.testDate || '') || 0);

    return {
      name: extractPeriod(new Date(start), filter.duration),
      ...radiologyStatusQuery[0],
      ...laboratoryStatusQuery[0],
      totalRequestedRadiologyAmount,
      totalProcessedRadiologyAmount,
      totalRequestedLaboratoryAmount,
      totalProcessedLaboratoryAmount,
      listLabTestByPatient,
      listRadiologyByPatient,
    };
  },

  async getBirthData(
    dataSource: DataSource,
    profile: ProfileModel,
    filter: ServicesAnalyticsFilter,
  ): Promise<BirthGenderData> {
    const { duration, startDate, endDate } = filter;
    const { start, end } = formatDate(startDate, endDate);

    const birthByGenderDataResult = await queryDSWithSlave(
      dataSource,
      `SELECT 
      COUNT(gender) FILTER (WHERE LOWER(gender) = 'female') as "totalFemale",
      COUNT(gender) FILTER (WHERE LOWER(gender) = 'male') as "totalMale"
    FROM obstetric_histories
    ${applyFilterForHmoAndPartners({
      profile,
      filter,
      table: 'obstetric_histories',
    })}
    AND date(obstetric_histories.created_date) BETWEEN '${start}' AND '${end}'
  `,
    );

    return {
      summary: birthByGenderDataResult[0],
      data: birthByGenderDataResult.map((item) => ({
        ...item,
        name: extractPeriod(new Date(start), duration),
      })),
    };
  },

  async getDemographicData(
    dataSource: DataSource,
    profile: ProfileModel,
    filter: ServicesAnalyticsFilter,
  ): Promise<BirthData[]> {
    const { duration, startDate, endDate } = filter;
    const { start, end } = formatDate(startDate, endDate);

    const demographicQueryData = await queryDSWithSlave(
      dataSource,
      `SELECT category,
          SUM(CASE WHEN gender = 'Male' THEN 1 ELSE 0 END) AS "totalMale",
          SUM(CASE WHEN gender = 'Female' THEN 1 ELSE 0 END) AS "totalFemale"
        FROM  (
        SELECT 
          ${demographicQuery()} 
          END AS category, 
          profiles.gender
        FROM details
          LEFT JOIN profiles ON profiles.details = details.id
        ${applyFilterForHmoAndPartnersOnPatients({
          profile,
          filter,
          table: 'profiles',
        })}  
          AND profiles.created_date IS NOT NULL
          AND profiles.is_deleted IS FALSE
          AND date_of_birth IS NOT NULL
          AND LOWER(profiles.type) = 'patient'
          AND date(profiles.created_date) BETWEEN '${start}' AND '${end}'
        GROUP BY   details.date_of_birth, profiles.gender
        ) AS t
      GROUP BY category
  `,
    );

    return demographicQueryData.map((item) => ({
      ...item,
      name: extractPeriod(new Date(start), duration),
    }));
  },
  async getAdverseEffectsFollowingImmunizationsData(
    dataSource: DataSource,
    profile: ProfileModel,
    filter: ServicesAnalyticsFilter,
  ): Promise<ServicesSummary> {
    const { duration, startDate, endDate } = filter;
    const { start, end } = formatDate(startDate, endDate);

    const result = await queryDSWithSlave(
      dataSource,
      `SELECT 
        COUNT(1) FILTER(WHERE adverse_effects_following_immunization = 'Serious') AS "totalSeriousAdverseEffects",
        COUNT(1) FILTER(WHERE adverse_effects_following_immunization = 'Non-serious') AS "totalNonSeriousAdverseEffects"
      FROM immunization_details
        LEFT JOIN immunizations ON immunization_details.immunization_id = immunizations.id
      ${applyFilterForHmoAndPartners({
        profile,
        filter,
        table: 'immunizations',
      })}
        AND date(immunization_details.created_date) BETWEEN '${start}' AND '${end}'
  `,
    );
    return {
      ...result[0],
      name: extractPeriod(new Date(startDate), duration),
    };
  },

  async getServicesByCoverageType(
    dataSource: DataSource,
    profile: ProfileModel,
    filter: ServicesAnalyticsFilter,
  ): Promise<ServiceSummaryWithList> {
    const userType = profile.type;
    type ServicesType =
      | 'Admission'
      | 'Immunization'
      | 'Laboratory'
      | 'Consultation'
      | 'Radiology'
      | 'Request Investigation'
      | 'Process Investigation'
      | 'Medications Prescribed'
      | 'Medications Dispensed'
      | 'Consumables Prescribed'
      | 'Consumables Dispensed'
      | 'Procedure'
      | 'Nursing Services'
      | 'Antenatal'
      | 'Labour and Delivery'
      | 'Postnatal';

    const {
      startDate,
      endDate,
      orderBy = 'Laboratory',
      duration,
      coverage,
    } = filter;
    const servicesType = orderBy as ServicesType;
    const { start, end } = formatDate(startDate, endDate);
    const coverageType = (coverage || 'all').toLowerCase();
    const coverageNameCase = ` CASE
            WHEN ci.coverage_type = 'HMO' THEN hmp.name
            WHEN ci.coverage_type = 'Company' THEN ci.company_name
            WHEN ci.coverage_type = 'Family' THEN ci.family_name
            ELSE ci.name END AS "coverageName"`;
    const enrolleeIdCase = ` CASE
            WHEN ci.coverage_type = 'HMO' THEN hp.member_number
            ELSE ci.member_number
            END AS "memberNumber"`;
    const byAdmission = async () => {
      const result = await queryDSWithSlave(
        dataSource,
        `SELECT
          ci.coverage_type as "coverageType",
          COUNT(a.id) AS "totalCount",
          SUM(b.amount_due) AS "totalAmountDue",
          SUM(b.amount_owning) AS "totalAmountOwing",
          SUM(b.amount_outstanding) AS "totalAmountOutstanding",
          SUM(b.discount_amount) AS "totalDiscount",
          SUM(b.amount_paid) AS "totalAmountPaid",
          SUM(b.total_amount) AS "totalAmount",
          patient.full_name AS "patientName",
          u.non_corporate_email_address AS "patientEmail",
          u.phone_number AS "patientPhone",
          to_char(a.admission_date, 'DD/MON/YYYY') AS "visitDate",
          ${coverageNameCase},
          ${enrolleeIdCase}
        FROM
          admissions a
          INNER JOIN profiles patient ON a.profile = patient.id
          INNER JOIN users u ON u.id = patient.user
          INNER JOIN coverage_information ci ON ci.profile = patient.id
          INNER JOIN bills b ON b.id = a.bill
          LEFT JOIN hmo_profiles hp ON hp.coverage_information_id = ci.id
          LEFT JOIN hmo_providers hmp ON hmp.id = hp.provider_id
          ${applyFilterForHmoAndPartners({ profile, filter, table: 'a' })}
          AND ci.coverage_type IS NOT NULL
          AND date (a.created_date) BETWEEN '${start}' AND '${end}'
          ${
            coverageType === 'all'
              ? ''
              : `AND LOWER(ci.coverage_type) = '${coverageType}'`
          }
        GROUP BY
          ci.coverage_type, "coverageName", "patientName", "patientEmail", "visitDate", "memberNumber", "patientPhone"
        `,
      );
      return result;
    };
    const byConsultation = async () => {
      const result = await queryDSWithSlave(
        dataSource,
        `SELECT
          ci.coverage_type as "coverageType",
          COUNT(c.id) AS "totalCount",
          SUM(b.amount_due) AS "totalAmountDue",
          SUM(b.amount_owning) AS "totalAmountOwing",
          SUM(b.amount_outstanding) AS "totalAmountOutstanding",
          SUM(b.discount_amount) AS "totalDiscount",
          SUM(b.amount_paid) AS "totalAmountPaid",
          SUM(b.total_amount) AS "totalAmount",
          patient.full_name AS "patientName",
          u.non_corporate_email_address AS "patientEmail",
          u.phone_number AS "patientPhone",
          to_char(c.consultation_date, 'DD/MON/YYYY') AS "visitDate",
          ${coverageNameCase},
          ${enrolleeIdCase}
        FROM
          consultations c
          LEFT JOIN profiles patient ON c.profile = patient.id
          INNER JOIN users u ON u.id = patient.user
          LEFT JOIN coverage_information ci ON ci.profile = patient.id
          LEFT JOIN bills b ON b.id = c.bill
          LEFT JOIN hmo_profiles hp ON hp.coverage_information_id = ci.id
          LEFT JOIN hmo_providers hmp ON hmp.id = hp.provider_id
          ${applyFilterForHmoAndPartners({ profile, filter, table: 'c' })}
          AND ci.coverage_type IS NOT NULL
          AND date (c.created_date) BETWEEN '${start}' AND '${end}'
          ${
            coverageType === 'all'
              ? ''
              : `AND LOWER(ci.coverage_type) = '${coverageType}'`
          }
        GROUP BY
          ci.coverage_type, "coverageName", "patientName", "patientEmail", "visitDate", "memberNumber", "patientPhone"
        `,
      );
      return result;
    };
    const byImmunization = async () => {
      const result = await queryDSWithSlave(
        dataSource,
        `SELECT
          ci.coverage_type as "coverageType",
          COUNT(i.id) AS "totalCount",
          SUM(b.amount_due) AS "totalAmountDue",
          SUM(b.amount_owning) AS "totalAmountOwing",
          SUM(b.amount_outstanding) AS "totalAmountOutstanding",
          SUM(b.discount_amount) AS "totalDiscount",
          SUM(b.amount_paid) AS "totalAmountPaid",
          SUM(b.total_amount) AS "totalAmount",
          patient.full_name AS "patientName",
          u.non_corporate_email_address AS "patientEmail",
          u.phone_number AS "patientPhone",
          to_char(imd.administered_date, 'DD/MON/YYYY') AS "visitDate",
          ${coverageNameCase},
          ${enrolleeIdCase}
        FROM
          immunization_details imd
          LEFT JOIN immunizations i ON imd.immunization_id = i.id
          LEFT JOIN profiles patient ON i.profile = patient.id
          INNER JOIN users u ON u.id = patient.user
          LEFT JOIN coverage_information ci ON ci.profile = patient.id
          LEFT JOIN bill_details bd ON bd.id = imd.billing
          LEFT JOIN bills b ON b.id = bd.bill
           LEFT JOIN hmo_profiles hp ON hp.coverage_information_id = ci.id
          LEFT JOIN hmo_providers hmp ON hmp.id = hp.provider_id
          ${applyFilterForHmoAndPartners({ profile, filter, table: 'i' })}
          AND ci.coverage_type IS NOT NULL
          AND date (imd.created_date) BETWEEN '${start}' AND '${end}'
          ${
            coverageType === 'all'
              ? ''
              : `AND LOWER(ci.coverage_type) = '${coverageType}'`
          }
        GROUP BY
          ci.coverage_type, "coverageName", "patientName", "patientEmail", "visitDate", "memberNumber", "patientPhone"
        `,
      );
      return result;
    };
    const byInvestigation = async (requestType: 'Laboratory' | 'Radiology') => {
      const result = await queryDSWithSlave(
        dataSource,
        `SELECT
          ci.coverage_type as "coverageType",
          COUNT(i.id) AS "totalCount",
          SUM(b.amount_due) AS "totalAmountDue",
          SUM(b.amount_owning) AS "totalAmountOwing",
          SUM(b.amount_outstanding) AS "totalAmountOutstanding",
          SUM(b.discount_amount) AS "totalDiscount",
          SUM(b.amount_paid) AS "totalAmountPaid",
          SUM(b.total_amount) AS "totalAmount",
          patient.full_name AS "patientName",
          u.non_corporate_email_address AS "patientEmail",
          u.phone_number AS "patientPhone",
          to_char(i.request_date, 'DD/MON/YYYY') AS "visitDate",
          ${coverageNameCase},
          ${enrolleeIdCase}
        FROM
          investigations i
          LEFT JOIN profiles patient ON i.profile = patient.id
          INNER JOIN users u ON u.id = patient.user
          LEFT JOIN coverage_information ci ON ci.profile = patient.id
          LEFT JOIN bills b ON b.id = i.bill
           LEFT JOIN hmo_profiles hp ON hp.coverage_information_id = ci.id
          LEFT JOIN hmo_providers hmp ON hmp.id = hp.provider_id
          ${applyFilterForHmoAndPartners({ profile, filter, table: 'i' })}
          AND i.request_type = '${requestType}'
          AND ci.coverage_type IS NOT NULL
          AND date (i.created_date) BETWEEN '${start}' AND '${end}'
          ${
            coverageType === 'all'
              ? ''
              : `AND LOWER(ci.coverage_type) = '${coverageType}'`
          }
        GROUP BY
          ci.coverage_type, "coverageName", "patientName", "patientEmail", "visitDate", "memberNumber", "patientPhone"
        `,
      );
      return result;
    };

    const byInvestigationStatus = async (processed: boolean) => {
      let request_type = '';
      switch (userType) {
        case UserType.OrganizationLabTechnician:
          request_type = InvestigationRequestType.Laboratory;
          break;
        case UserType.OrganizationRadiographer:
        case UserType.OrganizationRadiologist:
          request_type = InvestigationRequestType.Radiology;
          break;
      }
      const result = await queryDSWithSlave(
        dataSource,
        `SELECT
          ci.coverage_type as "coverageType",
          COUNT(i.id) AS "totalCount",
          SUM(b.amount_due) AS "totalAmountDue",
          SUM(b.amount_owning) AS "totalAmountOwing",
          SUM(b.amount_outstanding) AS "totalAmountOutstanding",
          SUM(b.discount_amount) AS "totalDiscount",
          SUM(b.amount_paid) AS "totalAmountPaid",
          SUM(b.total_amount) AS "totalAmount",
          patient.full_name AS "patientName",
          u.non_corporate_email_address AS "patientEmail",
          u.phone_number AS "patientPhone",
          to_char(i.request_date, 'DD/MON/YYYY') AS "visitDate",
          ${coverageNameCase},
          ${enrolleeIdCase}
        FROM
          investigations i
          LEFT JOIN profiles patient ON i.profile = patient.id
          INNER JOIN users u ON u.id = patient.user
          LEFT JOIN coverage_information ci ON ci.profile = patient.id
          LEFT JOIN bills b ON b.id = i.bill
           LEFT JOIN hmo_profiles hp ON hp.coverage_information_id = ci.id
          LEFT JOIN hmo_providers hmp ON hmp.id = hp.provider_id
          ${applyFilterForHmoAndPartners({ profile, filter, table: 'i' })}
          ${processed ? "AND i.status = 'Approved'" : ''}
          AND ci.coverage_type IS NOT NULL
          AND date (i.created_date) BETWEEN '${start}' AND '${end}'
          ${
            coverageType === 'all'
              ? ''
              : `AND LOWER(ci.coverage_type) = '${coverageType}'`
          }
          ${request_type ? `AND i.request_type = '${request_type}'` : ''}
        GROUP BY
          ci.coverage_type, "coverageName", "patientName", "patientEmail", "visitDate", "memberNumber", "patientPhone"
        `,
      );
      return result;
    };

    const byMedicationPrescribed = async (option: MedicationOptionType) => {
      const result = await queryDSWithSlave(
        dataSource,
        `SELECT
          ci.coverage_type as "coverageType",
          COUNT(m.id) AS "totalCount",
          SUM(b.amount_due) AS "totalAmountDue",
          SUM(b.amount_owning) AS "totalAmountOwing",
          SUM(b.amount_outstanding) AS "totalAmountOutstanding",
          SUM(b.discount_amount) AS "totalDiscount",
          SUM(b.amount_paid) AS "totalAmountPaid",
          SUM(b.total_amount) AS "totalAmount",
          patient.full_name AS "patientName",
          u.non_corporate_email_address AS "patientEmail",
          u.phone_number AS "patientPhone",
          to_char(m.date_prescribed, 'DD/MON/YYYY') AS "visitDate",
          ${coverageNameCase},
          ${enrolleeIdCase}
        FROM
          medication_details m
          LEFT JOIN medications med ON m.medication = med.id
          LEFT JOIN dispense_details d ON d.medication = med.id
          LEFT JOIN profiles patient ON med.profile = patient.id
          INNER JOIN users u ON u.id = patient.user
          LEFT JOIN coverage_information ci ON ci.profile = patient.id
          LEFT JOIN bill_details bd ON bd.id = d.billing
          LEFT JOIN bills b ON b.id = bd.bill
           LEFT JOIN hmo_profiles hp ON hp.coverage_information_id = ci.id
          LEFT JOIN hmo_providers hmp ON hmp.id = hp.provider_id
          ${applyFilterForHmoAndPartnersOnMedicationDispense({
            profile,
            filter,
            table: 'm',
          })}
          AND m.option = '${option}'
          AND ci.coverage_type IS NOT NULL
          AND date (m.created_date) BETWEEN '${start}' AND '${end}'
        ${
          coverageType === 'all'
            ? ''
            : `AND LOWER(ci.coverage_type) = '${coverageType}'`
        }
        GROUP BY
          ci.coverage_type, "coverageName", "patientName", "patientEmail", "visitDate", "memberNumber", "patientPhone"
        `,
      );
      return result;
    };
    const byMedicationDispensed = async (option: MedicationOptionType) => {
      const result = await queryDSWithSlave(
        dataSource,
        `SELECT
          ci.coverage_type as "coverageType",
          COUNT(d.id) AS "totalCount",
          SUM(b.amount_due) AS "totalAmountDue",
          SUM(b.amount_owning) AS "totalAmountOwing",
          SUM(b.amount_outstanding) AS "totalAmountOutstanding",
          SUM(b.discount_amount) AS "totalDiscount",
          SUM(b.amount_paid) AS "totalAmountPaid",
          SUM(b.total_amount) AS "totalAmount",
          patient.full_name AS "patientName",
          u.non_corporate_email_address AS "patientEmail",
          u.phone_number AS "patientPhone",
          to_char(d.dispense_date, 'DD/MON/YYYY') AS "visitDate",
          ${coverageNameCase},
          ${enrolleeIdCase}
        FROM
          dispense_details d
          LEFT JOIN medications m ON d.medication = m.id
          LEFT JOIN profiles patient ON m.profile = patient.id
          INNER JOIN users u ON u.id = patient.user
          LEFT JOIN coverage_information ci ON ci.profile = patient.id
          LEFT JOIN bill_details bd ON bd.id = d.billing
          LEFT JOIN bills b ON b.id = bd.bill
           LEFT JOIN hmo_profiles hp ON hp.coverage_information_id = ci.id
          LEFT JOIN hmo_providers hmp ON hmp.id = hp.provider_id
          ${applyFilterForHmoAndPartnersOnMedicationDispense({
            profile,
            filter,
            table: 'd',
          })}
          AND d.option = '${option}'
          AND ci.coverage_type IS NOT NULL
          AND date (d.created_date) BETWEEN '${start}' AND '${end}'
          ${
            coverageType === 'all'
              ? ''
              : `AND LOWER(ci.coverage_type) = '${coverageType}'`
          }
        GROUP BY
          ci.coverage_type, "coverageName", "patientName", "patientEmail", "visitDate", "memberNumber", "patientPhone"
        `,
      );
      return result;
    };

    const byProcedure = async () => {
      const result = await queryDSWithSlave(
        dataSource,
        `SELECT
          ci.coverage_type as "coverageType",
          COUNT(p.id) AS "totalCount",
          SUM(b.amount_due) AS "totalAmountDue",
          SUM(b.amount_owning) AS "totalAmountOwing",
          SUM(b.amount_outstanding) AS "totalAmountOutstanding",
          SUM(b.discount_amount) AS "totalDiscount",
          SUM(b.amount_paid) AS "totalAmountPaid",
          SUM(b.total_amount) AS "totalAmount",
          patient.full_name AS "patientName",
          u.non_corporate_email_address AS "patientEmail",
          u.phone_number AS "patientPhone",
          to_char(p.surgery_date, 'DD/MON/YYYY') AS "visitDate",
          ${coverageNameCase},
          ${enrolleeIdCase}
        FROM
          surgeries p
          LEFT JOIN profiles patient ON p.profile = patient.id
          INNER JOIN users u ON u.id = patient.user
          LEFT JOIN coverage_information ci ON ci.profile = patient.id
          LEFT JOIN bills b ON b.id = p.bill
           LEFT JOIN hmo_profiles hp ON hp.coverage_information_id = ci.id
          LEFT JOIN hmo_providers hmp ON hmp.id = hp.provider_id
          ${applyFilterForHmoAndPartners({ profile, filter, table: 'p' })}
          AND ci.coverage_type IS NOT NULL
          AND date (p.created_date) BETWEEN '${start}' AND '${end}'
          ${
            coverageType === 'all'
              ? ''
              : `AND LOWER(ci.coverage_type) = '${coverageType}'`
          }
        GROUP BY
          ci.coverage_type, "coverageName", "patientName", "patientEmail", "visitDate", "memberNumber", "patientPhone"
        `,
      );
      return result;
    };

    const byNursingServices = async () => {
      const result = await queryDSWithSlave(
        dataSource,
        `SELECT
          ci.coverage_type as "coverageType",
          COUNT(n.id) AS "totalCount",
          SUM(b.amount_due) AS "totalAmountDue",
          SUM(b.amount_owning) AS "totalAmountOwing",
          SUM(b.amount_outstanding) AS "totalAmountOutstanding",
          SUM(b.discount_amount) AS "totalDiscount",
          SUM(b.amount_paid) AS "totalAmountPaid",
          SUM(b.total_amount) AS "totalAmount",
          patient.full_name AS "patientName",
          u.non_corporate_email_address AS "patientEmail",
          u.phone_number AS "patientPhone",
          to_char(n.procedure_date_time, 'DD/MON/YYYY') AS "visitDate",
          ${coverageNameCase},
          ${enrolleeIdCase}
        FROM
          nursing_service_details n
          LEFT JOIN nursing_service ns ON n.nursing_service_id = ns.id
          LEFT JOIN profiles patient ON n.profile = patient.id
          INNER JOIN users u ON u.id = patient.user
          LEFT JOIN coverage_information ci ON ci.profile = patient.id
          LEFT JOIN bill_details bd ON bd.id = n.billing
          LEFT JOIN bills b ON b.id = bd.bill
           LEFT JOIN hmo_profiles hp ON hp.coverage_information_id = ci.id
          LEFT JOIN hmo_providers hmp ON hmp.id = hp.provider_id
          ${applyFilterForHmoAndPartners({
            profile,
            filter,
            table: 'ns',
            hospitalCol: 'hospital_id',
          })}
          AND ci.coverage_type IS NOT NULL
          AND date (n.created_date) BETWEEN '${start}' AND '${end}'
          ${
            coverageType === 'all'
              ? ''
              : `AND LOWER(ci.coverage_type) = '${coverageType}'`
          }
        GROUP BY
          ci.coverage_type, "coverageName", "patientName", "patientEmail", "visitDate", "memberNumber", "patientPhone"
        `,
      );
      return result;
    };

    const byAntenatal = async () => {
      const result = await queryDSWithSlave(
        dataSource,
        `SELECT
          ci.coverage_type as "coverageType",
          COUNT(a.id) AS "totalCount",
          SUM(b.amount_due) AS "totalAmountDue",
          SUM(b.amount_owning) AS "totalAmountOwing",
          SUM(b.amount_outstanding) AS "totalAmountOutstanding",
          SUM(b.discount_amount) AS "totalDiscount",
          SUM(b.amount_paid) AS "totalAmountPaid",
          SUM(b.total_amount) AS "totalAmount",
          patient.full_name AS "patientName",
          u.non_corporate_email_address AS "patientEmail",
          u.phone_number AS "patientPhone",
          to_char(ad.visitation_date_time, 'DD/MON/YYYY') AS "visitDate",
          ${coverageNameCase},
          ${enrolleeIdCase}
        FROM
          antenatal_details ad
          LEFT JOIN antenatals a ON ad.antenatal = a.id
          LEFT JOIN profiles patient ON a.profile = patient.id
          INNER JOIN users u ON u.id = patient.user
          LEFT JOIN coverage_information ci ON ci.profile = patient.id
          LEFT JOIN bill_details bd ON bd.id = ad.billing
          LEFT JOIN bills b ON b.id = bd.bill
           LEFT JOIN hmo_profiles hp ON hp.coverage_information_id = ci.id
          LEFT JOIN hmo_providers hmp ON hmp.id = hp.provider_id
          ${applyFilterForHmoAndPartners({ profile, filter, table: 'a' })}
          AND ci.coverage_type IS NOT NULL
          AND date (a.created_date) BETWEEN '${start}' AND '${end}'
          ${
            coverageType === 'all'
              ? ''
              : `AND LOWER(ci.coverage_type) = '${coverageType}'`
          }
        GROUP BY
          ci.coverage_type, "coverageName", "patientName", "patientEmail", "visitDate", "memberNumber", "patientPhone"
        `,
      );
      return result;
    };
    const byLabourAndDelivery = async () => {
      const result = await queryDSWithSlave(
        dataSource,
        `SELECT
          ci.coverage_type as "coverageType",
          COUNT(l.id) AS "totalCount",
          SUM(b.amount_due) AS "totalAmountDue",
          SUM(b.amount_owning) AS "totalAmountOwing",
          SUM(b.amount_outstanding) AS "totalAmountOutstanding",
          SUM(b.discount_amount) AS "totalDiscount",
          SUM(b.amount_paid) AS "totalAmountPaid",
          SUM(b.total_amount) AS "totalAmount",
          patient.full_name AS "patientName",
          u.non_corporate_email_address AS "patientEmail",
          u.phone_number AS "patientPhone",
          to_char(l.created_date, 'DD/MON/YYYY') AS "visitDate",
          ${coverageNameCase},
          ${enrolleeIdCase}
        FROM
          labour_delivery l
          LEFT JOIN profiles patient ON l.profile = patient.id
          INNER JOIN users u ON u.id = patient.user
          LEFT JOIN coverage_information ci ON ci.profile = patient.id
          LEFT JOIN bills b ON b.id = l.bill
           LEFT JOIN hmo_profiles hp ON hp.coverage_information_id = ci.id
          LEFT JOIN hmo_providers hmp ON hmp.id = hp.provider_id
          ${applyFilterForHmoAndPartners({ profile, filter, table: 'l' })}
          AND ci.coverage_type IS NOT NULL
          AND date (l.created_date) BETWEEN '${start}' AND '${end}'
          ${
            coverageType === 'all'
              ? ''
              : `AND LOWER(ci.coverage_type) = '${coverageType}'`
          }
        GROUP BY
          ci.coverage_type, "coverageName", "patientName", "patientEmail", "visitDate", "memberNumber", "patientPhone"
        `,
      );
      return result;
    };
    const byPostnatal = async () => {
      const result = await queryDSWithSlave(
        dataSource,
        `SELECT
          ci.coverage_type as "coverageType",
          COUNT(p.id) AS "totalCount",
          SUM(b.amount_due) AS "totalAmountDue",
          SUM(b.amount_owning) AS "totalAmountOwing",
          SUM(b.amount_outstanding) AS "totalAmountOutstanding",
          SUM(b.discount_amount) AS "totalDiscount",
          SUM(b.amount_paid) AS "totalAmountPaid",
          SUM(b.total_amount) AS "totalAmount",
          patient.full_name AS "patientName",
          u.non_corporate_email_address AS "patientEmail",
          u.phone_number AS "patientPhone",
          to_char(p.visitation_date_time, 'DD/MON/YYYY') AS "visitDate",
          ${coverageNameCase},
          ${enrolleeIdCase}
        FROM
          postnatal p
          LEFT JOIN profiles patient ON p.profile = patient.id
          INNER JOIN users u ON u.id = patient.user
          LEFT JOIN coverage_information ci ON ci.profile = patient.id
          LEFT JOIN bills b ON b.id = p.bill
           LEFT JOIN hmo_profiles hp ON hp.coverage_information_id = ci.id
          LEFT JOIN hmo_providers hmp ON hmp.id = hp.provider_id
          ${applyFilterForHmoAndPartners({ profile, filter, table: 'p' })}
          AND ci.coverage_type IS NOT NULL
          AND date (p.created_date) BETWEEN '${start}' AND '${end}'
          ${
            coverageType === 'all'
              ? ''
              : `AND LOWER(ci.coverage_type) = '${coverageType}'`
          }
        GROUP BY
          ci.coverage_type, "coverageName", "patientName", "patientEmail", "visitDate", "memberNumber", "patientPhone"
        `,
      );
      return result;
    };

    const byMedicalReports = async () => {
      const result = await queryDSWithSlave(
        dataSource,
        `SELECT
          ci.coverage_type as "coverageType",
          COUNT(mr.id) AS "totalCount",
          SUM(b.amount_due) AS "totalAmountDue",
          SUM(b.amount_owning) AS "totalAmountOwing",
          SUM(b.amount_outstanding) AS "totalAmountOutstanding",
          SUM(b.discount_amount) AS "totalDiscount",
          SUM(b.total_amount) AS "totalAmount",
          patient.full_name AS "patientName",
          u.non_corporate_email_address AS "patientEmail",
          u.phone_number AS "patientPhone",
          to_char(mr.report_date, 'DD/MON/YYYY') AS "visitDate",
          ${coverageNameCase},
          ${enrolleeIdCase}
        FROM
          medical_report mr
          LEFT JOIN profiles patient ON mr.profile_id = patient.id
          INNER JOIN users u ON u.id = patient.user
          LEFT JOIN coverage_information ci ON ci.profile = patient.id
          LEFT JOIN bills b ON b.id = mr.bill
          LEFT JOIN hmo_profiles hp ON hp.coverage_information_id = ci.id
          LEFT JOIN hmo_providers hmp ON hmp.id = hp.provider_id
          ${applyFilterForHmoAndPartners({
            profile,
            filter,
            table: 'mr',
            hospitalCol: 'hospital_id',
          })}
          AND ci.coverage_type IS NOT NULL
          AND date (mr.created_date) BETWEEN '${start}' AND '${end}'
          ${
            coverageType === 'all'
              ? ''
              : `AND LOWER(ci.coverage_type) = '${coverageType}'`
          }
          GROUP BY
          ci.coverage_type, "coverageName", "patientName", "patientEmail", "visitDate", "memberNumber", "patientPhone"
        `,
      );
      return result;
    };
    const mapper = {
      Admission: byAdmission,
      Consultation: byConsultation,
      Immunization: byImmunization,
      Laboratory: () => byInvestigation('Laboratory'),
      Radiology: () => byInvestigation('Radiology'),
      'Request Investigation': () => byInvestigationStatus(false),
      'Process Investigation': () => byInvestigationStatus(true),
      'Medications Prescribed': () =>
        byMedicationPrescribed(MedicationOptionType.M),
      'Consumables Prescribed': () =>
        byMedicationPrescribed(MedicationOptionType.C),
      'Medications Dispensed': () =>
        byMedicationDispensed(MedicationOptionType.M),
      'Consumables Consumed': () =>
        byMedicationDispensed(MedicationOptionType.C),
      Procedure: byProcedure,
      'Nursing Services': byNursingServices,
      Antenatal: byAntenatal,
      'Labour and Delivery': byLabourAndDelivery,
      Postnatal: byPostnatal,
      'Medical Reports': byMedicalReports,
    };

    const servicesByCoverageType = await mapper[servicesType]();
    return {
      servicesSummary: [],
      list: {
        servicesByCoverageType: servicesByCoverageType.map((item) => ({
          ...item,
          name: extractPeriod(new Date(startDate), duration),
        })),
      },
    };
  },
  async getInternalAndExternalInvestigation(
    dataSource: DataSource,
    profile: ProfileModel,
    filter: ServicesAnalyticsFilter,
  ): Promise<ServiceSummaryWithList> {
    const {
      startDate,
      endDate,
      orderBy = 'Laboratory',
      duration,
      investigation: inv = 'all',
    } = filter;
    const investigation = inv.toLowerCase();
    const external =
      investigation !== 'all'
        ? `AND external = ${investigation === 'external'}`
        : '';

    const createSummary = (list: (ResultsByPatient | ExamResults)[]) => {
      const summary = list.reduce(
        (acc, curr) => {
          if (!acc[curr.investigationId]) {
            acc[curr.investigationId] = {
              totalInternal: 0,
              totalInternalQuantity: 0,
              totalInternalAmount: 0,
              totalExternal: 0,
              totalExternalQuantity: 0,
              totalExternalAmount: 0,
            };
          }
          const isExternal = curr.external;
          acc[curr.investigationId][
            isExternal ? 'totalExternal' : 'totalInternal'
          ] = 1;
          acc[curr.investigationId][
            isExternal ? 'totalExternalQuantity' : 'totalInternalQuantity'
          ] += 1;
          acc[curr.investigationId][
            isExternal ? 'totalExternalAmount' : 'totalInternalAmount'
          ] += parseFloat(curr.amount || '0');

          return acc;
        },
        {} as Record<
          string,
          {
            totalInternal: number;
            totalInternalQuantity: number;
            totalInternalAmount: number;
            totalExternal: number;
            totalExternalQuantity: number;
            totalExternalAmount: number;
          }
        >,
      );
      const {
        totalInternal,
        totalExternal,
        totalInternalQuantity,
        totalExternalQuantity,
        totalInternalAmount,
        totalExternalAmount,
      } = Object.values(summary).reduce(
        (acc, curr) => ({
          totalInternal: acc.totalInternal + curr.totalInternal,
          totalExternal: acc.totalExternal + curr.totalExternal,
          totalInternalQuantity:
            acc.totalInternalQuantity + curr.totalInternalQuantity,
          totalExternalQuantity:
            acc.totalExternalQuantity + curr.totalExternalQuantity,
          totalInternalAmount:
            acc.totalInternalAmount + curr.totalInternalAmount,
          totalExternalAmount:
            acc.totalExternalAmount + curr.totalExternalAmount,
        }),
        {
          totalInternal: 0,
          totalExternal: 0,
          totalInternalQuantity: 0,
          totalExternalQuantity: 0,
          totalInternalAmount: 0,
          totalExternalAmount: 0,
        },
      );
      return {
        totalInternal,
        totalExternal,
        totalInternalQuantity,
        totalExternalQuantity,
        totalInternalAmount,
        totalExternalAmount,
      };
    };
    const { start, end } = formatDate(startDate, endDate);
    if (orderBy === 'Radiology') {
      let examResults: ExamResults[] = await queryDSWithSlave(
        dataSource,
        `SELECT 	exam->>'examType' as "examType",
        profiles.id,
        profiles.gender as "gender",
        bill_details.amount_due as "amount",
        profiles.full_name as "patientName",
        profiles.clinify_id as "clinifyId",
        bill_details."paymentType",
        radiology_results.details,
        investigations.external,
        investigations.id as "investigationId"
      FROM
        investigations
        LEFT JOIN radiology_results ON investigations.radiology_result = radiology_results.id
        LEFT JOIN profiles ON investigations.profile = profiles.id
        LEFT JOIN details ON profiles.details = details.id
        LEFT JOIN LATERAL jsonb_array_elements(examination_type) AS exam ON TRUE
        LEFT JOIN bill_details ON exam ->> 'ref' = bill_details.reference
      ${applyFilterForHmoAndPartners({
        profile,
        filter,
        table: 'investigations',
      })}
      AND request_type = 'Radiology'
      AND investigations.status = 'Approved'
      AND date(investigations.created_date) BETWEEN '${start}' AND '${end}'
          ${external}
    `,
      );

      examResults = examResults.map((item) => ({
        ...item,
        examinationDate: moment(
          item.details?.find((v) => v.examType === item.examType)
            ?.examinationDate,
        ).format('DD/MMM/YYYY hh:mm A'),
        radiographerName: item.details?.find(
          (v) => v.examType === item.examType,
        )?.radiographerName,
      }));
      const listByPatient = groupDataByCategoryWithMetrics(
        examResults,
        ['examType', 'patientId', 'testDate', 'performedBy'],
        {
          name: (items) => items[0].examType,
          count: (items) => items.length,
          totalAmount: (items) =>
            items.reduce(
              (acc, curr) => acc + parseFloat(curr.amount || '0'),
              0,
            ),
          quantity: (items) =>
            items.reduce(
              (acc, curr) => acc + parseFloat(curr.quantity || '1'),
              0,
            ),
          patientName: (items) => items[0].patientName,
          clinifyId: (items) => items[0].clinifyId,
          paymentType: (items) => items[0].paymentType,
          examinationDate: (items) => items[0].examinationDate,
          radiographerName: (items) => items[0].radiographerName,
          isExternal: (items) => items[0].external,
        },
      );
      const summary = createSummary(examResults);
      return {
        servicesSummary: [
          {
            name: extractPeriod(new Date(startDate), duration),
            totalInternalInvestigations: summary.totalInternal,
            totalExternalInvestigations: summary.totalExternal,
            totalInternalInvestigationsQuantity: summary.totalInternalQuantity,
            totalExternalInvestigationsQuantity: summary.totalExternalQuantity,
            totalInternalInvestigationsAmount: summary.totalInternalAmount,
            totalExternalInvestigationsAmount: summary.totalExternalAmount,
          },
        ],
        list: {
          byInternalAndExternalInvestigation: listByPatient.female
            .concat(listByPatient.male)
            .sort(
              (a, b) =>
                a.examinationDate?.localeCompare(b.examinationDate || '') || 0,
            )
            .map((item) => ({
              ...item,
              quantity: item.quantity.toString(),
            })),
        },
      };
    }

    let resultsByPatient: ResultsByPatient[] = await queryDSWithSlave(
      dataSource,
      `SELECT    lab_info ->> 'testName' as "testName",
        profiles.id,
        profiles.gender as "gender",
        bill_details.amount_due as "amount",
        profiles.full_name as "patientName",
        profiles.clinify_id as "clinifyId",
        bill_details."paymentType", 
        lab_results.details,
        investigations.id as "investigationId",
        investigations.external
      FROM
        investigations
        LEFT JOIN lab_results ON investigations.lab_result = lab_results.id
        LEFT JOIN profiles ON investigations.profile = profiles.id
        LEFT JOIN details ON profiles.details = details.id
        LEFT JOIN LATERAL jsonb_array_elements(test_info) AS lab_info ON TRUE
        LEFT JOIN bill_details ON lab_info->> 'ref' = bill_details.reference
      ${applyFilterForHmoAndPartners({
        profile,
        filter,
        table: 'investigations',
      })}
      AND request_type = 'Laboratory'
      AND investigations.status = 'Approved'
      AND date(investigations.created_date) BETWEEN '${start}' AND '${end}'
      ${external}
    `,
    );

    resultsByPatient = resultsByPatient.map((item) => ({
      ...item,
      testDate: moment(
        item.details?.find((v) => v.testName === item.testName)
          ?.testDate as unknown as string,
      ).format('DD/MMM/YYYY hh:mm A'),
      performedBy: item.details?.find((v) => v.testName === item.testName)
        ?.performedBy,
    }));

    const listByPatient = groupDataByCategoryWithMetrics(
      resultsByPatient,
      ['testName', 'patientId', 'testDate', 'performedBy'],
      {
        name: (items) => items[0].testName,
        count: (items) => items.length,
        totalAmount: (items) =>
          items.reduce((acc, curr) => acc + parseFloat(curr.amount || '0'), 0),
        quantity: (items) =>
          items.reduce(
            (acc, curr) => acc + parseFloat(curr.quantity || '1'),
            0,
          ),
        patientName: (items) => items[0].patientName,
        clinifyId: (items) => items[0].clinifyId,
        paymentType: (items) => items[0].paymentType,
        testDate: (items) => items[0].testDate,
        performedBy: (items) => items[0].performedBy,
        isExternal: (items) => items[0].external,
      },
    );
    const summary = createSummary(resultsByPatient);
    return {
      servicesSummary: [
        {
          name: extractPeriod(new Date(startDate), duration),
          totalInternalInvestigations: summary.totalInternal,
          totalExternalInvestigations: summary.totalExternal,
          totalInternalInvestigationsQuantity: summary.totalInternalQuantity,
          totalExternalInvestigationsQuantity: summary.totalExternalQuantity,
          totalInternalInvestigationsAmount: summary.totalInternalAmount,
          totalExternalInvestigationsAmount: summary.totalExternalAmount,
        },
      ],
      list: {
        byInternalAndExternalInvestigation: listByPatient.female
          .concat(listByPatient.male)
          .sort(
            (a, b) =>
              a.examinationDate?.localeCompare(b.examinationDate || '') || 0,
          )
          .map((item) => ({
            ...item,
            quantity: item.quantity.toString(),
          })),
      },
    };
  },
  async getNursingService(dataSource, profile, filter) {
    const { startDate, endDate, duration, gender, ageRange } = filter;
    const { start, end } = formatDate(startDate, endDate);

    const result: {
      gender: string;
      procedureType: string;
      total: string;
      totalAmount: string;
      quantity: string;
    }[] = await queryDSWithSlave(
      dataSource,
      `SELECT 
      profiles.gender as gender,
      nsd.procedure_type as "procedureType",
      count(nsd.procedure_type) as "total",
      SUM(COALESCE(bill_details.amount_due, 0)) AS "totalAmount",
      count(*) as "quantity"
    FROM nursing_service_details nsd
    LEFT JOIN nursing_service ON nursing_service.id = nsd.nursing_service_id
    LEFT JOIN profiles ON nursing_service.profile = profiles.id
    LEFT JOIN details ON profiles.details = details.id
    LEFT JOIN bill_details ON nsd.billing = bill_details.id
    ${applyFilterForHmoAndPartners({
      profile,
      filter,
      table: 'nursing_service',
      hospitalCol: 'hospital_id',
    })}
    ${gender && gender !== 'All' ? `AND profiles.gender = '${gender}'` : ''}
    ${ageRangeQueryString(ageRange)}
    AND date(nsd.created_date) BETWEEN '${start}' AND '${end}'
    AND nsd.procedure_type IS NOT NULL AND nsd.procedure_type != ''
    ${inPatientQueryString('nursing_service', filter.inPatient)}
    GROUP BY profiles.gender, nsd.procedure_type, details.date_of_birth
  `,
    );
    const byNurseNameResult: {
      nurseName: string;
      procedureType: string;
      procedureDate: string;
      paymentType: string;
      patientName: string;
      clinifyId: string;
      amount: string;
      quantity: string;
    }[] = await queryDSWithSlave(
      dataSource,
      `SELECT 
      nursing_service.nurse_name as "nurseName",
      nsd.procedure_type as "procedureType",
      nsd.procedure_date_time as "procedureDate",
       bill_details."paymentType" AS "paymentType",
      profiles.full_name AS "patientName",
      profiles.clinify_id AS "clinifyId",
      profiles.id AS "profileId",
      SUM(bill_details.amount_due) AS "amount",
      bill_details.id AS "billingId",
      count(*) as "quantity"
    FROM nursing_service_details nsd
    LEFT JOIN nursing_service ON nursing_service.id = nsd.nursing_service_id
      LEFT JOIN profiles ON nursing_service.profile = profiles.id
    LEFT JOIN details ON profiles.details = details.id
    LEFT JOIN bill_details ON nsd.billing = bill_details.id
    ${applyFilterForHmoAndPartners({
      profile,
      filter,
      table: 'nursing_service',
      hospitalCol: 'hospital_id',
    })}
     ${gender && gender !== 'All' ? `AND profiles.gender = '${gender}'` : ''}
    ${ageRangeQueryString(ageRange)}
    AND date(nsd.created_date) BETWEEN '${start}' AND '${end}'
    AND nsd.procedure_type IS NOT NULL AND nsd.procedure_type != ''
    AND nursing_service.nurse_name IS NOT NULL AND nursing_service.nurse_name != ''
    AND nsd.procedure_date_time IS NOT NULL 
    ${inPatientQueryString('nursing_service', filter.inPatient)}
    GROUP BY nursing_service.nurse_name, nsd.procedure_type, bill_details.id, profiles.id, nsd.procedure_date_time
    ORDER BY "procedureDate" DESC
  `,
    );
    const groups = result.reduce(
      (acc, item) => {
        const { gender, procedureType, total, totalAmount } = item;

        const ageRangeIndex = acc.byAgeRange.findIndex(
          (v) => v.procedureType === procedureType,
        );
        if (ageRangeIndex === -1) {
          acc.byAgeRange.push({
            ageRange: ageRange || 'All',
            procedureType,
            totalFemale: gender === 'Female' ? +total : 0,
            totalMale: gender === 'Male' ? +total : 0,
            totalFemaleAmount: gender === 'Female' ? +(totalAmount || '0') : 0,
            totalMaleAmount: gender === 'Male' ? +(totalAmount || '0') : 0,
          });
        } else {
          const ageRangeItem = acc.byAgeRange[ageRangeIndex];
          acc.byAgeRange[ageRangeIndex] = {
            ageRange: ageRange || 'All',
            procedureType,
            totalFemale:
              gender === 'Female'
                ? +total
                : 0 + (ageRangeItem?.totalFemale || 0),
            totalMale:
              (gender === 'Male' ? +total : 0) + (ageRangeItem?.totalMale || 0),
            totalFemaleAmount:
              gender === 'Female'
                ? +totalAmount
                : 0 + (ageRangeItem?.totalFemaleAmount || 0),
            totalMaleAmount:
              gender === 'Male'
                ? +totalAmount
                : 0 + (ageRangeItem?.totalMaleAmount || 0),
          };
        }

        return {
          ...acc,
          totalFemale: +(gender === 'Female' ? +total : 0) + acc.totalFemale,
          totalMale: +(gender === 'Male' ? +total : 0) + acc.totalMale,
          totalFemaleAmount:
            +(gender === 'Female' ? +totalAmount : 0) + acc.totalFemaleAmount,
          totalMaleAmount:
            +(gender === 'Male' ? +totalAmount : 0) + acc.totalMaleAmount,
        };
      },
      {
        totalFemale: 0,
        totalMale: 0,
        byAgeRange: [],
        byNurseName: [],
        totalFemaleAmount: 0,
        totalMaleAmount: 0,
      },
    );
    return {
      name: extractPeriod(new Date(start), duration),
      totalFemale: groups.totalFemale,
      totalMale: groups.totalMale,
      totalFemaleAmount: groups.totalFemaleAmount,
      totalMaleAmount: groups.totalMaleAmount,
      byAgeRange: groups.byAgeRange,
      byNurseName: byNurseNameResult.map((item) => ({
        ...item,
        procedureDate: moment(item.procedureDate).format('DD/MMM/YYYY hh:mm A'),
      })),
    };
  },
  async getRequestedToFacility(
    dataSource: DataSource,
    profile: ProfileModel,
    filter: ServicesAnalyticsFilter,
  ) {
    const { startDate, endDate, duration, orderBy = 'all' } = filter;
    const { start, end } = formatDate(startDate, endDate);
    const results: RequestedToFacilityResponse[] = [];
    if (['all', 'to'].includes(orderBy)) {
      const result = await queryDSWithSlave(
        dataSource,
        `SELECT 
      hospitals.name as "facilityName",
      COUNT(investigations.id) as "total",
      'to' as "requestType"
    FROM investigations
    LEFT JOIN hospitals ON investigations.hospital = hospitals.id
    ${applyFilterForHmoAndPartners({
      profile,
      filter,
      table: 'investigations',
      hospitalCol: 'referringHospitalId',
    })}
    AND investigations.hospital != '${profile.hospitalId}'
    AND date(investigations.created_date) BETWEEN '${start}' AND '${end}'
    AND investigations."referringHospitalId" IS NOT NULL
    AND investigations.external = true
    GROUP BY hospitals.name
  `,
      );
      results.push(...result);
    }
    if (['all', 'from'].includes(orderBy)) {
      const result = await queryDSWithSlave(
        dataSource,
        `SELECT 
      hospitals.name as "facilityName",
      COUNT(investigations.id) as "total",
       'from' as "requestType"
    FROM investigations
   LEFT JOIN hospitals ON investigations."referringHospitalId" = hospitals.id
    ${applyFilterForHmoAndPartners({
      profile,
      filter,
      table: 'investigations',
      hospitalCol: 'hospital',
    })}
    AND investigations."referringHospitalId" != '${profile.hospitalId}'
    AND date(investigations.created_date) BETWEEN '${start}' AND '${end}'
    AND investigations."referringHospitalId" IS NOT NULL
       AND investigations.external = true
    GROUP BY hospitals.name
  `,
      );
      results.push(...result);
    }
    return results.map((item) => ({
      ...item,
      name: extractPeriod(new Date(start), duration),
    }));
  },
  async getMedicalReport(
    dataSource,
    profile,
    filter,
  ): Promise<MedicalReportSummeryResponse> {
    const { startDate, endDate, duration, gender, ageRange } = filter;
    const { start, end } = formatDate(startDate, endDate);

    const result: {
      gender: string;
      name: string;
      doctor_name: string;
      reportDate: string;
      amount: string;
      paymentType: string;
      patientName: string;
      clinifyId: string;
    }[] = await queryDSWithSlave(
      dataSource,
      `SELECT  profiles.gender as gender,
       rt->>'name' as "name",
       mr.doctor_name,
       mr.report_date as "reportDate",
       COALESCE(bill_details.amount_due, 0) AS "amount",
       bill_details."paymentType" AS "paymentType",
       mr.patient_fullname AS "patientName",
       profiles.clinify_id AS "clinifyId"
    FROM medical_report mr
    LEFT JOIN profiles ON mr.profile_id = profiles.id
    LEFT JOIN details ON profiles.details = details.id
    LEFT JOIN LATERAL jsonb_array_elements(mr.report_type) AS rt ON true
    LEFT JOIN bill_details ON rt->>'ref' = bill_details.reference
    ${applyFilterForHmoAndPartners({
      profile,
      filter,
      table: 'mr',
      hospitalCol: 'hospital_id',
      profileCol: 'profile_id',
    })}
    ${gender && gender !== 'All' ? `AND profiles.gender = '${gender}'` : ''}
    ${ageRangeQueryString(ageRange)}
    AND date(mr.report_date) BETWEEN '${start}' AND '${end}'
    ${inPatientQueryString('mr', filter.inPatient)}
  `,
    );
    const groups = result.reduce(
      (acc, item) => {
        const { gender, name, amount } = item;

        const existingIndex = acc.byAgeRange?.findIndex(
          (v) => v.medicalReportType === name,
        );

        if (existingIndex === -1) {
          acc.byAgeRange.push({
            ageRange: ageRange || 'All',
            medicalReportType: name,
            totalFemale: gender === 'Female' ? 1 : 0,
            totalMale: gender === 'Male' ? 1 : 0,
            totalOther: !gender || gender === 'Others' ? 1 : 0,
            totalFemaleAmount: gender === 'Female' ? +(amount || '0') : 0,
            totalMaleAmount: gender === 'Male' ? +(amount || '0') : 0,
            totalOtherAmount:
              !gender || gender === 'Others' ? +(amount || '0') : 0,
          });
        } else {
          const ageRangeItem = acc.byAgeRange[existingIndex];
          acc.byAgeRange[existingIndex] = {
            ageRange: ageRange || 'All',
            medicalReportType: name,
            totalFemale:
              (gender === 'Female' ? 1 : 0) + (ageRangeItem?.totalFemale || 0),
            totalMale:
              (gender === 'Male' ? 1 : 0) + (ageRangeItem?.totalMale || 0),
            totalOther:
              (!gender || gender === 'Others' ? 1 : 0) +
              (ageRangeItem?.totalOther || 0),
            totalFemaleAmount:
              (gender === 'Female' ? +(amount || '0') : 0) +
              (ageRangeItem?.totalFemaleAmount || 0),
            totalMaleAmount:
              (gender === 'Male' ? +(amount || '0') : 0) +
              (ageRangeItem?.totalMaleAmount || 0),
            totalOtherAmount:
              (!gender || gender === 'Others' ? +(amount || '0') : 0) +
              (ageRangeItem?.totalOtherAmount || 0),
          };
        }

        switch (gender) {
          case 'Male':
            acc.totalMale += 1;
            break;
          case 'Female':
            acc.totalFemale += 1;
            break;
          default:
            acc.totalOther += 1;
            break;
        }
        return acc;
      },
      {
        totalFemale: 0,
        totalMale: 0,
        totalOther: 0,
        totalFemaleAmount: 0,
        totalMaleAmount: 0,
        totalOtherAmount: 0,
        byAgeRange: [],
      },
    );
    const byPatientName = result.map((item) => ({
      reportedBy: item.doctor_name,
      reportDate: moment(item.reportDate).format('DD/MM/YYYY HH:mm a'),
      patientName: item.patientName,
      paymentType: item.paymentType,
      totalAmount: +(item.amount || '0'),
      name: item.name,
      clinifyId: item.clinifyId,
    }));
    return {
      name: extractPeriod(new Date(start), duration),
      totalFemale: groups.totalFemale,
      totalMale: groups.totalMale,
      totalOther: groups.totalOther,
      totalFemaleAmount: groups.totalFemaleAmount,
      totalMaleAmount: groups.totalMaleAmount,
      totalOtherAmount: groups.totalOtherAmount,
      byAgeRange: groups.byAgeRange,
      byPatientName,
    };
  },
} as IServicesAnalyticsRepository;

function getOrderByKeyword(orderBy: string) {
  switch (orderBy) {
    case 'least':
      return 'ASC';
    case 'most':
      return 'DESC';
    default:
      return 'DESC';
  }
}
