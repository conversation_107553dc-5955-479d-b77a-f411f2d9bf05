import { Test, TestingModule } from '@nestjs/testing';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Chance } from 'chance';
import { DataSource, EntityManager } from 'typeorm';
import {
  CustomDeathsAnalyticsRepoMethods,
  DeathsAnalyticsModel,
  IDeathsAnalyticRepository,
} from './dealths.repository';
import * as db from '../../database';
import { setFilter } from '../inputs/services.input';
import { TestDataSourceOptions } from '@clinify/data-source';
import { extendDSRepo, extendModel } from '@clinify/database/extendModel';
import { UserType } from '@clinify/shared/enums/users';
import { Gender } from '@clinify/shared/validators/personal-information.input';
import { createAdmissions } from '@clinify/utils/tests/admission.fixtures';
import { createDischargePatients } from '@clinify/utils/tests/dischargePatient.fixtures';
import { createHospitals } from '@clinify/utils/tests/hospital.fixtures';
import { createObstetricHistory } from '@clinify/utils/tests/my-health.fixtures';
import { createUsers } from '@clinify/utils/tests/user.fixtures';

const chance = new Chance();

describe('CustomDeathsAnalyticsRepoMethods', () => {
  let manager: EntityManager;
  let dataSource: DataSource;
  let repository: IDeathsAnalyticRepository;
  let module: TestingModule;
  const spySlaveQuery = jest.spyOn(db, 'queryWithSlave');

  beforeAll(async () => {
    module = await Test.createTestingModule({
      imports: [
        TypeOrmModule.forRoot(TestDataSourceOptions),
        TypeOrmModule.forFeature([DeathsAnalyticsModel]),
      ],
      providers: [
        extendModel(DeathsAnalyticsModel, CustomDeathsAnalyticsRepoMethods),
      ],
    }).compile();

    dataSource = module.get<DataSource>(DataSource);
    manager = dataSource.manager;
    repository = extendDSRepo<IDeathsAnalyticRepository>(
      dataSource,
      DeathsAnalyticsModel,
      CustomDeathsAnalyticsRepoMethods,
    );
    spySlaveQuery.mockImplementation((qr, pr) => manager.query(qr, pr));
  });
  afterAll(async () => {
    await module.close();
    await dataSource.destroy();
    await module.close();
  });

  const createUserWithGender = async (gender: Gender, hospital) => {
    const user = await createUsers(
      manager,
      3,
      hospital,
      undefined,
      undefined,
      chance.pickone([UserType.OrganizationDoctor]),
      undefined,
      undefined,
      undefined,
      gender,
    );
    return user;
  };

  it('should be defined', () => {
    expect(repository).toBeDefined();
  });

  it('getDeathsAmongIndividuals(): should return individual deaths', async () => {
    const hospital = (await createHospitals(manager, 1))[0];
    const user = await createUserWithGender(Gender.Female, hospital);
    const userMale = await createUserWithGender(Gender.Male, hospital);
    const profile = user[0].defaultProfile;
    const profileMale = userMale[0].defaultProfile;
    const [admission] = await createAdmissions(
      manager,
      1,
      null,
      profile,
      undefined,
      hospital,
    );
    const [admissionMale] = await createAdmissions(
      manager,
      1,
      null,
      profileMale,
      undefined,
      hospital,
    );
    await createDischargePatients(manager, 3, admission, profile, {
      dischargedStatus: 'Dead',
      deathCause: 'Postpartom heamorrhage',
    });
    await createDischargePatients(manager, 3, admissionMale, profile, {
      dischargedStatus: 'Dead',
      deathCause: 'Malaria',
    });

    const result = await repository.getDeathsAmongIndividuals(
      dataSource,
      profile,
      {
        ...setFilter,
        duration: 'YEAR',
        ageRange: '15-30 years',
      },
    );
    expect(result).toBeDefined();
    expect(result.totalMale).toBe('3');
    expect(result.totalFemale).toBe('3');
    expect(result.byCauseOfDeath).toStrictEqual([
      { category: 'Malaria', count: 3 },
      { category: 'Pneumonia', count: 0 },
      { category: 'Malnutrition', count: 0 },
      { category: 'Postpartom heamorrhage', count: 3 },
      { category: 'Sepsis', count: 0 },
      { category: 'Obstructed Labour', count: 0 },
      { category: 'Abortion', count: 0 },
      { category: 'Anaemia', count: 0 },
      { category: 'HIV', count: 0 },
      { category: 'Stroke', count: 0 },
      { category: 'Hip fracture', count: 0 },
      { category: 'Other', count: 0 },
    ]);
    expect(result.ageRanges).toStrictEqual([
      { category: '0-28 Days', count: 0 },
      { category: '1-11 Months', count: 0 },
      { category: '12-59 months', count: 0 },
      { category: '5-9 Years', count: 0 },
      { category: '10-19 Years', count: 0 },
      { category: '20-40 years', count: 6 },
      { category: '> 40 Years', count: 0 },
    ]);

    const result1 = await repository.getDeathsAmongIndividuals(
      dataSource,
      profile,
      {
        ...setFilter,
        duration: 'YEAR',
        ageRange: '15-30 Years',
        gender: 'All',
      },
    );
    expect(result1).toBeDefined();
    expect(result1.totalMale).toBe('3');
    expect(result1.totalFemale).toBe('3');
    const result2 = await repository.getDeathsAmongIndividuals(
      dataSource,
      profile,
      {
        ...setFilter,
        duration: 'YEAR',
        ageRange: '15-30 Years',
        gender: 'Female',
      },
    );
    expect(result2).toBeDefined();
    expect(result2.totalMale).toBe('0');
    expect(result2.totalFemale).toBe('3');
  });

  it('getMaternalDeaths(): should return maternal deaths', async () => {
    const hospital = (await createHospitals(manager, 1))[0];
    const user = await createUserWithGender(Gender.Female, hospital);
    const profile = user[0].defaultProfile;

    await createObstetricHistory(
      manager,
      2,
      profile,
      undefined,
      hospital,
      profile,
      {
        motherStatus: 'Dead',
        motherCauseOfDeath: 'Sepsis',
      },
    );
    const result = await repository.getMaternalDeaths(dataSource, profile, {
      ...setFilter,
      duration: 'YEAR',
      ageRange: '15-30 years',
    });
    expect(result).toBeDefined();
    expect(result.byCauseOfDeath).toStrictEqual([
      { category: 'Abortion', count: 0 },
      { category: 'Anaemia', count: 0 },
      { category: 'HIV', count: 0 },
      { category: 'Obstructed Labour', count: 0 },
      { category: 'Postpartom heamorrhage', count: 0 },
      { category: 'Sepsis', count: 2 },
      { category: 'Malaria', count: 0 },
      { category: 'Others', count: 0 },
    ]);
    expect(result.ageRanges).toStrictEqual([
      { category: '10 - 19 years', count: 0 },
      { category: '> 20 years', count: 2 },
    ]);
  });

  it('getNeoNatalDeaths(): should return neo natal deaths', async () => {
    const hospital = (await createHospitals(manager, 1))[0];
    const user = await createUserWithGender(Gender.Female, hospital);
    const profile = user[0].defaultProfile;

    await createObstetricHistory(
      manager,
      2,
      profile,
      undefined,
      hospital,
      profile,
      {
        gender: profile.gender,
        babyStatus: 'Dead',
        babyCauseOfDeath: 'Neonatal Tetanus',
      },
    );
    const result = await repository.getNeoNatalDeaths(dataSource, profile, {
      ...setFilter,
      duration: 'YEAR',
      ageRange: '15-30 years',
    });
    expect(result).toBeDefined();
    expect(result.totalMale).toBe('0');
    expect(result.totalFemale).toBe('2');
    expect(result.byCauseOfDeath).toStrictEqual([
      { category: 'Congenital Malformation', count: 0 },
      { category: 'Neonatal Tetanus', count: 2 },
      { category: 'Prematurity', count: 0 },
      { category: 'Anaemia', count: 0 },
      { category: 'Malnutrition', count: 0 },
      { category: 'HIV', count: 0 },
      { category: 'Others', count: 0 },
    ]);
  });

  it('getUnderFiveDeaths(): should return deaths of individuals under 5yrs', async () => {
    const hospital = (await createHospitals(manager, 1))[0];
    const user = await createUserWithGender(Gender.Female, hospital);
    const profile = user[0].defaultProfile;
    const [admission] = await createAdmissions(manager, 1, null, profile);
    await createDischargePatients(manager, 3, admission, profile, {
      dischargedStatus: 'Dead',
      deathCause: 'Malaria',
    });

    const result = await repository.getUnderFiveDeaths(dataSource, profile, {
      ...setFilter,
      duration: 'YEAR',
      ageRange: '15-30 years',
    });
    expect(result).toBeDefined();
    expect(result.totalMale).toBeTruthy();
    expect(result.totalFemale).toBeTruthy();
    expect(result.byCauseOfDeath).toBeTruthy();
  });
  it('getDeathsByDepartment(): should return deaths by department', async () => {
    const hospital = (await createHospitals(manager, 1))[0];
    const user = await createUserWithGender(Gender.Female, hospital);
    const profile = user[0].defaultProfile;
    const departments = ['Surgery', 'Consultation', 'Maternity'];
    departments.forEach(async (department) => {
      const [admission] = await createAdmissions(
        manager,
        1,
        null,
        profile,
        undefined,
        undefined,
        {
          department,
        },
      );
      await createDischargePatients(manager, 3, admission, profile, {
        dischargedStatus: 'Dead',
        deathCause: 'Malaria',
      });
    });

    const results = await repository.getDeathsByDepartment(
      dataSource,
      profile,
      {
        ...setFilter,
        duration: 'YEAR',
      },
    );
    expect(results.length).toBeDefined();

    results.forEach((result) => {
      expect(result.category).toBeDefined();
      expect(result.count).toEqual(3);
    });
  });

  it('getDeathsOverview(): should return deaths overview', async () => {
    const hospital = (await createHospitals(manager, 1))[0];
    const user = await createUserWithGender(Gender.Female, hospital);
    const profile = user[0].defaultProfile;
    const [admission] = await createAdmissions(
      manager,
      1,
      null,
      profile,
      undefined,
      hospital,
      undefined,
    );
    await createDischargePatients(manager, 3, admission, profile, {
      dischargedStatus: 'Dead',
      deathCause: 'Postpartom heamorrhage',
      hispitalId: hospital.id,
    });

    await createObstetricHistory(
      manager,
      2,
      profile,
      undefined,
      hospital,
      profile,
      {
        gender: profile.gender,
        babyStatus: 'Dead',
        motherStatus: 'Dead',
        babyCauseOfDeath: 'Neonatal Tetanus',
        deadLessThan7days: 'Yes',
      },
    );

    const result = await repository.getDeathsOverview(dataSource, profile, {
      ...setFilter,
      duration: 'YEAR',
      ageRange: '15-30 years',
    });
    expect(result).toBeDefined();
    expect(result).toBeTruthy();
    expect(result).toHaveProperty('groupedData');
    expect(Number(result.groupedData.totalDeaths[0].count)).toBe(3);
    expect(result.groupedData.maternalAndNeonatalDeaths).toContainEqual(
      expect.objectContaining({
        totalMaternalDeaths: '2',
        totalNeonatalDeaths: '2',
      }),
    );
  });
});
