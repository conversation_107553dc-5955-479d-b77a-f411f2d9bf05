import { Column, Entity, ManyToOne, PrimaryGeneratedColumn } from 'typeorm';
import { SurgeryModel } from './surgery.model';
import { InvestigationModel } from '@clinify/investigation/models/investigation.model';
import { InvestigationRequestType } from '@clinify/shared/enums/investigation';

@Entity({ name: 'surgery_investigation' })
export class SurgeryToInvestigation {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  surgeryId: string;

  @Column()
  investigationId: string;

  @Column({
    type: 'enum',
    enum: InvestigationRequestType,
    name: 'type',
    nullable: false,
    default: InvestigationRequestType.Laboratory,
  })
  type: InvestigationRequestType;

  @ManyToOne(() => SurgeryModel, (surgery) => surgery.surgery_investigation, {
    onDelete: 'CASCADE',
  })
  surgery: SurgeryModel;

  @ManyToOne(
    () => InvestigationModel,
    (investigation) => investigation.surgery_investigation,
    { onDelete: 'CASCADE' },
  )
  investigation: InvestigationModel;

  constructor(surgeryToInvestigation: Partial<SurgeryToInvestigation>) {
    Object.assign(this, surgeryToInvestigation);
  }
}
