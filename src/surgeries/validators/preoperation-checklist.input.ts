import { Field, InputType } from '@nestjs/graphql';
import { IsOptional, IsUUID } from 'class-validator';

@InputType()
export class PreOperationChecklistInput {
  @Field({ nullable: true })
  @IsUUID('4')
  @IsOptional()
  id?: string;

  @Field(() => String, { nullable: true })
  clinifyId?: string;

  @Field(() => Date, { nullable: true })
  operationDate: Date;

  @Field(() => Boolean, { defaultValue: false })
  @IsOptional()
  preSurgeryReadiness: boolean;

  @Field(() => String, { nullable: true })
  completeConsent?: string;

  @Field(() => String, { nullable: true })
  bloodProductConent?: string;

  @Field(() => String, { nullable: true })
  alternateForm?: string;

  @Field(() => String, { nullable: true })
  historyAvailable?: string;

  @Field(() => String, { nullable: true })
  allergiesMarked?: string;

  @Field(() => String, { nullable: true })
  bpmhComplete?: string;

  @Field(() => String, { nullable: true })
  vitalsComplete?: string;

  @Field(() => String, { nullable: true })
  heightWeightDoc?: string;

  @Field(() => String, { nullable: true })
  anaestheticHistory?: string;

  @Field(() => String, { nullable: true })
  completeConsult?: string;

  @Field(() => String, { nullable: true })
  aroDocument?: string;

  @Field(() => String, { nullable: true })
  interpreterNeeded?: string;

  @Field(() => String, { nullable: true })
  fallsRisk?: string;

  @Field(() => String, { nullable: true })
  bariatricRequired?: string;

  @Field(() => String, { nullable: true })
  vteProphylaxis?: string;

  @Field(() => String, { nullable: true })
  presurgicalScrub?: string;

  @Field(() => Boolean, { defaultValue: false })
  @IsOptional()
  patientPreparation: boolean;

  @Field(() => String, { nullable: true })
  idBand?: string;

  @Field(() => String, { nullable: true })
  facilityDetails?: string;

  @Field(() => String, { nullable: true })
  allergyBand?: string;

  @Field(() => String, { nullable: true })
  tsinBand?: string;

  @Field(() => String, { nullable: true })
  preprocedureCollected?: string;

  @Field(() => String, { nullable: true })
  implants?: string;

  @Field(() => String, { nullable: true })
  belongingsDocumented?: string;

  @Field(() => String, { nullable: true })
  cbap?: string;

  @Field(() => String, { nullable: true })
  verifiedSurgicalSite?: string;

  @Field(() => String, { nullable: true })
  nutritionStatusDoc?: string;

  @Field(() => String, { nullable: true })
  preoperationMedications?: string;

  @Field(() => String, { nullable: true })
  orStaffReviewed: string;

  @Field(() => Boolean, { defaultValue: false })
  @IsOptional()
  surgicalSafetyChecklist: boolean;

  @Field(() => String, { nullable: true })
  patientConfirmation?: string;

  @Field(() => String, { nullable: true })
  isSiteMarked?: string;

  @Field(() => String, { nullable: true })
  anaesthesiaMedicationCheck?: string;

  @Field(() => String, { nullable: true })
  pulseOximeterFunctioning?: string;

  @Field(() => String, { nullable: true })
  knownAllergy?: string;

  @Field(() => String, { nullable: true })
  difficultAirway?: string;

  @Field(() => String, { nullable: true })
  riskOfBloodLoss?: string;

  @Field(() => String, { nullable: true })
  teamMembersIntroduced?: string;

  @Field(() => String, { nullable: true })
  pantientNameAndProcedure?: string;

  @Field(() => String, { nullable: true })
  antibioticBeenGiven?: string;

  @Field(() => String, { nullable: true })
  criticalSteps?: string;

  @Field(() => String, { nullable: true })
  casePeriod?: string;

  @Field(() => String, { nullable: true })
  anticipatedBloodLoss?: string;

  @Field(() => String, { nullable: true })
  patientSpecificConcern?: string;

  @Field(() => String, { nullable: true })
  specifyPatientConcern?: string;

  @Field(() => String, { nullable: true })
  sterilityConfirmed?: string;

  @Field(() => String, { nullable: true })
  equipmentIssues?: string;

  @Field(() => String, { nullable: true })
  specifyEquipmentIssues?: string;

  @Field(() => String, { nullable: true })
  essentialImagingDisplayed?: string;

  @Field(() => String, { nullable: true })
  confirmedProcedureName?: string;

  @Field(() => String, { nullable: true })
  confirmSpecimensLabeled?: string;

  @Field(() => String, { nullable: true })
  confirmInstrumentCount?: string;

  @Field(() => String, { nullable: true })
  equipmentProblemsToAddress?: string;

  @Field(() => String, { nullable: true })
  recoveryKeyConcerns?: string;

  @Field(() => String, { nullable: true })
  additionalNote?: string;

  @Field(() => String, { nullable: true })
  medicationProfileAvailable?: string;

  @Field(() => String, { nullable: true })
  identifiedPatient?: string;

  @Field(() => String, { nullable: true })
  patientCaseFileAvailable?: string;

  @Field(() => String, { nullable: true })
  assessedDvtRisk?: string;

  @Field(() => String, { nullable: true })
  equipmentImplantAvailable?: string;

  @Field(() => String, { nullable: true })
  verifiedAvailabilityOrderedBloodProduct?: string;

  @Field(() => String, { nullable: true })
  urinaryOutputAmount?: string;

  @Field(() => String, { nullable: true })
  urinaryOutputAmountUnit?: string;

  @Field(() => String, { nullable: true })
  catheterDateTime?: string;

  @Field(() => String, { nullable: true })
  denturePlateRemoved?: string;

  @Field(() => String, { nullable: true })
  contactLensRemoved?: string;

  @Field(() => String, { nullable: true })
  prosthesisRemoved?: string;

  @Field(() => String, { nullable: true })
  preoperationMedicationDateTime?: string;

  @Field(() => String, { nullable: true })
  bowelEmptied?: string;

  @Field(() => String, { nullable: true })
  lastMealDateTime?: string;

  @Field(() => String, { nullable: true })
  shavedSurgicalSite?: string;

  @Field(() => String, { nullable: true })
  jewelryRemoved?: string;

  @Field(() => Boolean, { defaultValue: true })
  @IsOptional()
  concealAdditionalNote: boolean;
}
