import { BadRequestException, Injectable } from '@nestjs/common';
import { DataSource, EntityManager } from 'typeorm';
import {
  BusinessRuleActionInput,
  BusinessRuleConditionInput,
  BusinessRuleFilterInput,
  NewBusinessRuleInput,
  UpdateBusinessRuleInput,
} from '@clinify/cms/inputs/business-rule.input';
import { BusinessRuleModel } from '@clinify/cms/models/business-rule.model';
import { BusinessRuleRepository } from '@clinify/cms/repositories/business-rule.repository';
import { FacilityPreferenceModel } from '@clinify/facility-preferences/models/facility-preference.model';
import { FlagDto } from '@clinify/pre-authorisations/inputs/flag.dto';
import { ProfileModel } from '@clinify/users/models/profile.model';

@Injectable()
export class BusinessRuleService {
  constructor(
    private readonly repository: BusinessRuleRepository,
    private readonly dataSource: DataSource,
  ) {}

  async createBusinessRule(
    mutator: ProfileModel,
    rules: NewBusinessRuleInput[],
  ): Promise<BusinessRuleModel[]> {
    return this.repository.createBusinessRule(mutator, rules);
  }

  async updateBusinessRule(
    mutator: ProfileModel,
    rule: UpdateBusinessRuleInput,
  ): Promise<BusinessRuleModel> {
    return this.repository.updateBusinessRule(mutator, rule);
  }

  async deleteBusinessRule(
    ruleId: string,
    hospitalId: string,
  ): Promise<BusinessRuleModel> {
    return this.repository.deleteBusinessRule(ruleId, hospitalId);
  }

  async fetchBusinessRules(hospitalId: string): Promise<BusinessRuleModel[]> {
    return this.repository.fetchBusinessRules(hospitalId);
  }

  async evaluateBusinessRule(
    conditionArgs: BusinessRuleConditionInput,
    actionArgs: BusinessRuleActionInput[],
  ): Promise<FlagDto[]> {
    return this.repository.evaluateBusinessRule(conditionArgs, actionArgs);
  }

  async evaluateBusinessRuleWithFiltering(
    hospitalId: string,
    conditionArgs: BusinessRuleConditionInput,
    actionArgs: BusinessRuleActionInput[],
    _manager?: EntityManager,
  ): Promise<{
    flags: FlagDto[];
    flaggedUtilizations: BusinessRuleActionInput[];
    notFlaggedUtilizations: BusinessRuleActionInput[];
    shouldPreventSubmission: boolean;
  }> {
    const repo = _manager
      ? _manager.withRepository(this.repository)
      : this.repository;

    const flags = await repo.evaluateBusinessRule(conditionArgs, actionArgs);
    const facilityPreference = await repo.manager.findOne(
      FacilityPreferenceModel,
      {
        where: {
          hospitalId,
        },
      },
    );
    const shouldPreventSubmission =
      facilityPreference?.enableBusinessRulePreventSubmit;
    // Filter flagged vs non-flagged utilizations
    const flaggedUtilizations = actionArgs.filter((util) =>
      flags?.some((f) => f.utilizationId === util.utilizationId),
    );
    const notFlaggedUtilizations = actionArgs.filter(
      (util) => !flags?.some((f) => f.utilizationId === util.utilizationId),
    );

    // If all utilizations are flagged, create combined message
    if (flags?.length > 0 && shouldPreventSubmission) {
      const flaggedItems = flags.flatMap((f) => f.flaggedItems || []);
      const combinedMessage = flaggedItems
        .map((item) => item.message)
        .reduce((acc, message, index) => {
          const prefix = `-${index + 1}. `;
          return `${acc}\n${prefix}${message}`;
        }, '');
      throw new BadRequestException(
        `Business Rules Violation: ${combinedMessage}`,
      );
    }
    return {
      flags,
      flaggedUtilizations,
      notFlaggedUtilizations,
      shouldPreventSubmission,
    };
  }

  async getVisitationTypes(
    hmoProviderId: string,
    filter: BusinessRuleFilterInput,
  ): Promise<string[]> {
    return this.repository.getVisitationTypes(
      this.dataSource,
      hmoProviderId,
      filter,
    );
  }

  async getUtilizationTypes(
    hmoProviderId: string,
    filter: BusinessRuleFilterInput,
  ): Promise<string[]> {
    return this.repository.getUtilizationTypes(
      this.dataSource,
      hmoProviderId,
      filter,
    );
  }

  async getUtilizationCategories(
    hmoProviderId: string,
    filter: BusinessRuleFilterInput,
  ): Promise<string[]> {
    return this.repository.getUtilizationCategories(
      this.dataSource,
      hmoProviderId,
      filter,
    );
  }
}
