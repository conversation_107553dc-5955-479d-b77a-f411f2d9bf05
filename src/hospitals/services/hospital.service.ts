/* eslint-disable max-lines */
/* eslint-disable @typescript-eslint/quotes */
import { DeleteObjectCommand, S3Client } from '@aws-sdk/client-s3';
import {
  BadRequestException,
  ConflictException,
  ForbiddenException,
  forwardRef,
  Inject,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import * as bcrypt from 'bcryptjs';
import cloneDeep from 'lodash.clonedeep';
import moment from 'moment';
import { nanoid } from 'nanoid';
import {
  Between,
  DataSource,
  EntityManager,
  In,
  IsNull,
  LessThanOrEqual,
  Raw,
  ILike,
  LessThan,
} from 'typeorm';
import { v4 as generateUUID } from 'uuid';
import { IHospitalRepository } from '../repositories/hospital.repository';
import { BankAccountDetailsResponse } from '../responses/bank-account-details.response';
import {
  HospitalResponse,
  CapitationDetails,
  CapitatedEnrolleeSummary,
  CapitatedProviderSummary,
  CapitationDetailByPlanType,
} from '../responses/hospital.response';
import { HospitalFilterInput } from '../validators/hospital-filter.input';
import {
  BranchInformationInput,
  CapitationPayoutInput,
  CreateHospitalInput,
  FacilityBillingInformationInput,
  HospitalInventoryItemInput,
  ProviderRegistrationInput,
  SponsorBillingInformationInput,
  UpdateHospitalInput,
  UpdateProviderInput,
} from '../validators/hospital.input';
import { PatientRegistrationInput } from '../validators/patient-registration.input';
import { SALT } from '@clinify/authentication/services/authentication.service';
import { EditHospitalStaffInput } from '@clinify/authentication/validators/registration.input';
import { getDefaultPermission } from '@clinify/authorization/abilities';
import { PermissionModel } from '@clinify/authorization/models/permission.model';
import { IPermissionRepository } from '@clinify/authorization/repositories/permission.repository';
import { GrantPatientAccessType } from '@clinify/authorization/types/permission.type';
import { FundTransactionStatus } from '@clinify/banks/enum/transfer-status.enum';
import {
  VirtualAccountProvider,
  VirtualAccountType,
  VirtualAccountTransactionType,
} from '@clinify/banks/enum/virtual-account.enum';
import { TransferFundModel } from '@clinify/banks/models/transfer-fund.model';
import { InitiateFundTransferResponse } from '@clinify/banks/responses/banks.response';
import { BankService } from '@clinify/banks/services/bank.service';
import { HMO_PROVIDER_ORIGINATOR_NAME } from '@clinify/banks/utils/hmo-provider-originator-name-mapper';
import { VirtualServicesPaymentModel } from '@clinify/bills/models/virtual-services-payment.model';
import { getCommissionAmount } from '@clinify/bills/utils/get-commission-fee-amount';
import { config } from '@clinify/config';
import {
  customDSSerializeInTransaction,
  queryDSWithSlave,
} from '@clinify/database';
import hmoProviders from '@clinify/database/seeds/hmo-providers.json';
import { EmployeeDependantModel } from '@clinify/employer/models/employee-dependant.model';
import { EmployeeModel } from '@clinify/employer/models/employee.model';
import { PayoutCommissionPayer } from '@clinify/facility-preferences/enums/commission-payer';
import { FacilityPreferenceModel } from '@clinify/facility-preferences/models/facility-preference.model';
import { IFacilityPreferenceRepository } from '@clinify/facility-preferences/repositories/facility-preference.repository';
import { ClaimsAccountApproval } from '@clinify/hmo-claims/inputs/hmo-claims.input';
import { HmoProfileModel } from '@clinify/hmo-profiles/models/hmo-profile.model';
import { IHmoProfileRepository } from '@clinify/hmo-profiles/repositories/hmo-profile.repository';
import { BulkEnrolleeRegistrationInput } from '@clinify/hmo-providers/inputs/bulk-enrollee-registration.input';
import { HmoHospitalModel } from '@clinify/hmo-providers/models/hmo-hospital.model';
import { HmoPlanTypeModel } from '@clinify/hmo-providers/models/hmo-plan-type.model';
import { HmoProviderModel } from '@clinify/hmo-providers/models/hmo-provider.model';
import { SponsorBillingInformationModel } from '@clinify/hmo-providers/models/sponsor-billing-information.model';
import { HmoProviderService } from '@clinify/hmo-providers/services/hmo-provider.service';
import { FacilityBillingInformationModel } from '@clinify/hospitals/models/billing-information.model';
import { HospitalModel } from '@clinify/hospitals/models/hospital.model';
import { validateBillingInformationMutation } from '@clinify/hospitals/validators/validate-billing-information-mutation';
import { FileNumberService } from '@clinify/integrations/file-number/services/file-number.service';
import { HmoService } from '@clinify/integrations/hmo-services/services/hmo.service';
import { TwilioWhatsAppService } from '@clinify/integrations/twilio-whatsapp/services/twilio-whatsapp.service';
import { TEMPLATE_SIDS } from '@clinify/integrations/twilio-whatsapp/template_sids';
import { PartnerModel } from '@clinify/partners/models/partner.model';
import { IPartnerRepository } from '@clinify/partners/repositories/partner.repository';
import { PriceService } from '@clinify/prices/services/prices.service';
import {
  CLINIFY_WALLET_ITEM_ID,
  CLINIFY_WALLET_NAME,
  CLINIFY_WALLET_PROVIDER_CODE,
  PRIVATE_ITEM_ID,
  PRIVATE_ITEM_NAME,
  PRIVATE_PROVIDER_CODE,
} from '@clinify/shared/constants';
import { RunModeTypes } from '@clinify/shared/enums/environment';
import { HospitalStatus } from '@clinify/shared/enums/hospital';
import { TransactionType } from '@clinify/shared/enums/transaction';
import { UserType } from '@clinify/shared/enums/users';
import {
  generatePassCode,
  generatePassword,
} from '@clinify/shared/generators/passcode.generate';
import {
  findClosestMatch,
  getMembershipNoFromMemberNo,
} from '@clinify/shared/helper';
import { formatMoney, toKobo, toNaira } from '@clinify/shared/helper/billing';
import { MAILER } from '@clinify/shared/mailer/constants';
import { IMessage } from '@clinify/shared/mailer/mailer.interface';
import { MailerService } from '@clinify/shared/mailer/mailer.service';
import mailerTemplate, {
  welcomeTemplate,
} from '@clinify/shared/mailer/mailer.template';
import { Gender } from '@clinify/shared/validators/personal-information.input';
import { validateProfilesRemover } from '@clinify/shared/validators/validate-record-mutation.validator';
import { CoverageDetailsInput } from '@clinify/users/inputs/profile-details.input';
import { DependentModel } from '@clinify/users/models/dependents.model';
import { ProfileDetailsModel } from '@clinify/users/models/details.model';
import { NextOfKinModel } from '@clinify/users/models/next-of-kin.model';
import { ProfileModel } from '@clinify/users/models/profile.model';
import { UserModel } from '@clinify/users/models/user.model';
import { IProfileRepository } from '@clinify/users/repositories/profile.repository';
import { IUserRepository } from '@clinify/users/repositories/user.repository';
import { ProfileService } from '@clinify/users/services/profile.service';
import { convertToNumber } from '@clinify/utils/dynamic-pdf/generate-enrollee-claim-details-doc';
import generateFullName from '@clinify/utils/profile/full-name';
import { templateGenerator } from '@clinify/utils/template-generators';
import { VirtualBankAccountService } from '@clinify/virtual-bank-accounts/services/virtual-bank-account.service';
import { WaitingListService } from '@clinify/waiting-list/services/waiting-list.service';
import { WalletTransactionService } from '@clinify/wallet-transactions/services/wallet-transaction.service';
import { WalletModel } from '@clinify/wallets/models/wallet.model';
import { WalletRepository } from '@clinify/wallets/repositories/wallet.repository';

@Injectable()
export class HospitalService {
  s3Client: S3Client;
  constructor(
    @InjectRepository(HospitalModel)
    public repository: IHospitalRepository,
    private readonly walletRepository: WalletRepository,
    @InjectRepository(ProfileModel)
    public profileRepository: IProfileRepository,
    @InjectRepository(UserModel)
    public userRepository: IUserRepository,
    @InjectRepository(PermissionModel)
    public permissionRepository: IPermissionRepository,
    @InjectRepository(HmoProfileModel)
    public hmoProfileRepository: IHmoProfileRepository,
    @InjectRepository(FacilityPreferenceModel)
    public facilityPreferenceRepository: IFacilityPreferenceRepository,
    @InjectRepository(PartnerModel)
    private readonly partnerRepository: IPartnerRepository,
    private profileService: ProfileService,
    public fileNumberService: FileNumberService,
    @Inject(WaitingListService) private waitingListService: WaitingListService,
    @Inject(EntityManager) readonly entityManager: EntityManager,
    @Inject(MAILER) private mailerService: MailerService,
    private dataSource: DataSource,
    private priceService: PriceService,
    private hmoService: HmoService,
    private readonly virtualAccountService: VirtualBankAccountService,
    private bankService: BankService,
    private readonly walletTransactionService: WalletTransactionService,
    @Inject(forwardRef(() => HmoProviderService))
    private hmoProviderService: HmoProviderService,
    private readonly twilioWhatsappService: TwilioWhatsAppService,
  ) {
    this.s3Client = new S3Client({
      region: config.aws.aws_region,
      ...(config.aws.useS3Endpoint ? { endpoint: config.aws.s3Endpoint } : {}),
    });
  }

  async getAllHospitals(
    filter: Partial<HospitalFilterInput>,
    mutator: ProfileModel,
  ): Promise<HospitalResponse> {
    return this.repository.findAllHospitals(filter, mutator);
  }

  async getOneHospital(hospitalId: string): Promise<HospitalModel> {
    const hospital = await this.repository.getOneHospital(hospitalId);
    if (!hospital) throw new NotFoundException('Facility Not Found');
    return hospital;
  }

  async findByProfile(profile: ProfileModel): Promise<HospitalModel> {
    return this.repository.findOne({ where: { id: profile?.hospital?.id } });
  }

  async createHospital(input: CreateHospitalInput): Promise<HospitalModel> {
    return customDSSerializeInTransaction(this.dataSource, (manager) =>
      this.createHospitalInTransaction(input, manager),
    );
  }

  async approveOrRejectHospital(
    hospitalId: string,
    status: HospitalStatus,
    reason?: string,
  ): Promise<HospitalModel> {
    if (status === HospitalStatus.Pending) {
      throw new BadRequestException(
        `Facility Status Already ${HospitalStatus.Pending}`,
      );
    }

    if (status === HospitalStatus.Rejected && !reason) {
      throw new BadRequestException('Reason Not Provided');
    }
    const hospital = await this.repository.updateHospitalStatus(
      hospitalId,
      status,
      reason,
    );

    const subject =
      status === HospitalStatus.Approved
        ? 'Facility Approved'
        : 'Facility Rejected';
    const message =
      status === HospitalStatus.Approved
        ? 'Your Application Has Been Approved'
        : `Your Application Was Denied <br /> Reason: ${reason}`;
    // get the first organization admin
    const profileRepo = this.entityManager.withRepository(
      this.profileRepository,
    );

    const user = await profileRepo.findOrganizationAdminForHospital(hospitalId);
    const msg: IMessage = {
      to: `${user?.user?.email}`,
      subject,
      html: `
      <p>Hi</p>
      <p>We Have Reviewed Your Facility.
      <br/>${message}</p>
      `,
    };

    await this.mailerService.sendMail(msg);
    return hospital;
  }

  private async createHospitalInTransaction(
    input: CreateHospitalInput,
    manager: EntityManager,
  ): Promise<HospitalModel> {
    const walletRepo = manager.withRepository(this.walletRepository);
    const transactionUserRepo = manager.withRepository(this.userRepository);

    const transactionProfileRepo = manager.withRepository(
      this.profileRepository,
    );

    const transactionPermissionRepo = manager.withRepository(
      this.permissionRepository,
    );

    const transactionHospitalRepo = manager.withRepository(this.repository);
    const partnerTrxnRepo = manager.withRepository(this.partnerRepository);

    const partner = input.partnerCode
      ? await partnerTrxnRepo.findByPartnerCode(input.partnerCode)
      : undefined;

    const clinifyId = await this.profileService.generateClinifyId();

    const hospital = new HospitalModel();

    hospital.lga = input.lga;
    hospital.city = input.city || '';
    hospital.address = input.hospitalAddress || '';
    hospital.politicalWard = input.politicalWard;
    hospital.country = input.country;
    hospital.name = input.hospitalName;
    hospital.ownership = input.ownership;
    hospital.state = input.state;
    hospital.level = input.level;
    hospital.clinifyId = clinifyId;
    hospital.licenseNumber = input.hospitalLicenseNumber;
    hospital.documentUrl = input.documentUrl;
    hospital.plan = input.plan;
    hospital.partnerId = partner?.id;
    hospital.wemaAccountNumber = input.wemaAccountNumber;
    if (!!input.wemaAccountNumber) {
      hospital.billingInformation = [
        new FacilityBillingInformationModel({
          accountName: input.wemaAccountName,
          accountNumber: input.wemaAccountNumber,
          bankName: 'Wema Bank Plc',
          creatorName: generateFullName({
            firstName: input.hospitalContactFirstName,
            lastName: input.hospitalContactLastName,
            middleName: input.hospitalContactMiddleName,
          }),
        }),
      ];
    }
    hospital.preference = new FacilityPreferenceModel({});
    const { countryCode, countryName, value } =
      input.hospitalContactPhoneNumber;
    hospital.phoneNumber = {
      value,
      countryName,
      countryCode: countryCode.replace(/^\+/, ''),
    };
    hospital.website = input.hospitalWebsite;

    const {
      hospitalContactEmail,
      hospitalContactPhoneNumber,
      hospitalContactFirstName,
      hospitalContactLastName,
      hospitalContactMiddleName,
      hospitalContactTitle,
    } = input;

    const userByCorporateEmail = await transactionUserRepo.byEmail(
      input.hospitalContactEmail,
    );

    if (userByCorporateEmail)
      throw new ConflictException('Email Already Exists');

    const user = new UserModel();
    user.email = hospitalContactEmail;
    user.forcePasswordChange = true;
    user.corporatePhoneNumber =
      `${hospitalContactPhoneNumber?.countryCode}${hospitalContactPhoneNumber?.value}`.replace(
        /^\+/,
        '',
      );
    user.country = input.country;
    const online = config.runMode === RunModeTypes.Online;
    const password = online
      ? generatePassword()
      : config.offline.defaultPassword;

    const userpassword = await bcrypt.hash(password, SALT);

    user.password = userpassword;

    const savedUser = await transactionUserRepo.save(user);
    const profile = new ProfileModel();
    profile.isDefault = true;
    profile.active = true;
    profile.fullName = generateFullName({
      firstName: hospitalContactFirstName,
      lastName: hospitalContactLastName,
      middleName: hospitalContactMiddleName,
    });
    profile.title = hospitalContactTitle;
    profile.type = UserType.OrganizationAdmin;
    profile.clinifyId = clinifyId;
    profile.user = savedUser;
    profile.transactionCharge = 0;

    const savedProfile = await transactionProfileRepo.save(profile);
    hospital.createdBy = savedProfile;
    const savedHospital = await transactionHospitalRepo.save(hospital);

    if (input.providerCode) {
      const hmoProvider = await manager.findOne(HmoProviderModel, {
        where: { providerCode: input.providerCode },
        select: ['id'],
      });
      const hmoProviderId = hmoProvider?.id;
      if (hmoProviderId) {
        await manager.save(
          HmoHospitalModel,
          new HmoHospitalModel({
            hospitalId: savedHospital.id,
            providerId: hmoProviderId,
            category: 'NonEmpanelled',
          }),
        );
      }
    }

    await walletRepo.createWallet({ hospital });
    // save organization phone number in the detail of the user
    const profileDetails = new ProfileDetailsModel();
    profileDetails.organizationPhoneNumber = input.hospitalContactPhoneNumber;
    profileDetails.profile = savedProfile;
    profileDetails.firstName = hospitalContactFirstName;
    profileDetails.middleName = hospitalContactMiddleName;
    profileDetails.lastName = hospitalContactLastName;

    await manager.save(ProfileDetailsModel, profileDetails);

    const perm = new PermissionModel();
    perm.rules = getDefaultPermission({
      userId: savedUser.id,
      clinifyId: savedProfile.clinifyId,
      type: savedProfile.type,
      organizationId: hospital?.id,
    });
    perm.profile = savedProfile;

    await transactionPermissionRepo.save(perm);

    const accountCreationMsg: IMessage = {
      to: `${user.email}`,
      subject: 'Creation of Account',
      html: mailerTemplate({
        firstName: hospitalContactFirstName,
        message: `An account was successfully created for you by <span class="blue-text">${hospital.name}</span>`,
        email: user.email,
        tempPass: password,
        isUser: false,
      }),
    };
    const msg: IMessage = {
      to: `${savedUser.email}`,
      subject: partner?.name
        ? `Welcome to ${partner.name}, Powered by Clinify!`
        : 'Welcome to Clinify!',
      html: welcomeTemplate({ firstName: profileDetails.firstName }),
    };

    await this.mailerService.sendMail(msg);
    await this.mailerService.sendMail(accountCreationMsg);
    savedProfile.hospital = savedHospital;

    await transactionProfileRepo.save(savedProfile);
    return savedHospital;
  }

  private async addVirtualServicesPayment(
    mutator: ProfileModel,
    hmoProfileId: string,
    amountDue: number,
    manager: EntityManager,
    receivingFacility: HospitalModel,
    virtualAccountProvider: VirtualAccountProvider = VirtualAccountProvider.WEMA,
  ) {
    const virtualAccount = await this.bankService.generateVirtualAccountNumber(
      {
        bankCode: virtualAccountProvider,
        virtualAccountType: VirtualAccountType.Temporary,
        expiryDate: new Date(
          new Date().setFullYear(new Date().getFullYear() + 1),
        ),
        vaTransactionType: VirtualAccountTransactionType.ProfileEnrollment,
      },
      receivingFacility.name,
      manager,
      undefined,
      undefined,
    );
    const virtualServicesPayment = new VirtualServicesPaymentModel({
      createdBy: mutator,
      creatorId: mutator.id,
      amountDue,
      amountPaid: 0,
      paymentMethod: 'Virtual Account',
      paymentStatus: 'Pending',
      registrationHmoProfileId: hmoProfileId,
      senderHospitalId: receivingFacility.id,
      virtualBankAccount: virtualAccount,
    });

    return await manager.save(
      VirtualServicesPaymentModel,
      virtualServicesPayment,
    );
  }

  private async registerPatientInTransaction(
    mutator: ProfileModel,
    input: PatientRegistrationInput,
    manager: EntityManager,
  ): Promise<ProfileModel> {
    let partnerId = mutator.partnerId;
    const { hmoId } = mutator;
    const walletRepo = manager.withRepository(this.walletRepository);
    const transactionUserRepo = manager.withRepository(this.userRepository);

    const transactionProfileRepo = manager.withRepository(
      this.profileRepository,
    );

    const transactionPermissionRepo = manager.withRepository(
      this.permissionRepository,
    );

    const facilityPreferenceRepo = manager.withRepository(
      this.facilityPreferenceRepository,
    );

    const _preference = await facilityPreferenceRepo.getFacilityPreference(
      mutator.hospitalId,
    );

    const registeringHospital = await this.repository.findOne({
      where: { id: mutator.hospitalId },
      select: [
        'id',
        'facilityLogo',
        'facebook',
        'instagram',
        'twitter',
        'website',
        'supportMail',
      ],
    });
    const { facilityLogo, facebook, instagram, twitter, website, supportMail } =
      registeringHospital || {};

    const { email, phoneNumber, country } = input;

    let userByNonCorporateEmail = null;
    if (!!email) {
      userByNonCorporateEmail = await manager.findOne(UserModel, {
        where: { nonCorporateEmail: email },
      });
    }
    let registeredWithHospital: HospitalModel;
    if (input.registeredWithId) {
      registeredWithHospital = await this.repository.getOneHospital(
        input.registeredWithId,
      );
    }

    let userByPhoneNumber;
    if (phoneNumber) {
      userByPhoneNumber = await transactionUserRepo.byPhoneNumber(phoneNumber);
    }
    if (userByNonCorporateEmail || userByPhoneNumber)
      throw new ConflictException('Patient Already Exists');

    if (input.partnerCode) {
      const partner = await manager.findOne(PartnerModel, {
        where: {
          partnerCode: input.partnerCode,
        },
        select: {
          id: true,
        },
      });
      if (partner?.id) {
        partnerId = partner.id;
      }
    }

    const passCode = generatePassCode();
    const userPassCode = await bcrypt.hash(passCode, SALT);
    const user = new UserModel();
    user.nonCorporateEmail = email || null;
    user.phoneNumber = phoneNumber;
    user.passCode = userPassCode;
    user.country = country;
    user.forcePasscodeChange = true;

    const coverageDetailsInput = [
      ...(input.personalInformation.coverageDetails || []),
      ...input.coverageInformation,
    ]
      .filter((v) => v.coverageType)
      .reduce((acc, curr) => {
        if (curr.coverageType === 'HMO') {
          const index = acc.findIndex(
            (item) => item.providerId === curr.providerId,
          );
          if (index > -1) {
            acc[index] = curr;
          } else {
            acc.push(curr);
          }
        } else {
          acc.push(curr);
        }
        return acc;
      }, []);

    const savedUser = await transactionUserRepo.save(user);

    const clinifyId = await this.profileService.generateClinifyId();

    const patientHospital: HospitalModel =
      registeredWithHospital || mutator?.hospital;

    const userProfile = new ProfileModel();
    userProfile.isDefault = true;
    userProfile.active = true;
    userProfile.type = UserType.Patient;
    userProfile.title = input?.personalInformation?.title;
    userProfile.fullName = generateFullName(input?.personalInformation);
    userProfile.registeredWith = patientHospital;
    userProfile.createdFromHmo = input.createdFromHmo;
    userProfile.shareData = input.personalInformation.shareData;
    userProfile.patientProfileType = input.patientProfileType;
    userProfile.nin = input.personalInformation.nin?.trim()
      ? input.personalInformation.nin
      : null;
    userProfile.votersId = input.personalInformation.votersId?.trim()
      ? input.personalInformation.votersId
      : null;
    userProfile.passportNumber =
      input.personalInformation.passportNumber?.trim()
        ? input.personalInformation.passportNumber
        : null;
    userProfile.bvn = input.personalInformation.bvn?.trim()
      ? input.personalInformation.bvn
      : null;
    const nextOfKins = input?.nextOfKins?.map(
      (kin) =>
        new NextOfKinModel({
          ...kin,
          createdBy: mutator,
          creatorName: mutator.fullName,
          hospital: mutator.hospital,
        }),
    );
    const dependents = input?.dependents?.map(
      (dependent) =>
        new DependentModel({
          ...dependent,
          createdBy: mutator,
          creatorName: mutator.fullName,
          hospital: mutator.hospital,
        }),
    );
    userProfile.dependents = dependents;
    userProfile.nextOfKin = nextOfKins;
    userProfile.clinifyId = clinifyId;
    userProfile.user = savedUser;
    userProfile.transactionCharge = config.wallet.transactionCharge;
    userProfile.clinicalTrials = input?.personalInformation?.clinicalTrials;
    userProfile.partnerId = partnerId;

    userProfile.gender = input?.personalInformation?.gender;
    userProfile.serviceDetails = input?.personalInformation?.serviceDetails;
    userProfile.patientStatus = input?.personalInformation?.patientStatus;
    userProfile.deathDateTime = input?.personalInformation?.deathDateTime;
    userProfile.deathLocation = input?.personalInformation?.deathLocation;
    userProfile.causeOfDeath = input?.personalInformation?.causeOfDeath;
    userProfile.dataAccessType = input?.personalInformation?.dataAccessType;
    userProfile.createdBy = mutator;
    userProfile.creatorName = mutator.fullName;

    if (hmoId) {
      const providerNetworkIds: string[] =
        (
          await queryDSWithSlave(
            this.dataSource,
            `SELECT array_agg(hmo_hospitals.hospital_id) AS ids
              FROM hmo_hospitals
              WHERE hmo_hospitals.provider_id = $1`,
            [hmoId],
          )
        )?.[0]?.ids || [];
      if (providerNetworkIds.length > 1) {
        userProfile.dataAccessType = GrantPatientAccessType.Custom;
        userProfile.dataAccess = [
          ...(mutator.hospitalId ? [mutator.hospitalId] : []),
          ...providerNetworkIds,
        ].map((id) => ({
          facilityId: id,
          staffIds: [],
        }));
      }
    }
    const hqFacilityAndBranchesId = Array.from(
      new Set([
        mutator.hospitalId,
        ...(mutator.hospital?.hqFacilityId
          ? [mutator.hospital?.hqFacilityId]
          : []),
        ...(mutator.branchIds || []),
      ]),
    );
    if (hqFacilityAndBranchesId?.length > 1) {
      userProfile.dataAccessType = GrantPatientAccessType.Custom;
      userProfile.dataAccess = hqFacilityAndBranchesId.map((id) => ({
        facilityId: id,
        staffIds: [],
      }));
    }

    const savedProfile = await transactionProfileRepo.save(userProfile);

    const profileDetails = new ProfileDetailsModel({
      ...input?.personalInformation,
      ...input?.backgroundInformation,
      nationality:
        input?.personalInformation?.nationality ||
        input?.backgroundInformation?.nationality,
      state:
        input?.personalInformation?.state ||
        input?.backgroundInformation?.state,
      profile: savedProfile,
    });

    await manager.save(ProfileDetailsModel, profileDetails);

    delete profileDetails.profile;

    savedProfile.details = profileDetails;
    const COVERAGE_REF_ID_MAP = {};
    const coverageDetails = await Promise.all(
      coverageDetailsInput.map(async (item) => {
        const itemId = item.id;
        delete item.id;
        const input = await this.profileService.syncHmoCoverageInformation(
          item,
          savedProfile,
        );
        input.clinifyId = clinifyId;
        const coverage = await transactionProfileRepo.addCoverageInformation(
          input,
          mutator,
        );
        const hmoProfile = await this.profileService.addHmoProfilePlan(
          input,
          manager,
          mutator,
          coverage,
        );
        await this.repository.addHmoProfileEligibility(
          manager,
          coverage?.hmoProfile,
          patientHospital?.id,
        );

        if (itemId) {
          COVERAGE_REF_ID_MAP[itemId] = coverage?.id;
        }

        return { ...coverage, hmoProfile };
      }),
    );

    const facilityRef = await this.profileRepository.manager.findOne(
      FacilityPreferenceModel,
      {
        select: ['id', 'generateFileNumber'],
        where: { hospitalId: userProfile.registeredWith?.id },
      },
    );

    let fileNumbers = input?.personalInformation?.fileNumbers || [];
    if (facilityRef?.generateFileNumber && fileNumbers?.length) {
      fileNumbers = await Promise.all(
        fileNumbers.map(async ({ coverageRef, fileNumber, existingFamily }) => {
          if (!fileNumber) return { coverageRef, fileNumber };

          const fileNumberToUse =
            await this.fileNumberService.generateFileNumber(
              clinifyId,
              fileNumber,
              existingFamily,
            );
          return {
            coverageRef: COVERAGE_REF_ID_MAP[coverageRef] || '',
            fileNumber: fileNumberToUse,
            existingFamily,
          };
        }),
      );
    }

    savedProfile.fileNumbers = fileNumbers;
    await transactionProfileRepo.save(savedProfile);

    savedProfile.coverageDetails = coverageDetails;

    await walletRepo.createWallet({ profile: savedProfile });
    const perm = new PermissionModel();
    perm.rules = getDefaultPermission({
      userId: savedUser.id,
      clinifyId: savedProfile.clinifyId,
      type: savedProfile.type,
    });
    perm.profile = savedProfile;
    await transactionPermissionRepo.save(perm);

    let sendEmailTo = savedUser.nonCorporateEmail;

    if (!savedUser.nonCorporateEmail) {
      const hospitalAdmin =
        await transactionProfileRepo.findOrganizationAdminForHospital(
          mutator.hospital.id,
        );
      sendEmailTo = hospitalAdmin.user.email;
    }

    const coverageForPayment = coverageDetails.find(
      ({ hmoProfile }) =>
        ['20', '107'].includes(hmoProfile?.provider?.providerCode) &&
        (!hmoProfile?.employerId || hmoProfile?.employer === 'Individual') &&
        !hmoProfile?.hmoPlanType?.isSponsor,
    );
    if (coverageForPayment) {
      const agencyFacility = await this.repository.findOne({
        where: { hmoId: coverageForPayment.hmoProfile.provider.id },
      });
      if (agencyFacility) {
        await this.addVirtualServicesPayment(
          mutator,
          coverageForPayment.hmoProfile.id,
          toKobo(
            convertToNumber(coverageForPayment.hmoProfile.premiumCollected) +
              convertToNumber(coverageForPayment.hmoProfile.premiumOutstanding),
          ),
          manager,
          agencyFacility,
        );
      }
    }

    if (hmoId) {
      const provider = await manager.findOne(HmoProviderModel, {
        where: { id: hmoId },
      });

      // LASHMA
      if (provider && ['107', '20'].includes(provider.providerCode)) {
        const coverage = coverageDetails.find(
          (c) => c.hmoProfile?.providerId === hmoId,
        );
        const hmoProfile = coverage?.hmoProfile;

        if (!hmoProfile) {
          throw new BadRequestException('Invalid HMO profile');
        }
        let employee: EmployeeModel;
        const hasDependents =
          input.dependents?.length &&
          input.dependents.some((d) => d.enrolleeId && d.firstName);
        if (hmoProfile.employerId) {
          employee = await manager.save(
            new EmployeeModel({
              firstName: input.personalInformation.firstName,
              lastName: input.personalInformation.lastName,
              middleName: input.personalInformation.middleName,
              createdBy: mutator,
              creatorName: mutator.fullName,
              employerId: hmoProfile.employerId,
              profileId: hmoProfile.profileId,
              hmoProfileId: hmoProfile.id,
              planCategory: hasDependents ? 'Dependents' : undefined,
              hmoPlanTypeId: hmoProfile.memberPlanId,
              displayPictureUrl: input.personalInformation.displayPictureUrl,
              enrolledBy: mutator.fullName,
              title: input.personalInformation.title,
            }),
          );
        }
        if (hasDependents) {
          const dependentProfiles: ProfileModel[] = [];
          let index = 0;
          for (const dependentInput of input.dependents) {
            const dependentId = dependents[index].id;
            index++;
            if (!dependentInput.enrolleeId) {
              continue;
            }
            const {
              profile: dependentProfile,
              hmoProfile: dependentHmoProfile,
            } = await this.profileService.createDependentProfileInTransaction(
              mutator,
              {
                coverageType: coverage.coverageType,
                providerId: hmoProfile.providerId,
                country: input?.country,
                memberPlanId: hmoProfile.memberPlanId,
                memberPlanName: hmoProfile.memberPlan,
                membershipNumber: hmoProfile.membershipNumber,
                memberStatus: hmoProfile.memberStatus,
                paymentDateTime: hmoProfile.paymentDateTime,
                paymentFrequency: hmoProfile.paymentFrequency,
                memberStartDate: hmoProfile.memberStartDate,
                memberDueDate: hmoProfile.memberDueDate,
                enrolledBy: hmoProfile.enrolledBy,
                enrollmentDateTime: hmoProfile.enrollmentDateTime,
                registeredWithId: mutator.hospital.id,
                capitatedAmount: hmoProfile.capitatedAmount,
                capitatedMember: hmoProfile.capitatedMember,
                capturedDate: hmoProfile.capturedDate,
                capturedBy: hmoProfile.capturedBy,
                memberPlanGroup: hmoProfile.memberPlanGroup,
                memberPlanSubGroup: hmoProfile.memberPlanSubGroup,
              },
              dependentInput,
              manager,
            );
            dependentProfiles.push(dependentProfile);
            await manager.update(DependentModel, dependentId, {
              dependentProfileId: dependentProfile.id,
              hmoProfileId: dependentHmoProfile.id,
            });
            if (employee) {
              await manager.save(
                new EmployeeDependantModel({
                  ...dependentInput,
                  employeeId: employee.id,
                  employerId: employee.employerId,
                  profile: dependentProfile,
                  profileId: dependentProfile.id,
                  hmoProfile: dependentHmoProfile,
                  hmoProfileId: dependentHmoProfile.id,
                  createdBy: mutator,
                  creatorName: mutator.fullName,
                }),
              );
            }
          }
        }
      }
    }
    const templateData = {
      facilityName: mutator.hospital.name,
      facilityAddress: mutator.hospital.address,
      registeredBy: mutator.fullName,
      patientFirstName: profileDetails.firstName,
      patientLastName: profileDetails.lastName,
      patientFullName: userProfile.fullName,
      patientPhone: user.phoneNumber,
    };

    const accountCreationMsg: IMessage = {
      from: `${templateData.facilityName} <${supportMail}>`,
      to: `${sendEmailTo}`,
      subject: 'Creation of Account',
      html: mailerTemplate({
        firstName: profileDetails.firstName,
        message: `An account was successfully created for you by <span class="blue-text">${mutator?.hospital?.name}</span>`,
        phoneNumber: user.phoneNumber,
        tempPass: passCode,
        isUser: false,
        facilityLogo,
        instagram,
        facebook,
        twitter,
        website,
        supportMail,
        facilityName: mutator.hospital.name,
      }),
    };
    const msg: IMessage = {
      from: `${templateData.facilityName} <${supportMail}>`,
      to: `${sendEmailTo}`,
      subject:
        templateGenerator({
          templateString: _preference?.welcomeMailTemplate?.subject,
          templateData,
        }) || `Welcome to ${templateData.facilityName}`,
      html: welcomeTemplate({
        firstName: profileDetails.firstName,
        body: templateGenerator({
          templateString: _preference?.welcomeMailTemplate?.body,
          templateData,
        }),
        facilityLogo,
        instagram,
        website,
        facebook,
        twitter,
      }),
    };

    this.mailerService.sendMail(msg);
    this.mailerService.sendMail(accountCreationMsg);
    return { ...savedProfile } as ProfileModel;
  }

  async registerEnrollee(
    mutator: ProfileModel,
    input: BulkEnrolleeRegistrationInput,
  ): Promise<ProfileModel> {
    return customDSSerializeInTransaction(
      this.dataSource,
      (manager) => this.registerEnrolleeInTransaction(mutator, input, manager),
      10_000,
    );
  }

  private async updateExistingEnrolleeInformationOnBulkRegistration(
    manager: EntityManager,
    input: BulkEnrolleeRegistrationInput,
    hmoId: string,
    existingHmoProfileId: string,
    userId?: string,
  ): Promise<ProfileModel> {
    let hmoProfileToUpdate: HmoProfileModel;
    if (existingHmoProfileId) {
      hmoProfileToUpdate = await manager.findOne(HmoProfileModel, {
        where: { id: existingHmoProfileId },
        relations: { profile: { details: true, user: true } },
      });
    }
    if (userId && !hmoProfileToUpdate) {
      hmoProfileToUpdate = await manager.findOne(HmoProfileModel, {
        where: { profile: { user: { id: userId } }, providerId: hmoId },
        relations: { profile: { details: true, user: true } },
      });
    }

    if (!hmoProfileToUpdate) {
      throw new NotFoundException('Unable To Update Existing Enrollee');
    }

    const transactionUserRepo = manager.withRepository(this.userRepository);
    const transactionProfileRepo = manager.withRepository(
      this.profileRepository,
    );

    // Update user information
    const userToUpdate = hmoProfileToUpdate.profile.user;
    if (
      input.enrolleePrimaryPhoneNumber &&
      input.enrolleePrimaryPhoneNumber !== userToUpdate.phoneNumber
    ) {
      // Check if phone number is already taken by another user
      const existingUserWithPhone = await transactionUserRepo.byPhoneNumber(
        input.enrolleePrimaryPhoneNumber,
      );
      if (
        !existingUserWithPhone ||
        existingUserWithPhone.id === userToUpdate.id
      ) {
        userToUpdate.phoneNumber = input.enrolleePrimaryPhoneNumber;
      }
    }
    if (input.enrolleeEmailAddress) {
      userToUpdate.nonCorporateEmail = input.enrolleeEmailAddress;
    }
    if (input.enrolleeCountryOfOrigin) {
      userToUpdate.country = input.enrolleeCountryOfOrigin;
    }
    await transactionUserRepo.save(userToUpdate);

    // Update profile information
    const profileToUpdate = hmoProfileToUpdate.profile;
    if (input.enrolleeTitle) {
      profileToUpdate.title = input.enrolleeTitle;
    }
    if (
      input.enrolleeFirstName ||
      input.enrolleeMiddleName ||
      input.enrolleeLastName
    ) {
      const firstName =
        input.enrolleeFirstName || profileToUpdate.details?.firstName || '';
      const middleName =
        input.enrolleeMiddleName || profileToUpdate.details?.middleName || '';
      const lastName =
        input.enrolleeLastName || profileToUpdate.details?.lastName || '';
      profileToUpdate.fullName =
        `${firstName} ${middleName} ${lastName}`.trim();
    }
    if (input.enrolleeGender) {
      profileToUpdate.gender = input.enrolleeGender as Gender;
    }
    await transactionProfileRepo.save(profileToUpdate);

    // Update profile details
    const detailsToUpdate = profileToUpdate.details;
    if (detailsToUpdate) {
      if (input.enrolleeFirstName) {
        detailsToUpdate.firstName = input.enrolleeFirstName;
      }
      if (input.enrolleeLastName) {
        detailsToUpdate.lastName = input.enrolleeLastName;
      }
      if (input.enrolleeMiddleName) {
        detailsToUpdate.middleName = input.enrolleeMiddleName;
      }
      if (input.enrolleeDateOfBirth) {
        detailsToUpdate.dateOfBirth = moment(
          input.enrolleeDateOfBirth,
          'DD/MM/YYYY',
        ).toDate();
      }
      if (input.enrolleeContactAddress) {
        detailsToUpdate.address = input.enrolleeContactAddress;
      }
      if (input.enrolleeLGAOfResidence) {
        detailsToUpdate.lga = input.enrolleeLGAOfResidence;
      }
      if (input.enrolleeStateOfOrigin) {
        detailsToUpdate.state = input.enrolleeStateOfOrigin;
      }
      if (input.enrolleeCountryOfOrigin) {
        detailsToUpdate.nationality = input.enrolleeCountryOfOrigin;
      }
      if (input.pictureUrl) {
        detailsToUpdate.displayPictureUrl = input.pictureUrl;
      }
      if (input.enrolleeMaritalStatus) {
        detailsToUpdate.maritalStatus = input.enrolleeMaritalStatus;
      }
      if (input.enrolleeBloodGroup) {
        detailsToUpdate.bloodGroup = input.enrolleeBloodGroup;
      }
      if (input.enrolleeWeight) {
        detailsToUpdate.weight = input.enrolleeWeight;
      }
      if (input.enrolleeHeight) {
        detailsToUpdate.height = input.enrolleeHeight;
      }
      if (input.enrolleeOccupation) {
        detailsToUpdate.occupation = input.enrolleeOccupation;
      }
      if (input.enrolleeCity) {
        detailsToUpdate.city = input.enrolleeCity;
      }
      if (input.enrolleePlaceOfBirth) {
        detailsToUpdate.placeOfBirth = input.enrolleePlaceOfBirth;
      }
      await manager.save(ProfileDetailsModel, detailsToUpdate);
    }

    // Update HMO profile information if plan details are provided
    if (input.planCode) {
      // Find the plan by planCode
      const hmoPlanType = await manager.findOne(HmoPlanTypeModel, {
        where: {
          planCode: input.planCode,
          hmoProviderId: hmoId,
        },
      });

      if (hmoPlanType) {
        // Update HMO profile plan information
        if (
          input.enrolleeMemberNumber &&
          input.enrolleeMemberNumber !== hmoProfileToUpdate.memberNumber
        ) {
          hmoProfileToUpdate.memberNumber = input.enrolleeMemberNumber;
        }
        if (input.planStatus) {
          hmoProfileToUpdate.memberStatus = input.planStatus;
        }
        if (input.paymentFrequency) {
          hmoProfileToUpdate.paymentFrequency = input.paymentFrequency;
        }
        if (input.registrationDateAndTime) {
          hmoProfileToUpdate.enrollmentDateTime = moment(
            input.registrationDateAndTime,
            'DD/MM/YYYY',
          ).toDate();
        }
        if (input.enrolledBy) {
          hmoProfileToUpdate.enrolledBy = input.enrolledBy;
        }
        if (input.paymentDateAndTime) {
          hmoProfileToUpdate.paymentDateTime = moment(
            input.paymentDateAndTime,
            'DD/MM/YYYY',
          ).toDate();
        }
        if (input.planStartDate) {
          hmoProfileToUpdate.memberStartDate = moment(
            input.planStartDate,
            'DD/MM/YYYY',
          ).toDate();
        }
        if (input.planDueDate) {
          hmoProfileToUpdate.memberDueDate = moment(
            input.planDueDate,
            'DD/MM/YYYY',
          ).toDate();
        }
        if (input.primaryProviderName) {
          hmoProfileToUpdate.primaryProviderName = input.primaryProviderName;

          // Get primary provider id
          const primaryCareProviderQueryResponse = await queryDSWithSlave(
            this.dataSource,
            `SELECT hospitals.id FROM hmo_hospitals
              INNER JOIN hospitals ON hospitals.id = hmo_hospitals.hospital_id
                                     AND hospitals.name ILIKE $2
                          WHERE provider_id = $1`,
            [hmoId, input.primaryProviderName],
          );
          const primaryProviderId = primaryCareProviderQueryResponse?.[0]?.id;
          if (primaryProviderId) {
            hmoProfileToUpdate.primaryProviderId = primaryProviderId;
          }
        }
        if (input.enrolleePlaceOfWork) {
          hmoProfileToUpdate.employeeType = input.enrolleePlaceOfWork;
        }
        if (input.referralCode) {
          hmoProfileToUpdate.referrerCode = input.referralCode;
        }
        if (input.enrolleeEmployeeNumber) {
          hmoProfileToUpdate.employeeNumber = input.enrolleeEmployeeNumber;
        }
        if (input.enrolleeGroupCode) {
          hmoProfileToUpdate.groupCode = input.enrolleeGroupCode;
        }
        if (input.agentEmail) {
          hmoProfileToUpdate.agentEmail = input.agentEmail;
        }

        // Update plan type if different
        if (hmoPlanType.id !== hmoProfileToUpdate.memberPlanId) {
          hmoProfileToUpdate.memberPlanId = hmoPlanType.id;
        }
        await manager.save(HmoProfileModel, hmoProfileToUpdate);
      }
    }

    await manager.save(ProfileDetailsModel, detailsToUpdate);
    await manager.save(ProfileModel, profileToUpdate);

    return profileToUpdate;
  }

  private async registerEnrolleeInTransaction(
    mutator: ProfileModel,
    input: BulkEnrolleeRegistrationInput,
    manager: EntityManager,
  ): Promise<ProfileModel> {
    let principalHmoProfile: HmoProfileModel;
    const walletRepo = manager.withRepository(this.walletRepository);
    const transactionUserRepo = manager.withRepository(this.userRepository);
    const transactionProfileRepo = manager.withRepository(
      this.profileRepository,
    );
    const transactionPermissionRepo = manager.withRepository(
      this.permissionRepository,
    );

    const facilityPreferenceRepo = manager.withRepository(
      this.facilityPreferenceRepository,
    );

    const _preference = await facilityPreferenceRepo.getFacilityPreference(
      mutator.hospitalId,
    );

    const agencies = _preference.enrollmentAgentAssigments;
    let commissionRate: number,
      commissionPayable: number,
      enrollmentAgency: string,
      administrationAgency: string;
    let enrollmentAgentProfileId = null;
    let enrollmentAgentName = '';
    if (input.agentEmail) {
      const enrollmentAgent = await queryDSWithSlave(
        this.dataSource,
        `SELECT profiles.id, profiles.full_name FROM profiles
        INNER JOIN users ON profiles."user" = users.id
        WHERE email = $1
      `,
        [input.agentEmail],
      );
      enrollmentAgentProfileId = enrollmentAgent?.[0]?.id;
      enrollmentAgentName = enrollmentAgent?.[0]?.full_name;
    }

    const enrollmentAgentAssignment = agencies.find(
      ({ profileId }) =>
        enrollmentAgentProfileId && profileId === enrollmentAgentProfileId,
    );

    // Get primary provider id
    const primaryCareProviderQueryResponse = await queryDSWithSlave(
      this.dataSource,
      `SELECT hospitals.id FROM hmo_hospitals
    INNER JOIN hospitals ON hospitals.id = hmo_hospitals.hospital_id
                               AND hospitals.name ILIKE $2
                    WHERE provider_id = $1`,
      [mutator.hmoId, input.primaryProviderName],
    );
    const primaryProviderId = primaryCareProviderQueryResponse?.[0]?.id;

    // Extract phone number from input
    const phoneNumber = input.enrolleePrimaryPhoneNumber;

    let userByPhoneNumber: UserModel;
    if (phoneNumber) {
      userByPhoneNumber = await transactionUserRepo.byPhoneNumber(phoneNumber);
    }
    if (userByPhoneNumber) {
      input.enrolleePrimaryPhoneNumber = null;
      input.enrolleeSecondaryPhoneNumber =
        input.enrolleeSecondaryPhoneNumber || input.enrolleePrimaryPhoneNumber;
    }

    const existingEnrollee = !input.enrolleeMemberNumber
      ? []
      : await queryDSWithSlave(
          this.dataSource,
          `SELECT id FROM hmo_profiles WHERE member_number = $1 AND provider_id = $2`,
          [input.enrolleeMemberNumber, mutator.hmoId],
        );
    if (existingEnrollee?.[0]?.id || userByPhoneNumber) {
      return this.updateExistingEnrolleeInformationOnBulkRegistration(
        manager,
        input,
        mutator.hmoId,
        existingEnrollee?.[0]?.id,
        userByPhoneNumber?.id,
      );
    }

    if (input.memberOrDependent === 'D' && input.enrolleeMemberNumber) {
      principalHmoProfile = await manager.findOne(HmoProfileModel, {
        where: {
          membershipNumber: getMembershipNoFromMemberNo(
            input.enrolleeMemberNumber,
          ),
          providerId: mutator.hmoId,
        },
        relations: { profile: true },
      });

      const dependent = await this.profileService.addDependentInfo(mutator, {
        enrolleeId: input.enrolleeMemberNumber,
        bloodGroup: input.enrolleeBloodGroup,
        clinifyId: principalHmoProfile.profile.clinifyId,
        dateOfBirth: input.enrolleeDateOfBirth
          ? moment(input.enrolleeDateOfBirth, 'DD/MM/YYYY').toDate()
          : null,
        displayPictureUrl: input.pictureUrl,
        firstName: input.enrolleeFirstName,
        lastName: input.enrolleeLastName,
        middleName: input.enrolleeMiddleName,
        gender: input.enrolleeGender as Gender,
        title: input.enrolleeTitle,
        emailAddress: input.enrolleeEmailAddress,
        phoneNumber: input.enrolleePrimaryPhoneNumber
          ? {
              countryCode: '+234',
              countryName: 'Nigeria',
              value: input.enrolleePrimaryPhoneNumber,
            }
          : undefined,
        primaryProviderName: input.primaryProviderName,
        primaryProviderId: primaryProviderId || undefined,
        primaryProviderAddress: input.enrolleeContactAddress,
        relationship: input.relationship,
      });

      return dependent.dependentProfile;
    }

    const registeringHospital = await this.repository.findOne({
      where: { id: mutator.hospitalId },
      select: [
        'id',
        'facilityLogo',
        'facebook',
        'instagram',
        'twitter',
        'website',
        'supportMail',
      ],
    });
    const { facilityLogo, facebook, instagram, twitter, website, supportMail } =
      registeringHospital || {};

    const passCode = generatePassCode();
    const userPassCode = await bcrypt.hash(passCode, SALT);
    const user = new UserModel();
    user.phoneNumber = phoneNumber;
    user.nonCorporateEmail = input.enrolleeEmailAddress || null;
    user.passCode = userPassCode;
    user.country = input.enrolleeCountryOfOrigin;
    user.forcePasscodeChange = true;

    const savedUser = await transactionUserRepo.save(user);

    const clinifyId = await this.profileService.generateClinifyId();

    const patientHospital = mutator?.hospital;

    const userProfile = new ProfileModel();
    userProfile.isDefault = true;
    userProfile.active = true;
    userProfile.type = UserType.Patient;
    userProfile.title = input.enrolleeTitle;
    userProfile.fullName = `${input.enrolleeFirstName} ${
      input.enrolleeMiddleName || ''
    } ${input.enrolleeLastName}`.trim();
    userProfile.registeredWith = patientHospital;
    userProfile.clinifyId = clinifyId;
    userProfile.user = savedUser;
    userProfile.transactionCharge = config.wallet.transactionCharge;
    userProfile.partnerId = mutator.partnerId;

    userProfile.gender = input.enrolleeGender as Gender;
    userProfile.createdBy = mutator;
    userProfile.creatorName = mutator.fullName;

    // grant data access to health provider network
    if (mutator.hmoId) {
      const providerNetworkIds: string[] =
        (
          await queryDSWithSlave(
            this.dataSource,
            `SELECT array_agg(hmo_hospitals.hospital_id) AS ids
              FROM hmo_hospitals
              WHERE hmo_hospitals.provider_id = $1`,
            [mutator.hmoId],
          )
        )?.[0]?.ids || [];
      if (providerNetworkIds.length) {
        userProfile.dataAccessType = GrantPatientAccessType.Custom;
        userProfile.dataAccess = [
          ...(mutator.hospitalId ? [mutator.hospitalId] : []),
          ...providerNetworkIds,
        ].map((id) => ({
          facilityId: id,
          staffIds: [],
        }));
      }
    }

    const hqFacilityAndBranchesId = Array.from(
      new Set([
        mutator.hospitalId,
        ...(mutator.hospital?.hqFacilityId
          ? [mutator.hospital?.hqFacilityId]
          : []),
        ...(mutator.branchIds || []),
      ]),
    );
    if (hqFacilityAndBranchesId?.length > 1) {
      userProfile.dataAccessType = GrantPatientAccessType.Custom;
      userProfile.dataAccess = [
        ...(userProfile.dataAccess || []),
        ...hqFacilityAndBranchesId.map((id) => ({
          facilityId: id,
          staffIds: [],
        })),
      ];
    }

    const savedProfile = await transactionProfileRepo.save(userProfile);

    const profileDetails = new ProfileDetailsModel({
      firstName: input.enrolleeFirstName,
      lastName: input.enrolleeLastName,
      middleName: input.enrolleeMiddleName,
      dateOfBirth: input.enrolleeDateOfBirth
        ? moment(input.enrolleeDateOfBirth, 'DD/MM/YYYY').toDate()
        : undefined,
      address: input.enrolleeContactAddress,
      lga: input.enrolleeLGAOfResidence,
      state: input.enrolleeStateOfOrigin,
      nationality: input.enrolleeCountryOfOrigin,
      profile: savedProfile,
      displayPictureUrl: input.pictureUrl,
      maritalStatus: input.enrolleeMaritalStatus,
      bloodGroup: input.enrolleeBloodGroup,
      weight: input.enrolleeWeight,
      height: input.enrolleeHeight,
      occupation: input.enrolleeOccupation,
      city: input.enrolleeCity,
      placeOfBirth: input.enrolleePlaceOfBirth,
    });

    await manager.save(ProfileDetailsModel, profileDetails);

    delete profileDetails.profile;

    savedProfile.details = profileDetails;

    // Create coverage details if plan information is provided
    if (input.planCode) {
      // Find the plan by planCode
      const hmoPlanType = await manager.findOne(HmoPlanTypeModel, {
        where: {
          planCode: input.planCode,
          hmoProviderId: mutator.hmoId,
        },
      });
      const premiumDetails = hmoPlanType?.premiumDetails?.find(
        ({ category, frequency }) =>
          category === input.planCategory &&
          frequency === input.paymentFrequency,
      );
      const premiumAmount = premiumDetails?.amount;
      if (enrollmentAgentAssignment) {
        enrollmentAgency = enrollmentAgentAssignment.enrollmentAgency;
        administrationAgency = enrollmentAgentAssignment.administrationAgency;

        if (premiumDetails) {
          commissionRate = premiumDetails.enrollmentAgencyCommissionRate?.find(
            ({ name }) =>
              name === enrollmentAgentAssignment.administrationAgency,
          )?.commissionRate;
          commissionPayable = commissionRate
            ? (commissionRate / 100) * premiumAmount
            : null;
        }
      }

      let employerQueryResult;
      if (input.employerCode || input.employerNumber) {
        employerQueryResult = await queryDSWithSlave(
          this.dataSource,
          `SELECT employers.id FROM employers
                    WHERE (employers.employer_plan_code = $1 AND $1 IS NOT NULL)
                       OR (employers.employer_number = $2 AND $2 IS NOT NULL)`,
          [input.employerCode, input.employerNumber],
        );
      }
      const employerId = employerQueryResult?.[0]?.id;

      let referrerName: string | null;
      if (input.referralCode) {
        const fp = await manager.findOne(FacilityPreferenceModel, {
          where: { hospitalId: mutator.hospitalId },
          select: {
            id: true,
            enrolleeReferrals: true,
          },
        });
        referrerName = fp?.enrolleeReferrals?.find(
          (r) => r.referrerCode === input.referralCode,
        )?.name;
      }

      if (!hmoPlanType) {
        throw new NotFoundException(
          `Plan With Code "${input.planCode}" Not Found`,
        );
      }

      const coverageInput: CoverageDetailsInput = {
        coverageType: 'HMO',
        providerId: mutator.hmoId,
        memberNumber:
          input.enrolleeMemberNumber ||
          (await this.hmoProviderService.principalMembershipNoGenerator({
            id: mutator.hmoId,
            planTypeId: hmoPlanType.id,
          })),
        memberPlanId: hmoPlanType.id,
        memberStatus: input.planStatus || 'Active',
        paymentFrequency: input.paymentFrequency,
        enrollmentDateTime: input.registrationDateAndTime
          ? moment(input.registrationDateAndTime, 'DD/MM/YYYY').toDate()
          : null,
        enrolledBy: enrollmentAgentName || input.enrolledBy || mutator.fullName,
        paymentDateTime: input.paymentDateAndTime
          ? moment(input.paymentDateAndTime, 'DD/MM/YYYY').toDate()
          : null,
        memberStartDate: input.planStartDate
          ? moment(input.planStartDate, 'DD/MM/YYYY').toDate()
          : null,
        memberDueDate: input.planDueDate
          ? moment(input.planDueDate, 'DD/MM/YYYY').toDate()
          : null,
        primaryProviderName: input.primaryProviderName,
        primaryProviderId,
        employeeType: input?.enrolleePlaceOfWork,
        referrerCode: input?.referralCode,
        referrer: referrerName,
        employeeNumber: input?.enrolleeEmployeeNumber,
        groupCode: input?.enrolleeGroupCode,
        agentEmail: input?.agentEmail,
        companyName: input?.employerName,
        employerId,
        commissionRate: commissionRate ? commissionRate.toString() : null,
        commissionPayable: commissionPayable
          ? toNaira(commissionPayable).toString()
          : null,
        enrollmentAgency,
        administrationAgency,
        premiumCollected: premiumAmount ? `${toNaira(premiumAmount)}` : null,
      };

      const coverage = await this.profileService.syncHmoCoverageInformation(
        coverageInput,
        savedProfile,
      );
      coverage.clinifyId = clinifyId;

      const coverageDetails =
        await transactionProfileRepo.addCoverageInformation(coverage, mutator);

      await this.profileService.addHmoProfilePlan(
        coverage,
        manager,
        mutator,
        coverageDetails,
      );

      await this.repository.addHmoProfileEligibility(
        manager,
        coverageDetails?.hmoProfile,
        patientHospital?.id,
      );

      savedProfile.coverageDetails = [coverageDetails];
    }

    await transactionProfileRepo.save(savedProfile);

    await walletRepo.createWallet({ profile: savedProfile });
    const perm = new PermissionModel();
    perm.rules = getDefaultPermission({
      userId: savedUser.id,
      clinifyId: savedProfile.clinifyId,
      type: savedProfile.type,
    });
    perm.profile = savedProfile;
    await transactionPermissionRepo.save(perm);

    let sendEmailTo = savedUser.nonCorporateEmail;

    if (!sendEmailTo) {
      const hospitalAdmin =
        await transactionProfileRepo.findOrganizationAdminForHospital(
          mutator.hospital.id,
        );
      sendEmailTo = hospitalAdmin.user.email;
    }

    const templateData = {
      facilityName: mutator.hospital.name,
      facilityAddress: mutator.hospital.address,
      registeredBy: mutator.fullName,
      patientFirstName: profileDetails.firstName,
      patientLastName: profileDetails.lastName,
      patientFullName: userProfile.fullName,
      patientPhone: user.phoneNumber,
    };

    const accountCreationMsg: IMessage = {
      from: `${templateData.facilityName} <${supportMail}>`,
      to: `${sendEmailTo}`,
      subject: 'Creation of Account',
      html: mailerTemplate({
        firstName: profileDetails.firstName,
        message: `An account was successfully created for you by <span class="blue-text">${mutator?.hospital?.name}</span>`,
        phoneNumber: user.phoneNumber,
        tempPass: passCode,
        isUser: false,
        facilityLogo,
        instagram,
        facebook,
        twitter,
        website,
        supportMail,
        facilityName: mutator.hospital.name,
      }),
    };
    const msg: IMessage = {
      from: `${templateData.facilityName} <${supportMail}>`,
      to: `${sendEmailTo}`,
      subject:
        templateGenerator({
          templateString: _preference?.welcomeMailTemplate?.subject,
          templateData,
        }) || `Welcome to ${templateData.facilityName}`,
      html: welcomeTemplate({
        firstName: profileDetails.firstName,
        body: templateGenerator({
          templateString: _preference?.welcomeMailTemplate?.body,
          templateData,
        }),
        facilityLogo,
        instagram,
        website,
        facebook,
        twitter,
      }),
    };

    this.mailerService.sendMail(msg);
    this.mailerService.sendMail(accountCreationMsg);
    return { ...savedProfile } as ProfileModel;
  }

  async registerPatientWithHospital(
    profile: ProfileModel,
    input: PatientRegistrationInput,
  ): Promise<ProfileModel> {
    return customDSSerializeInTransaction(
      this.dataSource,
      (manager) => this.registerPatientInTransaction(profile, input, manager),
      10_000,
    );
  }

  async deletePatientRegisteredWithHospital(
    profileId: string,
    profile: ProfileModel,
  ): Promise<ProfileModel> {
    const manager = this.entityManager;
    const profileToDelete = await manager.findOne(ProfileModel, {
      where: {
        id: profileId,
        type: UserType.Patient,
        registeredWithId: profile?.hospital.id,
      },
      relations: [
        'user',
        'details',
        'registeredWith',
        'bill',
        'bill.details',
        'hmoProfiles',
      ],
    });

    const { facilityLogo, website, facebook, instagram, twitter, supportMail } =
      await this.repository.findOne({
        where: { id: profile.hospitalId },
        select: [
          'id',
          'facilityLogo',
          'website',
          'facebook',
          'instagram',
          'twitter',
          'supportMail',
        ],
      });

    const [validProfile] = validateProfilesRemover(profile, [profileToDelete]);

    if (validProfile) {
      validProfile.user.passCode = null;
      validProfile.user.nonCorporateEmail = null;
      validProfile.user.phoneNumber = null;
      validProfile.details.secondaryEmail = null;
      validProfile.details.secondaryPhoneNumber = null;
      validProfile.active = false;
      validProfile.isDefault = false;
      validProfile.deleted = true;
      validProfile.deletedAt = new Date();

      await manager.save(ProfileModel, validProfile);

      await this.waitingListService.removePatientClinifyId(
        validProfile.clinifyId,
        manager,
      );

      await this.fileNumberService.deletePatientFileNumbers(
        validProfile.clinifyId,
      );

      const msg: IMessage = {
        from: `${profile.hospital.name} <${supportMail}>`,
        to: `${validProfile?.user?.nonCorporateEmail}`,
        subject: 'Account Deletion',
        html: mailerTemplate({
          firstName: validProfile?.fullName?.split(' ')[0],
          message: 'Your account has been deleted successfully.',
          facilityLogo,
          instagram,
          facebook,
          twitter,
          website,
          facilityName: profile.hospital.name,
          supportMail,
        }),
      };

      await this.mailerService.sendMail(msg);
      return validProfile;
    }
  }

  async bulkDeletePatientRegisteredWithHospital(
    profileIds: string[],
    profile: ProfileModel,
  ): Promise<ProfileModel[]> {
    const profilesToDelete: ProfileModel[] = [];

    await Promise.all(
      profileIds.map(async (profileId) => {
        const deletedProfiles = await this.deletePatientRegisteredWithHospital(
          profileId,
          profile,
        );
        if (deletedProfiles) {
          profilesToDelete.push(deletedProfiles);
        }
      }),
    );

    if (!profilesToDelete.length) throw new Error('Profile Cannot Be Deleted');

    return profilesToDelete;
  }

  async deleteHospitalStaff(
    profileId: string,
    profile: ProfileModel,
  ): Promise<ProfileModel> {
    const profileToDelete = await this.entityManager.findOne(ProfileModel, {
      where: {
        id: profileId,
        hospital: { id: profile?.hospital.id },
      },
      relations: ['user', 'details'],
    });
    const { facilityLogo, website, facebook, instagram, twitter, supportMail } =
      await this.repository.findOne({
        where: { id: profile.hospitalId },
        select: [
          'id',
          'facilityLogo',
          'website',
          'facebook',
          'instagram',
          'twitter',
          'supportMail',
        ],
      });

    if (
      profile.type !== UserType.OrganizationAdmin &&
      (profileToDelete?.type === UserType.OrganizationStaffAdmin ||
        profileToDelete?.type === UserType.OrganizationAdmin)
    ) {
      throw new ForbiddenException('Not Authorized To Delete This Staff');
    }

    if (profileToDelete) {
      profileToDelete.user.email = null;
      profileToDelete.user.phoneNumber = null;
      profileToDelete.user.password = null;
      profileToDelete.user.corporatePhoneNumber = null;
      profileToDelete.details.secondaryPhoneNumber = null;
      profileToDelete.active = false;
      profileToDelete.isDefault = false;
      profileToDelete.deleted = true;
      profileToDelete.deletedAt = new Date();

      await this.entityManager.save(ProfileModel, profileToDelete);

      const msg: IMessage = {
        from: `${profile.hospital.name} <${supportMail}>`,
        to: `${profileToDelete?.user?.nonCorporateEmail}`,
        subject: 'Account Deletion',
        html: mailerTemplate({
          firstName: profileToDelete?.fullName?.split(' ')[0],
          message: `Your account with ${profile?.hospital.name} has been deleted successfully.`,
          facilityLogo,
          instagram,
          website,
          facebook,
          twitter,
          facilityName: profile.hospital.name,
          supportMail,
        }),
      };

      await this.mailerService.sendMail(msg);

      return profileToDelete;
    }
  }

  async bulkDeleteHospitalStaff(
    profileIds: string[],
    profile: ProfileModel,
  ): Promise<ProfileModel[]> {
    const profilesToDelete: ProfileModel[] = [];
    await Promise.all(
      profileIds.map(async (profileId) => {
        const deletedProfiles = await this.deleteHospitalStaff(
          profileId,
          profile,
        );
        if (deletedProfiles) {
          profilesToDelete.push(deletedProfiles);
        }
      }),
    );

    return profilesToDelete;
  }

  async activateHospitalStaff(
    profileId: string,
    profile: ProfileModel,
    active: boolean,
  ): Promise<ProfileModel> {
    let updatedProfile = await this.entityManager.findOne(ProfileModel, {
      where: {
        id: profileId,
        hospital: { id: profile?.hospital.id },
      },
    });

    if (updatedProfile) {
      updatedProfile.active = active;
      updatedProfile = await this.entityManager.save(
        ProfileModel,
        updatedProfile,
      );

      return updatedProfile;
    }
  }

  async bulkActivateHospitalStaff(
    profileIds: string[],
    profile: ProfileModel,
    active: boolean,
  ): Promise<ProfileModel[]> {
    const profilesToDelete: ProfileModel[] = [];

    await Promise.all(
      profileIds.map(async (profileId) => {
        const deletedProfiles = await this.activateHospitalStaff(
          profileId,
          profile,
          active,
        );
        if (deletedProfiles) {
          profilesToDelete.push(deletedProfiles);
        }
      }),
    );

    return profilesToDelete;
  }

  async activateHospitalStaffForFacility(
    profileId: string,
    hospitalId: string,
    active: boolean,
  ): Promise<ProfileModel> {
    let updatedProfile = await this.entityManager.findOne(ProfileModel, {
      where: {
        id: profileId,
        hospital: { id: hospitalId },
      },
    });

    if (updatedProfile) {
      updatedProfile.active = active;
      updatedProfile = await this.entityManager.save(
        ProfileModel,
        updatedProfile,
      );

      return updatedProfile;
    }
  }

  async bulkActivateHospitalStaffForFacility(
    profileIds: string[],
    hospitalId: string,
    active: boolean,
  ): Promise<ProfileModel[]> {
    const profilesToUpdate: ProfileModel[] = [];

    await Promise.all(
      profileIds.map(async (profileId) => {
        const updatedProfile = await this.activateHospitalStaffForFacility(
          profileId,
          hospitalId,
          active,
        );
        if (updatedProfile) {
          profilesToUpdate.push(updatedProfile);
        }
      }),
    );

    return profilesToUpdate;
  }

  async deleteHospitalStaffForFacility(
    profileId: string,
    hospitalId: string,
  ): Promise<ProfileModel> {
    const profileToDelete = await this.entityManager.findOne(ProfileModel, {
      where: {
        id: profileId,
        hospital: { id: hospitalId },
      },
      relations: ['user', 'details'],
    });

    const hospital = await this.repository.findOne({
      where: { id: hospitalId },
      select: [
        'id',
        'name',
        'facilityLogo',
        'website',
        'facebook',
        'instagram',
        'twitter',
        'supportMail',
      ],
    });

    if (!hospital) {
      throw new NotFoundException('Hospital Not Found');
    }

    if (profileToDelete) {
      profileToDelete.user.email = null;
      profileToDelete.user.phoneNumber = null;
      profileToDelete.user.password = null;
      profileToDelete.user.corporatePhoneNumber = null;
      profileToDelete.details.secondaryPhoneNumber = null;
      profileToDelete.active = false;
      profileToDelete.isDefault = false;
      profileToDelete.deleted = true;
      profileToDelete.deletedAt = new Date();

      await this.entityManager.save(ProfileModel, profileToDelete);

      const msg: IMessage = {
        from: `${hospital.name} <${hospital.supportMail}>`,
        to: `${profileToDelete?.user?.nonCorporateEmail}`,
        subject: 'Account Deletion',
        html: mailerTemplate({
          firstName: profileToDelete?.fullName?.split(' ')[0],
          message: `Your account with ${hospital.name} has been deleted successfully.`,
          facilityLogo: hospital.facilityLogo,
          instagram: hospital.instagram,
          website: hospital.website,
          facebook: hospital.facebook,
          twitter: hospital.twitter,
          facilityName: hospital.name,
          supportMail: hospital.supportMail,
        }),
      };

      await this.mailerService.sendMail(msg);

      return profileToDelete;
    }
  }

  async bulkDeleteHospitalStaffForFacility(
    profileIds: string[],
    hospitalId: string,
  ): Promise<ProfileModel[]> {
    const profilesToDelete: ProfileModel[] = [];
    await Promise.all(
      profileIds.map(async (profileId) => {
        const deletedProfile = await this.deleteHospitalStaffForFacility(
          profileId,
          hospitalId,
        );
        if (deletedProfile) {
          profilesToDelete.push(deletedProfile);
        }
      }),
    );

    return profilesToDelete;
  }

  async deleteHospital(
    hospitalId: string,
    profile: ProfileModel,
  ): Promise<HospitalModel> {
    const hospital = await this.repository.deleteHospital(hospitalId, profile);

    const msg: IMessage = {
      to: `${profile?.user?.email}`,
      subject: 'Facility Deletion',
      html: mailerTemplate({
        firstName: profile?.details?.firstName,
        message: 'Your Facility Account Has Been Deleted Successfully',
      }),
    };

    await this.mailerService.sendMail(msg);

    return hospital;
  }

  async updateHospital(
    hospitalId: string,
    input: UpdateHospitalInput,
  ): Promise<HospitalModel> {
    if (input?.hospitalContactPhoneNumber?.countryCode) {
      input.hospitalContactPhoneNumber.countryCode =
        input?.hospitalContactPhoneNumber?.countryCode.replace(/^\+/, '');
    }
    const updatedHospital = await this.repository.updateHospital(
      hospitalId,
      input,
    );
    await this.virtualAccountService.updateHospitalVirtualAccountName(
      updatedHospital.id,
      updatedHospital.name,
    );

    return updatedHospital;
  }

  // Hospital Properties

  async addProperties(
    profile: ProfileModel,
    items: Record<any, any>[],
    name: 'services' | 'inventoryItems',
    csvImport?: boolean,
  ): Promise<HospitalModel> {
    const id = profile.hospitalId;
    const hospital = await this.repository
      .findOneOrFail({ where: { id } })
      .catch(() => {
        throw new Error('Facility Not Found');
      });
    const itemsWithId = items.map((item: any) => ({
      ...item,
      id: nanoid(),
      createdOn: item?.createdOn || new Date(),
      creatorName: profile.fullName,
    }));
    const newItems = csvImport
      ? itemsWithId
      : [...itemsWithId, ...(hospital[name] || [])];
    return this.repository.save({ ...hospital, [name]: newItems });
  }

  async addProvidersProperties(
    profile: ProfileModel,
    items: Record<any, any>[],
    csvImport?: boolean,
  ): Promise<HospitalModel> {
    const id = profile.hospitalId;
    const hospital = await this.repository
      .findOneOrFail({ where: { id } })
      .catch(() => {
        throw new Error('Facility Not Found');
      });
    let foundPrivate = false;
    let foundClinifyWallet = false;

    const itemsWithId = items.map((item: any) => {
      const itemName = item.name.trim();
      let itemId = nanoid();
      let itemCode = '';

      const alreadyExist = hospital.providers.find(({ name }) => {
        const localItemName = name.trim();
        if (itemName === CLINIFY_WALLET_NAME) {
          foundClinifyWallet = true;
        } else if (itemName === PRIVATE_ITEM_NAME) {
          foundPrivate = true;
        }
        return itemName === localItemName;
      });

      if (alreadyExist) {
        return { ...alreadyExist };
      }

      if (itemName === CLINIFY_WALLET_NAME) {
        itemId = CLINIFY_WALLET_ITEM_ID;
        itemCode = CLINIFY_WALLET_PROVIDER_CODE;
        foundClinifyWallet = true;
      } else if (itemName === PRIVATE_ITEM_NAME) {
        itemId = PRIVATE_ITEM_ID;
        itemCode = PRIVATE_PROVIDER_CODE;
        foundPrivate = true;
      } else {
        const provider = hmoProviders.find(
          ({ name }) => itemName === name.trim(),
        );

        if (!provider?.providerCode) {
          throw new Error('Tariff Not Found');
        }
        itemCode = provider.providerCode;
      }

      return {
        ...item,
        id: itemId,
        code: itemCode,
        createdOn: item?.createdOn || new Date(),
        creatorName: profile.fullName,
      };
    });
    if (csvImport && !foundPrivate) {
      itemsWithId.push({
        name: PRIVATE_ITEM_NAME,
        id: PRIVATE_ITEM_ID,
        code: PRIVATE_PROVIDER_CODE,
      });
    }

    if (csvImport && !foundClinifyWallet) {
      itemsWithId.push({
        name: CLINIFY_WALLET_NAME,
        id: CLINIFY_WALLET_ITEM_ID,
        code: CLINIFY_WALLET_PROVIDER_CODE,
      });
    }

    const newItems = csvImport
      ? itemsWithId
      : [...itemsWithId, ...(hospital.providers || [])];
    return this.repository.save({ ...hospital, providers: newItems });
  }

  async updateProperties(
    profile: ProfileModel,
    payload: HospitalInventoryItemInput,
    itemId: string,
    name: 'providers' | 'services' | 'inventoryItems',
  ): Promise<HospitalModel> {
    const id = profile.hospitalId;
    const hospital = await this.repository
      .findOneOrFail({ where: { id } })
      .catch(() => {
        throw new Error('Facility Not Found');
      });
    const update = [...(hospital[name] || [])];
    const updateIndx = update.findIndex((item) => item.id === itemId);
    if (updateIndx !== -1)
      update[updateIndx] = {
        ...payload,
        id: itemId,
        createdOn: update[updateIndx]?.createdOn,
        creatorName: update[updateIndx]?.creatorName,
        updatedOn: new Date(),
      };
    return this.repository.save({ ...hospital, [name]: update });
  }

  async removeProperties(
    profile: ProfileModel,
    ids: string[],
    name: 'services' | 'inventoryItems',
  ): Promise<HospitalModel> {
    const id = profile.hospitalId;
    const hospital = await this.repository
      .findOneOrFail({ where: { id } })
      .catch(() => {
        throw new Error('Facility Not Found');
      });
    const update = [...(hospital[name] || [])].filter(
      (item) => !ids.includes(item.id),
    );

    return this.repository.save({ ...hospital, [name]: update });
  }

  async removeHospitalProvider(
    profile: ProfileModel,
    providerIds: string[],
  ): Promise<HospitalModel> {
    const hospitalId = profile.hospitalId;
    const hospital = await this.repository
      .findOneOrFail({ where: { id: hospitalId } })
      .catch(() => {
        throw new Error('Facility Not Found');
      });
    const providerCodes = [];
    const ids = providerIds.map((id) => {
      const singleProvider = hospital.providers.find(
        ({ id: _id }) => _id === id,
      );
      singleProvider && providerCodes.push(singleProvider.code);
      return id;
    });
    const update = [...(hospital.providers || [])].filter(
      (item) => !ids.includes(item.id),
    );

    return await customDSSerializeInTransaction(
      this.dataSource,
      async (manager) => {
        const repositoryTrx = manager.withRepository(this.repository);
        const item = repositoryTrx.save({ ...hospital, providers: update });

        if (providerCodes.length) {
          await this.priceService.removeHmoPriceList(
            profile,
            providerCodes,
            manager,
          );
          await this.hmoService.deleteTarrifServiceList(
            hospitalId,
            providerCodes,
          );
        }
        return item;
      },
    );
  }

  async addFacilityBillingInformation(
    profile: ProfileModel,
    hospitalId: string,
    input: FacilityBillingInformationInput,
  ): Promise<FacilityBillingInformationModel> {
    const hospital = await this.repository
      .findOneOrFail({
        where: { id: hospitalId },
      })
      .catch(() => {
        throw new NotFoundException('Facility Not Found');
      });

    return this.entityManager.save(
      new FacilityBillingInformationModel({
        ...input,
        hospital,
        createdBy: profile,
        creatorName: profile.fullName,
      }),
    );
  }

  async updateFacilityBillingInformation(
    profile: ProfileModel,
    billingInformationId: string,
    input: FacilityBillingInformationInput,
  ): Promise<FacilityBillingInformationModel> {
    const billingInformation = await this.entityManager
      .findOneOrFail(FacilityBillingInformationModel, {
        where: { id: billingInformationId },
        relations: ['hospital'],
      })
      .catch(() => {
        throw new NotFoundException('Billing Information Not Found');
      });
    if (!validateBillingInformationMutation(profile, billingInformation)) {
      throw new ForbiddenException(
        'Not Authorized To Modify This Billing Information',
      );
    }
    return this.entityManager.save(FacilityBillingInformationModel, {
      ...billingInformation,
      ...input,
      updatedBy: profile,
      lastModifierName: profile.fullName,
      lastModifierId: profile.id,
    });
  }

  async updateFacilityBillingInformationByAgency(
    profile: ProfileModel,
    hospitalId: string,
    input: FacilityBillingInformationInput,
  ): Promise<FacilityBillingInformationModel> {
    if (!profile.hmoId) {
      throw new ForbiddenException(
        'Not Authorized To Modify This Billing Information',
      );
    }
    let billingInformation: FacilityBillingInformationModel;

    if (input.id) {
      billingInformation = await this.entityManager.findOne(
        FacilityBillingInformationModel,
        {
          where: { id: input.id },
          relations: ['hospital'],
        },
      );
      if (billingInformation) {
        await this.entityManager.update(
          FacilityBillingInformationModel,
          {
            hospitalId: billingInformation.hospitalId,
            isPreferredPayoutAccount: true,
          },
          {
            isPreferredPayoutAccount: false,
          },
        );
      }
    }
    delete input.id;
    return this.entityManager.save(FacilityBillingInformationModel, {
      ...billingInformation,
      ...input,
      isPreferredPayoutAccount: true,
      ...(billingInformation
        ? {
            updatedBy: profile,
            lastModifierName: profile.fullName,
            lastModifierId: profile.id,
          }
        : {
            createdBy: profile,
            creatorName: profile.fullName,
            hospitalId,
          }),
    });
  }

  async setPreferredPayoutAccount(
    mutator: ProfileModel,
    billingInformationId: string,
  ): Promise<FacilityBillingInformationModel[]> {
    const billingInformationRecords = await this.entityManager.find(
      FacilityBillingInformationModel,
      {
        where: {
          hospital: { id: mutator.hospitalId },
        },
      },
    );

    await this.entityManager.update(
      FacilityBillingInformationModel,
      {
        hospitalId: mutator.hospitalId,
        isPreferredPayoutAccount: true,
      },
      {
        isPreferredPayoutAccount: false,
      },
    );

    await this.entityManager.update(
      FacilityBillingInformationModel,
      { id: billingInformationId, hospitalId: mutator.hospitalId },
      {
        isPreferredPayoutAccount: true,
      },
    );

    return billingInformationRecords.map((b) => ({
      ...b,
      isPreferredPayoutAccount: b.id === billingInformationId,
    }));
  }

  async getPreferredPayoutAccount(
    hospitalId: string,
  ): Promise<BankAccountDetailsResponse> {
    return this.entityManager.findOne(FacilityBillingInformationModel, {
      where: {
        hospitalId,
        isPreferredPayoutAccount: true,
      },
    });
  }

  async deleteFacilityBillingInformation(
    profile: ProfileModel,
    billingInformationId: string,
  ): Promise<FacilityBillingInformationModel> {
    const billingInformation = await this.entityManager
      .findOneOrFail(FacilityBillingInformationModel, {
        where: { id: billingInformationId },
      })
      .catch(() => {
        throw new NotFoundException('Billing Information Not Found');
      });
    if (!validateBillingInformationMutation(profile, billingInformation)) {
      throw new ForbiddenException('Cannot Delete This Billing Information');
    }
    await this.entityManager.delete(FacilityBillingInformationModel, {
      id: billingInformationId,
    });

    return billingInformation;
  }

  private async removeS3Asset(url: string) {
    // https://<bk-name>.s3.<region>.amazonaws.com/<key>
    const key = url.split('.com')[1].slice(1);
    const command = new DeleteObjectCommand({
      Bucket: `clinify-uploads-${config.env}`,
      Key: key,
    });

    await this.s3Client.send(command);
  }

  async updateFacilityLogo(
    profile: ProfileModel,
    facilityLogo: string,
  ): Promise<HospitalModel> {
    const id = profile.hospitalId;
    const hospital = await this.repository.findOneOrFail({ where: { id } });

    const update = await this.repository.save({ ...hospital, facilityLogo });
    if (hospital.facilityLogo) {
      await this.removeS3Asset(hospital.facilityLogo);
    }

    return update;
  }

  async getHmoHospitals(
    hospitalId: string,
    hmoId?: string,
  ): Promise<HmoHospitalModel[]> {
    const hmoHospitals = await this.entityManager.find(HmoHospitalModel, {
      where: {
        hospital: { id: hospitalId },
        providerId: hmoId,
      },
      relations: ['provider'],
    });

    return hmoHospitals;
  }

  getBillingInformation(
    hospitalId: string,
    isPreferredPayoutAccount?: boolean,
  ): Promise<FacilityBillingInformationModel[]> {
    return this.entityManager.find(FacilityBillingInformationModel, {
      where: {
        hospital: { id: hospitalId },
        ...(isPreferredPayoutAccount ? { isPreferredPayoutAccount } : {}),
      },
    });
  }

  async addSponsorBillingInformation(
    profile: ProfileModel,
    hospitalId: string,
    input: SponsorBillingInformationInput,
  ): Promise<SponsorBillingInformationModel> {
    const hospital = await this.repository
      .findOneOrFail({
        where: { id: hospitalId },
      })
      .catch(() => {
        throw new NotFoundException('Facility Not Found');
      });

    return this.entityManager.save(
      new SponsorBillingInformationModel({
        ...input,
        hospital,
        createdBy: profile,
        creatorName: profile.fullName,
      }),
    );
  }

  async updateSponsorBillingInformation(
    profile: ProfileModel,
    billingInformationId: string,
    input: FacilityBillingInformationInput,
  ): Promise<SponsorBillingInformationModel> {
    const billingInformation = await this.entityManager
      .findOneOrFail(SponsorBillingInformationModel, {
        where: { id: billingInformationId },
        relations: ['hospital'],
      })
      .catch(() => {
        throw new NotFoundException('Sponsor Billing Information Not Found');
      });
    if (
      !this.validateSponsorBillingInformationMutation(
        profile,
        billingInformation,
      )
    ) {
      throw new ForbiddenException(
        'Not Authorized To Modify This Sponsor Billing Information',
      );
    }
    return this.entityManager.save(SponsorBillingInformationModel, {
      ...billingInformation,
      ...input,
      updatedBy: profile,
      lastModifierName: profile.fullName,
      lastModifierId: profile.id,
    });
  }

  async deleteSponsorBillingInformation(
    profile: ProfileModel,
    billingInformationId: string,
  ): Promise<SponsorBillingInformationModel> {
    const billingInformation = await this.entityManager
      .findOneOrFail(SponsorBillingInformationModel, {
        where: { id: billingInformationId },
      })
      .catch(() => {
        throw new NotFoundException('Sponsor Billing Information Not Found');
      });
    if (
      !this.validateSponsorBillingInformationMutation(
        profile,
        billingInformation,
      )
    ) {
      throw new ForbiddenException(
        'Cannot Delete This Sponsor Billing Information',
      );
    }
    await this.entityManager.delete(SponsorBillingInformationModel, {
      id: billingInformationId,
    });

    return billingInformation;
  }

  getSponsorBillingInformation(
    hospitalId: string,
  ): Promise<SponsorBillingInformationModel[]> {
    return this.entityManager.find(SponsorBillingInformationModel, {
      where: {
        hospital: { id: hospitalId },
      },
    });
  }

  private validateSponsorBillingInformationMutation(
    editor: ProfileModel,
    billingInformation: SponsorBillingInformationModel,
  ): boolean {
    return billingInformation.hospitalId === editor.hospitalId;
  }

  findByPartnerId(partnerId: string): Promise<HospitalModel[]> {
    return this.repository.find({ where: { partnerId } });
  }
  getFacilityPreference(
    preferenceId: string,
  ): Promise<FacilityPreferenceModel> {
    return this.repository.getFacilityPreference(preferenceId);
  }

  isHQFacility(id: string): Promise<boolean> {
    return this.repository.isHQFacility(id, this.dataSource);
  }

  getFacilityBranches(id: string): Promise<HospitalModel[]> {
    return this.repository.getFacilityBranches(id, this.dataSource);
  }

  private validateBranchInformationMutation(
    mutator: ProfileModel,
    hospitalId: string,
  ): Promise<HospitalModel> {
    return this.repository.findOneOrFail({
      where: { id: hospitalId, hqFacilityId: mutator.hospitalId },
    });
  }

  private async updateBranchInformationInTransaction(
    manager: EntityManager,
    branchHospital: HospitalModel,
    input: BranchInformationInput,
  ): Promise<HospitalModel> {
    const adminProfile = await manager.findOneOrFail(ProfileModel, {
      where: {
        hospital: {
          id: branchHospital.id,
        },
        type: UserType.OrganizationAdmin,
      },
      select: {
        id: true,
        detailsId: true,
      },
    });
    await manager.update(
      HospitalModel,
      { id: branchHospital.id },
      {
        name: input.name,
        supportMail: input.supportMail,
        website: input.website,
        phoneNumber: input.phoneNumber,
        address: input.address,
        secondaryPhoneNumber: input.secondaryPhoneNumber,
      },
    );
    await manager.update(
      FacilityPreferenceModel,
      { id: branchHospital.preferenceId },
      {
        useHQFacilityInventory: input.useHQFacilityInventory,
        useHQFacilityTariffs: input.useHQFacilityTariffs,
      },
    );
    await manager.update(
      ProfileModel,
      {
        id: adminProfile.id,
      },
      { title: input.administratorTitle },
    );
    await manager.update(
      ProfileDetailsModel,
      { id: adminProfile.detailsId },
      {
        firstName: input.administratorFirstName,
        middleName: input.administratorMiddleName,
        lastName: input.administratorLastName,
      },
    );

    return new HospitalModel({
      ...branchHospital,
      name: input.name,
      website: input.website,
      supportMail: input.supportMail,
      secondaryPhoneNumber: input.secondaryPhoneNumber,
      phoneNumber: input.phoneNumber,
      address: input.address,
    });
  }

  async updateBranchInformation(
    mutator: ProfileModel,
    hospitalId: string,
    input: BranchInformationInput,
  ): Promise<HospitalModel> {
    const branchHospital = await this.validateBranchInformationMutation(
      mutator,
      hospitalId,
    );

    return customDSSerializeInTransaction(this.dataSource, (manager) =>
      this.updateBranchInformationInTransaction(manager, branchHospital, input),
    );
  }

  async updateFacilityRecordVisibility(
    hospitalId: string,
    visibility: boolean,
  ): Promise<boolean> {
    return this.repository.updateFacilityRecordVisibility(
      hospitalId,
      visibility,
    );
  }

  getFacilityRecordVisibility(hospitalId: string): Promise<boolean> {
    return this.repository.getFacilityRecordVisibility(hospitalId);
  }
  async getCapitationDetails(
    mutator: ProfileModel,
    args: {
      hospitalId: string;
      startDate: string;
      endDate: string;
      planId?: string;
    },
  ): Promise<CapitationDetails[]> {
    const {
      hospitalId,
      startDate = new Date().toISOString(),
      endDate = new Date().toISOString(),
      planId,
    } = args;

    const fp = await this.entityManager.findOne(FacilityPreferenceModel, {
      where: {
        hospitalId: mutator.hospitalId,
      },
    });
    const isPerPlanType = fp?.enrolleeCapitationAmountPerPlan;

    const enrolleeCountQuery = `
      SELECT COUNT(*) as count
      FROM hmo_profiles 
      WHERE provider_id = $2 AND primary_provider_id = $1 
        AND LOWER(member_status) = 'active'
        ${planId && !isPerPlanType ? 'AND member_plan_id = $4' : ''}
        AND (
          member_start_date IS NULL 
          OR member_start_date <= $3
        )
    `;

    const queryParams = [
      hospitalId,
      mutator.hmoId,
      new Date(startDate),
      ...(planId && !isPerPlanType ? [planId] : []),
    ];

    const enrolleeCountResult = await await queryDSWithSlave(
      this.dataSource,
      enrolleeCountQuery,
      queryParams,
    );
    const enrolleeCount = parseInt(enrolleeCountResult[0]?.count || '0', 10);

    const detailsByPlanType: CapitationDetailByPlanType[] = [];
    const previousDecreasePercentageQueryPlanTypeWhereClauseString =
      ((): string => {
        if (!isPerPlanType) {
          if (planId) {
            return `AND hmo_plan_type_id = $4`;
          }
          return `AND hmo_plan_type_id IS NULL`;
        } else return `AND hmo_plan_type_id IS NULL`;
      })();
    const previousDecreasePercentageQueryResult = await queryDSWithSlave(
      this.dataSource,
      `SELECT payout_decrease_percentage FROM transfer_funds
                                  WHERE created_date < $1 AND transfer_status = 'Success'
                                  AND hmo_provider_id = $2 AND hospital_id = $3
                                  AND is_enrollee_payout = true
                                  ${previousDecreasePercentageQueryPlanTypeWhereClauseString}
                                  ORDER BY created_date DESC LIMIT 1`,
      [
        args.startDate,
        mutator.hmoId,
        hospitalId,
        ...(!isPerPlanType && planId ? [planId] : []),
      ],
    );
    const previousDecreasePercentage =
      previousDecreasePercentageQueryResult?.[0]?.payout_decrease_percentage;
    if (fp?.enrolleeCapitionAmountByPlanType) {
      for (const planType of fp.enrolleeCapitionAmountByPlanType) {
        if (planId && planType.planTypeId !== planId) continue;

        const planTypeEnrolleeCountQuery = `
          SELECT COUNT(*) as count
          FROM hmo_profiles 
          WHERE provider_id = $2 AND primary_provider_id = $1 
            AND LOWER(member_status) = 'active'
            AND member_plan_id = $3
            AND (
              member_start_date IS NULL 
              OR member_start_date <= $4
            )
        `;

        const planTypeQueryParams = [
          hospitalId,
          mutator.hmoId,
          planType.planTypeId,
          new Date(startDate),
        ];
        const planTypeEnrolleeCountResult = await queryDSWithSlave(
          this.dataSource,
          planTypeEnrolleeCountQuery,
          planTypeQueryParams,
        );
        const enrolleeCount = parseInt(
          planTypeEnrolleeCountResult[0]?.count || '0',
          10,
        );
        detailsByPlanType.push({
          planType: planType.planTypeId,
          enrolleeCount,
          totalCapitationAmount: planType.amount,
          planTypeName: planType?.planTypeName,
        });
      }
    }
    const totalCapitationAmount = fp?.enrolleeCapitationAmount || 0;
    const from = moment(startDate).startOf('month').toDate();
    const to = moment(endDate).endOf('month').toDate();
    // For where there's a paid capitation for all plans and is not per plan type
    const paidTransferFundsForAllPlans =
      !isPerPlanType && planId
        ? await this.entityManager.find(TransferFundModel, {
            where: {
              hospitalId,
              createdDate: Between(from, to),
              isEnrolleePayout: true,
              hmoPlanTypeId: IsNull(),
              hmoProviderId: mutator.hmoId,
              transferStatus: FundTransactionStatus.Success,
            },
            order: { createdDate: 'DESC' },
          })
        : null;
    // Use paid capitation gotten from above if it exists, else use row with persisted transfer funds if available
    let transferFunds =
      paidTransferFundsForAllPlans ||
      (await this.entityManager.find(TransferFundModel, {
        where: {
          hospitalId,
          createdDate: Between(from, to),
          isEnrolleePayout: true,
          hmoPlanTypeId: planId && !isPerPlanType ? planId : IsNull(),
          hmoProviderId: mutator.hmoId,
          ...(planId && !isPerPlanType
            ? { transferStatus: FundTransactionStatus.Success }
            : {}),
        },
        order: { createdDate: 'DESC' },
        relations: { hmoPlanType: true },
      }));

    if (!transferFunds?.length && planId && !isPerPlanType) {
      transferFunds = await this.entityManager.find(TransferFundModel, {
        where: {
          hospitalId,
          createdDate: Between(from, to),
          isEnrolleePayout: true,
          hmoPlanTypeId: planId,
          hmoProviderId: mutator.hmoId,
        },
        order: { createdDate: 'DESC' },
        relations: { hmoPlanType: true },
      });
    }
    if (!transferFunds?.length) {
      let previousTransferFund = await this.entityManager.findOne(
        TransferFundModel,
        {
          where: {
            hospitalId,
            isEnrolleePayout: true,
            hmoPlanTypeId: planId && !isPerPlanType ? planId : IsNull(),
            hmoProviderId: mutator.hmoId,
            createdDate: LessThan(from),
          },
          order: { createdDate: 'DESC' },
        },
      );

      if (!previousTransferFund && planId && !isPerPlanType) {
        previousTransferFund = await this.entityManager.findOne(
          TransferFundModel,
          {
            where: {
              hospitalId,
              isEnrolleePayout: true,
              hmoPlanTypeId: planId,
              hmoProviderId: mutator.hmoId,
            },
            order: { createdDate: 'DESC' },
          },
        );
      }
      // Merge previousTransferFund detailsByPlanType with missing items from main detailsByPlanType
      let mergedDetailsByPlanType = detailsByPlanType;
      if (previousTransferFund?.detailsByPlanType) {
        mergedDetailsByPlanType = [...previousTransferFund.detailsByPlanType];

        const existingPlanTypes = new Set(
          previousTransferFund.detailsByPlanType.map((item) => item.planType),
        );

        const missingItems = detailsByPlanType.filter(
          (item) => !existingPlanTypes.has(item.planType),
        );

        mergedDetailsByPlanType.push(...missingItems);
      }

      return [
        {
          enrolleeCount: previousTransferFund?.enrolleeCount || enrolleeCount,
          totalCapitationAmount,
          transferFund: null,
          detailsByPlanType: mergedDetailsByPlanType,
          payoutDecreasePercentage:
            previousTransferFund?.payoutDecreasePercentage ??
            previousDecreasePercentage,
        },
      ];
    }
    return transferFunds.map((t) => ({
      enrolleeCount,
      totalCapitationAmount:
        Number(t.totalCapitationAmount) || totalCapitationAmount,
      transferFund: t,
      detailsByPlanType,
      payoutDecreasePercentage: previousDecreasePercentage,
    }));
  }

  async payoutCapitation(
    mutator: ProfileModel,
    inputs: CapitationPayoutInput[],
    origin: string,
  ): Promise<TransferFundModel[]> {
    return this.entityManager.transaction(async (entityManager) => {
      const providersHospitalIds = inputs.map((i) => i.hospitalId);
      const facilityBillings = await entityManager.find(
        FacilityBillingInformationModel,
        {
          where: {
            hospitalId: In(providersHospitalIds),
            isPreferredPayoutAccount: true,
          },
        },
      );
      const fp = await entityManager.findOne(FacilityPreferenceModel, {
        where: {
          hospitalId: mutator.hospitalId,
        },
        select: ['payoutCommissionPayer'],
      });
      const commissionPayer = fp?.payoutCommissionPayer;
      const isAgencyPayer =
        (fp?.payoutCommissionPayer || PayoutCommissionPayer.Agency) ===
        PayoutCommissionPayer.Agency;
      const [{ hmoProviderCode }] = await queryDSWithSlave(
        this.dataSource,
        `
        SELECT id, provider_code AS "hmoProviderCode" FROM hmo_providers WHERE id = $1
        `,
        [mutator.hmoId],
      );

      if (!facilityBillings.length)
        throw new NotFoundException('Preferred Payout Bank Account Not Found');

      const token = await this.bankService.getOperationToken();

      const totalAmountToPay = inputs.reduce(
        (total, input) => total + input.totalAmountForPayout,
        0,
      );

      const amountToRemitFromWallet = isAgencyPayer
        ? Math.round(totalAmountToPay + getCommissionAmount(totalAmountToPay))
        : totalAmountToPay;
      const hmoAgencyWallet = await entityManager.findOne(WalletModel, {
        where: { hospitalId: mutator.hospitalId },
      });
      const hmoAgencyWalletBalance = hmoAgencyWallet.getTotalBalance();
      if (hmoAgencyWalletBalance < amountToRemitFromWallet) {
        throw new Error('Insufficient Funds In Wallet');
      }

      const banks = await this.bankService.getNIPBanks(
        VirtualAccountProvider.WEMA,
        token,
      );
      const transferFunds: TransferFundModel[] = [];
      for (const facilityBilling of facilityBillings) {
        const capitationDetail = inputs.find(
          (input) => input.hospitalId === facilityBilling.hospitalId,
        );
        let existingTransferFund: TransferFundModel | null = null;
        if (!capitationDetail?.transferFundId) {
          throw new Error('Approvals Not Found');
        }
        if (capitationDetail?.transferFundId) {
          existingTransferFund = await entityManager.findOne(
            TransferFundModel,
            {
              where: {
                id: capitationDetail.transferFundId,
              },
              relations: {
                hmoPlanType: true,
                hospital: true,
                hmoProvider: true,
              },
            },
          );
          if (
            existingTransferFund &&
            [
              FundTransactionStatus.Processing,
              FundTransactionStatus.Success,
            ].includes(existingTransferFund.transferStatus)
          ) {
            throw new Error('Capitation Payout Already Exists');
          }
        }
        if (!existingTransferFund) {
          throw new Error('Approvals Not Found');
        }

        const amountToPay = capitationDetail?.totalAmountForPayout;
        const bankName = findClosestMatch(
          facilityBilling.bankName,
          banks.list.map((bank) => bank.bankName),
        );
        const bankCode = banks.list.find(
          (bank) => bank.bankName === bankName,
        )?.bankCode;
        const destinationAccountInformation =
          await this.bankService.verifyAccountNumber(
            VirtualAccountProvider.WEMA,
            {
              bankCode,
              accountNumber: facilityBilling.accountNumber,
            },
            token,
          );
        if (!destinationAccountInformation.accountName) {
          throw new Error('Unable To Verify Bank Account Number');
        }
        const sourceBank = await this.bankService.getBank(
          VirtualAccountProvider.WEMA,
        );

        const serviceChargeAmount = Math.round(
          getCommissionAmount(amountToPay),
        );

        const paymentMonth = moment(existingTransferFund.createdDate).format(
          'MMMM YYYY',
        );

        const transferFund = new TransferFundModel({
          ...existingTransferFund,
          amount: amountToPay,
          destinationBankCode: bankCode,
          destinationAccountNumber: facilityBilling.accountNumber,
          destinationAccountName: destinationAccountInformation.accountName,
          destinationBankName: bankName,
          originatorName:
            HMO_PROVIDER_ORIGINATOR_NAME[hmoProviderCode] || 'CLINIFY',
          narration: `${
            HMO_PROVIDER_ORIGINATOR_NAME[hmoProviderCode]
              ? `${HMO_PROVIDER_ORIGINATOR_NAME[hmoProviderCode]} `
              : ''
          }${paymentMonth} Capitation Payment`,
          sourceAccountNumber: sourceBank.collectionAccountNumber,
          createdBy: mutator,
          transferStatus: FundTransactionStatus.Processing,
          additionalNote: '',
          serviceChargeAmount,
          hmoProviderId: mutator.hmoId,
          hospitalId: facilityBilling.hospitalId,
          enrolleeCount: capitationDetail?.enrolleeCount,
          totalCapitationAmount: capitationDetail?.totalCapitationAmount,
          isEnrolleePayout: true,
          payoutDecreasePercentage: capitationDetail.payoutDecreasePercentage,
          payoutCommissionPayer: commissionPayer,
          detailsByPlanType: existingTransferFund?.detailsByPlanType?.map(
            (d) => ({
              ...d,
              payoutDecreasePercentage:
                capitationDetail.perPlanPayoutDecreasePercentage?.find(
                  ({ hmoPlanTypeId }) => hmoPlanTypeId === d.planType,
                )?.payoutDecreasePercentage ?? d.payoutDecreasePercentage,
            }),
          ),
        });
        const savedTransferFund = await entityManager.save(
          TransferFundModel,
          transferFund,
        );
        let response: InitiateFundTransferResponse;
        try {
          const amountToDeposit = isAgencyPayer
            ? amountToPay
            : Math.round(amountToPay - getCommissionAmount(amountToPay));
          const paymentReference =
            savedTransferFund.newPaymentReference || savedTransferFund.id;
          response = await this.bankService.transferFund(
            VirtualAccountProvider.WEMA,
            savedTransferFund.id,
            {
              destinationAccountNumber: facilityBilling.accountNumber,
              destinationAccountName: destinationAccountInformation.accountName,
              destinationBankCode: bankCode,
              originatorName:
                HMO_PROVIDER_ORIGINATOR_NAME[hmoProviderCode] || 'CLINIFY',
              payoutReference: paymentReference,
              sourceAccountNumber: sourceBank.collectionAccountNumber,
              amount: amountToDeposit,
              narration: `${
                HMO_PROVIDER_ORIGINATOR_NAME[hmoProviderCode]
                  ? `${HMO_PROVIDER_ORIGINATOR_NAME[hmoProviderCode]} `
                  : ''
              }${paymentMonth} Capitation Payment`,
            },
            token,
          );
        } catch (e) {
          savedTransferFund.additionalTransactionMessage = e.message;
        }

        savedTransferFund.transferStatus = response?.success
          ? FundTransactionStatus.Success
          : FundTransactionStatus.Failed;
        if (response?.shouldGenerateNewPaymentReference) {
          const _ref = generateUUID();
          if (!savedTransferFund.additionalPaymentReferences) {
            savedTransferFund.additionalPaymentReferences = [];
          }
          savedTransferFund.additionalPaymentReferences.push(_ref);
          savedTransferFund.newPaymentReference = _ref;
        }
        if (response?.responseMessage) {
          savedTransferFund.additionalTransactionMessage =
            response.responseMessage;
        }
        savedTransferFund.transferReference = response?.payoutReference;
        const savedUpdatedTransferFund = await entityManager.save(
          TransferFundModel,
          savedTransferFund,
        );
        transferFunds.push(savedUpdatedTransferFund);
        if (response?.success) {
          savedTransferFund.paidById = mutator.id;
          await this.walletTransactionService.performWalletTransaction(
            entityManager,
            generateUUID(),
            undefined,
            amountToRemitFromWallet,
            TransactionType.WITHDRAWAL,
            hmoAgencyWallet,
          );
          try {
            const beneficiaryHospital = await entityManager.findOne(
              HospitalModel,
              {
                where: { id: facilityBilling.hospitalId },
              },
            );
            this.hmoProviderService.sendCapitationTransactionReceiptEmail(
              mutator,
              [{ id: savedTransferFund.id, amount: savedTransferFund.amount }],
              origin,
              beneficiaryHospital.supportMail,
              entityManager,
            );
            if (
              beneficiaryHospital.hospitalSupportPhoneNumber?.value &&
              existingTransferFund?.hmoProvider?.enableWhatsAppNotifications
            ) {
              this.twilioWhatsappService.sendWhatsappMessageFromTemplate(
                TEMPLATE_SIDS.capitationPaymentNotification,
                JSON.stringify({
                  1: beneficiaryHospital.name,
                  2: moment(existingTransferFund.createdDate).format(
                    'MMMM YYYY',
                  ),
                  3: formatMoney(toNaira(savedTransferFund.amount)),
                  4: mutator.hospital?.name,
                }),
                beneficiaryHospital.hospitalSupportPhoneNumber.value,
              );
            }
          } catch (e) {
            // no-op
          }
        }
      }
      return transferFunds;
    });
  }
  async capitationAuditApproval(
    mutator: ProfileModel,
    hospitalIds: string[],
    startDate: string,
    endDate: string,
    status: boolean,
    hmoPlanTypeId: string | null,
    hmoFacilityId: string,
  ): Promise<TransferFundModel[]> {
    const from = moment(startDate).startOf('day').toDate();
    const to = moment(endDate).endOf('day').toDate();
    const transferFunds: TransferFundModel[] = [];
    const fp = await this.entityManager.findOne(FacilityPreferenceModel, {
      where: {
        hospitalId: hmoFacilityId,
      },
    });

    const months = Math.ceil(moment(to).diff(moment(from), 'months')) || 1;
    for (let i = 0; i < months; i++) {
      for (const hospitalId of hospitalIds) {
        const month = moment(from).add(i, 'months');
        let transferFund = await this.entityManager.findOne(TransferFundModel, {
          where: {
            hospitalId,
            createdDate: Between(
              month.startOf('month').toDate(),
              month.endOf('month').toDate(),
            ),
            isEnrolleePayout: true,
            hmoPlanTypeId: hmoPlanTypeId ? hmoPlanTypeId : IsNull(),
            hmoProviderId: mutator.hmoId,
          },
        });

        if (!transferFund) {
          const enrolleeCountQuery = `
            SELECT COUNT(*) as count
            FROM hmo_profiles 
            WHERE provider_id = $2 AND primary_provider_id = $1
              AND LOWER(member_status) = 'active'
              ${hmoPlanTypeId ? 'AND member_plan_id = $3' : ''}
          `;

          const queryParams = [
            hospitalId,
            mutator.hmoId,
            ...(hmoPlanTypeId ? [hmoPlanTypeId] : []),
          ];

          const enrolleeCountResult = await queryDSWithSlave(
            this.dataSource,
            enrolleeCountQuery,
            queryParams,
          );
          const enrolleeCount = parseInt(
            enrolleeCountResult[0]?.count || '0',
            10,
          );

          let capitationAmountPerEnrollee = fp?.enrolleeCapitationAmount;
          if (fp?.enrolleeCapitationAmountPerPlan && hmoPlanTypeId) {
            const planType = fp.enrolleeCapitionAmountByPlanType.find(
              (p) => p.planTypeId === hmoPlanTypeId,
            );
            capitationAmountPerEnrollee = planType?.amount || 0;
          }
          let previousTransferFund: TransferFundModel;
          if (!hmoPlanTypeId) {
            previousTransferFund = await this.entityManager.findOne(
              TransferFundModel,
              {
                where: {
                  hospitalId,
                  isEnrolleePayout: true,
                  hmoPlanTypeId: IsNull(),
                  hmoProviderId: mutator.hmoId,
                },
                order: {
                  createdDate: 'DESC',
                },
              },
            );
          } else if (hmoPlanTypeId) {
            previousTransferFund = await this.entityManager.findOne(
              TransferFundModel,
              {
                where: {
                  hospitalId,
                  isEnrolleePayout: true,
                  hmoPlanTypeId,
                  hmoProviderId: mutator.hmoId,
                },
                order: {
                  createdDate: 'DESC',
                },
              },
            );
          }
          transferFund = new TransferFundModel({
            hospitalId,
            createdDate: month.startOf('month').toDate(),
            createdBy: mutator,
            auditApproval: [],
            isEnrolleePayout: true,
            enrolleeCount: previousTransferFund?.enrolleeCount || enrolleeCount,
            totalCapitationAmount: capitationAmountPerEnrollee,
            amount: 0,
            serviceChargeAmount: 0,
            transferStatus: FundTransactionStatus.Pending,
            hmoPlanTypeId,
            hmoProviderId: mutator.hmoId,
            detailsByPlanType: previousTransferFund?.detailsByPlanType,
          });
          await this.entityManager.save(TransferFundModel, transferFund);
        }

        const auditApprovals: ClaimsAccountApproval[] = cloneDeep(
          transferFund.auditApproval || [],
        );
        if (!auditApprovals.length && status) {
          transferFund.auditApproval = [
            {
              creatorId: mutator.id,
              creatorName: `${mutator.title ? `${mutator.title} ` : ''}${
                mutator.fullName
              }`,
              approvalGroup: mutator.type,
              createdDate: new Date(),
            },
          ];
        }
        if (!auditApprovals.length && !status) {
          continue;
        }

        if (!status) {
          transferFund.auditApproval = auditApprovals.filter(
            (approval) => approval.creatorId !== mutator.id,
          );
        }

        const existingApproval = auditApprovals.find(
          (approval) => approval.creatorId === mutator.id,
        );

        if (existingApproval && status) {
          continue;
        }

        if (!existingApproval && status) {
          transferFund.auditApproval = [
            ...auditApprovals,
            {
              creatorId: mutator.id,
              creatorName: `${mutator.title ? `${mutator.title} ` : ''}${
                mutator.fullName
              }`,
              approvalGroup: mutator.type,
              createdDate: new Date(),
            },
          ];
        }
        transferFund.hmoProviderId = mutator.hmoId;

        const savedTransferFunds = await this.entityManager.save(
          TransferFundModel,
          transferFund,
        );

        transferFunds.push(savedTransferFunds);
      }
    }
    return transferFunds;
  }

  async updateCapitationEnrolleeCount(
    mutator: ProfileModel,
    hospitalId: string,
    startDate: string,
    endDate: string,
    updatedEnrolleeCount: number,
    hmoPlanTypeId: string | null,
    hmoFacilityId: string,
  ): Promise<TransferFundModel[]> {
    const from = moment(startDate).startOf('day').toDate();
    const to = moment(endDate).endOf('day').toDate();
    const transferFunds: TransferFundModel[] = [];

    const months = Math.ceil(moment(to).diff(moment(from), 'months')) || 1;
    for (let i = 0; i < months; i++) {
      const month = moment(from).add(i, 'months');

      const fp = await this.entityManager.findOne(FacilityPreferenceModel, {
        where: { hospitalId: hmoFacilityId },
        select: [
          'enrolleeCapitionAmountByPlanType',
          'enrolleeCapitationAmountPerPlan',
          'enrolleeCapitationAmount',
        ],
      });
      const isPerPlanType = fp?.enrolleeCapitationAmountPerPlan;
      let transferFund = await this.entityManager.findOne(TransferFundModel, {
        where: {
          hospitalId,
          createdDate: Between(
            month.startOf('month').toDate(),
            month.endOf('month').toDate(),
          ),
          isEnrolleePayout: true,
          hmoPlanTypeId: isPerPlanType ? undefined : hmoPlanTypeId,
          hmoProviderId: mutator.hmoId,
        },
      });

      if (!transferFund) {
        const enrolleeCountQuery = `
          SELECT COUNT(*) as count
          FROM hmo_profiles 
          WHERE primary_provider_id = $1 
            AND provider_id = $2 
            AND LOWER(member_status) = 'active'
            ${hmoPlanTypeId && !isPerPlanType ? 'AND member_plan_id = $4' : ''}
            AND (
              member_start_date IS NULL 
              OR member_start_date <= $3
            )
        `;

        const queryParams = [
          hospitalId,
          mutator.hmoId,
          new Date(startDate),
          ...(hmoPlanTypeId && !isPerPlanType ? [hmoPlanTypeId] : []),
        ];

        const enrolleeCountResult = await queryDSWithSlave(
          this.dataSource,
          enrolleeCountQuery,
          queryParams,
        );
        const enrolleeCount = parseInt(
          enrolleeCountResult[0]?.count || '0',
          10,
        );
        const capitationAmountPerEnrollee = fp?.enrolleeCapitationAmountPerPlan
          ? fp?.enrolleeCapitionAmountByPlanType?.find(
              (v) => v.planTypeId === hmoPlanTypeId,
            )?.amount || 0
          : fp?.enrolleeCapitationAmount || 0;
        let previousTransferFund: TransferFundModel;
        if (!hmoPlanTypeId) {
          previousTransferFund = await this.entityManager.findOne(
            TransferFundModel,
            {
              where: {
                hospitalId,
                isEnrolleePayout: true,
                hmoPlanTypeId: isPerPlanType ? undefined : IsNull(),
                hmoProviderId: mutator.hmoId,
              },
            },
          );
        } else if (hmoPlanTypeId) {
          previousTransferFund = await this.entityManager.findOne(
            TransferFundModel,
            {
              where: {
                hospitalId,
                isEnrolleePayout: true,
                hmoPlanTypeId: isPerPlanType ? undefined : hmoPlanTypeId,
                hmoProviderId: mutator.hmoId,
              },
              order: {
                createdDate: 'DESC',
              },
            },
          );
        }
        let detailsByPlanType: CapitationDetailByPlanType[] =
          previousTransferFund?.detailsByPlanType || [];
        if (fp?.enrolleeCapitationAmountPerPlan) {
          const index = detailsByPlanType.findIndex(
            (d) => d.planType === hmoPlanTypeId,
          );
          const totalCapitationAmount =
            fp.enrolleeCapitionAmountByPlanType.find(
              (v) => v.planTypeId === hmoPlanTypeId,
            )?.amount || 0;
          if (index !== -1) {
            detailsByPlanType[index].enrolleeCount = updatedEnrolleeCount;
            detailsByPlanType[index].totalCapitationAmount =
              totalCapitationAmount;
          } else {
            const _detail = (
              await this.getCapitationDetails(mutator, {
                hospitalId,
                startDate,
                endDate,
              })
            )?.[0]?.detailsByPlanType;
            if (Array.isArray(_detail) && _detail?.length) {
              const _detailsByPlanType = _detail.map((_item) => {
                if (_item.planType === hmoPlanTypeId) {
                  return { ..._item, enrolleeCount: updatedEnrolleeCount };
                }
                return _item;
              });
              detailsByPlanType = _detailsByPlanType.map((_item) => _item);
            }
          }
        }
        transferFund = new TransferFundModel({
          hospitalId,
          createdDate: month.startOf('month').toDate(),
          createdBy: mutator,
          auditApproval: [],
          isEnrolleePayout: true,
          enrolleeCount: isPerPlanType
            ? 0
            : previousTransferFund?.enrolleeCount || enrolleeCount,
          totalCapitationAmount: isPerPlanType
            ? 0
            : capitationAmountPerEnrollee,
          amount: 0,
          serviceChargeAmount: 0,
          transferStatus: FundTransactionStatus.Pending,
          hmoPlanTypeId: isPerPlanType ? undefined : hmoPlanTypeId,
          hmoProviderId: mutator.hmoId,
          detailsByPlanType,
        });
      } else {
        const capitationAmountPerEnrollee = fp?.enrolleeCapitationAmount || 0;
        transferFund.totalCapitationAmount = capitationAmountPerEnrollee;
        const detailsByPlanType: CapitationDetailByPlanType[] =
          transferFund?.detailsByPlanType || [];
        if (fp?.enrolleeCapitationAmountPerPlan) {
          const index = detailsByPlanType.findIndex(
            (d) => d.planType === hmoPlanTypeId,
          );
          const totalCapitationAmount =
            fp.enrolleeCapitionAmountByPlanType.find(
              (v) => v.planTypeId === hmoPlanTypeId,
            )?.amount || 0;
          if (index !== -1) {
            detailsByPlanType[index].enrolleeCount = updatedEnrolleeCount;
            detailsByPlanType[index].totalCapitationAmount =
              totalCapitationAmount;
          } else {
            const hmoPlanType = await this.entityManager.findOne(
              HmoPlanTypeModel,
              { where: { id: hmoPlanTypeId } },
            );

            detailsByPlanType.push({
              planType: hmoPlanTypeId,
              enrolleeCount: updatedEnrolleeCount,
              totalCapitationAmount,
              planTypeName: hmoPlanType.name,
            });
          }
          transferFund.detailsByPlanType = detailsByPlanType;
        }
      }
      const savedTransferFunds = await this.entityManager.save(
        TransferFundModel,
        {
          ...transferFund,
          enrolleeCount: updatedEnrolleeCount,
          hmoProviderId: mutator.hmoId,
        },
      );

      transferFunds.push(savedTransferFunds);
    }
    return transferFunds;
  }

  async capitatedEnrolleeSummary(
    mutator: ProfileModel,
    args: {
      hmoProviderId?: string;
      registeredWith?: string;
      startDate?: string;
      endDate?: string;
      planId?: string;
    },
  ) {
    const { hmoProviderId, registeredWith, startDate, endDate, planId } = args;

    // Using raw SQL with parameters to avoid linting issues with quotes
    const query = this.entityManager
      .createQueryBuilder()
      .select('COUNT(*)', 'totalEnrolleeCount')
      .addSelect(
        "SUM(CASE WHEN LOWER(member_status) = 'active' THEN 1 ELSE 0 END)",
        'totalActiveEnrolleeCount',
      )
      .addSelect(
        "SUM(CASE WHEN LOWER(member_status) = 'inactive' THEN 1 ELSE 0 END)",
        'totalInactiveEnrolleeCount',
      )
      .addSelect(
        "SUM(CASE WHEN LOWER(member_status) = 'expired' THEN 1 ELSE 0 END)",
        'totalExpiredEnrolleeCount',
      )
      .addSelect(
        "SUM(CASE WHEN capitated_member = 'Yes' THEN 1 ELSE 0 END)",
        'totalCapitatedEnrolleeCount',
      )
      .from(HmoProfileModel, 'hmo_profile')
      .where('hmo_profile.member_status IS NOT NULL');

    if (registeredWith) {
      query.andWhere('hmo_profile.primary_provider_id = :hospitalId', {
        hospitalId: registeredWith,
      });
    }
    if (hmoProviderId) {
      query.andWhere('hmo_profile.provider_id = :providerId', {
        providerId: hmoProviderId,
      });
    }
    if (startDate && endDate) {
      const from = moment(startDate).startOf('day').toDate();
      const to = moment(endDate).endOf('day').toDate();
      query.andWhere(
        'hmo_profile.created_date BETWEEN :startDate AND :endDate',
        {
          startDate: from,
          endDate: to,
        },
      );
    }
    if (planId) {
      query.andWhere('hmo_profile.member_plan_id = :planId', {
        planId,
      });
    }

    const enrolleeCountsQuery = await query.getRawOne();

    const totalEnrolleeCount = parseInt(
      enrolleeCountsQuery?.totalEnrolleeCount || '0',
      10,
    );
    const totalActiveEnrolleeCount = parseInt(
      enrolleeCountsQuery?.totalActiveEnrolleeCount || '0',
      10,
    );
    const totalInactiveEnrolleeCount = parseInt(
      enrolleeCountsQuery?.totalInactiveEnrolleeCount || '0',
      10,
    );
    const totalExpiredEnrolleeCount = parseInt(
      enrolleeCountsQuery?.totalExpiredEnrolleeCount || '0',
      10,
    );
    const totalCapitatedEnrolleeCount = parseInt(
      enrolleeCountsQuery?.totalCapitatedEnrolleeCount || '0',
      10,
    );
    let totalCapitatedAmount = 0;
    if (registeredWith || hmoProviderId || mutator.hmoId) {
      // Sum the amount from all matching transfer funds
      const query = this.entityManager
        .createQueryBuilder()
        .select('SUM(transfer_fund.amount)', 'totalAmount')
        .from(TransferFundModel, 'transfer_fund')
        .where('transfer_fund.is_enrollee_payout = :isEnrolleePayout', {
          isEnrolleePayout: true,
        });
      if (!registeredWith && !hmoProviderId) {
        query.andWhere('transfer_fund.hmo_provider_id = :hmoProviderId', {
          hmoProviderId: mutator.hmoId,
        });
      }
      if (registeredWith) {
        query
          .andWhere('transfer_fund.hospital_id = :hospitalId', {
            hospitalId: registeredWith,
          })
          .andWhere('transfer_fund.hmo_provider_id = :hmoProviderId', {
            hmoProviderId: mutator.hmoId,
          });
      }

      if (hmoProviderId) {
        query.andWhere('transfer_fund.hmo_provider_id = :hmoProviderId', {
          hmoProviderId,
        });
      }

      const amountQuery = await query.getRawOne();

      totalCapitatedAmount = parseFloat(amountQuery?.totalAmount || '0');
    }

    return new CapitatedEnrolleeSummary({
      totalEnrolleeCount,
      totalActiveEnrolleeCount,
      totalInactiveEnrolleeCount,
      totalExpiredEnrolleeCount,
      totalCapitatedEnrolleeCount,
      totalCapitatedAmount,
    });
  }

  async getTotalProvidersCount(mutator: ProfileModel) {
    return this.entityManager.count(HmoHospitalModel, {
      where: {
        providerId: mutator.hmoId,
      },
    });
  }

  async capitatedProviderSummary(
    mutator: ProfileModel,
    args: {
      hospitalId?: string;
      startDate?: string;
      endDate?: string;
      planId?: string;
    },
  ) {
    const { hospitalId, startDate, endDate, planId } = args;

    const query = this.entityManager
      .createQueryBuilder()
      .select('COUNT(DISTINCT "hospital_id")', 'totalCapitatedProvidersCount')
      .addSelect('SUM(transfer_fund.enrollee_count)', 'totalEnrolleeCount')
      .addSelect('SUM(transfer_fund.amount)', 'totalAmount')
      .from(TransferFundModel, 'transfer_fund')
      .where('transfer_fund.hmo_provider_id = :hmoProviderId', {
        hmoProviderId: mutator.hmoId,
      })
      .andWhere(
        '(transfer_fund.is_enrollee_payout = :isEnrolleePayout AND transfer_fund.transfer_status != :pending)',
        {
          isEnrolleePayout: true,
          pending: 'Pending',
        },
      );
    if (startDate && endDate) {
      const from = moment(startDate).startOf('day').toDate();
      const to = moment(endDate).endOf('day').toDate();
      query.andWhere(
        'transfer_fund.created_date BETWEEN :startDate AND :endDate',
        {
          startDate: from,
          endDate: to,
        },
      );
    }
    if (hospitalId) {
      query.andWhere('transfer_fund.hospital_id = :hospitalId', {
        hospitalId,
      });
    }
    if (planId) {
      query.andWhere('transfer_fund.hmo_plan_type_id = :planId', {
        planId,
      });
    }
    const results = await query.getRawOne();
    const totalCapitatedProvidersCount = parseInt(
      results?.totalCapitatedProvidersCount || '0',
      10,
    );

    const totalCapitatedEnrolleeCount = parseInt(
      results?.totalEnrolleeCount || '0',
      10,
    );
    const totalCapitatedAmount = parseFloat(results?.totalAmount || '0');

    return new CapitatedProviderSummary({
      totalCapitatedProvidersCount,
      totalCapitatedEnrolleeCount,
      totalCapitatedAmount,
    });
  }
  async createProvider(
    mutator: ProfileModel,
    input: ProviderRegistrationInput,
  ): Promise<HospitalModel> {
    if (!mutator.hmoId) {
      throw new BadRequestException('Not Authorized To Create This Record');
    }
    return customDSSerializeInTransaction(this.dataSource, (manager) =>
      this.createProviderInTransaction(mutator, input, manager),
    );
  }
  private async createProviderInTransaction(
    mutator: ProfileModel,
    input: ProviderRegistrationInput,
    manager: EntityManager,
  ): Promise<HospitalModel> {
    const walletRepo = manager.withRepository(this.walletRepository);
    const transactionUserRepo = manager.withRepository(this.userRepository);

    const transactionProfileRepo = manager.withRepository(
      this.profileRepository,
    );

    const transactionPermissionRepo = manager.withRepository(
      this.permissionRepository,
    );

    const transactionHospitalRepo = manager.withRepository(this.repository);
    const agency = mutator.hospital;

    const clinifyId = await this.profileService.generateClinifyId();

    const hospital = new HospitalModel();

    hospital.lga = input.lga;
    hospital.city = input.city || '';
    hospital.address = input.hospitalAddress || '';
    hospital.politicalWard = input.politicalWard;
    hospital.country = input.country;
    hospital.name = input.hospitalName;
    hospital.ownership = input.ownership;
    hospital.state = input.state;
    hospital.level = input.level;
    hospital.clinifyId = clinifyId;
    hospital.licenseNumber = input.hospitalLicenseNumber;
    hospital.documentUrl = input.documentUrl;
    hospital.plan = input.plan;
    hospital.planStatus = input.planStatus;
    hospital.wemaAccountNumber = input.wemaAccountNumber;
    hospital.supportMail = input.supportMail;
    hospital.classification = input.classification;
    hospital.enrolleeCount = input.enrolleeCount;
    hospital.enrolleeLimit = input.enrolleeLimit;

    hospital.preference = new FacilityPreferenceModel({
      showServiceDetails: false,
      rolesServiceDetailsIsHidden: [
        UserType.OrganizationAdmin,
        UserType.OrganizationBillingOfficer,
        UserType.OrganizationRecordOfficer,
        UserType.OrganizationStaffAdmin,
        UserType.OrganizationFrontDeskOfficer,
        UserType.OrganizationLabTechnician,
        UserType.OrganizationRadiographer,
        UserType.OrganizationRadiologist,
        UserType.Pharmacist,
        UserType.OrganizationCashier,
        UserType.OrganizationStoreClerk,
        UserType.OrganizationNurse,
        UserType.OrganizationDoctor,
      ],
    });
    const { countryCode, countryName, value } =
      input.providerPrimaryPhoneNumber;
    hospital.phoneNumber = {
      value,
      countryName,
      countryCode: countryCode.replace(/^\+/, ''),
    };
    hospital.website = input.hospitalWebsite;
    if (input.providerSecondaryPhoneNumber) {
      const { countryCode, countryName, value } =
        input.providerSecondaryPhoneNumber;
      hospital.secondaryPhoneNumber = {
        value,
        countryName,
        countryCode: countryCode.replace(/^\+/, ''),
      };
    }

    const {
      hospitalContactEmail,
      hospitalContactPhoneNumber,
      hospitalContactFirstName,
      hospitalContactLastName,
      hospitalContactMiddleName,
      hospitalContactTitle,
    } = input;

    const userByCorporateEmail = await transactionUserRepo.byEmail(
      input.hospitalContactEmail,
    );

    if (userByCorporateEmail)
      throw new ConflictException('Email Already Exists');

    const user = new UserModel();
    user.email = hospitalContactEmail;
    user.forcePasswordChange = true;
    user.corporatePhoneNumber = `${hospitalContactPhoneNumber?.countryCode}${
      hospitalContactPhoneNumber?.value || ''
    }`.replace(/^\+/, '');
    user.country = input.country;
    const online = config.runMode === RunModeTypes.Online;
    const password = online
      ? generatePassword()
      : config.offline.defaultPassword;

    const userpassword = await bcrypt.hash(password, SALT);

    user.password = userpassword;

    const savedUser = await transactionUserRepo.save(user);
    const profile = new ProfileModel();
    profile.isDefault = true;
    profile.active = true;
    profile.fullName = generateFullName({
      firstName: hospitalContactFirstName,
      lastName: hospitalContactLastName,
      middleName: hospitalContactMiddleName,
    });
    profile.title = hospitalContactTitle;
    profile.type = UserType.OrganizationAdmin;
    profile.clinifyId = clinifyId;
    profile.user = savedUser;
    profile.transactionCharge = 0;

    const savedProfile = await transactionProfileRepo.save(profile);
    hospital.createdBy = savedProfile;
    const savedHospital = await transactionHospitalRepo.save(hospital);

    await walletRepo.createWallet({ hospital });
    // save organization phone number in the detail of the user
    const profileDetails = new ProfileDetailsModel();
    profileDetails.organizationPhoneNumber = input.hospitalContactPhoneNumber;
    profileDetails.profile = savedProfile;
    profileDetails.firstName = hospitalContactFirstName;
    profileDetails.middleName = hospitalContactMiddleName;
    profileDetails.lastName = hospitalContactLastName;

    await manager.save(ProfileDetailsModel, profileDetails);

    const perm = new PermissionModel();
    perm.rules = getDefaultPermission({
      userId: savedUser.id,
      clinifyId: savedProfile.clinifyId,
      type: savedProfile.type,
      organizationId: hospital?.id,
    });
    perm.profile = savedProfile;

    await transactionPermissionRepo.save(perm);
    savedProfile.hospital = savedHospital;

    let hmoProviderUniqueId = input.providerId;
    if (!input.providerId) {
      const lastHmoProviderUniqueId = await manager.query(
        `SELECT hmo_provider_unique_id FROM hmo_hospitals
          WHERE provider_id = $1 
            AND hmo_provider_unique_id IS NOT NULL
            AND hmo_provider_unique_id ~ '^[0-9]+$'
          ORDER BY CAST(hmo_provider_unique_id AS INTEGER) DESC
          LIMIT 1
        `,
        [mutator.hmoId],
      );
      hmoProviderUniqueId = '1';
      if (lastHmoProviderUniqueId.length > 0) {
        hmoProviderUniqueId = `${
          parseInt(lastHmoProviderUniqueId[0].hmo_provider_unique_id, 10) + 1
        }`;
      }
    }

    const providerIdExist = await manager.query(
      `SELECT hmo_provider_unique_id FROM hmo_hospitals
        WHERE provider_id = $1 AND hmo_provider_unique_id = $2
      `,
      [mutator.hmoId, hmoProviderUniqueId],
    );

    if (providerIdExist.length) {
      throw new BadRequestException('Provider ID Already Exists');
    }

    const providerCodeExist = await manager.query(
      `
        SELECT hmo_provider_id FROM hmo_hospitals
        WHERE provider_id = $1 AND hmo_provider_id = $2
      `,
      [mutator.hmoId, input.providerCode],
    );

    if (providerCodeExist.length) {
      throw new BadRequestException('Provider Code Already Exists');
    }

    const hmoHospital = await manager.save(HmoHospitalModel, {
      hmoProviderUniqueId,
      providerId: mutator.hmoId,
      hospitalId: savedHospital.id,
      hmoProviderId: input.providerCode,
      tariffBand: input.tariffBand,
      category: input.providerCategory,
      enrolleeLimit: input.enrolleeLimit ? Number(input.enrolleeLimit) : null,
      planVisibility: input.planVisibility?.filter((v) => v?.id),
    });
    savedHospital.hmoHospitals = [hmoHospital];
    await transactionProfileRepo.save(savedProfile);

    const accountCreationMsg: IMessage = {
      to: `${user.email}`,
      subject: 'Creation of Account',
      html: mailerTemplate({
        firstName: hospitalContactFirstName,
        message: `An account was successfully created for you by <span class="blue-text">${hospital.name}</span>`,
        email: user.email,
        tempPass: password,
        isUser: false,
      }),
    };
    const msg: IMessage = {
      to: `${savedUser.email}`,
      subject: agency?.name
        ? `Welcome to ${agency.name}, Powered by Clinify!`
        : 'Welcome to Clinify!',
      html: welcomeTemplate({ firstName: profileDetails.firstName }),
    };

    await this.mailerService.sendMail(msg);
    await this.mailerService.sendMail(accountCreationMsg);

    return savedHospital;
  }

  async updateProvider(
    mutator: ProfileModel,
    hospitalId: string,
    input: UpdateProviderInput,
  ): Promise<HospitalModel> {
    if (!mutator.hmoId) {
      throw new BadRequestException('Not Authorized To Modify This Record');
    }

    return customDSSerializeInTransaction(this.dataSource, async (manager) => {
      delete input.enrolleeCount;
      const transactionHospitalRepo = manager.withRepository(this.repository);

      if (input?.hospitalContactPhoneNumber?.countryCode) {
        input.hospitalContactPhoneNumber.countryCode =
          input?.hospitalContactPhoneNumber?.countryCode.replace(/^\+/, '');
      }
      const updatedHospital = await transactionHospitalRepo.updateProvider(
        mutator,
        hospitalId,
        input,
      );

      const {
        hospitalContactTitle,
        hospitalContactFirstName,
        hospitalContactMiddleName,
        hospitalContactLastName,
        hospitalContactPhoneNumber,
        hospitalContactEmail,
      } = input;

      const adminFullName = generateFullName({
        firstName: hospitalContactFirstName,
        middleName: hospitalContactMiddleName,
        lastName: hospitalContactLastName,
      });

      await manager.save(ProfileModel, {
        ...updatedHospital.createdBy,
        title: hospitalContactTitle,
        fullName: adminFullName,
      });

      const corporatePhoneNumber = `${hospitalContactPhoneNumber?.countryCode}${
        hospitalContactPhoneNumber?.value || ''
      }`.replace(/^\+/, '');

      await manager.save(UserModel, {
        ...updatedHospital.createdBy.user,
        email: hospitalContactEmail,
        corporatePhoneNumber,
      });

      await manager
        .createQueryBuilder()
        .update(ProfileDetailsModel)
        .set({
          firstName: hospitalContactFirstName,
          middleName: hospitalContactMiddleName,
          lastName: hospitalContactLastName,
        })
        .where('id = :id', { id: updatedHospital.createdBy.detailsId })
        .execute();

      if (input.providerId || input.providerCode) {
        const tempHmoHospital = cloneDeep(updatedHospital?.hmoHospitals || []);

        const agencyProviderIndex = tempHmoHospital.findIndex(
          (hmoHos) => !!mutator.hmoId && hmoHos?.providerId === mutator.hmoId,
        );
        const agencyProviderDetails = tempHmoHospital[agencyProviderIndex];

        if (
          input.providerCode &&
          (!agencyProviderDetails?.hmoProviderId ||
            agencyProviderDetails.hmoProviderId !== input.providerCode)
        ) {
          const providerCodeExist = await manager.query(
            `
        SELECT hmo_provider_id FROM hmo_hospitals
        WHERE provider_id = $1 AND hmo_provider_id = $2
      `,
            [mutator.hmoId, input.providerCode],
          );

          if (providerCodeExist.length) {
            throw new BadRequestException('Provider Code Already Assigned');
          }
        }

        if (
          input.providerId &&
          (!agencyProviderDetails?.hmoProviderUniqueId ||
            agencyProviderDetails.hmoProviderUniqueId !== input.providerId)
        ) {
          const providerIdExist = await manager.query(
            `
        SELECT hmo_provider_unique_id FROM hmo_hospitals
        WHERE provider_id = $1 AND hmo_provider_unique_id = $2
      `,
            [mutator.hmoId, input.providerId],
          );

          if (providerIdExist.length) {
            throw new BadRequestException('Provider ID Already Assigned');
          }
        }

        if (agencyProviderIndex === -1) {
          const hmoHospital = await manager.save(HmoHospitalModel, {
            hmoProviderUniqueId: input.providerId,
            providerId: mutator.hmoId,
            hospitalId: updatedHospital.id,
            hmoProviderId: input.providerCode,
            tariffBand: input.tariffBand,
            category: input.providerCategory,
            enrolleeLimit: input.enrolleeLimit
              ? Number(input.enrolleeLimit)
              : null,
            planVisibility: input.planVisibility?.filter((v) => v?.id),
          });

          updatedHospital.hmoHospitals = [...tempHmoHospital, hmoHospital];
        } else {
          const hmoHospital = await manager.save(HmoHospitalModel, {
            ...agencyProviderDetails,
            hmoProviderUniqueId: input.providerId,
            hmoProviderId: input.providerCode,
            tariffBand: input.tariffBand,
            category: input.providerCategory,
            enrolleeLimit: input.enrolleeLimit
              ? Number(input.enrolleeLimit)
              : null,
            planVisibility: input.planVisibility?.filter((v) => v?.id),
          });
          updatedHospital.hmoHospitals[agencyProviderIndex] = {
            ...hmoHospital,
          };
          updatedHospital.hmoHospitals = updatedHospital.hmoHospitals;
        }
      }

      await this.virtualAccountService.updateHospitalVirtualAccountName(
        updatedHospital.id,
        updatedHospital.name,
      );

      return updatedHospital;
    });
  }

  async updateFacilityStaffInformation(
    mutator: ProfileModel,
    profileId: string,
    facilityId: string,
    input: EditHospitalStaffInput,
  ): Promise<ProfileModel> {
    const staffProfile = await this.entityManager.findOne(ProfileModel, {
      where: { id: profileId, hospital: { id: facilityId } },
      relations: {
        details: true,
        user: true,
      },
    });

    if (!staffProfile) {
      throw new NotFoundException('Staff Profile Not Found');
    }

    const validateMutationByFacilityAdmins =
      [UserType.OrganizationAdmin, UserType.OrganizationStaffAdmin].includes(
        mutator.type as UserType,
      ) && mutator.hospitalId === staffProfile.hospitalId;
    const validateMutationByHmoAgencies = !mutator.hmoId
      ? false
      : await this.entityManager.findOne(HmoHospitalModel, {
          where: {
            providerId: mutator.hmoId,
            hospitalId: staffProfile.hospitalId,
          },
          select: { id: true },
        });

    if (!validateMutationByFacilityAdmins && !validateMutationByHmoAgencies) {
      throw new BadRequestException('Not Authorized To Modify This Record');
    }

    staffProfile.title = input.title;
    staffProfile.details.firstName = input.firstName;
    staffProfile.details.middleName = input.middleName;
    staffProfile.details.lastName = input.lastName;
    staffProfile.gender = input.gender;
    staffProfile.details.speciality = input.speciality;
    staffProfile.details.rank = input.rank;
    staffProfile.details.userRole = input.userRole;
    staffProfile.details.department = input.department;
    staffProfile.fullName = generateFullName({
      firstName: input.firstName,
      lastName: input.lastName,
      middleName: input.middleName,
    });
    staffProfile.user.corporatePhoneNumber = `${
      input.phoneNumber?.value || ''
    }`.replace(/^\+/, '');
    staffProfile.user.country = input.phoneNumber?.countryName;

    await this.entityManager.save(ProfileModel, staffProfile);

    return staffProfile;
  }
}
