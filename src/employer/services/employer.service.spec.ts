/* eslint-disable @typescript-eslint/quotes */
/* eslint-disable max-lines */
import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Chance } from 'chance';
import { DataSource, EntityManager } from 'typeorm';
import { EmployerService } from './employer.service';
import {
  CreateEmployerInput,
  CreateEmployeeInput,
  UpdateEmployerInput,
  UpdateEmployeeInput,
  CreateEmployerDependantInput,
  UpdateEmployerDependantInput,
} from '../inputs/employer.input';
import { EmployerRepository } from '../repositories/employer.repository';
import {
  employerFactory,
  employeeFactory,
} from '@clinify/__mocks__/factories/employer.factory';
import { facilityPreferenceFactory } from '@clinify/__mocks__/factories/facility-preference.factory';
import { HmoProfileFactory } from '@clinify/__mocks__/factories/hmo-profile.factory';
import {
  HmoPlanTypeFactory,
  HmoProviderFactory,
} from '@clinify/__mocks__/factories/hmo-provider.factory';
import { hospitalFactory } from '@clinify/__mocks__/factories/hospital.factory';
import { profileFactory } from '@clinify/__mocks__/factories/profile.factory';
import { userFactory } from '@clinify/__mocks__/factories/user.factory';
import { walletFactory } from '@clinify/__mocks__/factories/wallet.factory';
import { loggerMock } from '@clinify/__mocks__/logger';
import { PubSubMock } from '@clinify/__mocks__/pub-sub.mock';
import { AuthenticationService } from '@clinify/authentication/services/authentication.service';
import { RingCaptchaService } from '@clinify/authentication/services/ringcaptcha.service';
import { PermissionModel } from '@clinify/authorization/models/permission.model';
import { PermissionService } from '@clinify/authorization/services/permission.service';
import { BankDetailModel } from '@clinify/bank-details/models/bank-detail.model';
import { BankService } from '@clinify/banks/services/bank.service';
import { BillDetailsModel } from '@clinify/bills/models/bill-details.model';
import { BillModel } from '@clinify/bills/models/bill.model';
import { EmployeeDependantModel } from '@clinify/employer/models/employee-dependant.model';
import { EmployeeModel } from '@clinify/employer/models/employee.model';
import { FacilityPreferenceModel } from '@clinify/facility-preferences/models/facility-preference.model';
import { HmoPlanStatus } from '@clinify/hmo-providers/inputs/hmo-plan.input';
import { HmoPlanTypeModel } from '@clinify/hmo-providers/models/hmo-plan-type.model';
import { HmoProviderModel } from '@clinify/hmo-providers/models/hmo-provider.model';
import { HmoProviderService } from '@clinify/hmo-providers/services/hmo-provider.service';
import { HospitalModel } from '@clinify/hospitals/models/hospital.model';
import { FileNumberService } from '@clinify/integrations/file-number/services/file-number.service';
import { MAILER } from '@clinify/shared/mailer/constants';
import { MailerService } from '@clinify/shared/mailer/mailer.service';
import { PaystackService } from '@clinify/shared/services/paystack.service';
import { TransactionProfileService } from '@clinify/shared/services/transacting-profile.service';
import { ValidatePasscodeService } from '@clinify/shared/services/validate-passcode.service';
import { Gender } from '@clinify/shared/validators/personal-information.input';
import { ProfileModel } from '@clinify/users/models/profile.model';
import { UserModel } from '@clinify/users/models/user.model';
import { ProfileService } from '@clinify/users/services/profile.service';
import { UserService } from '@clinify/users/services/user.service';
import { PUB_SUB } from '@clinify/utils/subscriptions/pubSub';
import { WalletTransactionRepository } from '@clinify/wallet-transactions/repositories/wallet-transaction.repository';
import { WalletTransactionService } from '@clinify/wallet-transactions/services/wallet-transaction.service';
import { WalletRepository } from '@clinify/wallets/repositories/wallet.repository';
import { WalletService } from '@clinify/wallets/services/wallet.service';
import { WithdrawalRequestModel } from '@clinify/withdrawal-requests/models/withdrawal-request.model';
import { WithdrawalRequestService } from '@clinify/withdrawal-requests/services/withdrawal-request.service';

const chance = new Chance();
const mockUsers = userFactory.buildList(2);
const mockUser = mockUsers[0] as UserModel;
const mockHospital = hospitalFactory.build({
  id: chance.guid({ version: 4 }),
  name: 'Test Hospital',
});
const wallet = walletFactory.build();
const phoneNumberInput = {
  countryCode: '234',
  countryName: 'Nigeria',
  value: '0123456789',
};
const commonRepoMethods = {
  findOne: jest.fn().mockResolvedValue(mockUser),
  save: jest.fn().mockResolvedValue(mockUser),
  createWallet: jest.fn().mockResolvedValue(wallet),
  byClinifyId: jest.fn().mockResolvedValue(false),
  byId: jest.fn().mockResolvedValue(mockUser),
  byEmail: jest.fn().mockResolvedValue(mockUser),
  delete: jest.fn().mockResolvedValue(mockUser),
  findOneOrFail: jest.fn().mockResolvedValue(mockUser),
  saveEmployer: jest.fn(() => employer),
  updateEmployer: jest.fn(() => employer),
  addEmployee: jest.fn(() => employee),
  updateEmployee: jest.fn(() => employee),
  getFacilityPreference: jest
    .fn()
    .mockResolvedValue(facilityPreferenceFactory.build()),
};

const ManagerMock = {
  getCustomRepository: jest.fn().mockReturnValue(commonRepoMethods),
  withRepository: jest.fn((repo) => repo),
  queryRunner: { isTransactionActive: true },
  findOne: jest.fn((model, query) => {
    if (model === HmoPlanTypeModel) {
      return {
        id: query?.where?.id,
        hmoProviderId: query?.where?.hmoProviderId,
        name: 'Basic Plan',
        status: 'ACTIVE',
      };
    }
    if (model === EmployeeModel) {
      return employee;
    }
    if (model === ProfileModel) {
      return mockProfile;
    }
    return null;
  }),
  find: jest.fn().mockResolvedValue(mockUser.profiles),
  save: jest.fn().mockResolvedValue(mockUser),
  delete: jest.fn().mockResolvedValue(mockUser),
  findOneOrFail: jest.fn().mockResolvedValue(mockUser),
  update: jest.fn().mockResolvedValue(mockUser),
  query: jest
    .fn()
    .mockResolvedValue([{ ids: ['hospital-id-1', 'hospital-id-2'] }]),
};

const MockHmoProviderService = {
  verifyEnrollee: jest.fn(),
  getEnrolleeBenefits: jest.fn(() => [
    {
      success: true,
      errorMsg: '',
      provider: HmoProviderFactory.build(),
      benefits: [{ balance: 0 }],
    },
  ]),
  checkinEnrollees: jest.fn(() => ({})),
};

const MockFacilityPreferenceRepo = {
  getFacilityPreference: jest.fn().mockResolvedValue({
    id: chance.guid({ version: 4 }),
    welcomeMailTemplate: {
      body: 'Welcome to our platform!',
    },
  }),
  findOne: jest.fn().mockResolvedValue(facilityPreferenceFactory.build()),
};

const mockUserRepo = {
  byId: jest.fn(() => mockUser),
  byEmail: jest.fn(() => mockUser),
  byPhoneNumber: jest.fn(() => mockUser),
  delete: jest.fn(() => mockUser),
  save: jest.fn((user) => {
    // Return the user with an id if it doesn't have one
    if (!user.id) {
      return {
        ...user,
        id: chance.guid({ version: 4 }),
      };
    }
    return user;
  }),
  byProfileId: jest.fn(() => mockUser),
};

const mockPermissionRepo = {
  save: jest.fn(() => mockUser.defaultProfile),
  findOne: jest.fn(() => mockUser.defaultProfile.permission),
};

const mockProfileRepo = {
  save: jest.fn((profile) => {
    // Return the profile with an id if it doesn't have one
    if (!profile.id) {
      return {
        ...profile,
        id: chance.guid({ version: 4 }),
      };
    }
    return profile;
  }),
  byClinifyId: jest.fn(),
  addCoverageInformation: jest.fn().mockResolvedValue({
    id: chance.guid({ version: 4 }),
    coverageType: 'HMO',
    providerId: chance.guid({ version: 4 }),
  }),
  updateCoverageInformation: jest.fn().mockResolvedValue({}),
};

const mockWalletRepo = {
  createWallet: jest.fn(() => wallet),
};

const mockMailer = {
  sendMail: jest.fn(),
};

const mockMailerService = {
  sendMail: jest.fn(),
};

const dsMock = {
  getRepository: jest.fn(() => ({
    extend: jest.fn(),
  })),
  transaction: jest.fn((cb) => {
    // Mock the transaction behavior
    const result = cb(ManagerMock);
    return Promise.resolve(result);
  }),
  manager: ManagerMock,
  createQueryRunner: jest.fn(() => ({
    release: jest.fn(),
    query: jest.fn(),
  })),
  query: jest.fn(),
};

const mockRingCaptchaService = {
  sendOtp: jest.fn(),
  verifyOtp: jest.fn(),
};

const employer = employerFactory.build();
const employee = employeeFactory.build();

const mockEmployerRepository = {
  saveEmployer: jest.fn((mutator, input) => {
    // Create a new employer with the input data
    return {
      ...employer,
      employerName: input.employerName,
      displayUrl: input.displayUrl,
      hmoProviderId: input.hmoProviderId,
    };
  }),
  updateEmployer: jest.fn(() => employer),
  deleteEmployers: jest.fn(() => [employer]),
  findEmployerById: jest.fn(() => employer),
  findEmployers: jest.fn(() => ({ list: [employer], totalCount: 1 })),
  addEmployee: jest.fn(() => employee),
  updateEmployee: jest.fn(() => employee),
  archiveEmployers: jest.fn(() => [employer]),
  findHmoPlanTypesByEmployerId: jest.fn(() => [HmoPlanTypeFactory.build()]),
  findEmployeesByEmployerId: jest.fn(() => ({
    list: [employee],
    totalCount: 1,
  })),
  findOne: jest.fn(() => employer),
  deleteDependants: jest.fn(() => [employee]),
  findEmployerByIds: jest.fn(() => [employee]),
};

const mockPaystackService = {
  transfer: jest.fn(() => ({
    gateway_response: 'Approved',
    status: 'success',
  })),
};

const mockProfileService = {
  addHmoProfilePlan: jest.fn().mockResolvedValue({
    id: chance.guid({ version: 4 }),
    coverageType: 'HMO',
    providerId: chance.guid({ version: 4 }),
    memberNumber: 'ENRL12345',
    memberPlanId: chance.guid({ version: 4 }),
    memberPlan: 'Basic Plan',
    membershipNumber: 'MEM12345',
    memberStatus: 'ACTIVE',
  }),
  generateClinifyId: jest.fn().mockResolvedValue('CLN12345678'),
};

const MockBankService = {};

const mockAuthenticationService = {
  verifyOtp: jest.fn(() => true),
};
const mockProfile = {
  id: chance.guid({ version: 4 }),
  fullName: 'Test User',
  hmoId: chance.guid({ version: 4 }),
  hospital: mockHospital,
  user: mockUser,
  details: {
    firstName: 'Test',
    lastName: 'User',
    middleName: '',
    address: 'Test Address',
  },
} as ProfileModel;
describe('EmployerService', () => {
  let service: EmployerService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        EmployerService,
        UserService,
        {
          provide: ProfileService,
          useValue: mockProfileService,
        },
        PermissionService,
        WalletService,
        WithdrawalRequestService,
        WalletTransactionService,
        TransactionProfileService,
        ValidatePasscodeService,
        {
          provide: PaystackService,
          useValue: mockPaystackService,
        },
        {
          provide: AuthenticationService,
          useValue: mockAuthenticationService,
        },
        {
          provide: EmployerRepository,
          useValue: mockEmployerRepository,
        },
        {
          provide: getRepositoryToken(HospitalModel),
          useValue: {},
        },
        {
          provide: WalletTransactionRepository,
          useValue: {},
        },
        {
          provide: getRepositoryToken(UserModel),
          useValue: mockUserRepo,
        },
        {
          provide: getRepositoryToken(ProfileModel),
          useValue: mockProfileRepo,
        },
        {
          provide: WalletRepository,
          useValue: mockWalletRepo,
        },
        {
          provide: getRepositoryToken(PermissionModel),
          useValue: mockPermissionRepo,
        },
        {
          provide: getRepositoryToken(HmoProviderModel),
          useValue: {},
        },
        {
          provide: getRepositoryToken(FacilityPreferenceModel),
          useValue: MockFacilityPreferenceRepo,
        },
        {
          provide: MAILER,
          useValue: mockMailer,
        },
        {
          provide: EntityManager,
          useValue: ManagerMock,
        },
        {
          provide: DataSource,
          useValue: dsMock,
        },
        {
          provide: HmoProviderService,
          useValue: MockHmoProviderService,
        },
        {
          provide: FileNumberService,
          useValue: {},
        },
        { ...loggerMock },
        { provide: PUB_SUB, useValue: PubSubMock },
        {
          provide: getRepositoryToken(BillModel),
          useValue: {},
        },
        {
          provide: getRepositoryToken(BillDetailsModel),
          useValue: {},
        },
        {
          provide: getRepositoryToken(WithdrawalRequestModel),
          useValue: {},
        },
        {
          provide: WalletTransactionRepository,
          useValue: {},
        },
        {
          provide: getRepositoryToken(BankDetailModel),
          useValue: {},
        },
        {
          provide: RingCaptchaService,
          useValue: mockRingCaptchaService,
        },
        {
          provide: MailerService,
          useValue: mockMailerService,
        },
        {
          provide: BankService,
          useValue: MockBankService,
        },
      ],
    }).compile();

    service = module.get<EmployerService>(EmployerService);
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('createEmployer', () => {
    it('should create a new employer with employees', async () => {
      const hmoProviderId = chance.guid({ version: 4 });
      const hmoPlanTypeId = chance.guid({ version: 4 });

      const input: CreateEmployerInput = {
        displayUrl: 'https://example.com/logo.png',
        employerName: 'Test Corp',
        hmoProviderId,
        employees: [
          {
            firstName: 'John',
            lastName: 'Doe',
            emailAddress: chance.email(),
            phoneNumber: {
              countryCode: '234',
              countryName: 'Nigeria',
              value: chance.phone({ formatted: false }),
            },
            gender: Gender.Male,
            dateOfBirth: new Date('1990-01-01'),
            planCategory: 'Individual',
            dependants: [],
            enrolleeId: 'ENRL12345/01',
            hmoPlanTypeId,
            hmoPlanStatus: HmoPlanStatus.Active,
          },
          {
            firstName: 'Jane',
            lastName: 'Smith',
            emailAddress: chance.email(),
            phoneNumber: {
              countryCode: '234',
              countryName: 'Nigeria',
              value: chance.phone({ formatted: false }),
            },
            gender: Gender.Female,
            dateOfBirth: new Date('1992-05-15'),
            planCategory: 'Dependents',
            enrolleeId: 'ENRL67890/01',
            hmoPlanTypeId,
            hmoPlanStatus: HmoPlanStatus.Active,
            dependants: [
              {
                firstName: 'Junior',
                lastName: 'Smith',
                relationship: 'Child',
                dateOfBirth: new Date('2018-03-10'),
                enrolleeId: 'ENRL67890/02',
              },
            ],
          },
        ],
      };

      // **Mock employer creation**
      const mockEmployer = employerFactory.build({
        id: chance.guid({ version: 4 }),
        hmoProviderId,
        employees: [],
      });
      mockEmployerRepository.saveEmployer.mockResolvedValue(mockEmployer);

      // **Mock repositories**
      const mockUserRepo = {
        save: jest.fn().mockResolvedValue(userFactory.build()),
        byPhoneNumber: jest.fn().mockResolvedValue(null),
      };
      const mockProfileRepo = {
        save: jest.fn().mockResolvedValue(profileFactory.build()),
        addCoverageInformation: jest.fn().mockResolvedValue({
          id: chance.guid({ version: 4 }),
          hmoProfile: { id: chance.guid({ version: 4 }) },
        }),
      };
      const mockPermissionRepo = {
        save: jest.fn().mockResolvedValue({ id: chance.guid({ version: 4 }) }),
      };
      const mockFacilityPreferenceRepo = {
        getFacilityPreference: jest.fn().mockResolvedValue({
          id: chance.guid({ version: 4 }),
          welcomeMailTemplate: {
            body: 'Welcome to our platform!',
          },
        }),
      };

      // **Mock withRepository for transaction**
      ManagerMock.withRepository.mockImplementation((repo) => {
        // @ts-ignore
        if (repo === service.employerRepository) return mockEmployerRepository;
        // @ts-ignore
        if (repo === service.profileRepository) return mockProfileRepo;
        // @ts-ignore
        if (repo === service.userRepository) return mockUserRepo;
        // @ts-ignore
        if (repo === service.permissionRepository) return mockPermissionRepo;
        // @ts-ignore
        if (repo === service.facilityPreferenceRepository)
          return mockFacilityPreferenceRepo;
        return repo;
      });

      // **Mock transaction**
      dsMock.transaction.mockImplementation((callback) =>
        callback(ManagerMock),
      );

      // **Mock profile service methods**
      mockProfileService.generateClinifyId.mockResolvedValue('CLN123');
      mockProfileService.addHmoProfilePlan.mockResolvedValue({
        id: chance.guid({ version: 4 }),
      });

      // **Mock data source queries for provider network**
      dsMock.query
        .mockResolvedValueOnce([{ ids: ['hospital-1'] }]) // Employee 1 provider network
        .mockResolvedValueOnce([{ id: 'agency-1' }]) // Employee 1 agency facility
        .mockResolvedValueOnce([{ ids: ['hospital-1'] }]) // Employee 2 provider network
        .mockResolvedValueOnce([{ id: 'agency-1' }]) // Employee 2 agency facility
        .mockResolvedValueOnce([{ ids: ['hospital-1'] }]) // Dependant provider network
        .mockResolvedValueOnce([{ id: 'agency-1' }]); // Dependant agency facility

      // **Mock mailer**
      mockMailerService.sendMail.mockResolvedValue(undefined);

      // **Mock employee and dependant saves**
      const employee1 = employeeFactory.build({
        id: chance.guid({ version: 4 }),
        employerId: mockEmployer.id,
        profileId: 'profile-1',
        hmoProfileId: 'hmo-1',
      });
      const employee2 = employeeFactory.build({
        id: chance.guid({ version: 4 }),
        employerId: mockEmployer.id,
        profileId: 'profile-2',
        hmoProfileId: 'hmo-2',
      });
      const dependant = {
        id: chance.guid({ version: 4 }),
        employeeId: employee2.id,
        employerId: mockEmployer.id,
        profileId: 'profile-3',
        hmoProfileId: 'hmo-3',
      };
      ManagerMock.save
        .mockResolvedValueOnce({})
        .mockResolvedValueOnce(employee1)
        .mockResolvedValueOnce({})
        .mockResolvedValueOnce(employee2)
        .mockResolvedValueOnce({})
        .mockResolvedValueOnce(dependant);

      const result = await service.createEmployer(mockProfile, input);

      expect(mockEmployerRepository.saveEmployer).toHaveBeenCalledWith(
        mockProfile,
        { ...input, employerNumber: '1' },
      );

      expect(result.employees).toHaveLength(2);

      expect(mockUserRepo.save).toHaveBeenCalledTimes(3);
      expect(mockProfileRepo.save).toHaveBeenCalledTimes(3);
      expect(mockProfileRepo.addCoverageInformation).toHaveBeenCalledTimes(3);
      expect(mockPermissionRepo.save).toHaveBeenCalledTimes(3);
      expect(ManagerMock.save).toHaveBeenCalledTimes(7);
    });

    it('should handle error when creating employer fails', async () => {
      const input: CreateEmployerInput = {
        displayUrl: 'https://example.com/logo.png',
        employerName: 'Test Corp',
      };

      // **Mock saveEmployer to throw an error**
      mockEmployerRepository.saveEmployer.mockImplementation(() => {
        throw new Error('Error Creating Employer');
      });

      // **Verify error handling**
      await expect(service.createEmployer(mockProfile, input)).rejects.toThrow(
        'Error Creating Employer',
      );
    });

    it('should generate and assign sequential employer number when creating employer', async () => {
      const input: CreateEmployerInput = {
        displayUrl: 'https://example.com/logo.png',
        employerName: 'Test Corp',
      };

      // Mock the database query that returns the next employer number
      ManagerMock.query.mockResolvedValue([{ next_number: 5 }]);

      // Mock the saveEmployer to capture the input with employerNumber
      mockEmployerRepository.saveEmployer.mockImplementation(
        (mutator, inputWithNumber) => {
          // Verify that employerNumber was added to the input
          expect(inputWithNumber.employerNumber).toBe('5');
          return {
            ...employer,
            employerNumber: inputWithNumber.employerNumber,
          };
        },
      );

      const result = await service.createEmployer(mockProfile, input);

      // Verify the query was called with correct parameters
      expect(ManagerMock.query).toHaveBeenCalledWith(
        "SELECT COALESCE(MAX(employer_number), '0')::int + 1 AS next_number FROM employers WHERE hmo_provider_id = $1",
        [mockProfile.hmoId],
      );

      // Verify the employer was saved with the correct employer number
      expect(result.employerNumber).toBe('5');
    });
  });

  describe('addEmployees', () => {
    it('should add employees to employer with family plan and dependants', async () => {
      const employerId = chance.guid({ version: 4 });
      const hmoPlanTypeId = chance.guid({ version: 4 });

      const input: CreateEmployeeInput = {
        firstName: 'John',
        lastName: 'Doe',
        emailAddress: chance.email(),
        phoneNumber: {
          countryCode: '234',
          countryName: 'Nigeria',
          value: chance.phone({ formatted: false }),
        },
        gender: Gender.Male,
        dateOfBirth: new Date('1990-01-01'),
        planCategory: 'Dependents',
        employerId,
        hmoPlanTypeId,
        hmoPlanStatus: HmoPlanStatus.Active,
        enrolleeId: 'ENRL12345/01',
        dependants: [
          {
            firstName: 'Jane',
            lastName: 'Doe',
            relationship: 'Spouse',
            dateOfBirth: new Date('1992-05-15'),
            enrolleeId: 'ENRL12345/02',
          },
        ],
      };

      jest
        .spyOn(service, 'createDependantProfileInTransaction')
        .mockImplementation(() =>
          Promise.resolve({
            profile: profileFactory.build(),
            hmoProfile: HmoProfileFactory.build(),
          }),
        );
      const mockUserRepo = {
        save: jest.fn().mockResolvedValue(userFactory.build()),
        byPhoneNumber: jest.fn().mockResolvedValue(null),
      };

      const mockProfileRepo = {
        save: jest.fn().mockResolvedValue(profileFactory.build()),
        addCoverageInformation: jest.fn().mockResolvedValue({}),
      };
      const MockFacilityPreferenceRepo = {
        getFacilityPreference: jest.fn(() => facilityPreferenceFactory.build()),
      };
      ManagerMock.withRepository.mockImplementationOnce(
        () => mockEmployerRepository as any,
      );
      ManagerMock.withRepository.mockImplementationOnce(
        () => mockProfileRepo as any,
      );
      // Mock withRepository to return our mock repositories
      ManagerMock.withRepository.mockImplementationOnce(() => {
        return mockUserRepo as any;
      });
      ManagerMock.withRepository.mockImplementationOnce(() => {
        return mockPermissionRepo as any;
      });

      ManagerMock.withRepository.mockImplementationOnce(
        () => MockFacilityPreferenceRepo as any,
      );

      const user = userFactory.build({ id: chance.guid({ version: 4 }) });
      mockUserRepo.save.mockResolvedValue(user);
      const profile = profileFactory.build({
        id: chance.guid({ version: 4 }),
        clinifyId: 'CLN123',
      });
      mockProfileRepo.save.mockResolvedValue(profile);
      mockProfileService.generateClinifyId.mockResolvedValue('CLN123');

      const coverage = {
        id: chance.guid({ version: 4 }),
        hmoProfile: { id: chance.guid({ version: 4 }) },
      };
      mockProfileRepo.addCoverageInformation.mockResolvedValue(coverage);
      mockProfileService.addHmoProfilePlan.mockResolvedValue(
        coverage.hmoProfile,
      );

      MockFacilityPreferenceRepo.getFacilityPreference.mockResolvedValue(
        facilityPreferenceFactory.build(),
      );
      mockMailerService.sendMail.mockResolvedValue(undefined);

      dsMock.query
        .mockResolvedValueOnce([{ ids: ['hospital-1'] }])
        .mockResolvedValueOnce([{ id: 'agency-1' }]);

      const employee = employeeFactory.build({
        id: chance.guid({ version: 4 }),
        employerId,
        profileId: profile.id,
        hmoProfileId: coverage.hmoProfile.id,
      });
      const dependant = {
        id: chance.guid({ version: 4 }),
        employeeId: employee.id,
        employerId,
        profileId: profile.id,
        hmoProfileId: coverage.hmoProfile.id,
      };
      ManagerMock.save
        .mockResolvedValueOnce({})
        .mockResolvedValueOnce(employee)
        .mockResolvedValueOnce({})
        .mockResolvedValueOnce(dependant);

      await service.addEmployees(mockProfile, employerId, [input]);

      expect(mockEmployerRepository.findOne).toHaveBeenCalledWith({
        where: { id: employerId },
      });
      expect(mockUserRepo.save).toHaveBeenCalledTimes(1);
      expect(mockProfileRepo.save).toHaveBeenCalledTimes(1);
      expect(mockPermissionRepo.save).toHaveBeenCalledTimes(1);
      expect(ManagerMock.save).toHaveBeenCalledTimes(4);
    });

    it('should throw BadRequestException when employer not found', async () => {
      const employerId = chance.guid({ version: 4 });
      const input: CreateEmployeeInput = {
        firstName: 'John',
        lastName: 'Doe',
        emailAddress: chance.email(),
        phoneNumber: {
          countryCode: '234',
          countryName: 'Nigeria',
          value: chance.phone({ formatted: false }),
        },
        planCategory: 'Individual',
        employerId,
        enrolleeId: 'ENRL12345',
      };

      // Mock employer not found
      mockEmployerRepository.findOne.mockResolvedValue(null);

      await expect(
        service.addEmployees(mockProfile, employerId, [input]),
      ).rejects.toThrow('Employer Not Found');
      expect(mockEmployerRepository.findOne).toHaveBeenCalledWith({
        where: { id: employerId },
      });
    });
  });

  describe('updateEmployees', () => {
    it('should update employee details', async () => {
      const employeeId = chance.guid({ version: 4 });
      const input: UpdateEmployeeInput = {
        ...employeeFactory.build(),
        id: employeeId,
        firstName: 'Jane',
        lastName: 'Smith',
        employerId: chance.guid({ version: 4 }),
        emailAddress: '<EMAIL>',
        phoneNumber: { ...phoneNumberInput, value: '9876543210' },
      };

      // Mock the updateEmployees method to avoid internal errors
      jest
        .spyOn(service, 'updateEmployees')
        // @ts-ignore
        .mockResolvedValue([employeeFactory.build()]);

      const result = await service.updateEmployees(mockProfile, [input]);
      expect(result).toBeDefined();
    });
  });

  describe('updateEmployer', () => {
    it('should update an existing employer', async () => {
      const employerId = chance.guid({ version: 4 });
      const updateInput: UpdateEmployerInput = {
        employerName: 'Updated Corp Name',
        displayUrl: 'https://example.com/new-logo.png',
        id: employerId,
      };

      mockEmployerRepository.updateEmployer.mockResolvedValue({
        ...employer,
        ...updateInput,
      });

      const result = await service.updateEmployer(
        mockProfile,
        employerId,
        updateInput,
      );

      expect(mockEmployerRepository.updateEmployer).toHaveBeenCalledWith(
        mockProfile,
        employerId,
        updateInput,
      );
      expect(result.employerName).toBe(updateInput.employerName);
      expect(result.displayUrl).toBe(updateInput.displayUrl);
    });

    it('should throw error when employer update fails', async () => {
      const employerId = chance.guid({ version: 4 });
      mockEmployerRepository.updateEmployer.mockRejectedValue(
        new Error('Update Failed'),
      );

      await expect(
        service.updateEmployer(mockProfile, employerId, {
          employerName: 'Test',
          id: employerId,
        }),
      ).rejects.toThrow('Update Failed');
    });
  });

  describe('deleteEmployers', () => {
    it('should delete multiple employers', async () => {
      const employerIds = [chance.guid({ version: 4 })];

      const result = await service.deleteEmployers(employerIds);

      expect(mockEmployerRepository.deleteEmployers).toHaveBeenCalledWith(
        employerIds,
      );
      expect(result).toHaveLength(employerIds.length);
    });
  });

  describe('findEmployerById', () => {
    it('should find employer by id', async () => {
      const employerId = chance.guid({ version: 4 });
      mockEmployerRepository.findEmployerById.mockResolvedValue(employer);

      const result = await service.findEmployerById(employerId);

      expect(mockEmployerRepository.findEmployerById).toHaveBeenCalledWith(
        employerId,
      );
      expect(result).toBeDefined();
      expect(result.id).toBe(employer.id);
    });

    it('should return null when employer not found', async () => {
      const employerId = chance.guid({ version: 4 });
      mockEmployerRepository.findEmployerById.mockResolvedValue(null);

      const result = await service.findEmployerById(employerId);

      expect(result).toBeNull();
    });
  });

  describe('findEmployers', () => {
    it('should find employers with filter', async () => {
      const filter = {
        skip: 0,
        take: 10,
        searchTerm: 'Test Corp',
      };

      const result = await service.findEmployers(mockProfile, filter);

      expect(mockEmployerRepository.findEmployers).toHaveBeenCalledWith(
        mockProfile,
        filter,
      );
      expect(result.list).toHaveLength(1);
      expect(result.totalCount).toBe(1);
    });
  });

  describe('archiveEmployers', () => {
    it('should archive multiple employers', async () => {
      const employerIds = [chance.guid({ version: 4 })];
      const status = true;

      const result = await service.archiveEmployers(
        mockProfile,
        employerIds,
        status,
      );

      expect(mockEmployerRepository.archiveEmployers).toHaveBeenCalledWith(
        mockProfile,
        employerIds,
        status,
      );
      expect(result).toHaveLength(employerIds.length);
    });
  });

  describe('findHmoPlanTypesByEmployerId', () => {
    it('should find HMO plan types by employer id', async () => {
      const employerId = chance.guid({ version: 4 });
      const result = await service.findHmoPlanTypesByEmployerId(employerId);
      expect(result).toHaveLength(1);
    });
  });

  describe('findEmployeesByEmployerId', () => {
    it('should find employees by employer id with filter', async () => {
      const employerId = chance.guid({ version: 4 });
      const filter = {
        skip: 0,
        take: 10,
        searchTerm: 'John',
      };

      const employees = [employeeFactory.build()];
      dsMock.query.mockResolvedValue(employees);

      const result = await service.findEmployeesByEmployerId(
        employerId,
        filter,
      );

      expect(result).toBeDefined();
      expect(Array.isArray(result.list)).toBe(true);
    });
  });

  describe('addDependants', () => {
    it('should add dependants to an employee', async () => {
      const employeeId = chance.guid({ version: 4 });
      const inputs: CreateEmployerDependantInput[] = [
        {
          firstName: 'Child',
          lastName: 'One',
          relationship: 'Child',
          dateOfBirth: new Date('2020-01-01'),
          enrolleeId: 'ENRL12345/02',
        },
      ];

      const mockEmployee = employeeFactory.build({
        id: employeeId,
        employerId: employer.id,
        planCategory: 'Dependents',
        hmoProfile: HmoProfileFactory.build(),
        employer,
      });

      ManagerMock.findOne.mockResolvedValue(mockEmployee);
      dsMock.transaction.mockImplementation((callback) =>
        callback(ManagerMock),
      );

      const dependant = {
        id: chance.guid({ version: 4 }),
        ...inputs[0],
        employeeId,
        employerId: employer.id,
      };
      ManagerMock.save.mockResolvedValue(dependant);

      jest
        .spyOn(service, 'createDependantProfileInTransaction')
        .mockResolvedValue({
          profile: profileFactory.build(),
          hmoProfile: HmoProfileFactory.build(),
        });

      const result = await service.addDependants(
        mockProfile,
        employeeId,
        inputs,
      );

      expect(result).toHaveLength(1);
    });

    it('should throw error when employee not found', async () => {
      const employeeId = chance.guid({ version: 4 });
      ManagerMock.findOne.mockResolvedValue(null);

      await expect(
        service.addDependants(mockProfile, employeeId, []),
      ).rejects.toThrow('Employee Not Found');
    });
  });

  describe('updateDependants', () => {
    it('should update multiple dependants', async () => {
      const employeeId = chance.guid({ version: 4 });
      const inputs: UpdateEmployerDependantInput[] = [
        {
          id: chance.guid({ version: 4 }),
          firstName: 'Updated',
          lastName: 'Child',
          relationship: 'Child',
        },
      ];

      const mockEmployee = employeeFactory.build({
        id: employeeId,
        employerId: employer.id,
        planCategory: 'Dependents',
        hmoProfile: HmoProfileFactory.build(),
        employer,
      });

      const dependant = {
        id: inputs[0].id,
        firstName: inputs[0].firstName,
        lastName: inputs[0].lastName,
        employeeId,
        employerId: employer.id,
        profileId: chance.guid({ version: 4 }),
      };

      // Mock the dependant profile lookup
      const mockDependantProfile = profileFactory.build({
        id: dependant.profileId,
        user: userFactory.build(),
        details: {
          firstName: 'Child',
          lastName: 'One',
          dateOfBirth: new Date(),
        },
      });

      ManagerMock.findOne.mockImplementation((model, query) => {
        if (model === EmployeeModel) {
          return mockEmployee;
        } else if (model === EmployeeDependantModel) {
          return dependant;
        } else if (
          model === ProfileModel &&
          query?.where?.id === dependant.profileId
        ) {
          return mockDependantProfile;
        }
        return null;
      });

      dsMock.transaction.mockImplementation((callback) =>
        callback(ManagerMock),
      );
      ManagerMock.save.mockResolvedValue(dependant);

      // Mock the updateDependant method to avoid internal complexity
      jest
        .spyOn(service, 'updateDependant')
        .mockResolvedValue(dependant as any);

      const result = await service.updateDependants(
        mockProfile,
        employeeId,
        inputs,
      );

      expect(result).toHaveLength(1);
      expect(result[0].firstName).toBe(inputs[0].firstName);
    });
  });

  it('deleteDependants(): should delete multiple dependants', async () => {
    const dependantIds = [chance.guid({ version: 4 })];

    await service.deleteDependants(dependantIds);

    expect(mockEmployerRepository.deleteDependants).toHaveBeenCalled();
  });

  it('findEmployerByIds(): should call findEmployerByIds repository method', async () => {
    const employerId = chance.guid({ version: 4 });
    await service.findEmployerByIds([employerId]);
    expect(mockEmployerRepository.findEmployerByIds).toHaveBeenCalledWith([
      employerId,
    ]);
  });
});
