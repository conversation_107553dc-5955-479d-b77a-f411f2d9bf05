/* eslint-disable max-lines */
import {
  BadRequestException,
  NotFoundException,
  NotAcceptableException,
} from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { EntityManager } from 'typeorm';
import { AppointmentRepeatService } from './appointment_repeat.service';
import { AppointmentBookingModel } from '../models/appointment_booking.model';
import { AppointmentRepeatModel } from '../models/appointment_repeat.model';
import { billFactory } from '@clinify/__mocks__/factories/bill.factory';
import { walletTransactionFactory } from '@clinify/__mocks__/factories/wallet-transaction.factory';
import { loggerMock } from '@clinify/__mocks__/logger';
import { AvailabilityService } from '@clinify/availability/services/availability.service';
import { BillService } from '@clinify/bills/services/bill.service';
import { HospitalService } from '@clinify/hospitals/services/hospital.service';
import {
  AppointmentStatus,
  AppointmentType,
  AppointmentGuestStatus,
  AppointmentHostStatus,
} from '@clinify/shared/enums/appointment';
import { UserType } from '@clinify/shared/enums/users';
import { LinkService } from '@clinify/shared/services/dashboard-link.service';
import { TransactionProfileService } from '@clinify/shared/services/transacting-profile.service';
import { ProfileModel } from '@clinify/users/models/profile.model';
import { UserModel } from '@clinify/users/models/user.model';
import { WalletService } from '@clinify/wallets/services/wallet.service';
import { appointmentFeedbackFactory } from '@mocks/factories/appointment-feedback.factory';
import { appointmentFactory } from '@mocks/factories/appointment.factory';
import { availabilityFactory } from '@mocks/factories/availability.factory';
import { userFactory, buildUser } from '@mocks/factories/user.factory';

jest.mock('googleapis');

let user: UserModel = userFactory.build();
const userPatient = buildUser();
let appointmentData = appointmentFactory.build();
const appointmentsData = appointmentFactory.buildList(3);
const walletTransaction = walletTransactionFactory.build();
const feedbackData = appointmentFeedbackFactory.build();
const availabilityData = availabilityFactory.build({
  createdBy: { id: user.defaultProfile.id },
});
const bills = billFactory.buildList(3);
const bill = bills[0];
const averageRatingData = { averageRating: 3, profileId: 'my-profile-id' };

const MockAppointmentRepository = {
  findByProfile: jest.fn(() => appointmentsData),
  searchForPending: jest.fn(() => appointmentsData),
  updateAppointment: jest.fn(() => appointmentData),
  deleteAppointment: jest.fn(() => [appointmentData]),
  archiveAppointment: jest.fn(() => [appointmentData]),
  getOneAppointment: jest.fn(() => appointmentData),
  saveFeedback: jest.fn(() => feedbackData),
  getPrivateDoctorsCalender: jest.fn(() => [appointmentData]),
  getAverageRating: jest.fn(() => averageRatingData),
  saveAppointment: jest.fn(() => appointmentData),
  getAppointmentByGuest: jest.fn(() => appointmentData),
  findOneOrFail: jest.fn(() => appointmentData),
  findOne: jest.fn(() => appointmentData),
  save: jest.fn(() => appointmentData),
};
const MockLinkService = { getLinkedConnections: jest.fn(() => ({})) };
const HospitalServiceMock = {
  getOneHospital: jest.fn(() => appointmentData.facility),
};
const MockProfileRepository = {
  byClinifyId: jest.fn(() => user.defaultProfile),
};

const mockWalletService = {
  validatePasscode: jest.fn(() => true),
  transferFunds: jest.fn(() => walletTransaction),
  getWalletBalance: jest.fn(() => ({
    availableBalance: 1000,
    totalBalance: 2000,
  })),
};

const mockAvailabilityService = {
  getOneAvailability: jest.fn(() => availabilityData),
  updateAvailabilityBookDate: jest.fn(() => availabilityData),
};

const mockBillService = {
  createBill: jest.fn(() => bill),
  getBill: jest.fn(() => bill),
  billExists: jest.fn(() => false),
  getBillByReference: jest.fn(() => bill),
  payBill: jest.fn(() => bill),
  cancelBillOrChargeLevy: jest.fn(() => bill),
};

const mockEntityManager = {
  getCustomRepository: jest.fn().mockReturnValue({
    updateAppointment: jest.fn(() => appointmentData),
    saveFeedback: jest.fn(() => feedbackData),
    getAverageRating: jest.fn(() => averageRatingData),
    updateProfileRating: jest.fn(),
  }),
  queryRunner: { isTransactionActive: true },
};

const mockTransactionProfileService = {
  getTransactingProfile: jest.fn(() => ({
    profileType: 'profile',
    profile: { id: 'testId', clinifyId: '1234qwertt' },
  })),
};

describe('AppointmentBookingService', () => {
  let service: AppointmentRepeatService;
  let module: TestingModule;
  beforeEach(async () => {
    appointmentData = appointmentFactory.build();
    user = userFactory.build();
    module = await Test.createTestingModule({
      providers: [
        AppointmentRepeatService,
        {
          provide: EntityManager,
          useValue: mockEntityManager,
        },
        {
          provide: TransactionProfileService,
          useValue: mockTransactionProfileService,
        },
        {
          provide: WalletService,
          useValue: mockWalletService,
        },
        {
          provide: BillService,
          useValue: mockBillService,
        },
        {
          provide: getRepositoryToken(AppointmentBookingModel),
          useValue: MockAppointmentRepository,
        },
        {
          provide: getRepositoryToken(AppointmentRepeatModel),
          useValue: MockAppointmentRepository,
        },
        {
          provide: HospitalService,
          useValue: HospitalServiceMock,
        },
        {
          provide: AvailabilityService,
          useValue: mockAvailabilityService,
        },
        {
          provide: LinkService,
          useValue: MockLinkService,
        },
        {
          provide: getRepositoryToken(ProfileModel),
          useValue: MockProfileRepository,
        },
        {
          provide: EventEmitter2,
          useValue: {
            emit: jest.fn(),
            on: jest.fn(),
          },
        },
        { ...loggerMock },
      ],
    }).compile();

    service = module.get<AppointmentRepeatService>(AppointmentRepeatService);
    jest.clearAllMocks();
  });
  afterAll(async () => {
    await module.close();
  });
  it('should be defined', () => {
    expect(service).toBeDefined();
    expect(service).toBeTruthy();
  });

  it('updateAppointment(): should update an appointment', async () => {
    const appointmentMock = jest.spyOn(MockAppointmentRepository, 'findOne');
    const userProfile = user.defaultProfile;
    userProfile.type = UserType.Doctor;
    userProfile.id = 'testId';
    const customAppointment = {
      ...appointmentData,
      type: AppointmentType.Private,
      appointmentStatus: AppointmentStatus.Rejected,
      guest: { ...appointmentData.guest, id: 'testId' },
    };
    appointmentMock.mockImplementationOnce(() => customAppointment);
    const appointment = await service.updateAppointment(
      userProfile,
      customAppointment,
      customAppointment.id,
    );
    expect(appointment.id).toEqual(appointmentData.id);
  });

  it('updateAppointment(): should throw permission error if user not a host or a guest', async () => {
    const userProfile = user.defaultProfile;
    userProfile.type = UserType.Doctor;
    userProfile.id = 'testId';
    const customAppointment = {
      ...appointmentData,
      type: AppointmentType.Private,
      appointmentStatus: AppointmentStatus.Rejected,
      guest: { ...appointmentData.guest, id: 'testId' },
    };
    // appointmentMock.mockImplementationOnce(() => customAppointment);
    await expect(
      service.updateAppointment(
        userProfile,
        customAppointment,
        customAppointment.id,
      ),
    ).rejects.toThrow('Not Authorized To Modify This Record');
  });

  it('updateAppointment(): should throw error if id not found', async () => {
    MockAppointmentRepository.findOne = jest.fn(() => null);
    const userProfile = user.defaultProfile;
    userProfile.type = UserType.Doctor;
    userProfile.id = 'testId';
    const customAppointment = {
      ...appointmentData,
      type: AppointmentType.Private,
      appointmentStatus: AppointmentStatus.Rejected,
      guest: { ...appointmentData.guest, id: 'testId' },
    };
    // appointmentMock.mockImplementationOnce(() => customAppointment);
    await expect(
      service.updateAppointment(
        userProfile,
        customAppointment,
        customAppointment.id,
      ),
    ).rejects.toThrow(NotFoundException);
  });

  it('getOneAppointment() get one appointment', async () => {
    const record = await service.getOneAppointment(
      userPatient.defaultProfile,
      appointmentData.id,
    );
    expect(record.id).toEqual(appointmentData.id);
    expect(MockAppointmentRepository.getOneAppointment).toHaveBeenCalledWith(
      appointmentData.id,
      userPatient.defaultProfile,
    );
  });

  it('getPrivateDoctorsCalender() get ', async () => {
    const record = await service.getPrivateDoctorsCalender(
      userPatient.defaultProfile.id,
      new Date(),
    );
    expect(record[0].id).toEqual(appointmentData.id);
    expect(
      MockAppointmentRepository.getPrivateDoctorsCalender,
    ).toHaveBeenCalled();
  });

  it('getAppointments(): should get all appointment', async () => {
    const appointmentInput = appointmentFactory.build();
    delete appointmentInput.profile;
    await service.getAllAppointments(user.defaultProfile.id, {
      skip: 0,
      take: 50,
    });
    expect(MockAppointmentRepository.findByProfile).toHaveBeenCalledWith(
      user.defaultProfile.id,
      { skip: 0, take: 50 },
    );
  });

  it('getAppointments(): should get filtered appointments', async () => {
    const appointmentInput = appointmentFactory.build();
    delete appointmentInput.profile;
    const keyword = appointmentsData[0].condition;
    await service.getAllAppointments(user.defaultProfile.id, {
      skip: 0,
      take: 50,
      keyword,
    });
    expect(MockAppointmentRepository.findByProfile).toHaveBeenCalledWith(
      user.defaultProfile.id,
      { skip: 0, take: 50, keyword },
    );
  });

  it('rejectAppointment(): should reject appointment by guest', async () => {
    const appointmentMock = jest.spyOn(
      MockAppointmentRepository,
      'getAppointmentByGuest',
    );

    const userProfile = user.defaultProfile;
    userProfile.type = UserType.Patient;
    userProfile.id = appointmentData.guest.id;
    const customAppointment = {
      ...appointmentData,
      appointmentStatus: AppointmentStatus.Pending,
      guest: { ...appointmentData.guest, id: appointmentData.guest.id },
    };
    appointmentMock.mockImplementationOnce(() => customAppointment);
    await service.rejectAppointment(userProfile, customAppointment.id);
    expect(MockAppointmentRepository.save).toHaveBeenCalledWith({
      ...customAppointment,
      appointmentStatus: AppointmentStatus.Rejected,
    });
  });

  it('rejectAppointment(): should fail for already accepted appointment ', async () => {
    const appointmentMock = jest.spyOn(
      MockAppointmentRepository,
      'getAppointmentByGuest',
    );

    const userProfile = user.defaultProfile;
    userProfile.type = UserType.Patient;
    userProfile.id = appointmentData.guest.id;
    appointmentData.appointmentStatus = AppointmentGuestStatus.Accepted;
    const customAppointment = {
      ...appointmentData,
      guestStatus: AppointmentGuestStatus.Accepted,
      guest: { ...appointmentData.guest, id: 'testId' },
    };
    appointmentMock.mockImplementationOnce(() => customAppointment);
    await expect(
      service.rejectAppointment(userProfile, customAppointment.id),
    ).rejects.toThrow('Cannot Reject Accepted Appointment');
  });

  it('rejectAppointment(): should fail for already rejected appointment ', async () => {
    const appointmentMock = jest.spyOn(
      MockAppointmentRepository,
      'getAppointmentByGuest',
    );

    const userProfile = user.defaultProfile;
    userProfile.type = UserType.Patient;
    userProfile.id = appointmentData.guest.id;
    appointmentData.appointmentStatus = AppointmentGuestStatus.Rejected;

    const customAppointment = {
      ...appointmentData,
      guestStatus: AppointmentGuestStatus.Rejected,
      guest: { ...appointmentData.guest, id: 'testId' },
    };
    appointmentMock.mockImplementationOnce(() => customAppointment);
    await expect(
      service.rejectAppointment(userProfile, customAppointment.id),
    ).rejects.toThrow('Appointment Already Rejected');
  });

  it('rejectAppointment(): should fail if user is not guest', async () => {
    const appointmentMock = jest.spyOn(
      MockAppointmentRepository,
      'getAppointmentByGuest',
    );

    const userProfile = user.defaultProfile;
    userProfile.type = UserType.Patient;
    appointmentData.appointmentStatus = AppointmentGuestStatus.Rejected;

    const customAppointment = {
      ...appointmentData,
      guestStatus: AppointmentGuestStatus.Rejected,
      guest: { ...appointmentData.guest, id: 'testId' },
    };
    appointmentMock.mockImplementationOnce(() => customAppointment);
    await expect(
      service.rejectAppointment(userProfile, customAppointment.id),
    ).rejects.toThrow('Not Authorized To Reject This Appointment');
  });

  it('rejectAppointment(): should cancel an appointment', async () => {
    const appointmentMock = jest.spyOn(
      MockAppointmentRepository,
      'getOneAppointment',
    );

    const userProfile = user.defaultProfile;
    userProfile.type = UserType.Patient;
    userProfile.id = 'testId';
    const customAppointment = {
      ...appointmentData,
      guestStatus: AppointmentGuestStatus.Cancelled,
      guest: { ...appointmentData.guest, id: 'testId' },
    };
    appointmentMock.mockImplementationOnce(() => customAppointment);
    const appointment = await service.rejectAppointment(
      userProfile,
      customAppointment.id,
    );
    expect(appointment).toHaveProperty('id');
  });

  it('cancelAppointment(): should fail for already cancelled appointment ', async () => {
    const appointmentMock = jest.spyOn(
      MockAppointmentRepository,
      'getOneAppointment',
    );
    const userProfile = user.defaultProfile;
    userProfile.type = UserType.Patient;
    userProfile.id = 'testId';
    const customAppointment = {
      ...appointmentData,
      appointmentStatus: AppointmentStatus.Cancelled,
      guestStatus: AppointmentStatus.Cancelled,
      guest: { ...appointmentData.guest, id: 'testId' },
    };
    appointmentMock.mockImplementationOnce(() => customAppointment);
    await expect(
      service.cancelAppointment(userProfile, customAppointment.id),
    ).rejects.toThrow(NotAcceptableException);
  });

  it('cancelAppointment(): should fail for already completed appointment ', async () => {
    const appointmentMock = jest.spyOn(
      MockAppointmentRepository,
      'getOneAppointment',
    );
    const userProfile = user.defaultProfile;
    userProfile.type = UserType.Patient;
    userProfile.id = 'testId';
    appointmentData.appointmentStatus = AppointmentStatus.Completed;
    const customAppointment = {
      ...appointmentData,
      appointmentStatus: AppointmentStatus.Completed,
      guestStatus: AppointmentStatus.Completed,
      guest: { ...appointmentData.guest, id: 'testId' },
    };
    appointmentMock.mockImplementationOnce(() => customAppointment);
    await expect(
      service.cancelAppointment(userProfile, customAppointment.id),
    ).rejects.toThrow(NotAcceptableException);
  });

  it('cancelAppointment(): should allow host cancel appointment with', async () => {
    const appointmentMock = jest.spyOn(
      MockAppointmentRepository,
      'getOneAppointment',
    );
    const userProfile = user.defaultProfile;
    const hostId = appointmentData.host.id;
    userProfile.type = UserType.Patient;
    appointmentData.host.id = userProfile.id;
    const customAppointment = {
      ...appointmentData,
      guestStatus: AppointmentStatus.Completed,
      guest: { ...appointmentData.guest, id: 'testId' },
    };
    appointmentMock.mockImplementationOnce(() => customAppointment);
    const appointment = await service.cancelAppointment(
      userProfile,
      customAppointment.id,
    );
    expect(appointment).toHaveProperty('id');
    appointmentData.host.id = hostId;
  });

  it(
    'cancelAppointment(): should deduct amount from host when host cnceled an appointment' +
      ' that has already been acccepted by guest',
    async () => {
      const appointmentMock = jest.spyOn(
        MockAppointmentRepository,
        'getOneAppointment',
      );
      const hostId = appointmentData.host.id;
      const userProfile = user.defaultProfile;
      userProfile.type = UserType.Patient;
      appointmentData.host.id = userProfile.id;
      appointmentData.appointmentStatus = AppointmentStatus.Accepted;
      appointmentData.paymentStatus = 'Pending';

      const customAppointment = {
        ...appointmentData,
        guestStatus: AppointmentStatus.Completed,
        guest: { ...appointmentData.guest, id: 'testId' },
      };
      appointmentMock.mockImplementationOnce(() => customAppointment);
      const appointment = await service.cancelAppointment(
        userProfile,
        customAppointment.id,
      );
      expect(appointment).toHaveProperty('id');
      appointmentData.host.id = hostId;
    },
  );

  it('cancelAppointment(): should cancel appointment for host for completed payment', async () => {
    const appointmentMock = jest.spyOn(
      MockAppointmentRepository,
      'getOneAppointment',
    );
    const hostId = appointmentData.host.id;
    const userProfile = user.defaultProfile;
    userProfile.type = UserType.Patient;
    appointmentData.host.id = userProfile.id;
    appointmentData.appointmentStatus = AppointmentStatus.Accepted;
    appointmentData.paymentStatus = AppointmentStatus.Completed;

    const customAppointment = {
      ...appointmentData,
      guestStatus: AppointmentStatus.Completed,
      guest: { ...appointmentData.guest, id: 'testId' },
    };
    appointmentMock.mockImplementationOnce(() => customAppointment);
    const appointment = await service.cancelAppointment(
      userProfile,
      customAppointment.id,
    );
    expect(appointment).toHaveProperty('id');
    appointmentData.host.id = hostId;
  });

  it('cancelAppointment(): should cancel appointment by guest', async () => {
    const appointmentMock = jest.spyOn(
      MockAppointmentRepository,
      'getOneAppointment',
    );
    const hostId = appointmentData.host.id;
    const userProfile = user.defaultProfile;
    const fakeId = 'fakeId';
    userProfile.type = UserType.Patient;
    appointmentData.host.id = fakeId;
    appointmentData.appointmentStatus = AppointmentStatus.Accepted;
    appointmentData.paymentStatus = AppointmentStatus.Completed;

    const customAppointment = {
      ...appointmentData,
      guestStatus: AppointmentStatus.Completed,
      guest: { ...appointmentData.guest, id: 'testId' },
    };
    appointmentMock.mockImplementationOnce(() => customAppointment);
    const appointment = await service.cancelAppointment(
      userProfile,
      customAppointment.id,
    );
    expect(appointment).toHaveProperty('id');
    appointmentData.host.id = hostId;
  });

  it('cancelAppointment(): should cancel an appointment that has not been accepted', async () => {
    const appointmentMock = jest.spyOn(
      MockAppointmentRepository,
      'getOneAppointment',
    );
    const hostId = appointmentData.host.id;
    const userProfile = user.defaultProfile;
    userProfile.type = UserType.Patient;
    appointmentData.host.id = userProfile.id;
    appointmentData.appointmentStatus = AppointmentStatus.Pending;

    const customAppointment = {
      ...appointmentData,
      guestStatus: AppointmentStatus.Completed,
      guest: { ...appointmentData.guest, id: 'testId' },
    };
    appointmentMock.mockImplementationOnce(() => customAppointment);
    const appointment = await service.cancelAppointment(
      userProfile,
      customAppointment.id,
    );
    expect(appointment).toHaveProperty('id');
    appointmentData.host.id = hostId;
  });

  it('cancelAppointment(): cancel appointmnt when billing has been completed', async () => {
    const appointmentMock = jest.spyOn(
      MockAppointmentRepository,
      'getOneAppointment',
    );
    const hostId = appointmentData.host.id;
    const userProfile = user.defaultProfile;
    userProfile.type = UserType.Patient;
    appointmentData.host.id = userProfile.id;
    appointmentData.appointmentStatus = AppointmentStatus.Accepted;
    const customAppointment = {
      ...appointmentData,
      guestStatus: AppointmentStatus.Completed,
      guest: { ...appointmentData.guest, id: 'testId' },
    };
    appointmentMock.mockImplementationOnce(() => customAppointment);
    const appointment = await service.cancelAppointment(
      userProfile,
      customAppointment.id,
    );
    expect(appointment).toHaveProperty('id');
    appointmentData.host.id = hostId;
  });

  it('cancelAppointment(): cancel appointmnt by host and bill host', async () => {
    const appointmentMock = jest.spyOn(
      MockAppointmentRepository,
      'getOneAppointment',
    );
    const userProfile = user.defaultProfile;
    const hostId = appointmentData.host.id;
    userProfile.type = UserType.Patient;
    appointmentData.host.id = userProfile.id;
    appointmentData.appointmentStatus = AppointmentStatus.Accepted;
    appointmentData.paymentStatus = AppointmentStatus.Pending;
    const customAppointment = {
      ...appointmentData,
      guestStatus: AppointmentStatus.Completed,
      guest: { ...appointmentData.guest, id: 'testId' },
    };
    appointmentMock.mockImplementationOnce(() => customAppointment);
    const appointment = await service.cancelAppointment(
      userProfile,
      customAppointment.id,
    );
    expect(appointment).toHaveProperty('id');
    appointmentData.host.id = hostId;
  });

  it('cancelAppointment(): cancel appointmnt by host after host have made payment', async () => {
    const appointmentMock = jest.spyOn(
      MockAppointmentRepository,
      'getOneAppointment',
    );
    const userProfile = user.defaultProfile;
    const hostId = appointmentData.host.id;
    userProfile.type = UserType.Patient;
    appointmentData.host.id = userProfile.id;
    appointmentData.appointmentStatus = AppointmentStatus.Accepted;
    appointmentData.paymentStatus = AppointmentStatus.Completed;
    const customAppointment = {
      ...appointmentData,
      guestStatus: AppointmentStatus.Completed,
      guest: { ...appointmentData.guest, id: 'testId' },
    };
    appointmentMock.mockImplementationOnce(() => customAppointment);
    const appointment = await service.cancelAppointment(
      userProfile,
      customAppointment.id,
    );
    expect(appointment).toHaveProperty('id');
    appointmentData.host.id = hostId;
  });

  it('cancelAppointment(): cancel appointmnt by host after payment', async () => {
    const appointmentMock = jest.spyOn(
      MockAppointmentRepository,
      'getOneAppointment',
    );
    const userProfile = user.defaultProfile;
    const hostId = appointmentData.host.id;
    userProfile.type = UserType.Patient;
    appointmentData.host.id = userProfile.id;
    appointmentData.appointmentStatus = AppointmentStatus.Accepted;
    appointmentData.paymentStatus = AppointmentStatus.Completed;
    const customAppointment = {
      ...appointmentData,
      guestStatus: AppointmentStatus.Completed,
      guest: { ...appointmentData.guest, id: 'testId' },
    };
    appointmentMock.mockImplementationOnce(() => customAppointment);
    const appointment = await service.cancelAppointment(
      userProfile,
      customAppointment.id,
    );
    expect(appointment).toHaveProperty('id');
    appointmentData.host.id = hostId;
  });

  it('acknowledgeAppointment(): should acknowledgeAppointment an appointment', async () => {
    const appointmentMock = jest.spyOn(
      MockAppointmentRepository,
      'getOneAppointment',
    );

    const userProfile = user.defaultProfile;
    userProfile.type = UserType.Patient;
    userProfile.id = 'testId';
    const customAppointment = {
      ...appointmentData,
      guestStatus: AppointmentGuestStatus.Accepted,
      appointmentStatus: AppointmentGuestStatus.Accepted,
      guest: { ...appointmentData.guest, id: 'testId' },
    };
    appointmentMock.mockImplementationOnce(() => customAppointment);
    const appointment = await service.acknowledgeAppointment(
      userProfile,
      customAppointment.id,
    );
    expect(appointment).toHaveProperty('id');
  });

  it('acknowledgeAppointment(): should fail for appointment that has been completed', async () => {
    const appointmentMock = jest.spyOn(
      MockAppointmentRepository,
      'getAppointmentByGuest',
    );

    const userProfile = user.defaultProfile;
    userProfile.type = UserType.Patient;
    userProfile.id = 'testId';
    appointmentData.appointmentStatus = AppointmentStatus.Completed;
    const customAppointment = {
      ...appointmentData,
      guestStatus: AppointmentStatus.Completed,
      guest: { ...appointmentData.guest, id: 'testId' },
    };
    appointmentMock.mockImplementationOnce(() => customAppointment);
    await expect(
      service.acknowledgeAppointment(userProfile, customAppointment.id),
    ).rejects.toThrow('Appointment Already Completed');
  });

  it('acknowledgeAppointment(): should acknowledge appointment by guest or doctor', async () => {
    const appointmentMock = jest.spyOn(
      MockAppointmentRepository,
      'getAppointmentByGuest',
    );

    const userProfile = user.defaultProfile;
    userProfile.type = UserType.Patient;
    userProfile.id = 'testId';
    appointmentData.appointmentStatus = AppointmentStatus.Accepted;
    appointmentData.host.type = 'Doctor';
    const customAppointment = {
      ...appointmentData,
      guestStatus: AppointmentStatus.Completed,
      guest: { ...appointmentData.guest, id: 'testId' },
    };
    appointmentMock.mockImplementationOnce(() => customAppointment);
    const appointment = await service.acknowledgeAppointment(
      userProfile,
      customAppointment.id,
    );
    expect(appointment).toHaveProperty('id');
  });

  it('acknowledgeAppointment(): should acknowledge appointment for uncompleted payment', async () => {
    const appointmentMock = jest.spyOn(
      MockAppointmentRepository,
      'getAppointmentByGuest',
    );

    const userProfile = user.defaultProfile;
    userProfile.type = UserType.Patient;
    userProfile.id = appointmentData.host.id;
    appointmentData.appointmentStatus = AppointmentStatus.Accepted;
    appointmentData.host.type = 'Patient';
    appointmentData.paymentStatus = 'Pending';
    const customAppointment = {
      ...appointmentData,
      guestStatus: AppointmentStatus.Completed,
      guest: { ...appointmentData.guest, id: 'testId' },
    };
    appointmentMock.mockImplementationOnce(() => customAppointment);
    const appointment = await service.acknowledgeAppointment(
      userProfile,
      customAppointment.id,
    );
    expect(appointment).toHaveProperty('id');
  });

  it('acknowledgeAppointment(): should acknowledge appointment payment for doctor', async () => {
    const appointmentMock = jest.spyOn(
      MockAppointmentRepository,
      'getAppointmentByGuest',
    );

    const userProfile = user.defaultProfile;
    userProfile.type = UserType.Doctor;
    userProfile.id = appointmentData.host.id;
    appointmentData.appointmentStatus = AppointmentStatus.Accepted;
    appointmentData.host.type = 'Patient';
    appointmentData.paymentStatus = 'Pending';
    const customAppointment = {
      ...appointmentData,
      guestStatus: AppointmentStatus.Completed,
      guest: { ...appointmentData.guest, id: 'testId' },
    };
    appointmentMock.mockImplementationOnce(() => customAppointment);
    const appointment = await service.acknowledgeAppointment(
      userProfile,
      customAppointment.id,
    );
    expect(appointment).toHaveProperty('id');
  });

  it('acknowledgeAppointment(): should acknowledge appointment for uncompleted payment', async () => {
    const appointmentMock = jest.spyOn(
      MockAppointmentRepository,
      'getAppointmentByGuest',
    );

    const userProfile = user.defaultProfile;
    userProfile.type = UserType.Patient;
    userProfile.id = appointmentData.host.id;
    appointmentData.appointmentStatus = AppointmentStatus.Accepted;
    appointmentData.host.type = 'Patient';
    appointmentData.paymentStatus = 'Completed';
    const customAppointment = {
      ...appointmentData,
      guestStatus: AppointmentStatus.Completed,
      guest: { ...appointmentData.guest, id: 'testId' },
    };
    appointmentMock.mockImplementationOnce(() => customAppointment);
    const appointment = await service.acknowledgeAppointment(
      userProfile,
      customAppointment.id,
    );
    expect(appointment).toHaveProperty('id');
  });

  it('acknowledgeAppointment(): should acknowledge appointment for uncompleted payment', async () => {
    const appointmentMock = jest.spyOn(
      MockAppointmentRepository,
      'getAppointmentByGuest',
    );

    const userProfile = user.defaultProfile;
    userProfile.type = UserType.Patient;
    userProfile.id = appointmentData.host.id;
    appointmentData.appointmentStatus = AppointmentStatus.Accepted;
    appointmentData.host.type = 'Patient';
    appointmentData.paymentStatus = 'Pending';
    appointmentData.appointment.amount = 0;
    const customAppointment = {
      ...appointmentData,
      guestStatus: AppointmentStatus.Completed,
      guest: { ...appointmentData.guest, id: 'testId' },
    };
    appointmentMock.mockImplementationOnce(() => customAppointment);
    const appointment = await service.acknowledgeAppointment(
      userProfile,
      customAppointment.id,
    );
    expect(appointment).toHaveProperty('id');
  });

  it('acknowledgeAppointment(): should acknowledge appointment for completed payment', async () => {
    const appointmentMock = jest.spyOn(
      MockAppointmentRepository,
      'getAppointmentByGuest',
    );

    const userProfile = user.defaultProfile;
    userProfile.type = UserType.Patient;
    userProfile.id = appointmentData.host.id;
    appointmentData.appointmentStatus = AppointmentStatus.Accepted;
    appointmentData.host.type = 'Doctor';
    appointmentData.paymentStatus = 'Pending';
    const customAppointment = {
      ...appointmentData,
      guestStatus: AppointmentStatus.Completed,
      guest: { ...appointmentData.guest, id: 'testId' },
    };
    appointmentMock.mockImplementationOnce(() => customAppointment);
    const appointment = await service.acknowledgeAppointment(
      userProfile,
      customAppointment.id,
    );
    expect(appointment).toHaveProperty('id');
  });

  it('acknowledgeAppointment(): should acknowledge appointment by guest', async () => {
    const appointmentMock = jest.spyOn(
      MockAppointmentRepository,
      'getAppointmentByGuest',
    );

    const userProfile = user.defaultProfile;
    userProfile.type = UserType.Patient;
    appointmentData.appointmentStatus = AppointmentStatus.Accepted;
    appointmentData.host.type = 'Patient';
    appointmentData.paymentStatus = 'Pending';
    const customAppointment = {
      ...appointmentData,
      guestStatus: AppointmentStatus.Completed,
      guest: { ...appointmentData.guest, id: 'testId' },
    };
    appointmentMock.mockImplementationOnce(() => customAppointment);
    const appointment = await service.acknowledgeAppointment(
      userProfile,
      customAppointment.id,
    );
    expect(appointment).toHaveProperty('id');
  });

  it('acknowledgeAppointment(): should fail for appointment that has not been accepted', async () => {
    const appointmentMock = jest.spyOn(
      MockAppointmentRepository,
      'getAppointmentByGuest',
    );

    const userProfile = user.defaultProfile;
    userProfile.type = UserType.Patient;
    userProfile.id = 'testId';
    const customAppointment = {
      ...appointmentData,
      guestStatus: AppointmentGuestStatus.Rejected,
      guest: { ...appointmentData.guest, id: 'testId' },
    };
    appointmentMock.mockImplementationOnce(() => customAppointment);
    await expect(
      service.acknowledgeAppointment(userProfile, customAppointment.id),
    ).rejects.toThrow('Appointment Already Cancelled Or Rejected');
  });
  it('acceptAppointment(): should accept appointment by guest', async () => {
    const appointmentMock = jest.spyOn(
      MockAppointmentRepository,
      'getAppointmentByGuest',
    );
    const userProfile = user.defaultProfile;
    userProfile.type = UserType.Doctor;
    appointmentData.guest.id = userProfile.id;
    const customAppointment = {
      ...appointmentData,
      appointmentStatus: AppointmentStatus.Pending,
      hostStatus: AppointmentHostStatus.Pending,
      guestStatus: AppointmentGuestStatus.Pending,
      guest: { ...appointmentData.guest },
    };
    appointmentMock.mockImplementationOnce(() => customAppointment);
    await service.acceptAppointment(userProfile, customAppointment.id);
    expect(MockAppointmentRepository.getOneAppointment).toHaveBeenCalledWith(
      customAppointment.id,
      userProfile,
    );
  });

  it('acceptAppointment(): should create bill when a guest accept an appointmnt', async () => {
    const appointmentMock = jest.spyOn(
      MockAppointmentRepository,
      'getAppointmentByGuest',
    );
    const userProfile = user.defaultProfile;
    userProfile.type = UserType.Doctor;
    appointmentData.guest.id = userProfile.id;
    appointmentData.appointment.amount = 10;
    const customAppointment = {
      ...appointmentData,
      appointmentStatus: AppointmentStatus.Pending,
      hostStatus: AppointmentHostStatus.Pending,
      guestStatus: AppointmentGuestStatus.Pending,
      guest: { ...appointmentData.guest },
    };
    appointmentMock.mockImplementationOnce(() => customAppointment);
    await service.acceptAppointment(userProfile, customAppointment.id);
    expect(MockAppointmentRepository.getOneAppointment).toHaveBeenCalledWith(
      customAppointment.id,
      userProfile,
    );
  });

  it('acceptAppointment(): should throw error if host try to accept appointment', async () => {
    const userProfile = {
      ...user.defaultProfile,
      type: UserType.Doctor,
    } as ProfileModel;
    const customAppointment = {
      ...appointmentData,
      type: AppointmentType.Private,
      appointmentStatus: AppointmentStatus.Completed,
      guest: { ...appointmentData.guest, id: 'testId' },
    };
    MockAppointmentRepository.getAppointmentByGuest = jest
      .fn()
      .mockImplementationOnce(() => appointmentData);
    await expect(
      service.acceptAppointment(userProfile, customAppointment.id),
    ).rejects.toThrow(NotAcceptableException);
  });

  it('acceptAppointment(): should return error when Appointment Has Already Been Completed', async () => {
    const appointmentMock = jest.spyOn(
      MockAppointmentRepository,
      'getAppointmentByGuest',
    );
    const userProfile = user.defaultProfile;
    userProfile.type = UserType.Doctor;
    appointmentData.guest.id = userProfile.id;

    appointmentData.guestStatus = AppointmentStatus.Completed;
    const customAppointment = {
      ...appointmentData,
      type: AppointmentType.Private,
      appointmentStatus: AppointmentStatus.Completed,
      guest: { ...appointmentData.guest },
    };
    appointmentMock.mockImplementationOnce(() => customAppointment);
    await expect(
      service.acceptAppointment(userProfile, customAppointment.id),
    ).rejects.toThrow(BadRequestException);
  });

  it('acceptAppointment(): accept an appointment when host is a doctor', async () => {
    const appointmentMock = jest.spyOn(
      MockAppointmentRepository,
      'getAppointmentByGuest',
    );
    const userProfile = user.defaultProfile;
    userProfile.type = UserType.Doctor;
    userProfile.id = appointmentData.guest.id;
    appointmentData.host.type = 'Doctor';
    appointmentData.guestStatus = AppointmentGuestStatus.Pending;
    const customAppointment = {
      ...appointmentData,
      appointmentStatus: AppointmentStatus.Pending,
      hostStatus: AppointmentHostStatus.Pending,
      guest: { ...appointmentData.guest },
    };
    appointmentMock.mockImplementationOnce(() => customAppointment);
    await service.acceptAppointment(userProfile, customAppointment.id);
    expect(MockAppointmentRepository.getOneAppointment).toHaveBeenCalledWith(
      customAppointment.id,
      userProfile,
    );
  });
});
