import { Field, ID, ObjectType } from '@nestjs/graphql';
import { IsEmpty, IsUUID } from 'class-validator';
import {
  Column,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  OneToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { AuditEntitiesWithProfile } from './base-audits.entity';
import { NextAppointmentEntities } from '../validators/appointment-filter.input';
import { OrganisationAppointmentStatus } from '../validators/organisation_appointment_booking.input';
import { AdmissionModel } from '@clinify/admissions/models/admission.model';
import { ConsultationModel } from '@clinify/consultations/models/consultation.model';
import { PatientInformation } from '@clinify/handover-notes/inputs/patient-information';
import { HospitalModel } from '@clinify/hospitals/models/hospital.model';
import { ImmunizationModel } from '@clinify/immunizations/models/immunization.model';
import { OncologyConsultationHistoryModel } from '@clinify/oncology-consultation-history/models/oncology-consultation-history.model';
import { AntenatalModel } from '@clinify/pregnancy-care/models/antenatal.model';
import { LabourDeliveryModel } from '@clinify/pregnancy-care/models/labour-delivery.model';
import { PostnatalModel } from '@clinify/pregnancy-care/models/postnatal.model';
import { RequestProcedureModel } from '@clinify/request-procedures/models/request-procedures.model';
import {
  DiagnosisInput,
  ServiceDetailInput,
} from '@clinify/shared/validators/service-detail.input';
import { SurgeryModel } from '@clinify/surgeries/models/surgery.model';
import { ProfileModel } from '@clinify/users/models/profile.model';

@ObjectType()
@Entity({ name: 'organisation_appointments' })
export class OrganisationAppointmentModel extends AuditEntitiesWithProfile {
  @Field(() => ID)
  @PrimaryGeneratedColumn('uuid')
  @IsEmpty()
  @IsUUID('4')
  id: string;

  @Field(() => HospitalModel, { nullable: true })
  @ManyToOne(() => HospitalModel, (hospital) => hospital.appointments, {
    nullable: true,
  })
  @JoinColumn({ name: 'hospital' })
  hospital: HospitalModel;

  @Field(() => String, { nullable: true })
  @Index()
  @Column({ name: 'hospital', nullable: true })
  hospitalId?: string;

  @Field(() => ProfileModel, { nullable: true })
  @ManyToOne(
    () => ProfileModel,
    (profile) => profile.organisationAppointments,
    { nullable: true },
  )
  @JoinColumn({ name: 'specialist' })
  specialist?: ProfileModel;

  @Field(() => String, { nullable: true })
  @Index()
  @Column({ name: 'specialist', nullable: true })
  specialistId?: string;

  @Field(() => Date, { nullable: true })
  @Column({ name: 'start_date_time', nullable: true, type: 'timestamptz' })
  startDateTime?: Date;

  @Field(() => Date, { nullable: true })
  @Column({
    name: 'appointment_date_time',
    nullable: true,
    type: 'timestamptz',
  })
  appointmentDateTime?: Date;

  @Field(() => Date, { nullable: true })
  @Column({ name: 'end_date_time', nullable: true, type: 'timestamptz' })
  endDateTime?: Date;

  @Field(() => String, { nullable: true })
  @Column({
    name: 'status',
    nullable: true,
    type: 'text',
    default: OrganisationAppointmentStatus.Scheduled,
  })
  status: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'category', nullable: true, type: 'text' })
  category: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'rank', nullable: true, type: 'text' })
  rank: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'reason', nullable: true, type: 'text' })
  reason: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'cancel_reason', nullable: true, type: 'text' })
  cancelReason: string;

  @Field(() => [DiagnosisInput], { nullable: true })
  @Column({
    nullable: true,
    name: 'diagnosis',
    type: 'jsonb',
  })
  diagnosis: DiagnosisInput[];

  @Field(() => String, { nullable: true })
  @Column({ name: 'venue', nullable: true, type: 'text' })
  venue: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'live_session_url', nullable: true, type: 'text' })
  liveSessionUrl: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'specialty', nullable: true, type: 'text' })
  specialty: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'role', nullable: true, type: 'text' })
  role: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'patient_type', nullable: true, type: 'text' })
  patientType: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'payment_type', nullable: true, type: 'text' })
  paymentType: string;

  @Field(() => [ServiceDetailInput], { nullable: true })
  @Column({
    nullable: true,
    name: 'service_details',
    type: 'jsonb',
  })
  serviceDetails: ServiceDetailInput[];

  @Field(() => String, { nullable: true })
  @Column({ name: 'urgency', nullable: true, type: 'text' })
  urgency: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'confirmed_by', nullable: true, type: 'text' })
  confirmedBy: string;

  @Column({ name: 'archived', nullable: true, type: 'boolean', default: false })
  archived?: boolean;

  @Field(() => String, { nullable: true })
  @Column({ name: 'delivery_method', type: 'text', nullable: true })
  deliveryMethod: string;

  @Field(() => NextAppointmentEntities, { nullable: true })
  @Column({ name: 'record_type', type: 'text', nullable: true })
  recordType: NextAppointmentEntities;

  @Field(() => [String], { nullable: true })
  @Column({ name: 'dcoument_url', type: 'text', array: true, nullable: true })
  documentUrl: string[];

  @Field(() => String, { nullable: true })
  @Column({ name: 'duration', nullable: true, type: 'text' })
  duration?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'additional_note', type: 'text', nullable: true })
  additionalNote?: string;

  @Field(() => AdmissionModel, { nullable: true })
  @OneToOne(() => AdmissionModel, (admission) => admission.appointment)
  admission?: AdmissionModel;

  @Field(() => AntenatalModel, { nullable: true })
  @OneToOne(() => AntenatalModel, (antenatal) => antenatal.appointment)
  antenatal?: AntenatalModel;

  @Field(() => ConsultationModel, { nullable: true })
  @OneToOne(() => ConsultationModel, (consultation) => consultation.appointment)
  consultation?: ConsultationModel;

  @Field(() => ImmunizationModel, { nullable: true })
  @OneToOne(() => ImmunizationModel, (immunization) => immunization.appointment)
  immunization?: ImmunizationModel;

  @Field(() => SurgeryModel, { nullable: true })
  @OneToOne(() => SurgeryModel, (surgery) => surgery.appointment)
  surgery?: SurgeryModel;

  @Field(() => PostnatalModel, { nullable: true })
  @OneToOne(() => PostnatalModel, (postnatal) => postnatal.appointment)
  postnatal?: PostnatalModel;

  @Field(() => LabourDeliveryModel, { nullable: true })
  @OneToOne(
    () => LabourDeliveryModel,
    (labourDelivery) => labourDelivery.appointment,
  )
  labourDelivery?: LabourDeliveryModel;

  @Field(() => OncologyConsultationHistoryModel, { nullable: true })
  @OneToOne(
    () => OncologyConsultationHistoryModel,
    (oncologyConsultationHistory) => oncologyConsultationHistory.appointment,
  )
  oncologyConsultationHistory?: OncologyConsultationHistoryModel;

  @Field(() => RequestProcedureModel, { nullable: true })
  @OneToOne(
    () => RequestProcedureModel,
    (requestProcedure) => requestProcedure.appointment,
  )
  requestProcedure?: RequestProcedureModel;

  @Field(() => PatientInformation, { nullable: true })
  @Column({ name: 'patient_information', type: 'jsonb', nullable: true })
  patientInformation?: PatientInformation;

  @Field({ nullable: true })
  @Column({ nullable: true, name: 'patient_confirmation' })
  patientConfirmation?: string;

  @Field({ nullable: true })
  @Column({ nullable: true, name: 'patient_whatsapp_confirmation' })
  patientWhatsappConfirmation?: string;

  @Field({ nullable: true })
  @Column({ nullable: true, name: 'patient_sms_confirmation' })
  patientSmsConfirmation?: string;

  constructor(organisationAppointment: Partial<OrganisationAppointmentModel>) {
    super();
    Object.assign(this, organisationAppointment);
  }
}
