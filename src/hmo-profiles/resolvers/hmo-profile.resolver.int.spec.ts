import { EventEmitter2 } from '@nestjs/event-emitter';
import { Test, TestingModule } from '@nestjs/testing';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Chance } from 'chance';
import { RedisPubSub } from 'graphql-redis-subscriptions';
import { HmoProfileResolver } from './hmo-profile.resolver';
import { TestDataSourceOptions } from '../../data-source';
import { extendModel } from '../../database/extendModel';
import { HmoProviderModel } from '../../hmo-providers/models/hmo-provider.model';
import { NotificationsService } from '../../notifications/services/notifications.service';
import { QuestionnaireData } from '../inputs/hmo-profile.input';
import { EnrolleeRegistrationProducer } from '../producers/enrollee-registration.producer';
import { CustomHmoProfileRepoMethods } from '../repositories/hmo-profile.repository';
import { HmoProfileService } from '../services/hmo-profile.service';
import { profileFactory } from '@clinify/__mocks__/factories/profile.factory';
import { TypeormExtendedModule } from '@clinify/custom-repository/typeorm-extended.module';
import { HmoProfileModel } from '@clinify/hmo-profiles/models/hmo-profile.model';
import { HmoProviderRepository } from '@clinify/hmo-providers/repositories/hmo-provider.repository';
import { PUB_SUB } from '@clinify/utils/subscriptions/pubSub';
import { SubscriptionTypes } from '@clinify/utils/subscriptions/types';
import { HmoProfileFactory } from '@mocks/factories/hmo-profile.factory';

const { BulkRegistrationStatusUpdate } = SubscriptionTypes;

const chance = new Chance();

const profile = profileFactory.build();
let hmoProfileData = HmoProfileFactory.build();
hmoProfileData = {
  ...hmoProfileData,
  updatedBy: { ...hmoProfileData.updatedBy, fullName: chance.name() },
  createdBy: { ...hmoProfileData.createdBy, fullName: chance.name() },
};

const hmoProfileServiceMock = {
  saveCoverageInformation: jest.fn(() => hmoProfileData),
  deleteHmoProfile: jest.fn(() => hmoProfileData),
  getOneHmoProfile: jest.fn(() => hmoProfileData),
  addHmoProfile: jest.fn(() => hmoProfileData),
  updateHmoProfile: jest.fn(() => hmoProfileData),
  getAllProfileHmos: jest.fn(() => hmoProfileData),
  syncProfileWithHmo: jest.fn(() => [hmoProfileData]),
  getPlanEligibilityText: jest.fn(() => 'Description'),
  getCoverageNameList: jest.fn(() => ['AXA MANSARD', 'LEADWAY']),
  bulkActivateHmoProfiles: jest.fn(() => []),
  updateQuestionnaireData: jest.fn(() => hmoProfileData),
  registeredEnrolleesMetrics: jest.fn(() => ({
    totalEnrollees: 10,
    totalFacilitiesEnrolledIn: 5,
    totalCommissionPayable: 1000,
    totalRates: 500,
  })),
  getEnrolledEnrollees: jest.fn(),
  getSponsoredHmoProfiles: jest.fn().mockResolvedValue([
    {
      id: 'profile-1',
      fullName: 'John Doe',
      memberNumber: 'HMO001',
      planStartDate: new Date('2024-01-01'),
      planDueDate: new Date('2024-12-31'),
      status: 'active',
    },
  ]),
};

const MockEnrolleeRegistrationProducer: any = {};

const MockEnrolleeRegistrationQueue: any = {};

const pubSubMock = {
  publish: jest.fn(),
  asyncIterator: jest.fn(),
};

describe('HmoProfileResolver', () => {
  let resolver: HmoProfileResolver;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [
        TypeOrmModule.forRoot(TestDataSourceOptions),
        TypeOrmModule.forFeature([HmoProfileModel, HmoProviderModel]),
        TypeormExtendedModule.forCustomRepository([HmoProviderRepository]),
      ],
      providers: [
        extendModel(HmoProfileModel, CustomHmoProfileRepoMethods),
        HmoProviderRepository,
        HmoProfileResolver,
        {
          provide: HmoProfileService,
          useValue: hmoProfileServiceMock,
        },
        {
          provide: 'PUB_SUB',
          useValue: pubSubMock,
        },
        {
          provide: EventEmitter2,
          useValue: {
            emit: jest.fn(),
            on: jest.fn(),
          },
        },
        {
          provide: NotificationsService,
          useValue: {
            handleNoticationEvent: jest.fn(),
          },
        },
        {
          provide: EnrolleeRegistrationProducer,
          useValue: MockEnrolleeRegistrationProducer,
        },
        {
          provide: 'BullQueue_enrollee_registration_queue',
          useValue: MockEnrolleeRegistrationQueue,
        },
      ],
    }).compile();

    resolver = module.get<HmoProfileResolver>(HmoProfileResolver);
    jest.clearAllMocks();
  });

  it('saveMyHmos(): should call updateWhenInactive', async () => {
    const hmoProfile = hmoProfileData;
    delete hmoProfile.memberStatus;
    await resolver.saveMyHmos(profile, hmoProfile);
    expect(hmoProfileServiceMock.addHmoProfile).toHaveBeenCalledWith(
      profile,
      hmoProfile,
    );
  });

  it('saveMyHmos(): should call addHmoProfile service', async () => {
    const hmoProfile = hmoProfileData;
    delete hmoProfile.memberStatus;
    await resolver.saveMyHmos(profile, hmoProfile, 'record-id');
    expect(hmoProfileServiceMock.addHmoProfile).toHaveBeenCalledWith(profile, {
      ...hmoProfile,
      id: 'record-id',
    });
  });

  it('addCovergaeSubsHandler(): should trigger CoverageInformationAdded', async () => {
    await resolver.addCoverageSubsHandler('fake-id');
    expect(pubSubMock.asyncIterator).toHaveBeenCalledWith(
      'CoverageInformationAdded',
    );
  });

  it('fetchHmos(): should call getAllProfileHmos service', async () => {
    await resolver.fetchHmos(profile, hmoProfileData);
    expect(hmoProfileServiceMock.getAllProfileHmos).toBeCalledWith(
      profile,
      hmoProfileData,
      profile,
    );
  });

  it('updateMyHmos(): should call updateMyHmo service', async () => {
    const hmoProfile = hmoProfileData;
    delete hmoProfile.memberStatus;
    await resolver.updateMyHmos(profile, hmoProfile?.id, hmoProfile);
    expect(hmoProfileServiceMock.updateHmoProfile).toHaveBeenCalledWith(
      profile,
      hmoProfile?.id,
      hmoProfile,
    );
  });

  it('updateCoverageSubsHandler(): should trigger CoverageInformationUpdated', async () => {
    await resolver.updateCoverageSubsHandler('fake-id');
    expect(pubSubMock.asyncIterator).toHaveBeenCalledWith(
      'CoverageInformationUpdated',
    );
  });

  it('removeCoverageSubsHandler(): should trigger CoverageInformationUpdated', async () => {
    await resolver.removeCoverageSubsHandler('fake-id');
    expect(pubSubMock.asyncIterator).toHaveBeenCalledWith(
      'CoverageInformationRemoved',
    );
  });

  it('deleteMyHmo(): should call deleteHmoProfile service method', async () => {
    const hmoProfileId = chance.word();
    await resolver.deleteMyHmo(profile, hmoProfileId);

    expect(hmoProfileServiceMock.deleteHmoProfile).toHaveBeenCalledWith(
      profile,
      hmoProfileId,
    );
  });

  it('syncProfileWithHmo(): should sync profile with hmo', async () => {
    const res = await resolver.syncProfileWithHmo(profile, 'clinifyId');
    expect(hmoProfileServiceMock.syncProfileWithHmo).toHaveBeenCalledWith(
      'clinifyId',
      profile,
    );
    expect(res).toEqual([hmoProfileData]);
  });

  it('hmo(): should get specific hmo', async () => {
    const hmoProfileId = chance.word();
    await resolver.hmo(profile, hmoProfileId);

    expect(hmoProfileServiceMock.getOneHmoProfile).toHaveBeenCalledWith(
      profile,
      hmoProfileId,
    );
  });

  it('getLastCheckIn() should only show for hospital of check in', () => {
    const hospitalId = chance.guid({ version: 4 });
    const checkInDate = new Date();
    const visitId = 'visit-id';
    const verificationCode = 'verification-code';
    const lastCheckInData = [
      {
        checkInByHospitalId: hospitalId,
        checkInDate,
        visitId,
        verificationCode,
      },
    ];
    let checkInData;

    const hmoProfile: HmoProfileModel = {
      ...hmoProfileData,
      lastCheckIn: lastCheckInData,
    };

    checkInData = resolver.getLastCheckIn(profile, hmoProfile);
    expect(checkInData).toEqual(undefined);

    checkInData = resolver.getLastCheckIn(
      { ...profile, hospital: { ...profile.hospital, id: hospitalId } },
      hmoProfile,
    );
    expect(checkInData).toEqual(hmoProfile.lastCheckIn[0]);
  });

  it('getPlanEligibility', () => {
    let res = resolver.getPlanEligibility(hmoProfileData, profile);
    expect(res).toEqual(hmoProfileData.planEligibility);

    const hospitalId = chance.guid({ version: 4 });
    const mutator = { ...profile, hospitalId };
    const hmoProfile = {
      ...hmoProfileData,
      eligibleHospitalsIds: [mutator.hospitalId],
    };
    res = resolver.getPlanEligibility(hmoProfile, mutator);
    expect(res).toEqual('Yes');

    mutator.hospitalId = chance.guid({ version: 4 });
    res = resolver.getPlanEligibility(hmoProfile, mutator);
    expect(res).toEqual('No');
  });

  it('getPlanEligibilityText', async () => {
    await resolver.getPlanEligibilityText(hmoProfileData, profile);
    expect(hmoProfileServiceMock.getPlanEligibilityText).toHaveBeenCalledWith(
      hmoProfileData,
      profile,
    );
  });

  it('getCoverageNameList(): should get coverage name list', async () => {
    const localProfile = profileFactory.build({ hospitalId: 'hospital-id' });

    const res = await resolver.getCoverageNameList(localProfile, 'HMO');
    expect(hmoProfileServiceMock.getCoverageNameList).toHaveBeenCalledWith(
      'hospital-id',
      'HMO',
    );
    expect(res).toStrictEqual(['AXA MANSARD', 'LEADWAY']);
  });

  it('bulkActivateHmoProfiles', () => {
    const hmoProfileIds = ['hmo-id-1', 'hmo-id-2'];
    const profile = profileFactory.build();
    const res = resolver.bulkActivateHmoProfiles(profile, hmoProfileIds);
    expect(hmoProfileServiceMock.bulkActivateHmoProfiles).toHaveBeenCalledWith(
      profile,
      hmoProfileIds,
    );
    expect(res).toEqual([]);
  });

  it('registeredEnrolleesMetrics(): should get metrics for registered enrollees', async () => {
    const profile = profileFactory.build({ hmoId: 'hmo-id', id: 'creator-id' });
    const input = { dateRange: { from: new Date(), to: new Date() } };

    const result = await resolver.registeredEnrolleesMetrics(profile, input);

    expect(
      hmoProfileServiceMock.registeredEnrolleesMetrics,
    ).toHaveBeenCalledWith(input, profile.hmoId, profile.id);

    expect(result).toEqual({
      totalEnrollees: 10,
      totalFacilitiesEnrolledIn: 5,
      totalCommissionPayable: 1000,
      totalRates: 500,
    });
  });

  it('getEnrolledEnrollees(): should call getEnrolledEnrollees service method', async () => {
    const mutator = profileFactory.build({ hmoId: 'hmo-id', id: 'creator-id' });

    await resolver.getEnrolledEnrollees(mutator, {
      from: new Date('2025-06-04'),
      to: new Date('2025-06-07'),
    });

    expect(hmoProfileServiceMock.getEnrolledEnrollees).toHaveBeenCalledWith(
      mutator,
      {
        from: new Date('2025-06-04'),
        to: new Date('2025-06-07'),
      },
    );
  });

  describe('updateQuestionnaireData', () => {
    let notificationServiceMock;

    beforeEach(() => {
      // eslint-disable-next-line @typescript-eslint/dot-notation
      notificationServiceMock = resolver['notificationService'];
      jest.clearAllMocks();
    });

    it('should call updateQuestionnaireData service method and return the result', async () => {
      const profile = profileFactory.build();
      const hmoProfileId = 'test-hmo-profile-id';
      const questionnaireData: QuestionnaireData = {
        maritalStatus: 'Married',
        gender: 'Male',
        highestFormalEducationalLevel: 'University',
        employmentStatus: 'Employed',
        occupationalGroup: 'Professional',
        numberOfHouseholdMembers: '4',
        numberOfRooms: '3',
        numberOfMattresses: '2',
        typeOfRoof: 'Metal',
        typeOfToilet: 'Water Closet',
        primaryCookingImplement: 'Gas Cooker',
        numberOfMobilePhones: '2',
        numberOfTVs: '1',
        numberOfVehicles: '1',
        relationshipToHouseholdHead: 'Head',
        questionnaireScore: 85.5,
      };

      const result = await resolver.updateQuestionnaireData(
        profile,
        hmoProfileId,
        questionnaireData,
      );

      expect(
        hmoProfileServiceMock.updateQuestionnaireData,
      ).toHaveBeenCalledWith(profile, hmoProfileId, questionnaireData);
      expect(result).toEqual(hmoProfileData);
    });

    it('should call notification service with correct parameters', async () => {
      const profile = profileFactory.build();
      const hmoProfileId = 'test-hmo-profile-id';
      const questionnaireData: QuestionnaireData = {
        maritalStatus: 'Single',
      };

      await resolver.updateQuestionnaireData(
        profile,
        hmoProfileId,
        questionnaireData,
      );

      expect(
        notificationServiceMock.handleNoticationEvent,
      ).toHaveBeenCalledWith({
        profile,
        details: {
          modelName: 'Coverage Information',
          action: 'Updated',
          item: hmoProfileData,
        },
      });
    });

    it('should publish to PubSub with correct parameters', async () => {
      const profile = profileFactory.build();
      const hmoProfileId = 'test-hmo-profile-id';
      const questionnaireData: QuestionnaireData = {
        maritalStatus: 'Single',
      };

      await resolver.updateQuestionnaireData(
        profile,
        hmoProfileId,
        questionnaireData,
      );

      expect(pubSubMock.publish).toHaveBeenCalledWith(
        'CoverageInformationUpdated',
        {
          CoverageInformationUpdated: {
            ...hmoProfileData,
            updatedBy: {
              ...hmoProfileData?.updatedBy,
              fullName: hmoProfileData?.updatedBy?.fullName,
            },
            createdBy: {
              ...hmoProfileData?.createdBy,
              fullName: hmoProfileData?.createdBy?.fullName,
            },
          },
        },
      );
    });

    it('bulkRegisterEnrollees', async () => {
      const profile = profileFactory.build();
      const mockInput = {
        enrollees: [
          {
            enrolleeFirstName: 'John',
            enrolleeLastName: 'Doe',
            enrolleeMiddleName: 'Smith',
            enrolleeGender: 'Male',
            enrolleeDateOfBirth: '1990-01-01',
            enrolleePrimaryPhoneNumber: '+1234567890',
            enrolleeEmailAddress: '<EMAIL>',
            enrolleeLGAOfResidence: 'Test LGA',
            enrolleeCountryOfOrigin: 'Nigeria',
            enrolleeStateOfOrigin: 'Lagos',
            enrolleeContactAddress: '123 Test Street',
            planType: 'Basic Plan',
            paymentFrequency: 'Monthly',
            planStatus: 'Active',
            registrationDateAndTime: '2024-01-01T00:00:00Z',
            enrolledBy: 'Test Admin',
            paymentDateAndTime: '2024-01-01T00:00:00Z',
          },
        ],
      };

      const mockJob = { id: 'job-123' };
      MockEnrolleeRegistrationProducer.addBulkRegistrationJob = jest
        .fn()
        .mockResolvedValue(mockJob);

      const result = await resolver.bulkRegisterEnrollees(profile, mockInput);

      expect(
        MockEnrolleeRegistrationProducer.addBulkRegistrationJob,
      ).toHaveBeenCalledWith({
        enrollees: mockInput.enrollees,
        profileId: profile.id,
        batchId: expect.any(String),
      });

      expect(result).toEqual({
        jobId: 'job-123',
        batchId: expect.any(String),
        totalEnrollees: 1,
        status: 'QUEUED',
      });
    });

    it('getBulkRegistrationStatus', async () => {
      const jobId = 'test-job-123';
      const mockJob = {
        id: jobId,
        getState: jest.fn().mockResolvedValue('completed'),
        progress: jest.fn().mockResolvedValue(100),
        returnvalue: {
          total: 1,
          successful: 1,
          failed: 0,
          successfulEnrollees: [],
          failedEnrollees: [],
        },
      };

      MockEnrolleeRegistrationQueue.getJob = jest
        .fn()
        .mockResolvedValue(mockJob);

      const result = await resolver.getBulkRegistrationStatus(jobId);

      expect(MockEnrolleeRegistrationQueue.getJob).toHaveBeenCalledWith(jobId);
      expect(mockJob.getState).toHaveBeenCalled();
      expect(mockJob.progress).toHaveBeenCalled();

      expect(result).toEqual({
        jobId,
        status: 'completed',
        progress: 100,
        result: {
          total: 1,
          successful: 1,
          failed: 0,
          successfulEnrollees: [],
          failedEnrollees: [],
        },
      });
    });
  });
});

describe('HmoProfileResolver', () => {
  let resolver: HmoProfileResolver;
  let pubSub: RedisPubSub;

  const mockHmoProfileService = {
    addHmoProfile: jest.fn(),
    updateHmoProfile: jest.fn(),
    getAllProfileHmos: jest.fn(),
    deleteHmoProfile: jest.fn(),
    syncProfileWithHmo: jest.fn(),
    getOneHmoProfile: jest.fn(),
    getHmoProvider: jest.fn(),
    getPlanEligibilityText: jest.fn(),
    getCoverageNameList: jest.fn(),
    bulkActivateHmoProfiles: jest.fn(),
    registeredEnrolleesMetrics: jest.fn(),
    getEnrolledEnrollees: jest.fn(),
    updateQuestionnaireData: jest.fn(),
    getSponsoredHmoProfiles: jest.fn().mockResolvedValue([
      {
        id: 'profile-1',
        fullName: 'John Doe',
        memberNumber: 'HMO001',
        planStartDate: new Date('2024-01-01'),
        planDueDate: new Date('2024-12-31'),
        status: 'active',
      },
    ]),
  };

  const mockNotificationsService = {
    handleNoticationEvent: jest.fn(),
  };

  const mockEnrolleeRegistrationProducer = {
    addBulkRegistrationJob: jest.fn(),
  };

  const mockPubSub = {
    publish: jest.fn(),
    asyncIterator: jest.fn(),
  };

  const mockQueue = {
    getJob: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        HmoProfileResolver,
        {
          provide: HmoProfileService,
          useValue: mockHmoProfileService,
        },
        {
          provide: NotificationsService,
          useValue: mockNotificationsService,
        },
        {
          provide: EnrolleeRegistrationProducer,
          useValue: mockEnrolleeRegistrationProducer,
        },
        {
          provide: PUB_SUB,
          useValue: mockPubSub,
        },
        {
          provide: 'BullQueue_enrollee_registration_queue',
          useValue: mockQueue,
        },
      ],
    }).compile();

    resolver = module.get<HmoProfileResolver>(HmoProfileResolver);
    pubSub = module.get<RedisPubSub>(PUB_SUB);

    jest.clearAllMocks();
  });

  describe('getSponsoredHmoProfiles', () => {
    it('should call hmoProfileService.getSponsoredHmoProfiles with correct parameters', async () => {
      const validHmoId = '550e8400-e29b-41d4-a716-446655440000';
      const mockProfile = profileFactory.build({ hmoId: validHmoId });
      const sponsorName = 'Test Sponsor';
      const expectedResult = [
        {
          id: 'profile-1',
          fullName: 'John Doe',
          memberNumber: 'HMO001',
          planStartDate: new Date('2024-01-01'),
          planDueDate: new Date('2024-12-31'),
          status: 'active',
        },
      ];

      mockHmoProfileService.getSponsoredHmoProfiles.mockResolvedValue(
        expectedResult,
      );

      const result = await resolver.getSponsoredHmoProfiles(
        mockProfile,
        sponsorName,
      );

      expect(
        mockHmoProfileService.getSponsoredHmoProfiles,
      ).toHaveBeenCalledWith(mockProfile.hmoId, sponsorName);
      expect(result).toEqual(expectedResult);
    });

    it('should handle empty sponsor name', async () => {
      const validHmoId = '550e8400-e29b-41d4-a716-446655440000';
      const mockProfile = profileFactory.build({ hmoId: validHmoId });
      const sponsorName = '';
      const expectedResult = [];

      mockHmoProfileService.getSponsoredHmoProfiles.mockResolvedValue(
        expectedResult,
      );

      const result = await resolver.getSponsoredHmoProfiles(
        mockProfile,
        sponsorName,
      );

      expect(
        mockHmoProfileService.getSponsoredHmoProfiles,
      ).toHaveBeenCalledWith(mockProfile.hmoId, sponsorName);
      expect(result).toEqual(expectedResult);
    });

    it('should handle profile without hmoId', async () => {
      const mockProfile = profileFactory.build({ hmoId: null });
      const sponsorName = 'Test Sponsor';
      const expectedResult = [];

      mockHmoProfileService.getSponsoredHmoProfiles.mockResolvedValue(
        expectedResult,
      );

      const result = await resolver.getSponsoredHmoProfiles(
        mockProfile,
        sponsorName,
      );

      expect(
        mockHmoProfileService.getSponsoredHmoProfiles,
      ).toHaveBeenCalledWith(null, sponsorName);
      expect(result).toEqual(expectedResult);
    });
  });

  describe('bulkRegistrationStatusSubsHandler', () => {
    it('should return async iterator for BulkRegistrationStatusUpdate subscription', () => {
      const profileId = 'test-profile-123';
      const mockAsyncIterator = Symbol('asyncIterator');

      mockPubSub.asyncIterator.mockReturnValue(mockAsyncIterator);

      const result = resolver.bulkRegistrationStatusSubsHandler(profileId);

      expect(mockPubSub.asyncIterator).toHaveBeenCalledWith(
        BulkRegistrationStatusUpdate,
      );
      expect(result).toBe(mockAsyncIterator);
    });

    it('should handle different profile IDs', () => {
      const profileId1 = 'profile-1';
      const profileId2 = 'profile-2';
      const mockAsyncIterator1 = Symbol('asyncIterator1');
      const mockAsyncIterator2 = Symbol('asyncIterator2');

      mockPubSub.asyncIterator
        .mockReturnValueOnce(mockAsyncIterator1)
        .mockReturnValueOnce(mockAsyncIterator2);

      const result1 = resolver.bulkRegistrationStatusSubsHandler(profileId1);
      const result2 = resolver.bulkRegistrationStatusSubsHandler(profileId2);

      expect(mockPubSub.asyncIterator).toHaveBeenCalledTimes(2);
      expect(mockPubSub.asyncIterator).toHaveBeenNthCalledWith(
        1,
        BulkRegistrationStatusUpdate,
      );
      expect(mockPubSub.asyncIterator).toHaveBeenNthCalledWith(
        2,
        BulkRegistrationStatusUpdate,
      );
      expect(result1).toBe(mockAsyncIterator1);
      expect(result2).toBe(mockAsyncIterator2);
    });

    it('should work with empty profile ID', () => {
      const profileId = '';
      const mockAsyncIterator = Symbol('asyncIterator');

      mockPubSub.asyncIterator.mockReturnValue(mockAsyncIterator);

      const result = resolver.bulkRegistrationStatusSubsHandler(profileId);

      expect(mockPubSub.asyncIterator).toHaveBeenCalledWith(
        BulkRegistrationStatusUpdate,
      );
      expect(result).toBe(mockAsyncIterator);
    });

    it('should work with null profile ID', () => {
      const profileId = null;
      const mockAsyncIterator = Symbol('asyncIterator');

      mockPubSub.asyncIterator.mockReturnValue(mockAsyncIterator);

      const result = resolver.bulkRegistrationStatusSubsHandler(profileId);

      expect(mockPubSub.asyncIterator).toHaveBeenCalledWith(
        BulkRegistrationStatusUpdate,
      );
      expect(result).toBe(mockAsyncIterator);
    });

    it('should work with undefined profile ID', () => {
      const profileId = undefined;
      const mockAsyncIterator = Symbol('asyncIterator');

      mockPubSub.asyncIterator.mockReturnValue(mockAsyncIterator);

      const result = resolver.bulkRegistrationStatusSubsHandler(profileId);

      expect(mockPubSub.asyncIterator).toHaveBeenCalledWith(
        BulkRegistrationStatusUpdate,
      );
      expect(result).toBe(mockAsyncIterator);
    });
  });
});
