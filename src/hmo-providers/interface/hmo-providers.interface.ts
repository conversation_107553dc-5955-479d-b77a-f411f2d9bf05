import { HttpService } from '@nestjs/axios';
import { registerEnumType } from '@nestjs/graphql';
import { EntityManager } from 'typeorm';

import { HmoBeneficiary, HmoHospitalPayment } from '../responses/hmo.response';
import {
  EditHmoClaimInput,
  NewHmoClaimInput,
} from '@clinify/hmo-claims/inputs/hmo-claims.input';
import { HmoProfileModel } from '@clinify/hmo-profiles/models/hmo-profile.model';
import { HmoProviderModel } from '@clinify/hmo-providers/models/hmo-provider.model';
import { PreauthorisationReferralInput } from '@clinify/pre-authorisations-referral/inputs/preauthorisation-referral.input';
import { PreauthorisationReferralModel } from '@clinify/pre-authorisations-referral/models/preauthorisation-referral.model';
import { FlagDto } from '@clinify/pre-authorisations/inputs/flag.dto';
import { PreauthUtilisationInput } from '@clinify/pre-authorisations/inputs/preauth-utilisation.input';
import { PreauthorisationInput } from '@clinify/pre-authorisations/inputs/preauthorisation.input';
import { PreauthorisationModel } from '@clinify/pre-authorisations/models/preauthorisation.model';
import { PreAuthUtilisationsModel } from '@clinify/pre-authorisations/models/utilisations.model';
import { ProfileModel } from '@clinify/users/models/profile.model';

export enum HmoProviders {
  LeadwayHealthLimited = 'Leadway Health Limited',
}

export interface IValidateUtilisationTypeRequests {
  providerId: string;

  profileId: string;

  visitType: string;

  enrolleeId: string;

  profilePlanId: string;

  utilisations: PreauthUtilisationInput[];

  treatmentDate: Date;

  isClaim?: boolean;

  hospitalId: string;
}

export interface IValidateExternalUtilisationTypeRequests {
  providerId: string;

  profileId: string;

  visitType: string;

  enrolleeId: string;

  planTypeId: string;

  utilisations: PreauthUtilisationInput[];

  treatmentDate: Date;

  isClaim?: boolean;

  hospitalId: string;
}

export interface IHmoIds {
  code: string;
  name: string;
  values: string;
}

registerEnumType(HmoProviders, { name: 'HmoProviders' });

export interface IGetBenefitsResponseHTTP {
  benefits: IHmoEnrolleeBenefit[];
  provider: HmoProviderModel;
}

// Hmo Providers Interface and arguments interface

export interface IHmoProvidersEnrolleeCheckResponse {
  verificationCode: string;
  visitationId: string;
}
export interface IHmoProviderConstructoraArgs {
  providerId: string;
  httpService: HttpService;
  entityManager: EntityManager;
}

export interface IHmoEnrolleeBenefit {
  benefit: string;
  limit: string;
  used: number;
  balance: string;
}

export interface IHmoProviderHospitalPaymentsArgs {
  hospitalId: string;
  to: Date;
  from: Date;
  enrolleeId?: string;
}
export interface IHmoProviderSynchronizePreauthorizationResponse {
  paCode: string;
  price: string;
  status: string;
}
export interface IHmoProviderMovePreauthorizationToClaimResponse {
  visitDetailsId: string;
  claimIdentity: string;
  claimId: string;
  batchNumber?: string;
  totalTariffFee: number;
  visitId?: string;
}
export type IHmoProviderSynchronizeClaimsResponse =
  IHmoProviderSynchronizePreauthorizationResponse;
export interface IHmoAddClaimInput extends NewHmoClaimInput {
  doctorCode: string;
}
export interface IHmoProviderRequestPreauthorizationInput
  extends PreauthorisationInput {
  doctorCode: string;
}

export interface IHmoProviderCreatePreauthorizationReferralRequestInput
  extends PreauthorisationReferralInput {
  refferalProviderId: string;
  referralRemarks: string;
}
export interface IHmoEditClaimInput extends EditHmoClaimInput {
  enrolleeId: string;
  doctorCode: string;
  visitId?: string;
}
export interface IHmoPrognosisAddClaimInput extends IHmoEditClaimInput {
  memberUniqueId?: string;
  memberPlanId?: string;
  hmoProviderId?: string;
  staffEmail: string;
  patientPhone?: string;
  visitType: string;
}

export interface IHmoAddClaimResponse {
  claimId: string;
  claimIdentity?: string;
  totalCharged: number;
  totalTariffFee: number;
  visitId: string;
  batchNumber?: string;
  utilizations?: PreAuthUtilisationsModel[];
  byPharmacy?: boolean;
  flags?: FlagDto[];
}

export interface IHmoProvidersHospitalPaymentsDocumentResponse {
  pdfLink?: string;
  excelLink?: string;
}
export interface IHmoProvidersUtilizationType {
  label: string;
  value: string;
}
export interface ISyncClaimArgs {
  visitDetailsId: string;
  claimStatus: string;
  claimId: string;
  visitId?: string;
}

export interface IPrognosisCheckInEnrolleePayload {
  memberUniqueId: string;
  enrolleeId: string;
  staffEmail: string;
  patientPhone?: string;
  visitType: string;
}
export interface IPrognosisVerifyEnrolleePayload {
  enrolleeId: string;
  providerId: string;
}
export interface IPrognosisVisitationTypePayload {
  memberUniqueId: string;
  memberPlanId: string;
}

export interface IPrognosisGetUtilizationTypesPayload {
  memberUniqueId: string;
  memberPlanId: string;
  visitationType: string;
  utilizationId: string;
}

export interface IPrognosisGetUtilizationCategoriesPayload {
  memberUniqueId: string;
  memberPlanId: string;
  visitationType: string;
}
export interface IPrognosisSynchronizePreauthorizationPayload {
  visitDetailsId: string;
  visitId: string;
}

export interface IPrognosisGetEnrolleeBenefitsPayload {
  memberUniqueId: string;
  memberPlanId: string;
}
export interface IPrognosisGetTariffArgs {
  procedureCode: string;
  utilizationId: string;
}
export type IHmoProvidersVisitationType = IHmoProvidersUtilizationType;
export type IHmoProvidersUtilizationCategory = IHmoProvidersUtilizationType;

export interface IGetAgreedTarrifPayload {
  memberNumber: string;
  procedureCode: string;
  treatmentDate: Date;
}
export interface IHmoProvider {
  name?: string;

  isPrognosis: boolean;
  isClinify: boolean;
  // Enrollee related methods
  getEnrollees(enrolleeId: string): Promise<ProfileModel[]>;
  getEnrolleeDetails(enrolleeId: string): Promise<HmoProfileModel>;
  getEnrolleeBenefits(
    input: string | IPrognosisGetEnrolleeBenefitsPayload,
  ): Promise<IHmoEnrolleeBenefit[]>;
  getEnrolleeBeneficiaries(enrolleeId: string): Promise<HmoBeneficiary[]>;
  checkInEnrollee(
    enrolleeId: string | IPrognosisCheckInEnrolleePayload,
  ): Promise<IHmoProvidersEnrolleeCheckResponse>;
  verifyEnrollee(
    enrolleeId: string | IPrognosisVerifyEnrolleePayload,
  ): Promise<boolean>;
  requestPreAuthorization(
    input: IHmoProviderRequestPreauthorizationInput,
  ): Promise<PreauthorisationModel>;
  synchronizePreauthorizations(
    input: string,
  ): Promise<PreAuthUtilisationsModel[]>;
  movePreauthorizationToClaims(
    visitDetailId: string | string[],
    visitId?: string,
  ): Promise<IHmoProviderMovePreauthorizationToClaimResponse>;
  submitVisitToClaims?(
    visitId: string,
    staffEmail: string,
  ): Promise<IHmoProviderMovePreauthorizationToClaimResponse>;
  getAgreedTariff(
    procedureCode: IPrognosisGetTariffArgs | IGetAgreedTarrifPayload,
  ): Promise<number | { cost: number; label: string }>;
  addClaim(
    input: IHmoAddClaimInput | IHmoPrognosisAddClaimInput,
  ): Promise<IHmoAddClaimResponse>;
  editClaim(
    input: IHmoEditClaimInput | IHmoPrognosisAddClaimInput,
  ): Promise<IHmoAddClaimResponse>;
  submitClaim(
    claimIds: string[] | IHmoPrognosisAddClaimInput,
  ): Promise<string[] | IHmoAddClaimResponse>;
  synchronizeClaims(args: ISyncClaimArgs): Promise<{
    utilizations: PreAuthUtilisationsModel[];
    status: string;
    batchNumber?: string;
  }>;

  getHospitalPayments(
    input: IHmoProviderHospitalPaymentsArgs,
  ): Promise<HmoHospitalPayment[]>;
  generateHospitalPaymentDocument(): Promise<IHmoProvidersHospitalPaymentsDocumentResponse>;
  getUtilizationTypes(
    utilizationId: string | IPrognosisGetUtilizationTypesPayload,
  ): Promise<IHmoProvidersUtilizationType[]>;
  getVisitationTypes(
    input?: IPrognosisVisitationTypePayload | string,
  ): Promise<IHmoProvidersVisitationType[]>;
  getUtilizationCategories(
    input?: IPrognosisGetUtilizationCategoriesPayload | string,
  ): Promise<IHmoProvidersUtilizationCategory[]>;

  createPreauthorizationReferralRequest?: (
    input: IHmoProviderCreatePreauthorizationReferralRequestInput,
  ) => Promise<PreauthorisationReferralModel>;

  getProviderStaffs(
    providerId: string,
  ): Promise<Array<{ email: string; name: string }>>;
}
export interface IHMODetails {
  id: string;
  name: string;
  email: string;
  phone: string;
  address: string;
  city?: string;
  state?: string;
  zip?: string;
  logo?: string;
  website: string;
  apiEndpoint: string;
  apiVersion: string;
  status: 'active' | 'inactive';
  createdAt: Date;
  updatedAt: Date;
}
export interface IHMOEnrollee {
  id: string;
  hmoProviderId: string;
  hospitalId: string;
  hmoEnrolleeId: string;
  profileId: string;
}

export interface IHMORequest {
  id: string;
  name: string;
  address: string;
  email?: string;
  phoneNumber?: {
    countryCode: string;
    value: string;
    countryName: string;
  };
  website?: string;
  apiEndpoint: string;
  apiVersion?: string;
  status?: 'active' | 'inactive';
}
