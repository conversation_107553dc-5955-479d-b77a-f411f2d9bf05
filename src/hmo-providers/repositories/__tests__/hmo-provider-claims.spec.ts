/* eslint-disable max-lines */
import { NotAcceptableException } from '@nestjs/common';
import Chance from 'chance';
import { DataSource, EntityManager } from 'typeorm';
import { createTestingModule } from './setup';
import { HmoProviderRepository } from '../hmo-provider.repository';
import { HmoPlanTypeFactory } from '@clinify/__mocks__/factories/hmo-provider.factory';
import { NewHmoClaimInput } from '@clinify/hmo-claims/inputs/hmo-claims.input';
import { HmoClaimModel } from '@clinify/hmo-claims/models/hmo-claim.model';
import {
  BenefitCategory,
  BenefitCoverage,
} from '@clinify/hmo-providers/inputs/hmo-plan.input';
import { PaCodeStatus } from '@clinify/hmo-providers/inputs/hmo-provider.input';
import { HmoHospitalModel } from '@clinify/hmo-providers/models/hmo-hospital.model';
import { PreauthorisationReferralModel } from '@clinify/pre-authorisations-referral/models/preauthorisation-referral.model';
import { PreAuthReferralUtilisationsModel } from '@clinify/pre-authorisations-referral/models/utilisations-referral.model';
import { PreAuthUtilisationsModel } from '@clinify/pre-authorisations/models/utilisations.model';
import { UserType } from '@clinify/shared/enums/users';
import { createFacilityPreference } from '@clinify/utils/tests/facility-preference.fixture';
import { createHmoClaim } from '@clinify/utils/tests/hmo-claim.fixture';
import { createHmoProfileFixtures } from '@clinify/utils/tests/hmo-profiles.fixtures';
import { createHmoProviderFixtures } from '@clinify/utils/tests/hmo-provider.fixtures';
import { createHospitals } from '@clinify/utils/tests/hospital.fixtures';
import { createPreauthorization } from '@clinify/utils/tests/preauthorization.fixtures';
import { createUsers } from '@clinify/utils/tests/user.fixtures';

const chance = new Chance();

describe('HmoProviderRepository - Claims', () => {
  let repo: HmoProviderRepository;
  let manager: EntityManager;
  let ds: DataSource;
  let profile;

  beforeAll(async () => {
    const {
      ds: dataSource,
      manager: entityManager,
      repo: repository,
    } = await createTestingModule();

    ds = dataSource;
    manager = entityManager;
    repo = repository;

    const [hospital] = await createHospitals(manager, 1);
    const [{ defaultProfile }] = await createUsers(manager, 1, hospital);
    profile = defaultProfile;
  });

  afterAll(async () => {
    await repo.clear();
    await ds.destroy();
  });

  it('submitClaim(): should submit a claim successfully', async () => {
    const [hmoProvider] = await createHmoProviderFixtures(manager, 1);
    const [hospital] = await createHospitals(
      manager,
      1,
      undefined,
      undefined,
      undefined,
      hmoProvider?.id,
    );
    const [{ defaultProfile }] = await createUsers(manager, 1, hospital);
    await createFacilityPreference(manager, hospital, {
      hmoSingleVisitPACode: true,
      provider: hmoProvider,
      providerId: hmoProvider?.id,
    });
    profile = defaultProfile;
    await manager.save(HmoHospitalModel, {
      providerId: hmoProvider.id,
      hospitalId: hospital.id,
      hmoProviderId: 'HMO-00000001',
    });
    const planInput = HmoPlanTypeFactory.build();
    planInput.benefits[0].utilisationTypes[0].benefitCategory =
      BenefitCategory.FeeForService;
    planInput.benefits[0].utilisationTypes[0].benefitCoverage =
      BenefitCoverage.Covered;
    planInput.benefits[0] = {
      ...planInput.benefits[0],
      waitingPeriodDays: 0,
      annualLimitPerPerson: 0,
      benefitLimit: 0,
    };
    const plan = await repo.createPlan(profile, hmoProvider.id, planInput);

    const [hmoProfile] = await createHmoProfileFixtures(
      manager,
      1,
      profile,
      undefined,
      undefined,
      hmoProvider,
      true,
    );

    await repo.addBeneficiariesToPlan(profile, plan.id, [hmoProfile.id]);
    const [benefit] = plan.benefits;

    const input: NewHmoClaimInput = {
      providerId: hmoProvider.id,
      serviceTypeCode: benefit.visitTypeId,
      serviceName: benefit.visitTypeId,
      serviceType: benefit.visitTypeId,
      claimDate: new Date(),
      enrolleeId: hmoProfile.memberNumber,
      utilizations: [
        {
          utilizationId: benefit.id,
          utilizationCode: benefit.utilisationTypes[0].code,
          quantity: '1',
          category: benefit.utilisationCategory,
          price: benefit.utilisationTypes[0].price,
          type: 'service',
        },
      ],
    };

    const response = await repo.submitClaim(profile, input);

    expect(response).toHaveProperty('visitId');
    expect(response).toHaveProperty('claimId');
    expect(response).toHaveProperty('batchNumber');
    expect(response.totalTariffFee).toEqual(benefit.utilisationTypes[0].price);
    expect(response.utilizations).toHaveLength(1);
    expect(response.utilizations[0].utilizationCode).toEqual(
      benefit.utilisationTypes[0].code,
    );
  });
  it('submitClaim(): should submit a claim successfully with referral code', async () => {
    const [hmoProvider] = await createHmoProviderFixtures(manager, 1);
    const [hospital] = await createHospitals(manager, 1);
    const [{ defaultProfile }] = await createUsers(manager, 1, hospital);
    profile = defaultProfile;
    await manager.save(HmoHospitalModel, {
      providerId: hmoProvider.id,
      hospitalId: hospital.id,
      hmoProviderId: 'HMO-00000001',
    });
    const planInput = HmoPlanTypeFactory.build();
    planInput.benefits[0].utilisationTypes[0].benefitCategory =
      BenefitCategory.FeeForService;
    planInput.benefits[0].utilisationTypes[0].benefitCoverage =
      BenefitCoverage.Covered;
    planInput.benefits[0] = {
      ...planInput.benefits[0],
      waitingPeriodDays: 0,
      annualLimitPerPerson: 0,
      benefitLimit: 0,
    };
    const plan = await repo.createPlan(profile, hmoProvider.id, planInput);

    const [hmoProfile] = await createHmoProfileFixtures(
      manager,
      1,
      profile,
      undefined,
      undefined,
      hmoProvider,
      true,
    );

    await repo.addBeneficiariesToPlan(profile, plan.id, [hmoProfile.id]);
    const [benefit] = plan.benefits;
    const paCode = chance.string({ length: 10 });
    const referralUtil = new PreAuthReferralUtilisationsModel({
      paCode,
      status: PaCodeStatus.Approved,
      utilizationCode: '123456',
    });

    await manager.save(PreauthorisationReferralModel, {
      referredProviderId: profile.hospitalId,
      createdBy: profile,
      creatorName: profile.fullName,
      providerId: hmoProvider.id,
      referredBy: profile.fullName,
      profileId: profile.id,
      requestDateTime: new Date(),
      requestedBy: profile.fullName,
      serviceType: 'Inpatient',
      serviceTypeCode: 'IP',
      hospitalId: hospital.id,
      enrolleeNumber: hmoProfile.memberNumber,
      utilizations: [referralUtil],
    });
    const input: NewHmoClaimInput = {
      providerId: hmoProvider.id,
      serviceTypeCode: benefit.visitTypeId,
      serviceName: benefit.visitTypeId,
      serviceType: benefit.visitTypeId,
      claimDate: new Date(),
      enrolleeId: hmoProfile.memberNumber,
      referralCode: paCode,
      utilizations: [
        {
          utilizationId: benefit.id,
          utilizationCode: benefit.utilisationTypes[0].code,
          quantity: '1',
          category: benefit.utilisationCategory,
          price: benefit.utilisationTypes[0].price,
          type: 'service',
        },
      ],
    };

    const response = await repo.submitClaim(profile, input);

    expect(response).toHaveProperty('visitId');
    expect(response).toHaveProperty('claimId');
    expect(response).toHaveProperty('batchNumber');
    expect(response.totalTariffFee).toEqual(benefit.utilisationTypes[0].price);
    expect(response.utilizations).toHaveLength(1);
    expect(response.utilizations[0].utilizationCode).toEqual(
      benefit.utilisationTypes[0].code,
    );
  });

  it('submitClaim(): should throw NotAcceptableException if plan not found', async () => {
    const input: NewHmoClaimInput = {
      enrolleeId: chance.guid(),
      providerId: chance.guid(),
      serviceTypeCode: 'GC001',
      claimDate: new Date(),
      serviceType: 'General Consultation',
      serviceName: 'General Consultation',
      utilizations: [
        {
          utilizationCode: 'GC001',
          utilizationId: '1',
          quantity: '1',
          price: '1000',
          type: 'service',
          category: 'Outpatient',
        },
      ],
    };

    await expect(repo.submitClaim(profile, input)).rejects.toThrow(
      new NotAcceptableException('Plan Not Found'),
    );
  });

  it('submitClaim(): should throw NotAcceptableException if hospital not authorized for claims submission', async () => {
    const [hmoProvider] = await createHmoProviderFixtures(manager, 1);
    const [hospital] = await createHospitals(manager, 1);
    const [{ defaultProfile }] = await createUsers(manager, 1, hospital);
    profile = defaultProfile;

    const planInput = HmoPlanTypeFactory.build();
    planInput.benefits[0].utilisationTypes[0].benefitCategory =
      BenefitCategory.FeeForService;
    planInput.benefits[0].utilisationTypes[0].benefitCoverage =
      BenefitCoverage.Covered;
    planInput.benefits[0] = {
      ...planInput.benefits[0],
      waitingPeriodDays: 0,
      annualLimitPerPerson: 0,
      benefitLimit: 0,
    };
    const plan = await repo.createPlan(profile, hmoProvider.id, planInput);

    const [hmoProfile] = await createHmoProfileFixtures(
      manager,
      1,
      profile,
      undefined,
      undefined,
      hmoProvider,
      true,
    );

    await repo.addBeneficiariesToPlan(profile, plan.id, [hmoProfile.id]);
    const [benefit] = plan.benefits;

    const input: NewHmoClaimInput = {
      providerId: hmoProvider.id,
      serviceTypeCode: benefit.visitTypeId,
      serviceName: benefit.visitTypeId,
      serviceType: benefit.visitTypeId,
      claimDate: new Date(),
      enrolleeId: hmoProfile.memberNumber,
      utilizations: [
        {
          utilizationId: benefit.id,
          utilizationCode: benefit.utilisationTypes[0].code,
          quantity: '1',
          category: benefit.utilisationCategory,
          price: benefit.utilisationTypes[0].price,
          type: 'service',
        },
      ],
    };

    await expect(repo.submitClaim(profile, input)).rejects.toThrow(
      new NotAcceptableException(
        'Hospital Not Authorized For Claims Submission',
      ),
    );
  });

  it('submitClaim():  should throw NotAcceptableException if Utilizations Already Exist', async () => {
    const [hmoProvider] = await createHmoProviderFixtures(manager, 1);
    const [hospital] = await createHospitals(manager, 1);
    const [{ defaultProfile }] = await createUsers(manager, 1, hospital);
    profile = defaultProfile;
    await manager.save(HmoHospitalModel, {
      providerId: hmoProvider.id,
      hospitalId: hospital.id,
      hmoProviderId: 'HMO-00000001',
    });
    const planInput = HmoPlanTypeFactory.build();
    planInput.benefits[0].utilisationTypes[0].benefitCategory =
      BenefitCategory.FeeForService;
    planInput.benefits[0].utilisationTypes[0].benefitCoverage =
      BenefitCoverage.Covered;
    planInput.benefits[0] = {
      ...planInput.benefits[0],
      waitingPeriodDays: 0,
      annualLimitPerPerson: 0,
      benefitLimit: 0,
    };
    const plan = await repo.createPlan(profile, hmoProvider.id, planInput);

    const [hmoProfile] = await createHmoProfileFixtures(
      manager,
      1,
      profile,
      undefined,
      undefined,
      hmoProvider,
      true,
    );

    await repo.addBeneficiariesToPlan(profile, plan.id, [hmoProfile.id]);
    const [benefit] = plan.benefits;

    const input: NewHmoClaimInput = {
      providerId: hmoProvider.id,
      serviceTypeCode: benefit.visitTypeId,
      serviceName: benefit.visitTypeId,
      serviceType: benefit.visitTypeId,
      claimDate: new Date(),
      enrolleeId: hmoProfile.memberNumber,
      utilizations: [
        {
          utilizationId: benefit.id,
          utilizationCode: benefit.utilisationTypes[0].code,
          quantity: '1',
          category: benefit.utilisationCategory,
          price: benefit.utilisationTypes[0].price,
          type: 'service',
        },
      ],
    };

    const claim = await repo.submitClaim(profile, input);
    await manager.save(
      HmoClaimModel,
      new HmoClaimModel({
        providerId: hmoProvider.id,
        serviceTypeCode: benefit.visitTypeId,
        serviceName: benefit.visitTypeId,
        serviceType: benefit.visitTypeId,
        claimDate: new Date(),
        enrolleeNumber: input.enrolleeId,
        createdBy: profile.createdBy,
        creatorName: profile.fullName,
        updatedDate: new Date(),
        lastModifierId: profile.clinifyId,
        lastModifierName: profile.fullName,
        updatedBy: profile,
        profile,
        status: 'Submitted',
        hospitalId: profile.hospitalId,
        utilizations: claim.utilizations.map(
          (util) =>
            new PreAuthUtilisationsModel({
              ...util,
              createdBy: profile,
              creatorName: profile.fullName,
              createdDate: new Date(),
            }),
        ),
      }),
    );

    await expect(repo.submitClaim(profile, input)).rejects.toThrow(
      new NotAcceptableException('Utilizations Already Exist'),
    );
  });

  it('updatePreAuthUtilizationStatus(): should update the status of a pre-auth utilization', async () => {
    const [hmoProvider] = await createHmoProviderFixtures(manager, 1);
    const [hospital] = await createHospitals(manager, 1);

    const [{ defaultProfile: claimOfficerProfile }] = await createUsers(
      manager,
      1,
      hospital,
      undefined,
      undefined,
      UserType.ClaimOfficer,
    );
    const [{ defaultProfile: ClaimReviewerProfile }] = await createUsers(
      manager,
      1,
      hospital,
      undefined,
      undefined,
      UserType.ClaimReviewer,
    );
    const [{ defaultProfile: claimAdminProfile }] = await createUsers(
      manager,
      1,
      hospital,
      undefined,
      undefined,
      UserType.ClaimAdmin,
    );

    const [hmoClaim] = await createHmoClaim(
      manager,
      1,
      profile,
      undefined,
      undefined,
      hospital,
      {
        claimId: '1145',
        providerId: hmoProvider.id,
        utilizations: null,
      },
    );
    const util = await manager.save(PreAuthUtilisationsModel, {
      hmoProviderId: hmoProvider.id,
      utilizationCode: 'UTIL001',
      status: 'Pending',
      createdBy: ClaimReviewerProfile,
      creatorName: ClaimReviewerProfile.fullName,
      hmoClaimId: hmoClaim.id,
      category: 'Inpatient',
      type: 'service',
      quantity: '1',
      price: '1000',
    });

    let response = await repo.updatePreAuthUtilizationStatus(
      claimOfficerProfile,
      {
        id: util.id,
        status: PaCodeStatus.Approved,
        statusDescription: 'Utilization Approved',
        paCode: 'PA123456',
      },
    );

    let claimStatus = await repo.query(
      `SELECT status FROM hmo_claims WHERE id = '${hmoClaim.id}'`,
    );
    expect(claimStatus[0].status).toBe('Vetted');

    expect(response).toHaveProperty('id');
    expect(response.status).toEqual(PaCodeStatus.Pending);
    expect(response.statusDescription).toEqual('Utilization Approved');
    expect(response.paCode).toEqual('PA123456');

    response = await repo.updatePreAuthUtilizationStatus(ClaimReviewerProfile, {
      id: util.id,
      status: PaCodeStatus.Rejected,
      statusDescription: 'Utilization Rejected',
      paCode: 'PA123456',
    });

    claimStatus = await repo.query(
      `SELECT status FROM hmo_claims WHERE id = '${hmoClaim.id}'`,
    );
    expect(claimStatus[0].status).toBe(PaCodeStatus.Rejected);

    expect(response).toHaveProperty('id');
    expect(response.status).toEqual(PaCodeStatus.Rejected);
    expect(response.statusDescription).toEqual('Utilization Rejected');
    expect(response.paCode).toEqual('PA123456');

    response = await repo.updatePreAuthUtilizationStatus(ClaimReviewerProfile, {
      id: util.id,
      status: PaCodeStatus.Approved,
      statusDescription: 'Utilization Approved',
      paCode: 'PA123456',
    });

    claimStatus = await repo.query(
      `SELECT status FROM hmo_claims WHERE id = '${hmoClaim.id}'`,
    );
    expect(claimStatus[0].status).toBe('Checked');

    expect(response).toHaveProperty('id');
    expect(response.status).toEqual(PaCodeStatus.Pending);
    expect(response.statusDescription).toEqual('Utilization Approved');
    expect(response.paCode).toEqual('PA123456');

    response = await repo.updatePreAuthUtilizationStatus(claimAdminProfile, {
      id: util.id,
      status: PaCodeStatus.Approved,
      statusDescription: 'Utilization Approved',
      paCode: 'PA123456',
    });

    claimStatus = await repo.query(
      `SELECT status FROM hmo_claims WHERE id = '${hmoClaim.id}'`,
    );
    expect(claimStatus[0].status).toBe(PaCodeStatus.Approved);

    expect(response).toHaveProperty('id');
    expect(response.status).toEqual(PaCodeStatus.Approved);
    expect(response.statusDescription).toEqual('Utilization Approved');
    expect(response.paCode).toEqual('PA123456');

    response = await repo.updatePreAuthUtilizationStatus(claimAdminProfile, {
      id: util.id,
      status: PaCodeStatus.Rejected,
      statusDescription: 'Utilization Rejected',
      paCode: 'PA123456',
    });

    claimStatus = await repo.query(
      `SELECT status FROM hmo_claims WHERE id = '${hmoClaim.id}'`,
    );
    expect(claimStatus[0].status).toBe(PaCodeStatus.Rejected);

    expect(response).toHaveProperty('id');
    expect(response.status).toEqual(PaCodeStatus.Rejected);
    expect(response.statusDescription).toEqual('Utilization Rejected');
    expect(response.paCode).toEqual('PA123456');

    response = await repo.updatePreAuthUtilizationStatus(claimAdminProfile, {
      id: util.id,
      status: PaCodeStatus.Approved,
      statusDescription: 'Utilization Approved',
      paCode: 'PA123456',
    });

    claimStatus = await repo.query(
      `SELECT status FROM hmo_claims WHERE id = '${hmoClaim.id}'`,
    );
    expect(claimStatus[0].status).toBe(PaCodeStatus.Approved);

    expect(response).toHaveProperty('id');
    expect(response.status).toEqual(PaCodeStatus.Approved);
    expect(response.statusDescription).toEqual('Utilization Approved');
    expect(response.paCode).toEqual('PA123456');
  });

  it('updatePreAuthUtilizationStatus(): should throw NotAcceptableException if utilization not found', async () => {
    const input = {
      id: chance.guid(),
      status: PaCodeStatus.Approved,
      statusDescription: 'Utilization approved',
      paCode: 'PA123456',
    };

    await expect(
      repo.updatePreAuthUtilizationStatus(profile, input),
    ).rejects.toThrow(NotAcceptableException);
  });

  it('updatePreAuthUtilizationStatus(): should update the status of a claim utilization if pre-auth has a claim', async () => {
    const [hmoProvider] = await createHmoProviderFixtures(manager, 1);
    const [hospital] = await createHospitals(manager, 1);
    const [{ defaultProfile }] = await createUsers(
      manager,
      1,
      hospital,
      undefined,
      undefined,
      UserType.ClaimOfficer,
    );
    profile = defaultProfile;

    const [hmoClaim] = await createHmoClaim(
      manager,
      1,
      profile,
      undefined,
      profile,
      hospital,
      { claimId: '11', providerId: hmoProvider.id },
    );

    const preAuthUtilization = await manager.save(PreAuthUtilisationsModel, {
      hmoProviderId: hmoProvider.id,
      utilizationCode: 'UTIL001',
      status: 'Pending',
      createdBy: profile,
      creatorName: profile.fullName,
      hmoClaimId: hmoClaim.id,
      category: 'Inpatient',
      type: 'service',
      quantity: '1',
      price: '1000',
    });
    const [preAuth] = await createPreauthorization(
      manager,
      1,
      profile,
      undefined,
      profile,
      hospital,
      { claimId: '11', providerId: hmoProvider.id },
    );
    const util = await manager.save(PreAuthUtilisationsModel, {
      hmoProviderId: hmoProvider.id,
      utilizationCode: 'UTIL001',
      status: 'Pending',
      createdBy: profile,
      creatorName: profile.fullName,
      preAuthorizationId: preAuth.id,
      category: 'Inpatient',
      type: 'service',
      quantity: '1',
      price: '1000',
    });
    const input = {
      id: util.id,
      status: PaCodeStatus.Rejected,
      statusDescription: 'Utilization Rejected',
      paCode: 'PA123456',
    };

    const response = await repo.updatePreAuthUtilizationStatus(profile, input);

    expect(response).toHaveProperty('id');
    expect(response.status).toEqual(input.status);
    expect(response.statusDescription).toEqual(input.statusDescription);
    expect(response.paCode).toEqual(input.paCode);

    const updatedClaimUtilization = await manager.findOne(
      PreAuthUtilisationsModel,
      {
        where: {
          utilizationCode: preAuthUtilization.utilizationCode,
          hmoClaimId: hmoClaim.id,
        },
      },
    );

    expect(updatedClaimUtilization).toBeTruthy();
    expect(updatedClaimUtilization.status).toEqual(input.status);
    expect(updatedClaimUtilization.statusDescription).toEqual(
      input.statusDescription,
    );
  });

  it('updatePreAuthUtilizationsStatus(): should update the status of multiple pre-auth utilizations', async () => {
    const [hmoProvider] = await createHmoProviderFixtures(manager, 1);
    const [hospital] = await createHospitals(manager, 1);
    const [{ defaultProfile }] = await createUsers(manager, 1, hospital);
    profile = defaultProfile;

    const [preAuth] = await createPreauthorization(
      manager,
      1,
      undefined,
      undefined,
      undefined,
      hospital,
    );
    await manager.save(PreAuthUtilisationsModel, {
      hmoProviderId: hmoProvider.id,
      utilizationCode: 'UTIL001',
      status: 'Pending',
      createdBy: profile,
      creatorName: profile.fullName,
      preAuthorizationId: preAuth.id,
      category: 'Inpatient',
      type: 'service',
      quantity: '1',
      price: '1000',
    });

    const input = {
      ids: [preAuth.id],
      status: PaCodeStatus.Approved,
      statusDescription: 'Utilization approved',
    };

    const response = await repo.updatePreAuthUtilizationsStatus(profile, input);

    expect(response).toHaveLength(1);
    expect(response[0].utilizations[0].status).toEqual('Approved');
  });

  it('updatePreAuthUtilizationsStatus(): should throw NotAcceptableException if no utilizations found', async () => {
    const input = {
      status: PaCodeStatus.Approved,
      ids: [chance.guid()],
      statusDescription: 'Utilization approved',
    };
    const res = await repo.updatePreAuthUtilizationsStatus(profile, input);
    expect(res).toHaveLength(0);
  });
  it('updateClaimUtilizationsStatus(): should update claim utilizations status successfully', async () => {
    const [hmoProvider] = await createHmoProviderFixtures(manager, 1);
    const [hospital] = await createHospitals(manager, 1);
    const [{ defaultProfile }] = await createUsers(manager, 1, hospital);
    profile = defaultProfile;

    const [hmoClaim] = await createHmoClaim(
      manager,
      1,
      profile,
      undefined,
      undefined,
      hospital,
      { claimId: '11', providerId: hmoProvider.id },
    );

    const input = {
      ids: [hmoClaim.id],
      status: 'Paid' as any,
    };

    const response = await repo.updateClaimUtilizationsStatus(profile, input);

    expect(response).toHaveLength(1);
    expect(response[0].status).toEqual(input.status);
  });

  it('updateClaimUtilizationsStatus(): should throw NotAcceptableException if claim not found', async () => {
    const input = {
      ids: [chance.guid()],
      status: PaCodeStatus.Approved,
    };
    const res = await repo.updateClaimUtilizationsStatus(profile, input);
    expect(res).toHaveLength(0);
  });

  it('updateClaimStatus(): should update claim status successfully', async () => {
    const [hmoProvider] = await createHmoProviderFixtures(manager, 1);
    const [hospital] = await createHospitals(manager, 1);
    const [{ defaultProfile }] = await createUsers(manager, 1, hospital);
    profile = defaultProfile;

    const [hmoClaim] = await createHmoClaim(
      manager,
      1,
      profile,
      undefined,
      undefined,
      hospital,
      { claimId: '11', providerId: hmoProvider.id },
    );
    const updatedClaim = await repo.updateClaimStatus(
      profile,
      'Paid',
      hmoClaim.id,
    );

    expect(updatedClaim).toHaveProperty('id');
    expect(updatedClaim.status).toEqual('Paid');
    expect(updatedClaim.paidBy).toEqual(profile.fullName);
    expect(updatedClaim.updatedBy.id).toEqual(profile.id);
  });

  it('updateClaimStatus(): should throw NotAcceptableException if claim not found', async () => {
    await expect(
      repo.updateClaimStatus(profile, 'Approved', chance.guid()),
    ).rejects.toThrow(NotAcceptableException);
  });
});
