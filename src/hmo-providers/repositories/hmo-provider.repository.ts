/* eslint-disable prefer-arrow/prefer-arrow-functions */
/* eslint-disable max-lines */
/* eslint-disable @typescript-eslint/quotes */
import {
  BadRequestException,
  NotAcceptableException,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import moment from 'moment';
import { Between, DataSource, In, IsNull, Not, Repository } from 'typeorm';
import {
  GetAgreedTariffInput,
  HmoFilterOptions,
  HmoPlanBeneficiariesFilterOptions,
  HmoPlanFilterOptions,
  PaCodeStatus,
  UpdateBenefitCustomPriceInput,
  UpdatePreAuthUtilizationStatusInput,
  UpdateUtilizationsStatusInput,
} from '../inputs/hmo-provider.input';
import { HmoProviderModel } from '../models/hmo-provider.model';
import {
  HmoBeneficiary,
  HmoBenefits,
  HmoHospitalResponse,
  HmoPlanBeneficiariesResponse,
  HmoPlanTypesResponse,
  HmoProviderResponse,
  OptionObject,
  UtilizationTypeObject,
} from '../responses/hmo.response';
import { type BusinessRuleService } from '@clinify/cms/services/business-rule.service';
import { CustomRepository } from '@clinify/custom-repository/decorators/custom-repo.decorator';
import { queryDSWithSlave } from '@clinify/database';
import { EmployeeDependantModel } from '@clinify/employer/models/employee-dependant.model';
import { EmployeeModel } from '@clinify/employer/models/employee.model';
import { EmployerModel } from '@clinify/employer/models/employer.model';
import { FacilityPreferenceModel } from '@clinify/facility-preferences/models/facility-preference.model';
import { NewHmoClaimInput } from '@clinify/hmo-claims/inputs/hmo-claims.input';
import { HmoClaimModel } from '@clinify/hmo-claims/models/hmo-claim.model';
import { LastCheckInData } from '@clinify/hmo-profiles/inputs/hmo-profile.input';
import { HmoProfileModel } from '@clinify/hmo-profiles/models/hmo-profile.model';
import {
  CLINIFY_HMO_AGENCY_CODES,
  CLINIFY_HMO_AGENCY_PREFIX_CODE,
} from '@clinify/hmo-providers/constants/constants';
import { BenefitCustomPrice } from '@clinify/hmo-providers/dtos/BenefitCustomPrice';
import {
  BenefitCategory,
  BenefitCoverage,
  HmoPlanInput,
  HmoPlanStatus,
  HMOTariffBand,
  NewHmoPlanInput,
} from '@clinify/hmo-providers/inputs/hmo-plan.input';
import {
  IHmoAddClaimResponse,
  IValidateExternalUtilisationTypeRequests,
  IValidateUtilisationTypeRequests,
} from '@clinify/hmo-providers/interface/hmo-providers.interface';
import { HmoHospitalModel } from '@clinify/hmo-providers/models/hmo-hospital.model';
import { HmoPlanBenefitModel } from '@clinify/hmo-providers/models/hmo-plan-benefit.model';
import { HmoPlanTypeModel } from '@clinify/hmo-providers/models/hmo-plan-type.model';
import { HmoProfileBenefitModel } from '@clinify/hmo-providers/models/hmo-profile-benefit.model';
import { HmoProfilePlanModel } from '@clinify/hmo-providers/models/hmo-profile-plan.model';
import { HmoVisitTypeModel } from '@clinify/hmo-providers/models/hmo-visit-type.model';
import { HmoVisitationModel } from '@clinify/hmo-providers/models/hmo-visitation.model';
import { validateHmoPlanTypeRemoval } from '@clinify/hmo-providers/validators/validate-delete';
import { HospitalModel } from '@clinify/hospitals/models/hospital.model';
import { PreauthorisationReferralInput } from '@clinify/pre-authorisations-referral/inputs/preauthorisation-referral.input';
import { PreauthorisationReferralModel } from '@clinify/pre-authorisations-referral/models/preauthorisation-referral.model';
import { PreAuthReferralUtilisationsModel } from '@clinify/pre-authorisations-referral/models/utilisations-referral.model';
import { ClaimsApprovalInput } from '@clinify/pre-authorisations/inputs/approval-detail.input';
import { FlagDto } from '@clinify/pre-authorisations/inputs/flag.dto';
import { PreauthUtilisationInput } from '@clinify/pre-authorisations/inputs/preauth-utilisation.input';
import {
  PreauthorisationInput,
  PreauthorisationUpdateInput,
} from '@clinify/pre-authorisations/inputs/preauthorisation.input';
import { PreauthorisationModel } from '@clinify/pre-authorisations/models/preauthorisation.model';
import { PreAuthUtilisationsModel } from '@clinify/pre-authorisations/models/utilisations.model';
import { HmoClaimStatus } from '@clinify/shared/enums/hmo-claims';
import { HospitalPlan } from '@clinify/shared/enums/hospital';
import { UserType } from '@clinify/shared/enums/users';
import {
  getMembershipNoFromMemberNo,
  getTargetRole,
  isPrincipalEnrollee,
  modifyClaimStatus,
} from '@clinify/shared/helper';
import { ClaimStatus } from '@clinify/shared/hmo-providers/modules/leadway/leadway.interface';
import { DependentModel } from '@clinify/users/models';
import { CoverageInformationModel } from '@clinify/users/models/coverage-information.model';
import { ProfileModel } from '@clinify/users/models/profile.model';
import {
  generateBatchNumber,
  isLASHMA,
  memCache,
} from '@clinify/utils/helpers';
import {
  getClaimNumberCollection,
  getPaCodeNumberCollection,
  getPrincipalMembershipNoGeneratorLASHMA,
} from '@clinify/utils/mongo.utils';
import { takePaginatedResponses } from '@clinify/utils/pagination';

const utilisationCategoryPriceControl = /drug|medication/i;
const PLAN_TYPES_PREFIX = { LSHPB: 'LSHSB' };

@CustomRepository(HmoProviderModel)
export class HmoProviderRepository extends Repository<HmoProviderModel> {
  get paGenerator() {
    return {
      ['107']: this.generateLASHMAPACode.bind(this),
      ['20']: this.generateLASHMAPACode.bind(this),
    };
  }
  async getProviderPreference(providerId: string) {
    const facilityPreference = await this.manager
      .createQueryBuilder(FacilityPreferenceModel, 'facility_preference')
      .innerJoinAndSelect('facility_preference.hospital', 'hospital')
      .where('hospital.hmo_id = :providerId', {
        providerId,
      })
      .select([
        'facility_preference.customPaFormatType',
        'facility_preference.hmoSingleVisitPACode',
      ])
      .getOne();
    return facilityPreference;
  }
  async fetchHmos(
    filterOptions: HmoFilterOptions,
  ): Promise<HmoProviderResponse> {
    const { skip, take, name } = filterOptions;
    let query = this.createQueryBuilder('hmo_providers');

    if (name) {
      query = query.where('LOWER(name) like :hmoName', {
        hmoName: '%' + name.toLowerCase() + '%',
      });
    }
    const hmos = await query.offset(skip).limit(take).getManyAndCount();

    return new HmoProviderResponse(...takePaginatedResponses(hmos, take));
  }
  async updateLastCheckinInformation(
    enrolleeId: string,
    providerId: string,
    checkInData: LastCheckInData,
  ) {
    const hmoProfile = await this.manager.findOne(HmoProfileModel, {
      where: {
        memberNumber: enrolleeId,
        providerId,
      },
    });
    if (hmoProfile) {
      hmoProfile.lastCheckIn = [checkInData, ...(hmoProfile.lastCheckIn || [])];
      await this.manager.save(hmoProfile);
    }
    return {
      id: hmoProfile?.id,
      memberNumber: enrolleeId,
      lastCheckinDate: new Date(),
      checkedIn: true,
      verificationCode: checkInData.verificationCode,
      visitationId: checkInData.visitId,
    };
  }

  async getHmoProviderByFacilityId(facilityId, filterOptions) {
    const { skip, take } = filterOptions;
    const query = this.createQueryBuilder('hmo_providers')
      .innerJoin('hmo_providers.hmoHospitals', 'hmo_hospitals')
      .where('hmo_hospitals.hospitalId = :facilityId', { facilityId });

    const hmos = await query.offset(skip).limit(take).getManyAndCount();

    return new HmoProviderResponse(...takePaginatedResponses(hmos, take));
  }

  async getRegisteredHmoHospitals(
    datasource: DataSource,
    hmoId: string,
    filterOptions: HmoFilterOptions,
  ): Promise<HmoHospitalResponse> {
    const { skip, take, name, providersIn, providersNotIn } = filterOptions;
    const params = [hmoId, take, skip];
    if (name) params.push(`%${name}%`);
    const res: HospitalModel[] = await queryDSWithSlave(
      datasource,
      `SELECT hospitals.id, hospitals.name, hospitals.plan, hospitals.address, hospitals."planStatus" AS "planStatus"
        FROM hmo_hospitals
        INNER JOIN hospitals ON hmo_hospitals.hospital_id = hospitals.id
        WHERE hmo_hospitals.provider_id = $1
        ${name ? 'AND hospitals.name ILIKE $4' : ''}
        ${
          providersIn?.length
            ? `AND hospitals.id IN ('${providersIn.join("','")}')`
            : ''
        }
        ${
          providersNotIn?.length
            ? `AND hospitals.id NOT IN ('${providersNotIn.join("','")}')`
            : ''
        }
        ORDER BY hospitals.name ASC
        LIMIT $2 OFFSET $3`,
      params,
    );

    return new HmoHospitalResponse(res, res.length);
  }

  async getHmoHospitalByProviderId(
    datasource: DataSource,
    providerId: string,
    filterOptions: HmoFilterOptions,
  ): Promise<HmoHospitalResponse> {
    const { skip, take, name, lga, plans } = filterOptions;
    const params: any[] = [providerId, take, skip];
    let paramIndex = 4;

    const conditions = [];
    if (name) {
      params.push(`%${name}%`);
      conditions.push(`hospitals.name ILIKE $${paramIndex++}`);
    }

    if (lga) {
      params.push(`${lga}`);
      conditions.push(`hospitals.lga ILIKE $${paramIndex++}`);
    }

    if (plans && plans.length > 0) {
      params.push(`${plans}%`);
      conditions.push(`hospitals.plan ILIKE $${paramIndex++}`);
    }

    const whereClause =
      conditions.length > 0 ? `AND ${conditions.join(' AND ')}` : '';

    const res: HospitalModel[] = await queryDSWithSlave(
      datasource,
      `SELECT hospitals.id, hospitals.name, hospitals.address, hospitals.plan, hospitals.lga
        FROM hmo_hospitals
        INNER JOIN hospitals ON hmo_hospitals.hospital_id = hospitals.id
        WHERE hmo_hospitals.provider_id = $1
        ${whereClause}
        ORDER BY hospitals.name ASC
        LIMIT $2 OFFSET $3`,
      params,
    );

    return new HmoHospitalResponse(res, res.length);
  }

  async createPlan(
    mutator: ProfileModel,
    providerId: string,
    input: NewHmoPlanInput,
  ) {
    const planTypeObj = new HmoPlanTypeModel({
      name: input.name,
      hmoProviderId: providerId,
      premiumCountry: input.premiumCountry,
      status: input.status,
      planDateTime: input.planDateTime,
      plannedBy: input.plannedBy,
      creatorName: mutator.fullName,
      createdBy: mutator,
      hospital: mutator.hospital,
      premiumDetails: input.premiumDetails,
      isExternalPlan: input.isExternalPlan,
      isSponsor: input.isSponsor,
      planCode: input.planCode,
    });
    const newPlanType = await this.manager.save(HmoPlanTypeModel, planTypeObj);
    const uniqueVisitTypes = [
      ...new Set(input.benefits.map((b) => b.visitType)),
    ];
    const visiTypes = await this.manager.save(
      HmoVisitTypeModel,
      uniqueVisitTypes.map(
        (name) =>
          new HmoVisitTypeModel({
            name,
            planTypeId: newPlanType.id,
            createdBy: mutator,
            creatorName: mutator.fullName,
            hmoProviderId: providerId,
          }),
      ),
    );

    const benefits = input.benefits.map((benefit) => {
      const visitType = visiTypes.find((e) => e.name === benefit.visitType);
      const planBenefits = new HmoPlanBenefitModel({
        name: benefit.utilisationCategory,
        utilisationCategory: benefit.utilisationCategory,
        limit: benefit.benefitLimit,
        visitLimit: benefit.visitLimit,
        annualLimitPerPerson: benefit.annualLimitPerPerson,
        waitingPeriodDays: benefit.waitingPeriodDays,
        hmoProviderId: providerId,
        visitTypeId: visitType.id,
        createdBy: mutator,
        utilisationTypes: benefit.utilisationTypes,
        planTypeId: newPlanType.id,
        creatorId: mutator.id,
        creatorName: mutator.fullName,
        code: benefit.code,
      });
      return planBenefits;
    });
    const savedBenefits = await this.manager.save(
      HmoPlanBenefitModel,
      benefits,
    );
    newPlanType.benefits = savedBenefits;
    newPlanType.visitTypes = visiTypes;
    return newPlanType;
  }
  async updatePlan(mutator: ProfileModel, input: HmoPlanInput) {
    const oldPlan = await this.manager
      .findOneOrFail(HmoPlanTypeModel, {
        where: {
          id: input.id,
        },
        relations: [
          'visitTypes',
          'visitTypes.benefits',
          'benefits',
          'benefits.visitType',
        ],
      })
      .catch(() => {
        throw new NotAcceptableException('Plan Not Found');
      });
    oldPlan.name = input.name;
    oldPlan.premiumCountry = input.premiumCountry;
    oldPlan.status = input.status;
    oldPlan.planDateTime = input.planDateTime;
    oldPlan.plannedBy = input.plannedBy;
    oldPlan.updatedBy = mutator;
    oldPlan.updatedDate = new Date();
    oldPlan.lastModifierName = mutator.fullName;
    oldPlan.premiumDetails = input.premiumDetails;
    oldPlan.isExternalPlan = input.isExternalPlan;
    await this.manager.save(HmoPlanTypeModel, oldPlan);
    // compare and update visit types and benefits
    const oldVisitTypes = oldPlan.visitTypes;
    const toDeleteVisitTypes = oldVisitTypes.filter(
      (v) => !input.benefits.find((b) => b.visitTypeId === v.id),
    );
    const toUpdateVisitTypes = oldVisitTypes.filter((v) =>
      input.benefits.find(
        (b) => b.visitTypeId === v.id && b.visitType !== v.name,
      ),
    );
    const toCreateVisitTypes = input.benefits.filter(
      (b) => !oldVisitTypes.find((v) => v.id === b.visitTypeId),
    );
    if (toDeleteVisitTypes.length) {
      const hmoProfileBenefits = await this.manager.count(
        HmoProfileBenefitModel,
        {
          where: {
            benefitId: In(
              toDeleteVisitTypes
                .map((v) => v.benefits.map((b) => b.id))
                .flat(1),
            ),
          },
        },
      );
      if (hmoProfileBenefits) {
        throw new NotAcceptableException('You Cannot Delete Benefits');
      }
      await this.manager.delete(HmoVisitTypeModel, toDeleteVisitTypes);
    }
    if (toUpdateVisitTypes.length) {
      await Promise.all(
        toUpdateVisitTypes.map(async (v) => {
          const update = input.benefits.find((b) => b.visitTypeId === v.id);
          await this.manager.update(HmoVisitTypeModel, v.id, {
            name: update.visitType,
            updatedBy: mutator,
            updatedDate: new Date(),
            lastModifierName: mutator.fullName,
          });
        }),
      );
    }

    if (toCreateVisitTypes.length) {
      const newVisitTypes = await this.manager.save(
        HmoVisitTypeModel,
        toCreateVisitTypes.map(
          (v) =>
            new HmoVisitTypeModel({
              name: v.visitType,
              planTypeId: oldPlan.id,
              createdBy: mutator,
              creatorName: mutator.fullName,
              hmoProviderId: oldPlan.hmoProviderId,
            }),
        ),
      );
      oldPlan.visitTypes = [...oldVisitTypes, ...newVisitTypes];
    }

    let benefits = oldPlan.benefits;
    const toDeleteBenefits = benefits.filter(
      (b) => !input.benefits.find((v) => v.id === b.id),
    );
    const toUpdateBenefits = benefits.filter((b) =>
      input.benefits.find(
        (v) =>
          v.id === b.id &&
          (JSON.stringify(v.utilisationTypes) !==
            JSON.stringify(b.utilisationTypes) ||
            v.utilisationCategory !== b.utilisationCategory ||
            v.benefitLimit !== b.limit ||
            v.visitLimit !== b.visitLimit ||
            v.annualLimitPerPerson !== b.annualLimitPerPerson ||
            v.waitingPeriodDays !== b.waitingPeriodDays),
      ),
    );

    const toCreateBenefits = input.benefits.filter(
      (b) => !benefits.find((v) => v.id === b.id),
    );

    if (toDeleteBenefits.length) {
      const hmoProfileBenefits = await this.manager.count(
        HmoProfileBenefitModel,
        {
          where: {
            benefitId: In(toDeleteBenefits.map((b) => b.id)),
          },
        },
      );
      if (hmoProfileBenefits) {
        throw new NotAcceptableException('You Cannot Delete Benefits');
      }
      await this.manager.delete(HmoPlanBenefitModel, toDeleteBenefits);
      benefits = benefits.filter(
        (b) => !toDeleteBenefits.find((v) => v.id === b.id),
      );
    }

    if (toUpdateBenefits.length) {
      for (const benefit of toUpdateBenefits) {
        const newBenefit = input.benefits.find((v) => v.id === benefit.id);

        const update = {
          utilisationTypes: newBenefit.utilisationTypes,
          utilisationCategory: newBenefit.utilisationCategory,
          name: newBenefit.utilisationCategory,
          limit: newBenefit.benefitLimit,
          visitLimit: newBenefit.visitLimit,
          annualLimitPerPerson: newBenefit.annualLimitPerPerson,
          waitingPeriodDays: newBenefit.waitingPeriodDays,
          updatedBy: mutator,
          updatedDate: new Date(),
          lastModifierName: mutator.fullName,
          code: newBenefit.code,
        };
        await this.manager.update(HmoPlanBenefitModel, benefit.id, update);
        benefits = benefits.map((b) => {
          if (b.id === benefit.id) return { ...b, ...update };
          return b;
        });
      }
    }

    if (toCreateBenefits.length) {
      const newBenefits = await this.manager.save(
        HmoPlanBenefitModel,
        toCreateBenefits.map((b) => {
          const visitType = oldPlan.visitTypes.find(
            (v) => v.id === b.visitTypeId || v.name === b.visitType,
          );
          return new HmoPlanBenefitModel({
            name: b.utilisationCategory,
            utilisationCategory: b.utilisationCategory,
            limit: b.benefitLimit,
            visitLimit: b.visitLimit,
            annualLimitPerPerson: b.annualLimitPerPerson,
            waitingPeriodDays: b.waitingPeriodDays,
            hmoProviderId: oldPlan.hmoProviderId,
            visitType,
            visitTypeId: visitType.id,
            createdBy: mutator,
            creatorName: mutator.fullName,
            utilisationTypes: b.utilisationTypes,
            planTypeId: oldPlan.id,
            creatorId: mutator.id,
            code: b.code,
          });
        }),
      );
      benefits = [...benefits, ...newBenefits];
      oldPlan.visitTypes.forEach((v) => {
        v.benefits = [...(v.benefits || []), ...newBenefits];
      });
    }
    oldPlan.benefits = benefits;
    await this.manager
      .createQueryBuilder(HmoProfileModel, 'hmo_profiles')
      .update(HmoProfileModel)
      .set({ memberPlan: input.name })
      .where('hmo_profiles.member_plan_id = :memberPlanId', {
        memberPlanId: oldPlan.id,
      })
      .execute();
    return oldPlan;
  }
  async deletePlans(
    mutator: ProfileModel,
    ids: string[],
  ): Promise<HmoPlanTypeModel[]> {
    const planTypes = await this.manager.find(HmoPlanTypeModel, {
      where: { id: In(ids) },
      relations: ['createdBy'],
    });
    const validResources = validateHmoPlanTypeRemoval(mutator, planTypes);
    if (!validResources.length) {
      return [];
    }

    const hmoProfilePlans = await this.manager.count(HmoProfilePlanModel, {
      where: {
        planTypeId: In(validResources.map(({ id }) => id)),
      },
    });
    if (hmoProfilePlans) {
      throw new NotAcceptableException('You Cannot Delete Benefits');
    }
    await this.manager.delete(
      HmoPlanTypeModel,
      validResources.map(({ id }) => id),
    );

    return validResources;
  }
  async archivePlans(
    mutator: ProfileModel,
    ids: string[],
    archive: boolean,
  ): Promise<HmoPlanTypeModel[]> {
    const planTypes = await this.manager.find(HmoPlanTypeModel, {
      where: { id: In(ids) },
      relations: ['createdBy'],
    });
    const validResources = validateHmoPlanTypeRemoval(mutator, planTypes);

    await this.manager
      .createQueryBuilder(HmoPlanTypeModel, 'hmo_plan_types')
      .update(HmoPlanTypeModel)
      .set({
        archived: archive,
        updatedDate: () => 'updated_date',
        updatedBy: mutator,
        lastModifierName: mutator.fullName,
      })
      .whereInIds(validResources.map(({ id }) => id))
      .execute();
    validResources.map((plan) => {
      plan.archived = archive;
      plan.updatedDate = new Date();
      plan.updatedBy = mutator;
      plan.lastModifierName = mutator.fullName;
      return plan;
    });
    return validResources;
  }

  async fetchPlanTypes(
    hospitalId: string,
    filterOptions: HmoPlanFilterOptions,
  ): Promise<HmoPlanTypesResponse> {
    const {
      keyword,
      dateRange,
      skip = 0,
      take = 50,
      category,
      status,
      archive,
    } = filterOptions;
    let query = this.manager
      .createQueryBuilder(HmoPlanTypeModel, 'hmo_plan_types')
      .leftJoinAndSelect('hmo_plan_types.createdBy', 'createdBy')
      .leftJoinAndSelect('hmo_plan_types.updatedBy', 'updatedBy')
      .where('hmo_plan_types.hospital_id = :hospitalId', {
        hospitalId,
      })
      .andWhere('(hmo_plan_types.archived = :archived)', {
        archived: !!archive,
      });

    if (keyword) {
      query = query.andWhere(
        `hmo_plan_types.name ILIKE :keyword
        OR EXISTS (SELECT 1 FROM jsonb_array_elements(hmo_plan_types.premium_details)
        pd WHERE pd->>'category' LIKE :keyword)`,
        {
          keyword: `%${keyword}%`,
        },
      );
    }

    if (dateRange?.from) {
      query = query.andWhere('(hmo_plan_types.plan_date_time >= :from)', {
        from: dateRange.from,
      });
    }

    if (dateRange?.to) {
      query = query.andWhere('(hmo_plan_types.plan_date_time < :to)', {
        to: dateRange.to,
      });
    }

    if (category) {
      query = query.andWhere(
        `EXISTS (SELECT 1 FROM jsonb_array_elements(hmo_plan_types.premium_details) pd WHERE pd->>'category' = :category)`,
        {
          category,
        },
      );
    }

    if (status) {
      query = query.andWhere('hmo_plan_types.status = :status', {
        status,
      });
    }

    query = query
      .orderBy('hmo_plan_types.createdDate', 'DESC')
      .skip(skip)
      .take(take);

    const response = await query.getManyAndCount();
    if (response[0]?.length) {
      for (const plan of response[0]) {
        const beneficiariesCount = await this.manager.count(HmoProfileModel, {
          where: {
            memberPlanId: plan.id,
            providerId: plan.hmoProviderId,
          },
        });
        plan.beneficiariesCount = beneficiariesCount;
      }
    }
    return new HmoPlanTypesResponse(...takePaginatedResponses(response, take));
  }
  async fetchPlanTypesByProviderId(
    providerId: string,
    filterOptions: HmoPlanFilterOptions,
  ): Promise<HmoPlanTypesResponse> {
    const {
      keyword,
      dateRange,
      skip = 0,
      take = 50,
      category,
      status,
      archive,
    } = filterOptions;
    let query = this.manager
      .createQueryBuilder(HmoPlanTypeModel, 'hmo_plan_types')
      .leftJoinAndSelect('hmo_plan_types.hospital', 'hospital')
      .where('hospital.hmoId = :hmoId', { hmoId: providerId })
      .andWhere('(hmo_plan_types.archived = :archived)', {
        archived: !!archive,
      });

    if (keyword) {
      query = query.andWhere(
        `hmo_plan_types.name like :keyword
        OR EXISTS (SELECT 1 FROM jsonb_array_elements(hmo_plan_types.premium_details)
        pd WHERE pd->>'category' LIKE :keyword)`,
        {
          keyword: `%${keyword}%`,
        },
      );
    }

    if (dateRange?.from) {
      query = query.andWhere('(hmo_plan_types.plan_date_time >= :from)', {
        from: dateRange.from,
      });
    }

    if (dateRange?.to) {
      query = query.andWhere('(hmo_plan_types.plan_date_time < :to)', {
        to: dateRange.to,
      });
    }

    if (category) {
      query = query.andWhere(
        `EXISTS (SELECT 1 FROM jsonb_array_elements(hmo_plan_types.premium_details) pd WHERE pd->>'category' = :category)`,
        {
          category,
        },
      );
    }

    if (status) {
      query = query.andWhere('hmo_plan_types.status = :status', {
        status,
      });
    }

    query = query
      .orderBy('hmo_plan_types.createdDate', 'DESC')
      .skip(skip)
      .take(take);

    const response = await query.getManyAndCount();
    return new HmoPlanTypesResponse(...takePaginatedResponses(response, take));
  }
  async getPlanTypeById(planId: string) {
    return this.manager
      .createQueryBuilder(HmoPlanTypeModel, 'hmoPlanType')
      .leftJoinAndSelect('hmoPlanType.benefits', 'benefits')
      .leftJoinAndSelect('benefits.visitType', 'visitType')
      .where('hmoPlanType.id = :planId', { planId })
      .orderBy('visitType.name', 'ASC')
      .getOne();
  }
  async getVisitationTypes(providerId: string, planId: string) {
    if (!planId) {
      return [];
    }
    const getVisitTypes = await this.manager.find(HmoVisitTypeModel, {
      where: {
        planTypeId: planId,
        hmoProviderId: providerId,
      },
    });
    return getVisitTypes.map((e) => new OptionObject(e.name, e.id));
  }

  async getUtilisationCategories(providerId: string, visitTypeId: string) {
    const getUtilisationCategories = await this.manager
      .createQueryBuilder(HmoPlanBenefitModel, 'benefit')
      .where('benefit.hmoProviderId = :providerId', { providerId })
      .andWhere('benefit.visitTypeId = :visitTypeId', { visitTypeId })
      .distinctOn(['benefit.utilisationCategory'])
      .getMany();
    return getUtilisationCategories.map(
      (e) => new OptionObject(e.utilisationCategory, e.id),
    );
  }

  async getUtilisationTypes(
    providerId: string,
    visitTypeId: string,
    category: string,
  ) {
    const getUtilisationTypes = await this.manager
      .createQueryBuilder(HmoPlanBenefitModel, 'benefit')
      .where('benefit.hmoProviderId = :providerId', { providerId })
      .andWhere('benefit.visitTypeId = :visitTypeId', { visitTypeId })
      .andWhere('benefit.id = :category', { category })
      .select('benefit.utilisationTypes')
      .getOne();
    return (
      getUtilisationTypes?.utilisationTypes?.map(
        (e) => new OptionObject(e.name, e.code),
      ) || []
    );
  }

  async getAllUtilizationTypes(
    dataSource: DataSource,
    providerId: string,
    visitTypeId: string,
    keyword: string,
  ): Promise<UtilizationTypeObject[]> {
    return queryDSWithSlave(
      dataSource,
      `
    SELECT
    elem->> 'code' AS "code",
    elem->>'name' AS "label",
    elem->>'id' AS "value",
    hmo_plan_benefits.id AS "hmoPlanBenefitId",
    hmo_plan_benefits.utilisation_category AS "utilizationCategory"
    FROM hmo_plan_benefits,
         jsonb_array_elements(utilisation_types) AS elem
    WHERE
        hmo_plan_benefits.hmo_provider_id = $1
      AND hmo_plan_benefits.visit_type_id = $2
      AND elem->>'name' ILIKE '%' || $3 || '%'
    `,
      [providerId, visitTypeId, keyword],
    );
  }

  async findUtilizationType(
    dataSource: DataSource,
    providerId: string,
    visitTypeId: string,
    keyword: string,
  ): Promise<UtilizationTypeObject> {
    const res = await queryDSWithSlave(
      dataSource,
      `
    SELECT
    elem->> 'code' AS "code",
    elem->>'name' AS "label",
    elem->>'id' AS "value",
    hmo_plan_benefits.id AS "hmoPlanBenefitId",
    hmo_plan_benefits.utilisation_category AS "utilizationCategory"
    FROM hmo_plan_benefits,
         jsonb_array_elements(utilisation_types) AS elem
    WHERE
        hmo_plan_benefits.hmo_provider_id = $1
      AND hmo_plan_benefits.visit_type_id = $2
      AND elem->>'name' <-> $3 < 0.8
    ORDER BY similarity (elem->> 'name', $3) DESC
    LIMIT 1
    `,
      [providerId, visitTypeId, keyword],
    );
    return res[0];
  }

  async findUtilizationTypeByVisitType(
    dataSource: DataSource,
    providerId: string,
    planTypeId: string,
    visitTypeId: string,
    codes: string[],
  ): Promise<UtilizationTypeObject[]> {
    const res = await queryDSWithSlave(
      dataSource,
      `SELECT
      elem ->> 'code' AS "code",
      elem ->> 'name' AS "label",
      elem ->> 'id' AS "value",
      hpb.id AS "hmoPlanBenefitId",
      hpb.utilisation_category AS "utilizationCategory",
      hvt.id as "visitTypeId"
    FROM
      hmo_plan_benefits hpb
      INNER JOIN hmo_visit_types hvt ON hvt.ID = hpb.visit_type_id,
      jsonb_array_elements(utilisation_types) AS elem
    WHERE
      hpb.hmo_provider_id = '${providerId}'
      AND hpb.plan_type_id = '${planTypeId}'
      AND hvt.name ILIKE '${visitTypeId}'
        AND elem ->> 'code' IN ('${codes.join("','")}')
    `,
    );
    return res;
  }
  async getProviderBandKey(
    providerId: string,
    hospitalId: string,
  ): Promise<string> {
    const hmoHospital = await this.manager.findOne(HmoHospitalModel, {
      where: {
        hospitalId,
        providerId,
      },
    });
    const priceKey = {
      [HMOTariffBand.C]: 'bandCPrice',
      [HMOTariffBand.B]: 'bandBPrice',
      [HMOTariffBand.A]: 'price',
    }[hmoHospital?.tariffBand || HMOTariffBand.A];
    return priceKey;
  }
  async getEnrolleeBenefits(
    dataSource: DataSource,
    args: {
      providerId: string;
      planTypeId: string;
      profileId: string;
    },
  ) {
    const startOfYear = moment().startOf('year').toDate().toISOString();
    const endOfYear = moment().endOf('year').toDate().toISOString();
    const hmoProfile = await this.manager.findOne(HmoProfileModel, {
      where: {
        profileId: args.profileId,
        providerId: args.providerId,
        memberPlanId: Not(IsNull()),
      },
    });

    if (!hmoProfile || !hmoProfile?.primaryProviderId) {
      return [];
    }

    const benefits: {
      utilisationCategory: string;
      amountUsed: number;
      limitAmount?: number;
      quantityLimit?: string;
      quantityUsed?: string;
      visitUsed?: string;
      visitLimit?: string;
      visitType?: string;
    }[] = await queryDSWithSlave(
      dataSource,
      `WITH
  unnested_benefit_types AS (
    SELECT
      hpb.plan_type_id,
      (elem ->> 'code') as benefit_type_code,
      elem ->> 'name' as benefit_type_name,
      (elem ->> 'benefitLimit') as "limitAmount",
      (elem ->> 'quantity')::int as "quantityLimit",
      COALESCE(
        (elem ->> 'annualLimitPerPerson')::int,
        (elem ->> 'visitLimit')::int
      ) as "visitLimit",
      hvt.name AS visit_type
    FROM
      hmo_plan_benefits hpb
      INNER JOIN hmo_visit_types hvt ON hvt.id = hpb.visit_type_id,
      jsonb_array_elements(hpb.utilisation_types) AS elem
    WHERE
      hpb.utilisation_types IS NOT NULL
      AND jsonb_array_length(hpb.utilisation_types) > 0
      AND hpb.plan_type_id = $2
  ),
  util_used AS (
    SELECT
      "utilizationCode" as benefit_type_code,
      profile_id,
      SUM(
        utl.quantity::int * CAST(COALESCE(NULLIF(utl.price, ''), '0') AS DECIMAL)
      ) as amount_used,
      SUM(
        CAST(COALESCE(NULLIF(utl.quantity, ''), '0') AS INT)
      ) AS quantity_Used,
      COUNT(DISTINCT utl.hmo_claim) as visit_used
    FROM
      pre_auth_utilisations utl
    WHERE
      utl.hmo_provider_id = $1
      AND utl.status != 'Rejected'
      AND utl.hmo_claim IS NOT NULL
      AND utl.profile_id = $3
      AND (
        utl.created_date >= $4
        AND utl.created_date <= $5
      )
    GROUP BY
      "utilizationCode",
      profile_id,
      visit_type_id
  )
SELECT
  ubt.benefit_type_name as "utilisationCategory",
  COALESCE(SUM(util_used.amount_used), 0) as "amountUsed",
  ubt."visitLimit",
  ubt."limitAmount",
  ubt."quantityLimit",
  COALESCE(SUM(util_used.quantity_Used), 0) as "quantityUsed",
  ubt.visit_type as "visitType",
  util_used.visit_used as "visitUsed"
FROM
  hmo_profile_plan hpp
  INNER JOIN unnested_benefit_types ubt ON ubt.plan_type_id = hpp.hmo_plan_type_id
  LEFT JOIN util_used ON util_used.benefit_type_code = ubt.benefit_type_code
  AND util_used.profile_id = hpp.profile_id
WHERE
  hpp.profile_id = $3
  AND hpp.hmo_plan_type_id = $2
GROUP BY
  ubt.benefit_type_code,
  ubt.benefit_type_name,
  ubt."limitAmount",
  ubt."quantityLimit",
  ubt.visit_type,
  ubt."limitAmount",
  ubt."visitLimit",
  util_used.visit_used
ORDER BY
  ubt.benefit_type_name`,
      [
        args.providerId,
        args.planTypeId,
        args.profileId,
        startOfYear,
        endOfYear,
      ],
    );

    const result = benefits.map((e) => {
      let limitDisplay: string;
      let balanceDisplay: string;

      if (e.limitAmount === null || Number(e.limitAmount) === 0) {
        limitDisplay = 'Unlimited';
        balanceDisplay = 'Unlimited';
      } else {
        limitDisplay = String(e.limitAmount);
        const balance = Number(e.limitAmount) - Number(e.amountUsed || 0) || 0;
        balanceDisplay = String(balance);
      }
      let quantityBalanceDisplay: string;
      let quantityLimitDisplay: string;
      if (e.quantityLimit === null || Number(e.quantityLimit) === 0) {
        quantityBalanceDisplay = 'Unlimited';
        quantityLimitDisplay = 'Unlimited';
      } else {
        quantityBalanceDisplay = String(
          Number(e.quantityLimit || 0) - Number(e.quantityUsed || 0) || 0,
        );
        quantityLimitDisplay = String(e.quantityLimit);
      }
      let visitUsedDisplay: string;
      let visitLimitDisplay: string;
      if (e.visitLimit === null || Number(e.visitLimit) === 0) {
        visitLimitDisplay = 'Unlimited';
        visitUsedDisplay = 'Unlimited';
      } else {
        visitLimitDisplay = String(e.visitLimit);
        visitUsedDisplay = String(
          Number(e.visitLimit || 0) - (Number(e.visitUsed || 0) || 0),
        );
      }

      return new HmoBenefits(
        e.utilisationCategory,
        limitDisplay,
        e.amountUsed || 0,
        balanceDisplay,
        quantityLimitDisplay,
        quantityBalanceDisplay,
        e.visitType,
        visitLimitDisplay,
        visitUsedDisplay,
      );
    });
    return result;
  }
  async getAgreedTariff(
    dataSource: DataSource,
    mutator: ProfileModel,
    input: GetAgreedTariffInput,
  ) {
    const {
      providerId,
      visitTypeId,
      utilizationCategory,
      utilizationCode,
      enrolleeId,
      facilityId,
      externalPlanId,
    } = input;
    const hospitalId = facilityId || mutator.hospital?.id || mutator.hospitalId;

    let planTypeId: string | null = null;
    if (externalPlanId) {
      const externalPlan = await this.manager.findOne(HmoPlanTypeModel, {
        where: {
          id: externalPlanId,
          hmoProviderId: providerId,
          isExternalPlan: true,
        },
      });
      if (externalPlan) {
        planTypeId = externalPlan.id;
      } else {
        throw new NotAcceptableException('External Plan Not Found');
      }
    } else {
      const hmoProfile = await this.manager.findOne(HmoProfileModel, {
        where: {
          memberNumber: enrolleeId,
          providerId,
          memberPlanId: Not(IsNull()),
        },
      });
      if (!hmoProfile) {
        throw new NotAcceptableException('Enrollee Plan Not Found');
      }
      const plan = await this.manager.findOne(HmoProfilePlanModel, {
        where: {
          membershipNumber: getMembershipNoFromMemberNo(enrolleeId),
          hmoProviderId: providerId,
          status: HmoPlanStatus.Active,
        },
      });
      if (!plan) {
        throw new NotAcceptableException('Enrollee Plan Not Found');
      }
      planTypeId = plan.planTypeId;
    }
    const priceKey = await this.getProviderBandKey(providerId, hospitalId);

    let query = this.manager
      .createQueryBuilder(HmoPlanBenefitModel, 'benefit')
      .where('benefit.hmoProviderId = :providerId', { providerId })
      .andWhere('benefit.planTypeId = :planTypeId', {
        planTypeId,
      })
      .andWhere('benefit.visitTypeId = :visitTypeId', { visitTypeId });

    if (utilizationCategory) {
      query = query.andWhere('benefit.id = :category', {
        category: utilizationCategory,
      });
    } else {
      query = query.andWhere('benefit.utilisationTypes @> :code', {
        code: `[{"code": "${utilizationCode}"}]`,
      });
    }
    const enrolleeBenefit = await query.getOne();
    if (!enrolleeBenefit) {
      throw new NotAcceptableException('Benefits Not Found');
    }
    const util = enrolleeBenefit.utilisationTypes.find(
      (e) => e.code === input.utilizationCode,
    );
    if (!util) {
      throw new NotAcceptableException('Utilization Type Not Found');
    }
    if (util.benefitCategory === BenefitCategory.Capitated) {
      return {
        cost: 0,
        label: util.name,
        utilisationCategory: enrolleeBenefit.utilisationCategory,
        utilisationCategoryId: enrolleeBenefit.id,
        capitated: true,
      };
    }
    let controlled = memCache.get(`controlledPriceVisibility-${providerId}`);
    let visibleTo = true;
    if (controlled) {
      visibleTo = memCache.get(
        `controlledPriceVisibility-${providerId}-${hospitalId}`,
      );
    } else {
      const config = await this.getBenefitPriceVisible(hospitalId, providerId);
      controlled = config.controlled;
      visibleTo = config.visibleTo;
    }
    let cost = +util[priceKey] || 0;
    if (externalPlanId) {
      // check if they have a custom price for this benefit
      const customPrice =
        await this.getCustomPlanTypePriceByHospitalIdAndBenefitId(
          dataSource,
          providerId,
          hospitalId,
          planTypeId,
          enrolleeBenefit.id,
          utilizationCode,
        );
      if (customPrice) {
        cost = customPrice.price;
      }
    }
    if (
      controlled &&
      utilisationCategoryPriceControl.test(
        enrolleeBenefit.utilisationCategory,
      ) &&
      !visibleTo
    ) {
      cost = 0;
    }
    return {
      cost,
      label: util.name,
      utilisationCategory: enrolleeBenefit.utilisationCategory,
      utilisationCategoryId: enrolleeBenefit.id,
      capitated: false,
    };
  }

  async checkInEnrollee(
    mutator: ProfileModel,
    providerId: string,
    enrolleeId: string,
    facilityId?: string,
  ) {
    const enrollee = await this.manager.findOne(HmoProfileModel, {
      where: { memberNumber: enrolleeId, providerId },
      relations: ['profile'],
    });
    if (!enrollee) {
      throw new NotAcceptableException('Enrollee Not Found');
    }
    const plan = await this.manager.findOne(HmoProfilePlanModel, {
      where: {
        planTypeId: enrollee.memberPlanId,
        hmoProviderId: providerId,
      },
    });
    if (!plan) {
      throw new NotAcceptableException('Plan Not Found');
    }

    const visitation = new HmoVisitationModel({
      memberNumber: enrolleeId,
      profileId: enrollee.profileId,
      createdBy: mutator,
      hmoProviderId: providerId,
      creatorId: mutator.id,
      creatorName: mutator.fullName,
      hospitalId: facilityId || mutator.hospitalId,
    });
    const savedVisitation = await this.manager.save(
      HmoVisitationModel,
      visitation,
    );
    return {
      visitationId: savedVisitation.id,
      verificationCode: String(savedVisitation.code),
    };
  }

  async fetchPlanBeneficiariesByProviderId(
    providerId: string,
    filterOptions: HmoPlanBeneficiariesFilterOptions,
  ): Promise<HmoPlanBeneficiariesResponse> {
    const { skip, take, planTypeId, keyword, withoutPlan } = filterOptions;
    let query = this.manager
      .createQueryBuilder(HmoProfileModel, 'hmo_profiles')
      .innerJoinAndSelect('hmo_profiles.profile', 'profile')
      .where('hmo_profiles.providerId = :providerId', { providerId });

    if (planTypeId) {
      query = query.andWhere('hmo_profiles.memberPlanId = :planTypeId', {
        planTypeId,
      });
    }

    if (withoutPlan) {
      query = query.andWhere('(hmo_profiles.memberPlanId IS NULL)');
    }

    if (keyword) {
      query = query.andWhere(
        '(LOWER(profile.fullName) like :keyword OR LOWER(hmo_profiles.memberNumber) like :keyword)',
        {
          keyword: `%${keyword.toLowerCase()}%`,
        },
      );
    }

    query = query.offset(skip).limit(take);

    const result = await query.getManyAndCount();
    return new HmoPlanBeneficiariesResponse(
      ...takePaginatedResponses(result, take),
    );
  }

  async addBeneficiariesToPlan(
    profile: ProfileModel,
    planId: string,
    hmoProfileId: string[],
  ): Promise<HmoProfileModel[]> {
    const plan = await this.manager.findOne(HmoPlanTypeModel, {
      where: {
        id: planId,
      },
      relations: ['benefits'],
    });
    if (!plan) {
      throw new NotAcceptableException('Plan Not Found');
    }
    const enrollees = await this.manager.find(HmoProfileModel, {
      where: {
        id: In(hmoProfileId),
        providerId: plan.hmoProviderId,
        memberPlanId: IsNull(),
      },
    });
    if (!enrollees.length) {
      throw new NotAcceptableException(
        'Enrollee Not Found Or Enrollee Already Exists',
      );
    }
    // seperate principal and dependants
    const principalEnrollees = enrollees.filter((e) =>
      isPrincipalEnrollee(e.memberNumber),
    );
    const dependants = enrollees.filter(
      (e) => !isPrincipalEnrollee(e.memberNumber),
    );

    for (const dependant of dependants) {
      const principal = principalEnrollees.find((e) =>
        isPrincipalEnrollee(e.memberNumber),
      );

      if (!principal) {
        const principalEnrollee = await this.manager.findOne(
          HmoProfilePlanModel,
          {
            where: {
              membershipNumber: getMembershipNoFromMemberNo(
                dependant.memberNumber,
              ),
              status: HmoPlanStatus.Active,
              hmoProviderId: plan.hmoProviderId,
            },
          },
        );
        if (!principalEnrollee) {
          throw new NotAcceptableException('Principal Enrollee Plan Not Found');
        }
        if (principalEnrollee.planTypeId !== planId) {
          throw new NotAcceptableException(
            'Principal Enrollee Plan Does Not Match',
          );
        }
      }
    }
    for (const enrollee of enrollees) {
      const membershipNumber = getMembershipNoFromMemberNo(
        enrollee.memberNumber,
      );
      // check if the is a plan for this enrollee membership number
      const existingPlan = await this.manager.findOne(HmoProfilePlanModel, {
        where: {
          membershipNumber,
          planTypeId: planId,
          status: HmoPlanStatus.Active,
        },
      });
      if (existingPlan) {
        enrollee.memberPlanId = plan.id;
        enrollee.memberPlan = plan.name;
        enrollee.updatedBy = profile;
        enrollee.updatedDate = new Date();
        enrollee.lastModifierName = profile.fullName;
        await this.manager.save(HmoProfileModel, enrollee);
        continue;
      }
      await this.manager.save(
        HmoProfilePlanModel,
        new HmoProfilePlanModel({
          membershipNumber: getMembershipNoFromMemberNo(enrollee.memberNumber),
          profileId: enrollee.profileId,
          planType: plan,
          hmoProviderId: plan.hmoProviderId,
          createdBy: profile,
          creatorName: profile.fullName,
        }),
      );
      enrollee.memberPlanId = plan.id;
      enrollee.memberPlan = plan.name;
      enrollee.updatedBy = profile;
      enrollee.updatedDate = new Date();
      enrollee.lastModifierName = profile.fullName;
      await this.manager.save(HmoProfileModel, enrollee);
    }
    const profiles = await this.manager.find(HmoProfileModel, {
      where: {
        providerId: plan.hmoProviderId,
        memberPlanId: IsNull(),
      },
    });
    return profiles;
  }

  async removeBeneficiariesFromPlan(
    profile: ProfileModel,
    planId: string,
    hmoProfileId: string[],
  ) {
    const plan = await this.manager.findOne(HmoPlanTypeModel, {
      where: {
        id: planId,
      },
      relations: ['benefits'],
    });
    if (!plan) {
      throw new NotAcceptableException('Plan Not Found');
    }
    const enrollees = await this.manager.find(HmoProfileModel, {
      where: {
        id: In(hmoProfileId),
        providerId: plan.hmoProviderId,
        memberPlanId: planId,
      },
    });
    if (!enrollees.length) {
      throw new NotAcceptableException('Enrollee Not Found');
    }
    for (const enrollee of enrollees) {
      if (!isPrincipalEnrollee(enrollee.memberNumber)) {
        enrollee.memberPlanId = null;
        enrollee.memberPlan = null;
        enrollee.updatedBy = profile;
        enrollee.updatedDate = new Date();
        enrollee.lastModifierName = profile.fullName;

        await this.manager.save(HmoProfileModel, enrollee);
        continue;
      }
      const hmoProfilePlans = await this.manager.find(HmoProfilePlanModel, {
        where: {
          planTypeId: enrollee.memberPlanId,
          membershipNumber: enrollee.membershipNumber,
        },
        relations: ['utilizations'],
      });
      if (hmoProfilePlans?.length) {
        for (const hmoProfilePlan of hmoProfilePlans) {
          if (hmoProfilePlan.utilizations?.length) {
            hmoProfilePlan.status = HmoPlanStatus.Inactive;
            hmoProfilePlan.updatedBy = profile;
            hmoProfilePlan.updatedDate = new Date();
            hmoProfilePlan.lastModifierName = profile.fullName;
            await this.manager.save(HmoProfilePlanModel, hmoProfilePlan);
          } else {
            await this.manager.delete(HmoProfilePlanModel, {
              id: hmoProfilePlan.id,
            });

            await this.manager.delete(HmoProfileBenefitModel, {
              profilePlanTypeId: hmoProfilePlan.id,
            });
          }
        }
      }
      enrollee.memberPlanId = null;
      enrollee.memberPlan = null;
      enrollee.updatedBy = profile;
      enrollee.updatedDate = new Date();
      enrollee.lastModifierName = profile.fullName;

      await this.manager.save(HmoProfileModel, enrollee);
      if (isPrincipalEnrollee(enrollee.memberNumber)) {
        await this.manager
          .createQueryBuilder(HmoProfileModel, 'hmo_profiles')
          .update(HmoProfileModel)
          .set({
            memberPlanId: null,
            memberPlan: null,
            lastModifierId: profile.id,
            updatedDate: () => 'updated_date',
            lastModifierName: profile.fullName,
          })
          .where('hmo_profiles.membership_number = :membershipNumber', {
            membershipNumber: enrollee.membershipNumber,
          })
          .execute();
      }
    }
    const profiles = await this.manager.find(HmoProfileModel, {
      where: {
        providerId: plan.hmoProviderId,
        memberPlanId: planId,
      },
    });
    return profiles;
  }

  async addHmoProfilePlanByCoverageInformation(
    profile: ProfileModel,
    coverage: CoverageInformationModel,
    planStatus = HmoPlanStatus.Active,
  ): Promise<HmoProfileModel> {
    const planType = await this.manager.findOne(HmoPlanTypeModel, {
      where: {
        id: coverage.hmoProfile.memberPlanId,
      },
    });
    const provider = await this.manager.findOne(HmoProviderModel, {
      where: {
        id: coverage.hmoProfile.providerId,
      },
    });
    if (!provider) {
      throw new NotAcceptableException('Provider Not Found');
    }
    const lashmaPrefix = isLASHMA(provider.providerCode)
      ? CLINIFY_HMO_AGENCY_PREFIX_CODE[Number(provider.providerCode)]
      : 'N/A';
    let code: string;
    if (
      planType?.isSponsor &&
      planType?.planCode &&
      coverage.memberNumber.startsWith(lashmaPrefix) &&
      PLAN_TYPES_PREFIX[planType?.planCode]
    ) {
      code = PLAN_TYPES_PREFIX[planType?.planCode];
      coverage.memberNumber = coverage.memberNumber.replace(lashmaPrefix, code);
    }
    const membershipNumber = getMembershipNoFromMemberNo(coverage.memberNumber);
    const existingPlan = await this.manager.findOne(HmoProfilePlanModel, {
      where: {
        membershipNumber,
        hmoProviderId: coverage.hmoProfile.providerId,
        status: planStatus,
      },
      relations: ['planType'],
    });

    const enrollee = await this.manager.findOne(HmoProfileModel, {
      where: {
        id: coverage.hmoProfile.id,
        providerId: coverage.hmoProfile.providerId,
      },
      relations: ['profile', 'profile.details'],
    });
    if (existingPlan) {
      enrollee.memberPlanId = existingPlan.planType.id;
      enrollee.memberPlan = existingPlan.planType.name;
      enrollee.hmoPlanType = existingPlan.planType;
      enrollee.updatedBy = profile;
      enrollee.updatedDate = new Date();
      enrollee.lastModifierName = profile.fullName;
      enrollee.membershipNumber = membershipNumber;
      enrollee.memberNumber = coverage.memberNumber;
      await this.manager.save(HmoProfileModel, enrollee);
      return enrollee;
    }

    if (!coverage.hmoProfile.memberPlanId) {
      return null;
    }
    const plan = await this.manager.findOne(HmoPlanTypeModel, {
      where: {
        id: coverage.hmoProfile.memberPlanId,
      },
      relations: ['benefits'],
    });

    if (isPrincipalEnrollee(coverage.memberNumber)) {
      await this.manager.save(
        HmoProfilePlanModel,
        new HmoProfilePlanModel({
          membershipNumber,
          profileId: enrollee.profileId,
          planType: plan,
          hmoProviderId: plan.hmoProviderId,
          createdBy: profile,
          creatorName: profile.fullName,
          status: planStatus,
        }),
      );
      enrollee.memberPlanId = plan.id;
      enrollee.memberPlan = plan.name;
      enrollee.hmoPlanType = plan;
      enrollee.updatedBy = profile;
      enrollee.updatedDate = new Date();
      enrollee.lastModifierName = profile.fullName;
      enrollee.membershipNumber = membershipNumber;
      enrollee.memberNumber = coverage.memberNumber;
      await this.manager.save(HmoProfileModel, enrollee);
      const dependants = await this.manager.find(HmoProfileModel, {
        where: {
          membershipNumber,
          memberNumber: Not(coverage.memberNumber),
          providerId: plan.hmoProviderId,
        },
      });
      for (const dependant of dependants) {
        if (code && !dependant.memberNumber.startsWith(code)) {
          dependant.memberNumber = dependant.memberNumber.replace(
            lashmaPrefix,
            code,
          );
          dependant.membershipNumber = membershipNumber;
          dependant.memberPlan = plan.name;
          dependant.memberPlanId = plan.id;
          dependant.updatedBy = profile;
          dependant.updatedDate = new Date();
          dependant.lastModifierName = profile.fullName;
          await this.manager.save(HmoProfileModel, dependant);
        }
      }
    }
    return enrollee;
  }
  async updateHmoProfilePlanByCoverageInformation(
    mutator: ProfileModel,
    updatedRecord: CoverageInformationModel,
    oldRecord: CoverageInformationModel,
  ) {
    if (!isPrincipalEnrollee(updatedRecord.memberNumber)) return updatedRecord;
    const provider = await this.manager.findOne(HmoProviderModel, {
      where: {
        id: updatedRecord.hmoProfile.providerId,
      },
    });
    if (!provider) {
      throw new NotAcceptableException('Provider Not Found');
    }

    if (
      oldRecord.coverageType === 'HMO' &&
      updatedRecord.coverageType === 'HMO' &&
      oldRecord.hmoProfile.memberPlanId ===
        updatedRecord.hmoProfile.memberPlanId &&
      oldRecord.hmoProfile.providerId === oldRecord.hmoProfile.providerId &&
      oldRecord.hmoProfile.employerId === updatedRecord.hmoProfile.employerId
    ) {
      const hmoPlanType = await this.manager.findOne(HmoProfilePlanModel, {
        where: {
          planTypeId: updatedRecord.hmoProfile.memberPlanId,
          membershipNumber: getMembershipNoFromMemberNo(
            updatedRecord.memberNumber,
          ),
        },
        relations: ['benefits'],
      });
      if (hmoPlanType) {
        await this.manager.update(HmoProfilePlanModel, hmoPlanType.id, {
          membershipNumber: getMembershipNoFromMemberNo(
            updatedRecord.memberNumber,
          ),
          updatedBy: mutator,
          updatedDate: new Date(),
        });
      }
      if (hmoPlanType && hmoPlanType.benefits?.length) {
        return updatedRecord;
      }
    }
    if (oldRecord.coverageType === 'HMO') {
      const hmoProfilePlan = await this.manager.findOne(HmoProfilePlanModel, {
        where: {
          membershipNumber: getMembershipNoFromMemberNo(
            oldRecord.memberNumber || oldRecord.hmoProfile.memberNumber,
          ),
          planTypeId: oldRecord.hmoProfile.memberPlanId,
        },
        relations: ['hmoProvider', 'utilizations'],
      });

      if (
        hmoProfilePlan &&
        CLINIFY_HMO_AGENCY_CODES.includes(
          hmoProfilePlan.hmoProvider.providerCode,
        )
      ) {
        if (
          oldRecord.hmoProfile.employerId &&
          updatedRecord.hmoProfile.employerId &&
          oldRecord.hmoProfile.employerId !==
            updatedRecord.hmoProfile.employerId
        ) {
          const newEmployer = await this.manager.findOne(EmployerModel, {
            where: {
              id: updatedRecord.hmoProfile.employerId,
            },
          });

          if (!newEmployer) {
            throw new NotAcceptableException('Employer Not Found');
          }

          const samePlan = newEmployer.selectedMemberPlans?.find(
            (plan) => plan.id === oldRecord.hmoProfile.memberPlanId,
          );
          let planTypeId = oldRecord.hmoProfile.memberPlanId;

          if (!samePlan) {
            const newPlan = newEmployer.selectedMemberPlans?.[0];
            if (newPlan) {
              planTypeId = newPlan.id;
            } else {
              throw new NotAcceptableException('No Plan Selected For Employer');
            }
          }
          updatedRecord.hmoProfile.memberPlanId = planTypeId;
          const employee = await this.manager.findOne(EmployeeModel, {
            where: {
              hmoProfileId: updatedRecord.hmoProfile.id,
            },
            relations: ['dependants'],
          });
          if (employee) {
            employee.employerId = updatedRecord.hmoProfile.employerId;
            employee.hmoPlanTypeId = planTypeId;
            employee.updatedBy = mutator;
            employee.updatedDate = new Date();
            employee.lastModifierName = mutator.fullName;
            await this.manager.save(EmployeeModel, employee);
            for (const dependant of employee.dependants) {
              dependant.employerId = updatedRecord.hmoProfile.employerId;
              dependant.updatedBy = mutator;
              dependant.updatedDate = new Date();
              dependant.lastModifierName = mutator.fullName;
              await this.manager.save(EmployeeDependantModel, dependant);
            }
          }
        }
        if (hmoProfilePlan.utilizations?.length) {
          hmoProfilePlan.status = HmoPlanStatus.Inactive;
          hmoProfilePlan.updatedBy = mutator;
          hmoProfilePlan.updatedDate = new Date();
          hmoProfilePlan.lastModifierName = mutator.fullName;

          await this.manager.save(HmoProfilePlanModel, hmoProfilePlan);
        } else {
          await this.manager.delete(HmoProfileBenefitModel, {
            profilePlanTypeId: hmoProfilePlan.id,
          });

          await this.manager.delete(HmoProfilePlanModel, {
            id: hmoProfilePlan.id,
          });
        }
      }
    }

    const hmoPlanType = await this.manager.findOne(HmoPlanTypeModel, {
      where: {
        id: updatedRecord.hmoProfile.memberPlanId,
      },
      relations: ['benefits'],
    });
    if (!hmoPlanType) {
      throw new NotAcceptableException('Plan Not Found');
    }

    const hmoProfilePlanType = await this.manager.findOne(HmoProfilePlanModel, {
      where: {
        planTypeId: hmoPlanType.id,
        hmoProviderId: hmoPlanType.hmoProviderId,
        membershipNumber: getMembershipNoFromMemberNo(
          updatedRecord.memberNumber,
        ),
        status: HmoPlanStatus.Inactive,
      },
      relations: ['benefits'],
    });
    const lashmaPrefix = isLASHMA(provider.providerCode)
      ? CLINIFY_HMO_AGENCY_PREFIX_CODE[Number(provider.providerCode)]
      : 'N/A';
    let code: string;
    let swap = false;

    const currentMembershipNumber = getMembershipNoFromMemberNo(
      updatedRecord.memberNumber,
    );

    if (
      hmoPlanType?.isSponsor &&
      hmoPlanType?.planCode &&
      updatedRecord.memberNumber.startsWith(lashmaPrefix)
    ) {
      code = PLAN_TYPES_PREFIX[hmoPlanType?.planCode];
      updatedRecord.memberNumber = updatedRecord.memberNumber.replace(
        lashmaPrefix,
        code,
      );
    } else if (
      Object.values(PLAN_TYPES_PREFIX).some((e) =>
        updatedRecord.memberNumber.startsWith(e),
      )
    ) {
      code = Object.values(PLAN_TYPES_PREFIX).find((value) =>
        updatedRecord.memberNumber.startsWith(value),
      );
      updatedRecord.memberNumber = updatedRecord.memberNumber.replace(
        code,
        lashmaPrefix,
      );
      swap = true;
    }
    const membershipNumber = getMembershipNoFromMemberNo(
      updatedRecord.memberNumber,
    );
    if (hmoProfilePlanType) {
      hmoProfilePlanType.status = HmoPlanStatus.Active;
      hmoProfilePlanType.updatedBy = mutator;
      hmoProfilePlanType.updatedDate = new Date();
      hmoProfilePlanType.membershipNumber = membershipNumber;
      await this.manager.save(HmoProfilePlanModel, hmoProfilePlanType);
    } else {
      await this.manager.save(
        HmoProfilePlanModel,
        new HmoProfilePlanModel({
          membershipNumber,
          profileId: updatedRecord.profileId,
          planType: hmoPlanType,
          hmoProviderId: hmoPlanType.hmoProviderId,
          createdBy: mutator,
          creatorName: mutator.fullName,
        }),
      );
    }

    const profiles = await this.manager.find(HmoProfileModel, {
      where: {
        membershipNumber: currentMembershipNumber,
        providerId: hmoPlanType.hmoProviderId,
      },
    });
    for (const profile of profiles) {
      const codeToUse = swap ? lashmaPrefix : code;
      const prefixToUse = swap ? code : lashmaPrefix;
      if (code && !profile.memberNumber.startsWith(codeToUse)) {
        profile.memberNumber = profile.memberNumber.replace(
          prefixToUse,
          codeToUse,
        );
      }

      profile.memberPlanId = hmoPlanType.id;
      profile.memberPlan = hmoPlanType.name;
      profile.updatedBy = mutator;
      profile.updatedDate = new Date();
      profile.lastModifierName = mutator.fullName;
      profile.membershipNumber = membershipNumber;
      if (isPrincipalEnrollee(updatedRecord.memberNumber)) {
        updatedRecord.hmoProfile = profile;
      }
      await this.manager.save(HmoProfileModel, profile);
    }
    return updatedRecord;
  }

  async removeHmoProfilePlanByCoverageInformation(
    mutator: ProfileModel,
    coverage: CoverageInformationModel,
  ) {
    if (!isPrincipalEnrollee(coverage.memberNumber)) return;
    const hmoProfilePlan = await this.manager.findOne(HmoProfilePlanModel, {
      where: {
        membershipNumber: getMembershipNoFromMemberNo(coverage.memberNumber),
        planTypeId: coverage.hmoProfile.memberPlanId,
      },
      relations: ['hmoProvider', 'utilizations'],
    });
    if (hmoProfilePlan) {
      if (hmoProfilePlan.utilizations?.length) {
        hmoProfilePlan.status = HmoPlanStatus.Inactive;
        hmoProfilePlan.updatedBy = mutator;
        hmoProfilePlan.updatedDate = new Date();
        hmoProfilePlan.lastModifierName = mutator.fullName;

        await this.manager.save(HmoProfilePlanModel, hmoProfilePlan);
      } else {
        await this.manager.delete(HmoProfilePlanModel, {
          id: hmoProfilePlan.id,
        });

        await this.manager.delete(HmoProfileBenefitModel, {
          profilePlanTypeId: hmoProfilePlan.id,
        });
      }
      await this.manager
        .createQueryBuilder(HmoProfileModel, 'hmo_profiles')
        .update(HmoProfileModel)
        .set({
          memberPlanId: null,
          memberPlan: null,
          lastModifierId: mutator.id,
          updatedDate: () => 'updated_date',
          lastModifierName: mutator.fullName,
        })
        .where('hmo_profiles.membership_number = :membershipNumber', {
          membershipNumber: getMembershipNoFromMemberNo(coverage.memberNumber),
        })
        .execute();
      await this.manager.delete(DependentModel, {
        hmoProfileId: coverage.hmoProfile.id,
      });
    }
  }

  async fetchHmoPlansByProviderId(providerId: string, memberNumber: string) {
    const isPrincipal = isPrincipalEnrollee(memberNumber);

    const provider = await this.manager.findOne(HmoProviderModel, {
      where: Number.isNaN(+providerId)
        ? { id: providerId }
        : { providerCode: providerId },
    });
    if (!provider) {
      throw new NotAcceptableException('Plan Not Found');
    }

    if (!isPrincipal) {
      const plan = await this.manager.findOne(HmoProfilePlanModel, {
        where: {
          membershipNumber: getMembershipNoFromMemberNo(memberNumber),
          hmoProviderId: provider.id,
          status: HmoPlanStatus.Active,
        },
        relations: ['planType'],
      });
      if (!plan) {
        return [];
      }
      return [plan.planType];
    }

    return this.manager.find(HmoPlanTypeModel, {
      where: {
        hmoProviderId: provider.id,
      },
    });
  }

  async validateUtilisationTypeRequests(
    mutator: ProfileModel,
    input: IValidateUtilisationTypeRequests,
    preference: Pick<
      FacilityPreferenceModel,
      'customPaFormatType' | 'hmoSingleVisitPACode'
    >,
  ) {
    const {
      providerId,
      visitType,
      enrolleeId,
      utilisations,
      profilePlanId,
      profileId,
      treatmentDate,
      hospitalId,
    } = input;
    const categories = utilisations.map((e) => e.utilizationId);
    const priceKey = await this.getProviderBandKey(providerId, hospitalId);
    const membersCount = await this.manager.count(HmoProfileModel, {
      where: {
        membershipNumber: getMembershipNoFromMemberNo(enrolleeId),
        providerId,
      },
    });
    let paCode: string;
    const profilePlan = await this.manager.findOne(HmoProfilePlanModel, {
      where: {
        id: profilePlanId,
      },
      relations: ['planType'],
    });
    if (!profilePlan?.planType) {
      throw new NotAcceptableException('Plan Not Found');
    }
    const planBenefits = await this.manager
      .createQueryBuilder(HmoPlanBenefitModel, 'benefit')
      .innerJoinAndSelect('benefit.planType', 'planType')
      .where('benefit.hmoProviderId = :providerId', { providerId })
      .andWhere('benefit.planTypeId = :planId', {
        planId: profilePlan.planTypeId,
      })
      .andWhere('benefit.visitTypeId = :visitType', { visitType })
      .andWhere('benefit.id = ANY(:categories)', {
        categories,
      })
      .getMany();

    const provider = await this.manager.findOne(HmoProviderModel, {
      where: { id: input.providerId },
    });

    const uStatuses: (PreauthUtilisationInput & {
      status: string;
      statusDescription?: string;
    })[] = [];

    const categoryLimitCached: Record<string, number> = {};
    const categoryQuantityCached: Record<string, number> = {};
    const firstDayOfYear = new Date(new Date().getFullYear(), 0, 1);
    // Find all utilizations for the plan and visit type for the year
    const utils = await this.manager.find(PreAuthUtilisationsModel, {
      where: {
        hmoProviderId: providerId,
        hmoProfilePlanId: profilePlanId,
        createdDate: Between(firstDayOfYear, new Date()),
        status: Not(PaCodeStatus.Rejected),
        visitTypeId: visitType,
        utilizationId: In(categories),
        hmoClaimId: Not(IsNull()),
        hmoClaim: {
          status: Not('draft'),
        },
      },
    });
    const occurrences: Record<string, number> = {};

    utilisations.forEach((item) => {
      const code = item.utilizationCode;
      occurrences[code] = (occurrences[code] || 0) + 1;
    });

    const duplicates = utilisations.filter(
      (item) => occurrences[item.utilizationCode] > 1,
    );

    if (utilisations.length && duplicates.length === utilisations.length) {
      throw new NotAcceptableException('Duplicate Utilizations Exists');
    }
    const nonDuplicateUtilisations = utilisations.filter(
      (item) => occurrences[item.utilizationCode] === 1,
    );

    const unique = duplicates.filter(
      (item, index, self) =>
        self.findIndex((t) => t.utilizationCode === item.utilizationCode) ===
        index,
    );
    const uTypeInputs = [...nonDuplicateUtilisations, ...unique];
    // let capitatedCount = 0;
    for (const uTypeInput of uTypeInputs) {
      const {
        amount: totalLimitAmountUsedPerType,
        quantity: totalQuantityUsedPerType,
        uTVisitLimitUsed,
        amountByUsedByUser,
      } = utils
        .filter(
          (ut) =>
            ut.profileId === profileId &&
            ut.utilizationId === uTypeInput.utilizationId &&
            ut.utilizationCode === uTypeInput.utilizationCode,
        )
        .reduce(
          (acc, curr) => {
            const amount = Number(curr.price) * Number(curr.quantity);
            return {
              amount: acc.amount + amount,
              quantity: acc.quantity + Number(curr.quantity),
              uTVisitLimitUsed: acc.uTVisitLimitUsed + 1,
              amountByUsedByUser:
                acc.amountByUsedByUser +
                (profileId === curr.profileId ? amount : 0),
            };
          },
          {
            amount: 0,
            quantity: 0,
            uTVisitLimitUsed: 0,
            amountByUsedByUser: 0,
          },
        );
      const {
        amount: totalLimitAmountUsed,
        quantity: totalQuantityUsed,
        amountByCategoryUsedByUser,
        visitLimitUsed,
      } = utils
        .filter((ut) => ut.utilizationId === uTypeInput.utilizationId)
        .reduce(
          (acc, curr) => {
            const amount = Number(curr.price) * Number(curr.quantity);
            return {
              amount: acc.amount + amount,
              quantity: acc.quantity + Number(curr.quantity),
              amountByCategoryUsedByUser:
                acc.amountByCategoryUsedByUser +
                (profileId === curr.profileId ? amount : 0),
              visitLimitUsed: acc.visitLimitUsed + 1,
            };
          },
          {
            amount: 0,
            quantity: 0,
            amountByCategoryUsedByUser: 0,
            visitLimitUsed: 0,
          },
        );

      const benefit = planBenefits.find(
        (e) => e.id === uTypeInput.utilizationId,
      );
      const uType = benefit.utilisationTypes.find(
        (e) => e.code === uTypeInput.utilizationCode,
      );
      // check if same utilization type has been used within the same day
      const sameDayUtilisation = await this.manager.findOne(
        PreAuthUtilisationsModel,
        {
          where: {
            hmoProviderId: providerId,
            hmoProfilePlanId: profilePlanId,
            createdDate: Between(
              moment(treatmentDate).startOf('day').toDate(),
              moment(treatmentDate).endOf('day').toDate(),
            ),
            utilizationCode: uTypeInput.utilizationCode,
            status: Not(PaCodeStatus.Rejected),
            profileId,
            visitTypeId: visitType,
            hmoClaimId: Not(IsNull()),
            hmoClaim: {
              status: Not('draft'),
            },
          },
        },
      );

      if (sameDayUtilisation) {
        continue;
      }

      if (categoryLimitCached[uType.benefitCategory] === undefined) {
        categoryLimitCached[uType.benefitCategory] = 0;
      }
      if (categoryQuantityCached[uType.benefitCategory] === undefined) {
        categoryQuantityCached[uType.benefitCategory] = 0;
      }

      const amount = Number(uType[priceKey]) * Number(uTypeInput.quantity);
      const totalAmount = amount + categoryLimitCached[uType.benefitCategory];
      categoryLimitCached[uType.benefitCategory] = totalAmount;
      const benefitLimit = Number(benefit.limit || '0');
      const annualLimitPerPerson = Number(benefit.annualLimitPerPerson || '0');
      const benefitLimitSingle = Number(uType.benefitLimit || '0');
      const annualLimitPerPersonSingle = Number(
        uType.annualLimitPerPerson || '0',
      );
      const waitingPeriodDays = Number(benefit.waitingPeriodDays || '0');
      const waitingPeriodDaysSingle = Number(uType.waitingPeriodDays || '0');
      const planStartDate = new Date(profilePlan.createdDate);
      let status = checkLimits({
        annualLimitPerPerson,
        totalAmountUserByUser: amountByCategoryUsedByUser,
        totalAmountUsed: totalAmount + totalLimitAmountUsed,
        benefitLimit,
        checkAnnualLimitPerPerson: !!membersCount,
        quantity: Number(uTypeInput.quantity),
        quantityUsed:
          categoryQuantityCached[uType.benefitCategory] + totalQuantityUsed,
        quantityLimit: Number(benefit.quantity),
        waitingPeriodDays,
        planStartDate,
        visitLimit: Number(benefit.visitLimit || '0'),
        visitLimitUsed,
      });

      if (status.status !== PaCodeStatus.Rejected) {
        status = checkLimits({
          annualLimitPerPerson: annualLimitPerPersonSingle,
          totalAmountUserByUser: amountByUsedByUser,
          totalAmountUsed: amount + totalLimitAmountUsedPerType,
          benefitLimit: benefitLimitSingle,
          checkAnnualLimitPerPerson: !!membersCount,
          quantity: Number(uTypeInput.quantity),
          quantityUsed: totalQuantityUsedPerType,
          quantityLimit: Number(uType.quantity),
          waitingPeriodDays: waitingPeriodDaysSingle,
          planStartDate,
          visitLimit: Number(uType.visitLimit || '0'),
          visitLimitUsed: uTVisitLimitUsed,
        });
      }
      // if (uType.benefitCoverage === BenefitCoverage.Covered && !input.isClaim) {
      //   capitatedCount += 1;
      //   continue;
      // }
      if (preference?.hmoSingleVisitPACode) {
        if (!paCode) {
          paCode = await this.generatePACode({
            providerCode: provider.providerCode,
            referringHospitalId: hospitalId,
            requestDateTime: treatmentDate,
            type: 'PA',
            customPaFormatType: preference.customPaFormatType,
            benefitCode: benefit.code,
          });
        }
      } else {
        paCode = await this.generatePACode({
          providerCode: provider.providerCode,
          referringHospitalId: hospitalId,
          requestDateTime: treatmentDate,
          type: 'PA',
          customPaFormatType: preference?.customPaFormatType,
          benefitCode: benefit.code,
        });
      }

      // if (
      //   capitatedCount !== 0 &&
      //   capitatedCount === uTypeInputs.length &&
      //   !input.isClaim
      // ) {
      //   throw new NotAcceptableException('Utilizations Are Capitation');
      // }

      if (uType.benefitCoverage !== BenefitCoverage.Covered) {
        uStatuses.push({
          ...uTypeInput,
          status: PaCodeStatus.Rejected,
          statusDescription: 'Not Covered',
          paCode,
          paymentModel: uType.benefitCategory,
        });
        continue;
      }

      uStatuses.push({
        ...uTypeInput,
        ...status,
        paCode,
        paymentModel: uType.benefitCategory,
      });
    }

    await this.ensureProfileBenefits(
      mutator,
      profilePlan,
      uStatuses.map((e) => e.utilizationId),
    );
    return uStatuses;
  }
  async validateAddedUtilisationTypeRequests(
    mutator: ProfileModel,
    preference: Pick<
      FacilityPreferenceModel,
      'customPaFormatType' | 'hmoSingleVisitPACode'
    >,
    _paCode: string,
    input: IValidateUtilisationTypeRequests,
  ) {
    const {
      providerId,
      visitType,
      enrolleeId,
      utilisations,
      profilePlanId,
      profileId,
      treatmentDate,
      hospitalId,
    } = input;
    const categories = utilisations.map((e) => e.utilizationId);
    const priceKey = await this.getProviderBandKey(providerId, hospitalId);
    const membersCount = await this.manager.count(HmoProfileModel, {
      where: {
        membershipNumber: getMembershipNoFromMemberNo(enrolleeId),
        providerId,
      },
    });
    const profilePlan = await this.manager.findOne(HmoProfilePlanModel, {
      where: {
        id: profilePlanId,
      },
      relations: ['planType'],
    });
    if (!profilePlan?.planType) {
      throw new NotAcceptableException('Plan Not Found');
    }
    const planBenefits = await this.manager
      .createQueryBuilder(HmoPlanBenefitModel, 'benefit')
      .innerJoinAndSelect('benefit.planType', 'planType')
      .where('benefit.hmoProviderId = :providerId', { providerId })
      .andWhere('benefit.planTypeId = :planId', {
        planId: profilePlan.planTypeId,
      })
      .andWhere('benefit.visitTypeId = :visitType', { visitType })
      .andWhere('benefit.id = ANY(:categories)', { categories })
      .getMany();

    const provider = await this.manager.findOne(HmoProviderModel, {
      where: { id: input.providerId },
    });

    const uStatuses: (PreauthUtilisationInput & {
      status: string;
      statusDescription?: string;
    })[] = [];

    const categoryLimitCached: Record<string, number> = {};
    const categoryQuantityCached: Record<string, number> = {};
    const firstDayOfYear = new Date(new Date().getFullYear(), 0, 1);
    const utils = await this.manager.find(PreAuthUtilisationsModel, {
      where: {
        hmoProviderId: providerId,
        hmoProfilePlanId: profilePlanId,
        createdDate: Between(firstDayOfYear, new Date()),
        status: Not(PaCodeStatus.Rejected),
        visitTypeId: visitType,
        utilizationId: In(categories),
      },
    });

    const uTypeInputs = [...utilisations];
    let capitatedCount = 0;
    for (const uTypeInput of uTypeInputs) {
      const {
        amount: totalLimitAmountUsedPerType,
        quantity: totalQuantityUsedPerType,
        uTVisitLimitUsed,
        amountByUsedByUser,
      } = utils
        .filter(
          (ut) =>
            ut.profileId === profileId &&
            ut.utilizationId === uTypeInput.utilizationId &&
            ut.utilizationCode === uTypeInput.utilizationCode &&
            uTypeInput.id &&
            ut.id !== uTypeInput.id,
        )
        .reduce(
          (acc, curr) => {
            const amount = Number(curr.price) * Number(curr.quantity);
            return {
              amount: acc.amount + amount,
              quantity: acc.quantity + Number(curr.quantity),
              uTVisitLimitUsed: acc.uTVisitLimitUsed + 1,
              amountByUsedByUser: acc.amountByUsedByUser + amount,
            };
          },
          {
            amount: 0,
            quantity: 0,
            uTVisitLimitUsed: 0,
            amountByUsedByUser: 0,
          },
        );

      const {
        amount: totalLimitAmountUsed,
        quantity: totalQuantityUsed,
        visitLimitUsed,
        amountByCategoryUsedByUser,
      } = utils
        .filter(
          (ut) =>
            ut.utilizationId === uTypeInput.utilizationId &&
            uTypeInput.id &&
            ut.id !== uTypeInput.id,
        )
        .reduce(
          (acc, curr) => {
            const amount = Number(curr.price) * Number(curr.quantity);
            return {
              amount: acc.amount + amount,
              quantity: acc.quantity + Number(curr.quantity),
              visitLimitUsed: acc.visitLimitUsed + 1,
              amountByCategoryUsedByUser:
                acc.amountByCategoryUsedByUser + amount,
            };
          },
          {
            amount: 0,
            quantity: 0,
            amountByCategoryUsedByUser: 0,
            visitLimitUsed: 0,
          },
        );

      const benefit = planBenefits.find(
        (e) => e.id === uTypeInput.utilizationId,
      );
      const uType = benefit?.utilisationTypes.find(
        (e) => e.code === uTypeInput.utilizationCode,
      );
      // check if same utilization type has been used within the same day
      const sameDayUtilisation = await this.manager.findOne(
        PreAuthUtilisationsModel,
        {
          where: {
            hmoProviderId: providerId,
            hmoProfilePlanId: profilePlanId,
            createdDate: Between(
              moment(treatmentDate).startOf('day').toDate(),
              moment(treatmentDate).endOf('day').toDate(),
            ),
            utilizationCode: uTypeInput.utilizationCode,
            status: Not(PaCodeStatus.Rejected),
            profileId,
            visitTypeId: visitType,
          },
        },
      );

      if (sameDayUtilisation && sameDayUtilisation.id !== uTypeInput.id) {
        continue;
      }

      if (categoryLimitCached[uType.benefitCategory] === undefined) {
        categoryLimitCached[uType.benefitCategory] = 0;
      }
      if (categoryQuantityCached[uType.benefitCategory] === undefined) {
        categoryQuantityCached[uType.benefitCategory] = 0;
      }

      const amount = Number(uType[priceKey]) * Number(uTypeInput.quantity);
      const totalAmount = amount + categoryLimitCached[uType.benefitCategory];
      categoryLimitCached[uType.benefitCategory] = totalAmount;
      const benefitLimit = Number(benefit.limit || '0');
      const annualLimitPerPerson = Number(benefit.annualLimitPerPerson || '0');
      const benefitLimitSingle = Number(uType.benefitLimit || '0');
      const annualLimitPerPersonSingle = Number(
        uType.annualLimitPerPerson || '0',
      );
      const waitingPeriodDays = Number(benefit.waitingPeriodDays || '0');
      const waitingPeriodDaysSingle = Number(uType.waitingPeriodDays || '0');
      const planStartDate = new Date(profilePlan.createdDate);
      let status = checkLimits({
        totalAmountUsed: amount + totalLimitAmountUsed,
        totalAmountUserByUser: Number(amountByCategoryUsedByUser),
        annualLimitPerPerson,
        benefitLimit,
        checkAnnualLimitPerPerson: !!membersCount,
        quantity: Number(uTypeInput.quantity),
        quantityUsed:
          categoryQuantityCached[uType.benefitCategory] + totalQuantityUsed,
        quantityLimit: Number(benefit.quantity),
        waitingPeriodDays,
        planStartDate,
        visitLimit: Number(benefit.visitLimit || '0'),
        visitLimitUsed,
      });
      if (status.status !== PaCodeStatus.Rejected) {
        status = checkLimits({
          totalAmountUsed: amount + totalLimitAmountUsedPerType,
          totalAmountUserByUser: Number(amountByUsedByUser),
          annualLimitPerPerson: annualLimitPerPersonSingle,
          benefitLimit: benefitLimitSingle,
          checkAnnualLimitPerPerson: !!membersCount,
          quantity: Number(uTypeInput.quantity),
          quantityUsed: totalQuantityUsedPerType,
          quantityLimit: Number(uType.quantity),
          waitingPeriodDays: waitingPeriodDaysSingle,
          planStartDate,
          visitLimit: Number(uType.visitLimit || '0'),
          visitLimitUsed: uTVisitLimitUsed,
        });
      }

      let paCode = _paCode || uTypeInput.paCode;
      if (!paCode) {
        paCode = await this.generatePACode({
          providerCode: provider.providerCode,
          referringHospitalId: hospitalId,
          requestDateTime: treatmentDate,
          type: 'PA',
          customPaFormatType: preference?.customPaFormatType,
          benefitCode: benefit.code,
        });
      }
      if (uType.benefitCategory === BenefitCategory.Capitated) {
        if (uType.benefitCoverage === BenefitCoverage.Covered) {
          capitatedCount += 1;
          continue;
        }
        uStatuses.push({
          ...uTypeInput,
          status: PaCodeStatus.Rejected,
          statusDescription: 'Not Covered',
          paCode,
          paymentModel: uType.benefitCategory,
        });
        continue;
      }
      if (uType.benefitCoverage !== BenefitCoverage.Covered) {
        uStatuses.push({
          ...uTypeInput,
          status: PaCodeStatus.Rejected,
          statusDescription: 'Not Covered',
          paCode,
          paymentModel: uType.benefitCategory,
        });
        continue;
      }

      uStatuses.push({
        ...uTypeInput,
        ...status,
        paCode,
        paymentModel: uType.benefitCategory,
      });
    }
    if (capitatedCount !== 0 && capitatedCount === uTypeInputs.length) {
      throw new NotAcceptableException('Utilizations Are Capitation');
    }
    await this.ensureProfileBenefits(
      mutator,
      profilePlan,
      uStatuses.map((e) => e.utilizationId),
    );
    return uStatuses;
  }

  async validateExternalUtilisationTypeRequests(
    input: IValidateExternalUtilisationTypeRequests,
    preference: Pick<
      FacilityPreferenceModel,
      'customPaFormatType' | 'hmoSingleVisitPACode'
    >,
  ) {
    const {
      providerId,
      visitType,
      utilisations,
      planTypeId,
      profileId,
      treatmentDate,
      hospitalId,
    } = input;
    const categories = utilisations.map((e) => e.utilizationId);

    let paCode: string;

    const planBenefits = await this.manager
      .createQueryBuilder(HmoPlanBenefitModel, 'benefit')
      .innerJoinAndSelect('benefit.planType', 'planType')
      .where('benefit.hmoProviderId = :providerId', { providerId })
      .andWhere('benefit.planTypeId = :planId', {
        planId: planTypeId,
      })
      .andWhere('benefit.visitTypeId = :visitType', { visitType })
      .andWhere('benefit.id = ANY(:categories)', {
        categories,
      })
      .getMany();
    const provider = await this.manager.findOne(HmoProviderModel, {
      where: { id: input.providerId },
    });

    const uStatuses: (PreauthUtilisationInput & {
      status: string;
      statusDescription?: string;
    })[] = [];

    const occurrences: Record<string, number> = {};

    utilisations.forEach((item) => {
      const code = item.utilizationCode;
      occurrences[code] = (occurrences[code] || 0) + 1;
    });

    const duplicates = utilisations.filter(
      (item) => occurrences[item.utilizationCode] > 1,
    );

    if (utilisations.length && duplicates.length === utilisations.length) {
      throw new NotAcceptableException('Duplicate Utilizations Exists');
    }
    const nonDuplicateUtilisations = utilisations.filter(
      (item) => occurrences[item.utilizationCode] === 1,
    );

    const unique = duplicates.filter(
      (item, index, self) =>
        self.findIndex((t) => t.utilizationCode === item.utilizationCode) ===
        index,
    );
    const uTypeInputs = [...nonDuplicateUtilisations, ...unique];

    for (const uTypeInput of uTypeInputs) {
      const benefit = planBenefits.find(
        (e) => e.id === uTypeInput.utilizationId,
      );
      const uType = benefit?.utilisationTypes.find(
        (e) => e.code === uTypeInput.utilizationCode,
      );

      // check if same utilization type has been used within the same day
      const sameDayUtilisation = await this.manager.findOne(
        PreAuthUtilisationsModel,
        {
          where: {
            hmoProviderId: providerId,
            createdDate: Between(
              moment(treatmentDate).startOf('day').toDate(),
              moment(treatmentDate).endOf('day').toDate(),
            ),
            utilizationCode: uTypeInput.utilizationCode,
            status: Not(PaCodeStatus.Rejected),
            profileId,
            visitTypeId: visitType,
            hmoClaimId: Not(IsNull()),
            hmoClaim: {
              status: Not('draft'),
            },
          },
        },
      );

      if (sameDayUtilisation) {
        continue;
      }

      if (preference?.hmoSingleVisitPACode) {
        if (!paCode) {
          paCode = await this.generatePACode({
            providerCode: provider.providerCode,
            referringHospitalId: hospitalId,
            requestDateTime: treatmentDate,
            type: 'PA',
            customPaFormatType: preference.customPaFormatType,
            benefitCode: benefit.code,
          });
        }
      } else {
        paCode = await this.generatePACode({
          providerCode: provider.providerCode,
          referringHospitalId: hospitalId,
          requestDateTime: treatmentDate,
          type: 'PA',
          customPaFormatType: preference?.customPaFormatType,
          benefitCode: benefit.code,
        });
      }

      uStatuses.push({
        ...uTypeInput,
        status: PaCodeStatus.Pending,
        paCode,
        paymentModel: uType.benefitCategory,
      });
    }

    return uStatuses;
  }

  async ensureProfileBenefits(
    mutator: ProfileModel,
    profilePlan: HmoProfilePlanModel,
    benefitIds: string[],
  ) {
    const benefits = await this.manager.find(HmoProfileBenefitModel, {
      where: {
        benefitId: In(benefitIds),
        profileId: profilePlan.profileId,
      },
      select: ['benefitId', 'id'],
    });
    const benefitsToCreate = [
      ...new Set(
        benefitIds.filter(
          (benefitId) =>
            !benefits.some((benefit) => benefit.benefitId === benefitId),
        ),
      ),
    ];
    if (benefitsToCreate.length) {
      const benefits = await this.manager.find(HmoPlanBenefitModel, {
        where: {
          id: In(benefitsToCreate),
        },
        select: ['id', 'utilisationCategory', 'planTypeId'],
      });
      await this.manager.save(
        HmoProfileBenefitModel,
        benefits.map((benefit) => {
          return new HmoProfileBenefitModel({
            benefitId: benefit.id,
            planTypeId: benefit.planTypeId,
            createdBy: mutator,
            creatorName: mutator.fullName,
            hmoProviderId: profilePlan.hmoProviderId,
            membershipNumber: profilePlan.membershipNumber,
            profilePlanTypeId: profilePlan.id,
            profileId: profilePlan.profileId,
            name: benefit.utilisationCategory,
          });
        }),
      );
    }
  }
  async requestPreAuthorization(
    mutator: ProfileModel,
    input: PreauthorisationInput,
  ): Promise<PreauthorisationModel> {
    const profilePlan = await this.manager.findOne(HmoProfilePlanModel, {
      where: {
        membershipNumber: getMembershipNoFromMemberNo(input.enrolleeId),
        hmoProviderId: input.providerId,
        status: HmoPlanStatus.Active,
      },
      relations: ['planType'],
    });
    if (!profilePlan && !input.isExternalPlanType) {
      throw new NotAcceptableException('Plan Not Found');
    }
    const profile = await this.manager.findOne(ProfileModel, {
      where: {
        hmoProfiles: {
          memberNumber: input.enrolleeId,
          providerId: input.providerId,
        },
      },
    });
    if (input.referralCode) {
      await this.referralCodeValidation(
        mutator,
        input.providerId,
        input.referralCode,
        input.enrolleeId,
        input.facilityId,
      );
    }
    const visit = await this.checkInEnrollee(
      mutator,
      input.providerId,
      input.enrolleeId,
      input.facilityId,
    );
    input.visitId = visit.verificationCode;

    const facilityPreference = await this.getProviderPreference(
      input.providerId,
    );
    let validations: (PreauthUtilisationInput & {
      status: string;
      statusDescription?: string;
    })[] = [];
    if (input.isExternalPlanType) {
      validations = await this.validateExternalUtilisationTypeRequests(
        {
          utilisations: input.utilizations,
          enrolleeId: input.enrolleeId,
          planTypeId: input.externalPlanTypeId,
          visitType: input.serviceTypeCode,
          providerId: input.providerId,
          profileId: profile.id,
          treatmentDate: input.requestDateTime,
          isClaim: false,
          hospitalId: input.facilityId || mutator.hospitalId,
        },
        facilityPreference,
      );
    } else {
      validations = await this.validateUtilisationTypeRequests(
        mutator,
        {
          utilisations: input.utilizations,
          enrolleeId: input.enrolleeId,
          profilePlanId: profilePlan.id,
          visitType: input.serviceTypeCode,
          providerId: input.providerId,
          profileId: profile.id,
          treatmentDate: input.requestDateTime,
          isClaim: false,
          hospitalId: input.facilityId || mutator.hospitalId,
        },
        facilityPreference,
      );
    }

    if (!validations.length && input.utilizations?.length) {
      throw new NotAcceptableException('Utilizations Already Exist');
    }

    return Promise.resolve(
      new PreauthorisationModel({
        ...input,
        visitDetailIds: [],
        utilizations: validations?.map((utilisation) => {
          return new PreAuthUtilisationsModel({
            ...utilisation,
            visitDetailsId: visit.visitationId,
            hmoProviderId: input.providerId,
            visitTypeId: input.serviceTypeCode,
            profileId: profile.id,
            hmoProfilePlanId: profilePlan.id,
            creatorName: mutator.fullName,
            createdBy: mutator,
          });
        }),
        createdBy: mutator,
        requestedBy: mutator.fullName,
        singleVisitPACode: !!facilityPreference?.hmoSingleVisitPACode,
      }),
    );
  }
  async updatePreAuthorization(
    mutator: ProfileModel,
    preAuthId: string,
    input: PreauthorisationUpdateInput,
  ): Promise<PreauthorisationModel> {
    const profilePlan = await this.manager.findOne(HmoProfilePlanModel, {
      where: {
        membershipNumber: getMembershipNoFromMemberNo(input.enrolleeId),
        hmoProviderId: input.providerId,
        status: HmoPlanStatus.Active,
      },
      relations: ['planType'],
    });
    if (!profilePlan) {
      throw new NotAcceptableException('Plan Not Found');
    }

    const profile = await this.manager.findOne(ProfileModel, {
      where: {
        hmoProfiles: {
          memberNumber: input.enrolleeId,
          providerId: profilePlan.hmoProviderId,
        },
      },
    });

    const inputUtils = input.utilizations || [];
    delete input.utilizations;

    let preAuthorisation = await this.manager.findOne(PreauthorisationModel, {
      where: { id: preAuthId },
      relations: ['utilizations', 'createdBy', 'profile', 'provider'],
    });

    if (
      (input.facilityId || mutator.hospitalId) !== preAuthorisation.hospitalId
    ) {
      throw new NotAcceptableException('Cannot Modify Record');
    }
    if (input.serviceTypeCode !== preAuthorisation.serviceTypeCode) {
      // This is done to avoid complexity of handling newly added or deleted utilizations
      // when the service type is changed.
      const existingUtils = preAuthorisation.utilizations || [];
      const codes = existingUtils.map((util) => util.utilizationCode);

      if (existingUtils?.length) {
        const query = this.manager
          .createQueryBuilder(HmoPlanBenefitModel, 'benefit')
          .where('benefit.hmoProviderId = :providerId', {
            providerId: preAuthorisation.providerId,
          })
          .andWhere('benefit.planTypeId = :planTypeId', {
            planTypeId: profilePlan.planTypeId,
          })
          .andWhere('benefit.visitTypeId = :visitTypeId', {
            visitTypeId: input.serviceTypeCode,
          })
          .andWhere(
            `EXISTS (
            SELECT 1
            FROM jsonb_array_elements(benefit.utilisationTypes) AS elem
            WHERE elem->>'code' = ANY(:codes)
          )`,
            { codes },
          )
          .select(['benefit.id', 'benefit.utilisationTypes']);
        const result = await query.getMany();
        if (result?.length) {
          for (const util of existingUtils) {
            const benefit = result.find((b) =>
              b.utilisationTypes.some(
                (type) => type.code === util.utilizationCode,
              ),
            );
            if (!benefit)
              throw new NotAcceptableException('Invalid Utilization Code');

            const uType = benefit.utilisationTypes.find(
              (type) => type.code === util.utilizationCode,
            );
            if (!uType)
              throw new NotAcceptableException('Invalid Utilization Code');

            util.utilizationId = benefit.id;
            util.utilizationCode = uType.code;
            util.visitTypeId = input.serviceTypeCode;
            await this.manager.save(PreAuthUtilisationsModel, util);
            const inputUtilIndex = inputUtils.findIndex(
              (u) => u.utilizationCode === util.utilizationCode,
            );
            if (inputUtilIndex > -1) {
              inputUtils[inputUtilIndex].utilizationId = benefit.id;
            }
          }
          await this.manager.update(
            PreauthorisationModel,
            { id: preAuthId },
            {
              serviceType: input.serviceType,
              serviceTypeCode: input.serviceTypeCode,
              serviceName: input.serviceName,
            },
          );
          preAuthorisation = await this.manager.findOne(PreauthorisationModel, {
            where: { id: preAuthId },
            relations: ['utilizations', 'createdBy', 'profile', 'provider'],
          });
        } else {
          throw new NotAcceptableException(
            'No Valid Utilization Types Found For This Visitation Type',
          );
        }
      }
    }
    const existingUtils = preAuthorisation.utilizations || [];
    const deletedUtils = [];

    const newExistingUtils = existingUtils
      .map((_util) => {
        if (_util.status !== 'Pending') return _util;
        const _exist = inputUtils.find((_item) => _item.id === _util?.id);
        if (_exist) return { ..._util, ..._exist };

        deletedUtils.push(_util);
        return null;
      })
      .filter(Boolean);

    const newUtilizations = [];
    let pendingUtilizations = newExistingUtils.filter(
      (util) => util.status === 'Pending',
    );
    pendingUtilizations = newExistingUtils.map((_pendUtil) => {
      const _utilization = inputUtils.find(
        (_util) => _util.id && _util.id === _pendUtil?.id,
      );
      if (!_utilization) return _pendUtil;

      if (
        _pendUtil?.utilizationCode !== _utilization.utilizationCode ||
        _pendUtil?.utilizationId !== _utilization.utilizationId ||
        _pendUtil?.quantity !== _utilization.quantity
      ) {
        newUtilizations.push(_utilization as PreAuthUtilisationsModel);
      }

      return _utilization as PreAuthUtilisationsModel;
    });
    inputUtils.forEach((util) => {
      if (util?.id) return;
      const _exist = pendingUtilizations.find(
        (_util) =>
          _util.utilizationCode === util?.utilizationCode &&
          _util.utilizationId === util?.utilizationId,
      );
      if (_exist) return;
      newUtilizations.push(util);
    });

    if (deletedUtils.length) {
      await this.manager.delete(PreAuthUtilisationsModel, deletedUtils);
    }

    if (!newUtilizations.length) {
      return this.manager.save(PreauthorisationModel, {
        ...preAuthorisation,
        ...input,
        utilizations: newExistingUtils,
        lastModifierName: mutator.fullName,
        updatedBy: mutator,
        updatedDate: new Date(),
      });
    }
    const facilityPreference = await this.getProviderPreference(
      input.providerId,
    );

    const validations = await this.validateAddedUtilisationTypeRequests(
      mutator,
      facilityPreference,
      preAuthorisation?.singleVisitPACode ? existingUtils[0].paCode : null,
      {
        utilisations: newUtilizations,
        enrolleeId: input.enrolleeId,
        profilePlanId: profilePlan.id,
        visitType: input.serviceTypeCode,
        providerId: input.providerId,
        profileId: profile.id,
        treatmentDate: input.requestDateTime,
        isClaim: false,
        hospitalId: preAuthorisation.hospitalId,
      },
    );

    if (!validations.length) {
      throw new NotAcceptableException('Utilizations Already Exist');
    }

    const updatedUtilization = await this.manager.save(
      PreAuthUtilisationsModel,
      validations?.map((utilisation) => {
        return new PreAuthUtilisationsModel({
          ...utilisation,
          preAuthorizationId: preAuthId,
          visitDetailsId: existingUtils[0].visitDetailsId,
          hmoProviderId: input.providerId,
          visitTypeId: input.serviceTypeCode,
          profileId: profile.id,
          hmoProfilePlanId: profilePlan.id,
          creatorName: mutator.fullName,
          createdBy: mutator,
          createdDate: new Date(),
        });
      }),
    );

    const utilizationsToReturn = [...newExistingUtils];

    updatedUtilization.forEach((utilisation) => {
      const _index = utilizationsToReturn.findIndex(
        ({ id: _id }) => utilisation.id === _id,
      );
      if (_index === -1) {
        utilizationsToReturn.push(utilisation);
      }
      utilizationsToReturn[_index] = utilisation;
    });

    return this.manager.save(PreauthorisationModel, {
      ...preAuthorisation,
      ...input,
      utilizations: utilizationsToReturn,
      claimStatus:
        preAuthorisation.claimStatus === 'Submitted'
          ? 'Partially Submitted'
          : preAuthorisation.claimStatus,
      lastModifierName: mutator.fullName,
      updatedBy: mutator,
      updatedDate: new Date(),
    });
  }
  async referralCodeValidation(
    mutator: ProfileModel,
    providerId: string,
    referralCode: string,
    enrolleeId: string,
    facilityId?: string,
  ): Promise<boolean> {
    const referralUtil = await this.manager.findOne(
      PreAuthReferralUtilisationsModel,
      {
        where: {
          paCode: referralCode,
          preAuthorizationReferral: {
            providerId,
          },
        },
        relations: ['preAuthorizationReferral'],
      },
    );

    if (!referralUtil) {
      throw new NotAcceptableException('Invalid Referral Code');
    }

    if (referralUtil.status !== PaCodeStatus.Approved) {
      throw new NotAcceptableException('Referral Code Not Approved');
    }

    const referral = referralUtil.preAuthorizationReferral;

    // Restrict usage to referred hospital only
    if (referral.referredProviderId !== (facilityId || mutator.hospitalId)) {
      throw new NotAcceptableException('Referral Code Not Valid For Provider');
    }

    // Check if referral has already been used
    const existingClaim = await this.manager.findOne(HmoClaimModel, {
      where: {
        referralCode,
        status: Not('draft'),
      },
    });

    if (existingClaim) {
      throw new NotAcceptableException('Referral Code Already Used'); // Restrict single usage
    }

    if (referral.enrolleeNumber !== enrolleeId) {
      throw new NotAcceptableException('Invalid Enrollee Referral Code');
    }

    // check if not more than 30 days
    const thirtyDaysAgo = moment().subtract(30, 'days').toDate();
    if (referral.createdDate < thirtyDaysAgo) {
      throw new NotAcceptableException('Referral Code Expired');
    }

    return true;
  }

  async submitClaim(
    mutator: ProfileModel,
    input: NewHmoClaimInput,
  ): Promise<IHmoAddClaimResponse> {
    let claimProviderId = mutator.hospitalId;
    if (input.facilityId) {
      claimProviderId = input.facilityId;
    }
    const profilePlan = await this.manager.findOne(HmoProfilePlanModel, {
      where: {
        membershipNumber: getMembershipNoFromMemberNo(input.enrolleeId),
        hmoProviderId: input.providerId,
        status: HmoPlanStatus.Active,
      },
      relations: ['planType'],
    });
    if (!profilePlan && !input.isExternalPlanType) {
      throw new NotAcceptableException('Plan Not Found');
    }
    const hmoHospital = await this.manager.findOne(HmoHospitalModel, {
      where: {
        providerId: input.providerId,
        hospitalId: claimProviderId,
      },
    });

    if (!hmoHospital) {
      throw new NotAcceptableException(
        'Hospital Not Authorized For Claims Submission',
      );
    }
    if (input.referralCode) {
      await this.referralCodeValidation(
        mutator,
        input.providerId,
        input.referralCode,
        input.enrolleeId,
        claimProviderId,
      );
    }
    const visit = await this.checkInEnrollee(
      mutator,
      input.providerId,
      input.enrolleeId,
      claimProviderId,
    );
    const profile = await this.manager.findOne(ProfileModel, {
      where: {
        hmoProfiles: {
          memberNumber: input.enrolleeId,
          providerId: input.providerId,
        },
      },
    });

    const facilityPreference = await this.getProviderPreference(
      input.providerId,
    );

    const batchNumber = generateBatchNumber(hmoHospital.hmoProviderId);
    let validations: (PreauthUtilisationInput & {
      status: string;
      statusDescription?: string;
    })[] = [];
    if (input.isExternalPlanType) {
      validations = await this.validateExternalUtilisationTypeRequests(
        {
          utilisations: input.utilizations,
          enrolleeId: input.enrolleeId,
          planTypeId: input.externalPlanTypeId,
          visitType: input.serviceTypeCode,
          providerId: input.providerId,
          profileId: profile.id,
          treatmentDate: input.claimDate,
          isClaim: false,
          hospitalId: input.facilityId || mutator.hospitalId,
        },
        facilityPreference,
      );
    } else {
      validations = await this.validateUtilisationTypeRequests(
        mutator,
        {
          utilisations: input.utilizations,
          enrolleeId: input.enrolleeId,
          profilePlanId: profilePlan.id,
          visitType: input.serviceTypeCode,
          providerId: input.providerId,
          profileId: profile.id,
          treatmentDate: input.claimDate,
          isClaim: true,
          hospitalId: claimProviderId,
        },
        facilityPreference,
      );
    }
    if (!validations.length) {
      throw new NotAcceptableException('Utilizations Already Exist');
    }

    const claimNumberDoc = await getClaimNumberCollection(input.providerId);
    return {
      visitId: visit.verificationCode,
      claimId: claimNumberDoc.value.value,
      batchNumber,
      totalCharged: 0,
      totalTariffFee: validations?.reduce(
        (acc, { quantity, price }) => acc + Number(quantity) * Number(price),
        0,
      ),
      utilizations: validations?.map((utilisation) => {
        return new PreAuthUtilisationsModel({
          ...utilisation,
          visitDetailsId: visit.visitationId,
          hmoProviderId: input.providerId,
          visitTypeId: input.serviceTypeCode,
          profileId: profile.id,
          hmoProfilePlanId: profilePlan.id,
          createdBy: mutator,
          creatorName: mutator.fullName,
          createdDate: new Date(),
        });
      }),
    };
  }
  async submitCapitationClaim(
    mutator: ProfileModel,
    input: NewHmoClaimInput,
    utilisations: PreauthUtilisationInput[],
  ) {
    // No need validation for utilisations as they are already validated
    const profilePlan = await this.manager.findOne(HmoProfilePlanModel, {
      where: {
        membershipNumber: getMembershipNoFromMemberNo(input.enrolleeId),
        hmoProviderId: input.providerId,
        status: HmoPlanStatus.Active,
      },
      relations: ['planType'],
    });
    if (!profilePlan && !input.isExternalPlanType) {
      throw new NotAcceptableException('Plan Not Found');
    }
    const hmoHospital = await this.manager.findOne(HmoHospitalModel, {
      where: {
        providerId: input.providerId,
        hospitalId: mutator.hospitalId,
      },
    });
    if (!hmoHospital) {
      throw new NotAcceptableException(
        'Hospital Not Authorized For Claims Submission',
      );
    }
    if (input.referralCode) {
      await this.referralCodeValidation(
        mutator,
        input.providerId,
        input.referralCode,
        input.enrolleeId,
      );
    }
    const visit = await this.checkInEnrollee(
      mutator,
      input.providerId,
      input.enrolleeId,
      mutator.hospitalId,
    );
    const profile = await this.manager.findOne(ProfileModel, {
      where: {
        hmoProfiles: {
          memberNumber: input.enrolleeId,
          providerId: input.providerId,
        },
      },
    });

    const batchNumber = generateBatchNumber(hmoHospital.hmoProviderId);
    const claimNumberDoc = await getClaimNumberCollection(input.providerId);
    const record = new HmoClaimModel({
      ...input,
      visitId: visit.verificationCode,
      claimId: claimNumberDoc.value.value,
      enrolleeNumber: input.enrolleeId,
      batchNumber,
      totalCharged: '0',
      totalTariffFee: utilisations
        ?.reduce(
          (acc, { quantity, price }) => acc + Number(quantity) * Number(price),
          0,
        )
        .toString(),
      createdBy: mutator,
      creatorName: mutator.fullName,
      profile,
      status: input.status,
      hospitalId: mutator.hospitalId,
      submitDateTime: new Date(),
      providerId: input.providerId,
      submittedBy: mutator.fullName,
      utilizations: utilisations?.map((utilisation) => {
        return new PreAuthUtilisationsModel({
          ...utilisation,
          visitDetailsId: visit.visitationId,
          hmoProviderId: input.providerId,
          visitTypeId: input.serviceTypeCode,
          profileId: profile.id,
          hmoProfilePlanId: profilePlan.id,
          createdBy: mutator,
          creatorName: mutator.fullName,
          createdDate: new Date(),
        });
      }),
    });
    const response = await this.manager.save(HmoClaimModel, record);
    return response;
  }
  async getEnrolleeBeneficiaries(memberNumber: string, providerId: string) {
    if (!isPrincipalEnrollee(memberNumber)) {
      return [];
    }
    const plan = await this.manager.findOne(HmoProfilePlanModel, {
      where: {
        membershipNumber: getMembershipNoFromMemberNo(memberNumber),
        hmoProviderId: providerId,
        status: HmoPlanStatus.Active,
      },
    });
    if (!plan) {
      return [];
    }
    const enrollees = await this.manager.find(HmoProfileModel, {
      where: {
        memberPlanId: plan.planTypeId,
        membershipNumber: getMembershipNoFromMemberNo(memberNumber),
        memberNumber: Not(memberNumber),
      },
      relations: ['profile', 'provider', 'profile.details', 'profile.user'],
    });

    return enrollees.map((result) => {
      const { profile, memberNumber, provider } = result;
      return new HmoBeneficiary(
        profile.fullName,
        profile.details.dateOfBirth,
        profile.user.email || profile.secondaryEmail || '',
        profile.user.phoneNumber || profile.secondaryPhoneNumber?.value || '',
        result.relationshipToMember || '',
        memberNumber,
        profile.details.displayPictureUrl || '',
        provider,
        plan.createdDate,
      );
    });
  }
  generateStatus(
    input: PreAuthUtilisationsModel,
    facilityRolesRegistered = [],
    isPreAuth = false,
  ): PaCodeStatus {
    const defaultVet = { status: PaCodeStatus.Pending } as ClaimsApprovalInput;
    const defaultVetApproved = {
      status: PaCodeStatus.Approved,
    } as ClaimsApprovalInput;

    let claimOfficerApproval = [defaultVetApproved];
    if (facilityRolesRegistered.includes(UserType.ClaimOfficer)) {
      claimOfficerApproval = (input?.utilisationStatus || [])?.filter(
        ({ vettingGroup }) => vettingGroup === UserType.ClaimOfficer,
      );
    }
    let claimOfficerHODApproval = [defaultVetApproved];
    if (facilityRolesRegistered.includes(UserType.ClaimOfficerHOD)) {
      claimOfficerHODApproval = (input?.utilisationStatus || [])?.filter(
        ({ vettingGroup }) => vettingGroup === UserType.ClaimOfficerHOD,
      );
    }
    let ClaimReviewerApproval = [defaultVetApproved];
    if (facilityRolesRegistered.includes(UserType.ClaimReviewer)) {
      ClaimReviewerApproval = (input?.utilisationStatus || [])?.filter(
        ({ vettingGroup }) => vettingGroup === UserType.ClaimReviewer,
      );
    }
    let ClaimReviewerHODApproval = [defaultVetApproved];
    if (facilityRolesRegistered.includes(UserType.ClaimReviewerHOD)) {
      ClaimReviewerHODApproval = (input?.utilisationStatus || [])?.filter(
        ({ vettingGroup }) => vettingGroup === UserType.ClaimReviewerHOD,
      );
    }
    let ClaimAuditApproval = [defaultVetApproved];
    if (facilityRolesRegistered.includes(UserType.ClaimAudit)) {
      ClaimAuditApproval = (input?.utilisationStatus || [])?.filter(
        ({ vettingGroup }) => vettingGroup === UserType.ClaimAudit,
      );
    }
    let ClaimAuditHODApproval = [defaultVetApproved];
    if (facilityRolesRegistered.includes(UserType.ClaimAuditHOD)) {
      ClaimAuditHODApproval = (input?.utilisationStatus || [])?.filter(
        ({ vettingGroup }) => vettingGroup === UserType.ClaimAuditHOD,
      );
    }
    let ClaimAdminApproval = [defaultVetApproved];
    if (
      facilityRolesRegistered.includes(UserType.ClaimAdmin) ||
      facilityRolesRegistered.includes(UserType.ClaimFinance)
    ) {
      ClaimAdminApproval = (input?.utilisationStatus || [])?.filter(
        ({ vettingGroup }) =>
          vettingGroup === UserType.ClaimAdmin ||
          vettingGroup === UserType.ClaimFinance,
      );
    }
    // Only Pre-Auth
    let ClaimAgentApproval = [defaultVetApproved];
    if (facilityRolesRegistered.includes(UserType.ClaimAgent)) {
      ClaimAgentApproval = (input?.utilisationStatus || [])?.filter(
        ({ vettingGroup }) => vettingGroup === UserType.ClaimAgent,
      );
    }
    let ClaimAgentHODApproval = [defaultVetApproved];
    if (facilityRolesRegistered.includes(UserType.ClaimAgentHOD)) {
      ClaimAgentHODApproval = (input?.utilisationStatus || [])?.filter(
        ({ vettingGroup }) => vettingGroup === UserType.ClaimAgentHOD,
      );
    }
    let OrganizationAdminApproval = [defaultVetApproved];
    if (facilityRolesRegistered.includes(UserType.OrganizationAdmin)) {
      OrganizationAdminApproval = (input?.utilisationStatus || [])?.filter(
        ({ vettingGroup }) => vettingGroup === UserType.OrganizationAdmin,
      );
    }

    const vetPreAuthDetails = [
      ...(ClaimAgentApproval?.length ? ClaimAgentApproval : [defaultVet]),
      ...(ClaimAgentHODApproval?.length ? ClaimAgentHODApproval : [defaultVet]),
      ...(OrganizationAdminApproval?.length
        ? OrganizationAdminApproval
        : [defaultVet]),
    ];

    const vetDetails = [
      ...(claimOfficerApproval?.length ? claimOfficerApproval : [defaultVet]),
      ...(claimOfficerHODApproval?.length
        ? claimOfficerHODApproval
        : [defaultVet]),
      ...(ClaimReviewerApproval?.length ? ClaimReviewerApproval : [defaultVet]),
      ...(ClaimReviewerHODApproval?.length
        ? ClaimReviewerHODApproval
        : [defaultVet]),
      ...(ClaimAuditApproval?.length ? ClaimAuditApproval : [defaultVet]),
      ...(ClaimAuditHODApproval?.length ? ClaimAuditHODApproval : [defaultVet]),
      ...(ClaimAdminApproval?.length ? ClaimAdminApproval : [defaultVet]),
      ...(isPreAuth ? vetPreAuthDetails : []),
    ];

    if (isPreAuth) {
      const _claimOfficerHOD = vetDetails.find(
        (_item) => _item?.vettingGroup === UserType.ClaimOfficerHOD,
      );
      if (_claimOfficerHOD) {
        return _claimOfficerHOD.status as PaCodeStatus;
      }
    }

    if (
      isPreAuth &&
      vetDetails.find(({ status }) => status === PaCodeStatus.Rejected)
    ) {
      return PaCodeStatus.Rejected;
    }

    if (
      (isPreAuth &&
        vetDetails.find(({ status }) => status === PaCodeStatus.Approved)) ||
      vetDetails.every(({ status }) => status === PaCodeStatus.Approved)
    ) {
      return PaCodeStatus.Approved;
    }

    const rejectedIndex = vetDetails.findIndex(
      ({ status }) => status === PaCodeStatus.Rejected,
    );
    if (!isPreAuth && rejectedIndex !== -1) {
      const restVetDetails = vetDetails
        .slice(rejectedIndex + 1)
        .reverse()
        ?.filter((item) =>
          facilityRolesRegistered.includes(item?.vettingGroup),
        );

      if (!restVetDetails.length) {
        return PaCodeStatus.Rejected;
      }

      let highestRankStatus = PaCodeStatus.Rejected;
      for (const _item of restVetDetails) {
        if (_item?.status !== PaCodeStatus.Pending) {
          highestRankStatus = _item?.status as PaCodeStatus;
          break;
        }
      }

      return highestRankStatus;
    }

    return PaCodeStatus.Pending;
  }
  getNewClaimStatus(
    utilizations: PreAuthUtilisationsModel[],
    facilityRolesRegistered: string[],
    useCustomStatus = false,
  ): string {
    let newStatus = PaCodeStatus.Approved;
    if (!facilityRolesRegistered.length) {
      throw new Error('Facility UserTypes Not Provided');
    }

    if (
      utilizations.every(
        (_item) =>
          this.generateStatus(_item, facilityRolesRegistered) ===
          PaCodeStatus.Rejected,
      )
    ) {
      return PaCodeStatus.Rejected;
    }

    if (
      utilizations.filter(
        (_item) =>
          this.generateStatus(_item, facilityRolesRegistered) ===
          PaCodeStatus.Rejected,
      )?.length
    ) {
      newStatus = PaCodeStatus.Rejected;
    }

    if (newStatus === PaCodeStatus.Rejected) {
      const flatVet = utilizations.flatMap(
        ({ utilisationStatus }) => utilisationStatus,
      );
      const checkForAnyApproval = flatVet?.filter(
        (_item) => _item?.status === PaCodeStatus.Approved,
      );

      if (checkForAnyApproval?.length) {
        return HmoClaimStatus.PartiallyApproved;
      } else {
        return HmoClaimStatus.Rejected;
      }
    }

    for (const util of utilizations) {
      const status = this.generateStatus(util, facilityRolesRegistered);
      if (status !== PaCodeStatus.Approved) {
        newStatus = status;
        break;
      }
    }

    const utilisationStatuses = (utilizations || []).flatMap(
      ({ utilisationStatus }) => utilisationStatus,
    );

    if (newStatus === PaCodeStatus.Approved) {
      const ClaimFinanceApproval = (utilisationStatuses || [])?.filter(
        (_item) => _item?.vettingGroup === UserType.ClaimFinance,
      );
      if (ClaimFinanceApproval.length) {
        return modifyClaimStatus(
          UserType.ClaimFinance,
          utilizations || [],
          useCustomStatus
            ? `CFO ${HmoClaimStatus.Approved}`
            : HmoClaimStatus.Approved,
        );
      }
      const ClaimAdminApproval = (utilisationStatuses || [])?.filter(
        (_item) => _item?.vettingGroup === UserType.ClaimAdmin,
      );
      if (ClaimAdminApproval.length) {
        return modifyClaimStatus(
          UserType.ClaimAdmin,
          utilizations || [],
          useCustomStatus
            ? `MD ${HmoClaimStatus.Approved}`
            : HmoClaimStatus.Approved,
        );
      }
      return HmoClaimStatus.Approved;
    }

    const ClaimFinanceApproval = (utilisationStatuses || [])?.filter(
      (_item) => _item?.vettingGroup === UserType.ClaimFinance,
    );
    if (ClaimFinanceApproval.length) {
      return `Partially ${
        useCustomStatus
          ? `CFO ${HmoClaimStatus.Approved}`
          : HmoClaimStatus.Approved
      }`;
    }
    const ClaimAdminApproval = (utilisationStatuses || [])?.filter(
      (_item) => _item?.vettingGroup === UserType.ClaimAdmin,
    );
    if (ClaimAdminApproval.length) {
      return `Partially ${
        useCustomStatus
          ? `MD ${HmoClaimStatus.Approved}`
          : HmoClaimStatus.Approved
      }`;
    }
    const ClaimAuditHODApproval = (utilisationStatuses || [])?.filter(
      (_item) => _item?.vettingGroup === UserType.ClaimAuditHOD,
    );
    if (ClaimAuditHODApproval.length) {
      return modifyClaimStatus(
        UserType.ClaimAuditHOD,
        utilizations || [],
        useCustomStatus
          ? `HOD Audit ${HmoClaimStatus.Checked}`
          : HmoClaimStatus.Checked,
      );
    }
    const ClaimAuditApproval = (utilisationStatuses || [])?.filter(
      (_item) => _item?.vettingGroup === UserType.ClaimAudit,
    );
    if (ClaimAuditApproval.length) {
      return modifyClaimStatus(
        UserType.ClaimAudit,
        utilizations || [],
        useCustomStatus
          ? `Audit ${HmoClaimStatus.Reviewed}`
          : HmoClaimStatus.Reviewed,
      );
    }
    const ClaimReviewerHODApproval = (utilisationStatuses || [])?.filter(
      (_item) => _item?.vettingGroup === UserType.ClaimReviewerHOD,
    );
    if (ClaimReviewerHODApproval.length) {
      return modifyClaimStatus(
        UserType.ClaimReviewerHOD,
        utilizations || [],
        useCustomStatus
          ? `HOD Operations ${HmoClaimStatus.Approved}`
          : HmoClaimStatus.Approved,
      );
    }
    const ClaimReviewerApproval = (utilisationStatuses || [])?.filter(
      (_item) => _item?.vettingGroup === UserType.ClaimReviewer,
    );
    if (ClaimReviewerApproval.length) {
      return modifyClaimStatus(
        UserType.ClaimReviewer,
        utilizations || [],
        useCustomStatus
          ? `Reconciliation ${HmoClaimStatus.Checked}`
          : HmoClaimStatus.Checked,
      );
    }
    const ClaimOfficerHODApproval = (utilisationStatuses || [])?.filter(
      (_item) => _item?.vettingGroup === UserType.ClaimOfficerHOD,
    );
    if (ClaimOfficerHODApproval.length) {
      return modifyClaimStatus(
        UserType.ClaimOfficerHOD,
        utilizations || [],
        HmoClaimStatus.QA,
      );
    }
    const ClaimOfficerApproval = (utilisationStatuses || [])?.filter(
      (_item) => _item?.vettingGroup === UserType.ClaimOfficer,
    );
    if (ClaimOfficerApproval.length) {
      return modifyClaimStatus(
        UserType.ClaimOfficer,
        utilizations || [],
        HmoClaimStatus.Vetted,
      );
    }
  }
  async updatePreAuthUtilizationStatus(
    mutator: ProfileModel,
    input: UpdatePreAuthUtilizationStatusInput,
  ) {
    const util = await this.manager.findOne(PreAuthUtilisationsModel, {
      where: { id: input.id },
      relations: [
        'preAuthorization',
        'hmoClaim',
        'preAuthorization.profile',
        'preAuthorization.hospital',
      ],
    });
    if (!util) {
      throw new NotAcceptableException('Utilization Not Found');
    }
    if (util.transferFundId) {
      throw new Error('This Utilization Has Been Paid For');
    }

    const serviceAmount =
      util.amountCovered ||
      Number(util.quantity || '0') * Number(util.price || '0');

    const vetingIndex = (util?.utilisationStatus || []).findIndex(
      (item) => item?.creatorId === mutator.id,
    );
    const enrolleeProfileId =
      util?.preAuthorization?.profileId || util?.hmoClaim?.profileId;
    if (vetingIndex !== -1) {
      util.utilisationStatus[vetingIndex] = {
        ...util.utilisationStatus[vetingIndex],
        status: input.status,
        comment: input.comment,
        rejectionReason: input.rejectionReason,
        specifyReasonForRejection: input?.specifyReasonForRejection,
        statusDescription: input.statusDescription,
        serviceAmount,
        vettingGroup: mutator.type,
        creatorName: mutator.fullName,
        updatedDate: new Date(),
      };
    } else {
      util.utilisationStatus = [
        ...(util.utilisationStatus || []),
        {
          status: input.status,
          comment: input.comment,
          rejectionReason: input.rejectionReason,
          statusDescription: input.statusDescription,
          specifyReasonForRejection: input?.specifyReasonForRejection,
          serviceAmount,
          vettingGroup: mutator.type,
          creatorId: mutator.id,
          creatorName: mutator.fullName,
          createdDate: new Date(),
        },
      ];
    }

    const facilityTypes = await this.manager.find(ProfileModel, {
      select: ['type'],
      where: { hospital: { id: mutator.hospitalId } },
    });
    const facilityTypeList = (facilityTypes || []).map(({ type }) => type);

    // Here...
    const newStatus = this.generateStatus(
      util,
      facilityTypeList,
      !!util?.preAuthorizationId,
    );

    util.status = newStatus || input.status;
    util.rejectionReason = input.rejectionReason;
    util.specifyReasonForRejection = input?.specifyReasonForRejection;
    util.statusDescription = input.statusDescription;
    util.updatedBy = mutator;
    util.updatedDate = new Date();
    util.lastModifierName = mutator.fullName;
    util.paCode = input.paCode || util.paCode;

    await this.manager.save(PreAuthUtilisationsModel, util);

    if (util?.hmoClaim?.providerId) {
      const claim = await this.manager.findOne(HmoClaimModel, {
        where: {
          id: util.hmoClaim.id,
          providerId: util.hmoClaim.providerId,
        },
        relations: ['utilizations', 'profile', 'provider'],
      });

      let newClaimStatus;
      let newPayoutStatus;
      if (
        claim?.status &&
        ['paid', 'partially paid'].includes(claim.status.toLowerCase())
      ) {
        newClaimStatus = claim.status;
        if (
          claim?.payoutStatus === 'paid' &&
          input.status === PaCodeStatus.Approved
        ) {
          newPayoutStatus = 'partially paid';
        }
      } else {
        newClaimStatus = this.getNewClaimStatus(
          claim.utilizations,
          facilityTypeList,
          `${claim?.provider?.providerCode}` === '33',
        );
      }

      if (claim && newClaimStatus) {
        const hmoClaim = { ...claim };

        if (
          newClaimStatus !== claim.status ||
          claim.payoutStatus !== newPayoutStatus
        ) {
          hmoClaim.status = newClaimStatus;
          hmoClaim.payoutStatus = newPayoutStatus;
          hmoClaim.updatedBy = mutator;
          hmoClaim.lastModifierName = mutator.fullName;
          await this.manager.save(HmoClaimModel, hmoClaim);
        }

        util.hmoClaim = hmoClaim;
      }
    }

    if (util.preAuthorization) {
      if (util.preAuthorization.claimId) {
        const claim = await this.manager.findOne(HmoClaimModel, {
          where: {
            claimId: util.preAuthorization.claimId,
            providerId: util.hmoProviderId,
            profileId: enrolleeProfileId,
          },
          relations: ['utilizations'],
        });

        if (claim) {
          const claimUtil = claim.utilizations.find(
            (cUtil) => cUtil.utilizationCode === util.utilizationCode,
          );
          if (claimUtil) {
            claimUtil.status = input.status;
            claimUtil.rejectionReason = input.rejectionReason;
            claimUtil.specifyReasonForRejection =
              input?.specifyReasonForRejection;
            claimUtil.statusDescription = input.statusDescription;
            claimUtil.updatedBy = mutator;
            claimUtil.updatedDate = new Date();
            claimUtil.lastModifierName = mutator.fullName;
            await this.manager.save(PreAuthUtilisationsModel, claimUtil);
          }
        }
      }
    }
    if (util.preAuthorization && !util.preAuthorization.responseDateTime) {
      util.preAuthorization.responseDateTime = new Date();
      await this.manager.save(PreauthorisationModel, util.preAuthorization);
    }
    if (util.hmoClaim) {
      util.hmoClaim = await this.manager.findOne(HmoClaimModel, {
        where: {
          id: util.hmoClaim.id,
        },
        relations: ['utilizations', 'profile', 'hospital'],
        order: { utilizations: { type: 'ASC' } },
      });
    }
    if (util.preAuthorization) {
      util.preAuthorization = await this.manager.findOne(
        PreauthorisationModel,
        {
          where: {
            id: util.preAuthorization.id,
          },
          relations: ['utilizations', 'profile', 'hospital'],
          order: { utilizations: { type: 'ASC' } },
        },
      );
    }
    return util;
  }
  async updatePreAuthUtilizationsStatus(
    mutator: ProfileModel,
    input: UpdateUtilizationsStatusInput,
  ) {
    await this.manager
      .createQueryBuilder(PreAuthUtilisationsModel, 'pre_auth_utilisations')
      .update(PreAuthUtilisationsModel)
      .set({
        status: input.status,
        updatedBy: mutator,
        updatedDate: new Date(),
        lastModifierName: mutator.fullName,
      })
      .where('pre_auth_utilisations.pre_auth = ANY(:ids)', {
        ids: input.ids,
      })
      .execute();
    const preAuths = await this.manager.find(PreauthorisationModel, {
      where: {
        id: In(input.ids),
      },
      relations: ['utilizations'],
    });

    for (const preAuth of preAuths) {
      const enrolleeProfileId = preAuth.profileId;
      if (preAuth.claimId) {
        const claim = await this.manager.findOne(HmoClaimModel, {
          where: {
            claimId: preAuth.claimId,
            providerId: preAuth.providerId,
            profileId: enrolleeProfileId,
          },
          relations: ['utilizations'],
        });
        if (claim) {
          claim.utilizations.forEach((cUtil) => {
            const preAuthUtil = preAuth.utilizations.find(
              (pUtil) => pUtil.utilizationCode === cUtil.utilizationCode,
            );
            if (preAuthUtil) {
              cUtil.status = preAuthUtil.status;
              cUtil.updatedBy = mutator;
              cUtil.updatedDate = new Date();
              cUtil.lastModifierName = mutator.fullName;
            }
          });
          await this.manager.save(PreAuthUtilisationsModel, claim.utilizations);
        }
      }
      if (!preAuth.responseDateTime) {
        preAuth.responseDateTime = new Date();
        await this.manager.save(PreauthorisationModel, preAuth);
      }
    }

    return preAuths;
  }
  async updateClaimUtilizationsStatus(
    mutator: ProfileModel,
    input: UpdateUtilizationsStatusInput,
  ) {
    await this.manager
      .createQueryBuilder(HmoClaimModel, 'hmo_claims')
      .update(HmoClaimModel)
      .set({
        status: input.status,
        updatedBy: mutator,
        updatedDate: new Date(),
        lastModifierName: mutator.fullName,
      })
      .where('hmo_claims.id = ANY(:ids)', {
        ids: input.ids,
      })
      .andWhere('hmo_claims.status != :status', {
        status: ClaimStatus.Draft,
      })
      .execute();

    const claims = await this.manager.find(HmoClaimModel, {
      where: {
        id: In(input.ids),
      },
      relations: ['utilizations'],
    });
    for (const claim of claims) {
      if ((input.status as any) === ClaimStatus.Paid) {
        claim.paidBy = mutator.fullName;
      }
      if (
        !claim.responseDateTime &&
        [ClaimStatus.Paid, ClaimStatus.Rejected].includes(input.status as any)
      ) {
        claim.responseDateTime = new Date();
        await this.manager.save(HmoClaimModel, claim);
      }
    }
    return claims;
  }
  async movePreAuthUtilizationToClaim(
    mutator: ProfileModel,
    hmoProfile: HmoProfileModel,
    _input: PreauthorisationModel,
    evaluateBusinessRule: typeof BusinessRuleService.prototype.evaluateBusinessRuleWithFiltering,
  ) {
    const input = { ..._input };
    const hmoPlanType = await this.manager.findOne(HmoPlanTypeModel, {
      where: {
        id: hmoProfile.memberPlanId,
      },
    });
    if (!hmoPlanType) {
      throw new BadRequestException('HMO Plan Type Not Found');
    }
    // Validate date submission validity
    if (['20', '107'].includes(input.provider?.providerCode)) {
      input.utilizations = (input.utilizations || []).map((_util) => {
        const isAdmission = ['admission', 'admit', 'admitted', 'bed'].some(
          (keyword) =>
            `${_util.category}${_util.type}`.toLowerCase().includes(keyword),
        );
        const isFeeding = `${_util.category}${_util.type}`
          .toLowerCase()
          .includes('feeding');

        const _quantity = Number(_util.quantity);
        if (isAdmission && _quantity && !isFeeding) {
          return { ..._util, quantity: `${_quantity - 1}` } as typeof _util;
        }
        return _util;
      });
      const daysDiff = moment(new Date()).diff(
        moment(input.requestDateTime),
        'days',
      );
      if (daysDiff > 30) {
        throw new Error('Claim Submission Timeline Exceeded');
      }
      const drugs = input.utilizations.filter((util) =>
        util.category.toLowerCase().startsWith('drug'),
      );
      input.utilizations = input.utilizations.filter(
        (util) => !util.category.toLowerCase().startsWith('drug'),
      );
      if (!input.utilizations.length) {
        throw new NotAcceptableException(
          `Not Allowed To Submit Undispensed Drugs (${drugs
            .map((v) => v.type)
            .join(', ')})`,
        );
      }
    }
    if (input.provider?.providerCode === '100') {
      function shouldAllowClaimTreatmentDate() {
        const paStartOfMonth = moment(input.requestDateTime)
          .startOf('month')
          .format('YYYY-MM-DD');
        const currentMonth = moment(new Date())
          .startOf('month')
          .format('YYYY-MM-DD');
        if (paStartOfMonth !== currentMonth) {
          const afterCurrentMonthDay15 = moment(new Date())
            .endOf('day')
            .isAfter(moment(new Date()).startOf('month').add(15, 'day'));
          return !afterCurrentMonthDay15;
        }
        return true;
      }
      if (!shouldAllowClaimTreatmentDate()) {
        throw new Error('Claim Submission Timeline Exceeded');
      }
    }

    const runEvaluateBusinessRule = async (
      utilizations: PreAuthUtilisationsModel[],
    ) => {
      const businessRuleResult = await evaluateBusinessRule(
        hmoPlanType.hospitalId,
        {
          hmoProviderId: hmoProfile.providerId,
          planId: hmoProfile.memberPlanId,
          enrolleeId: hmoProfile.memberNumber,
          diagnosis: input.diagnosis
            .map((d) =>
              [d.diagnosisICD10, d.diagnosisICD11, d.diagnosisSNOMED].filter(
                Boolean,
              ),
            )
            .flat(),
          visitationType: input.serviceType,
        },
        utilizations.map((util) => ({
          utilizationCategory: util.category,
          utilizationType: util.type,
          quantity: Number(util.quantity),
          utilizationId: util.utilizationCode,
          amount: util.price
            ? parseFloat(util.price) * Number(util.quantity)
            : 0,
          enrolleeStatus: hmoProfile.memberStatus,
          planCategory: hmoProfile.planCategory,
          specialty: util.specialty,
        })),
      );

      if (businessRuleResult?.flags?.length) {
        utilizations = utilizations.map((util) => {
          util.flags = businessRuleResult.flags
            .filter((f) => f.utilizationId === util.utilizationCode)
            .map(
              (v) =>
                ({
                  flag: v.flag,
                  ruleId: v.ruleId,
                } as FlagDto),
            );
          return util;
        });
        if (businessRuleResult.shouldPreventSubmission) {
          utilizations = utilizations.filter((util) =>
            businessRuleResult.notFlaggedUtilizations.some(
              (notFlagged) => notFlagged.utilizationId === util.utilizationCode,
            ),
          );
        }
        return utilizations;
      }
      return utilizations;
    };
    let validUtils = input.utilizations.filter(
      (util) => util.status === PaCodeStatus.Approved,
    );

    if (!validUtils.length) {
      throw new NotAcceptableException('No Approved Utilizations');
    }
    if (input.claimId) {
      const claim = await this.manager.findOne(HmoClaimModel, {
        where: {
          claimId: input.claimId,
          providerId: input.providerId,
          profileId: hmoProfile.profileId,
        },
        relations: ['utilizations'],
      });
      if (claim) {
        validUtils = validUtils.filter(
          (util) =>
            !claim.utilizations.find(
              (cUtil) => cUtil.utilizationCode === util.utilizationCode,
            ),
        );
        const claimUtiLength = claim.utilizations.length + validUtils.length;

        let claimStatus = 'Partially Submitted';

        if ((input.claimStatus || '').toLowerCase() === 'partially paid') {
          claimStatus = 'Partially Paid';
        } else if (
          claimUtiLength === input.utilizations.length ||
          input.utilizations.length === 0
        ) {
          claimStatus = 'Submitted';
        }

        if (!validUtils.length) {
          return {
            claimId: claim.claimId,
            batchNumber: claim.batchNumber,
            utilizations: [],
            claimStatus,
          };
        }
        validUtils = input.isExternalPlanType
          ? validUtils
          : await runEvaluateBusinessRule(validUtils);

        const newClaimUtilizations = validUtils.map(
          (util) =>
            new PreAuthUtilisationsModel({
              paCode: util.paCode,
              status: PaCodeStatus.Pending,
              visitDetailsId: util.visitDetailsId,
              utilizationCode: util.utilizationCode,
              utilizationId: util.utilizationId,
              type: util.type,
              quantity: util.quantity,
              price: util.price,
              category: util.category,
              hmoProviderId: util.hmoProviderId,
              visitTypeId: util.visitTypeId,
              profileId: util.profileId,
              hmoProfilePlanId: util.hmoProfilePlanId,
              createdBy: mutator,
              creatorName: mutator.fullName,
              hmoClaimId: claim.id,
              paUtilProcessed: !!util.utilisationStatus?.length,
              flags: util.flags,
              medicationCategory: util.medicationCategory,
              dosage: util.dosage,
              dosageUnit: util.dosageUnit,
              frequency: util.frequency,
              duration: util.duration,
              birthCount: util.birthCount,
              deliveryDateTime: util.deliveryDateTime,
              specialty: util.specialty,
              paymentModel: util?.paymentModel,
            }),
        );
        await this.manager.save(PreAuthUtilisationsModel, newClaimUtilizations);
        if (claim?.status && claim.status.toLowerCase() === 'paid') {
          await this.manager
            .createQueryBuilder(HmoClaimModel, 'hmo_claims')
            .update(HmoClaimModel)
            .set({
              status: 'Partially Paid',
              updatedDate: () => 'updated_date',
            })
            .where('hmo_claims.id = :claimId', { claimId: claim.id })
            .execute();
        }

        return {
          claimId: claim.claimId,
          batchNumber: claim.batchNumber,
          utilizations: [],
          claimStatus,
        };
      }
    }
    const hmoHospital = await this.manager.findOne(HmoHospitalModel, {
      where: {
        providerId: input.providerId,
        hospitalId: input.hospitalId || mutator.hospitalId,
      },
    });

    if (!hmoHospital) {
      throw new NotAcceptableException(
        'Hospital Not Authorized For Claims Submission',
      );
    }
    const batchNumber = generateBatchNumber(hmoHospital.hmoProviderId);

    const claimNumberDoc = await getClaimNumberCollection(input.providerId);
    validUtils = input.isExternalPlanType
      ? validUtils
      : await runEvaluateBusinessRule(validUtils);
    return {
      visitId: input.visitId,
      visitDetailsId: input.visitId,
      claimId: claimNumberDoc.value.value,
      claimIdentity: undefined,
      batchNumber,
      totalCharged: 0,
      claimStatus:
        validUtils.length === input.utilizations.length
          ? 'Submitted'
          : 'Partially Submitted',
      totalTariffFee: input.utilizations?.reduce(
        (acc, { quantity, price }) => acc + Number(quantity) * Number(price),
        0,
      ),
      referredBy: input.referredBy,
      referralCode: input.referralCode,
      referredFrom: input.referredFrom,
      referredTo: input.referredTo,
      utilizations: validUtils?.map((util) => {
        return new PreAuthUtilisationsModel({
          paCode: util.paCode,
          status: PaCodeStatus.Pending,
          visitDetailsId: util.visitDetailsId,
          utilizationCode: util.utilizationCode,
          utilizationId: util.utilizationId,
          type: util.type,
          quantity: util.quantity,
          price: util.price,
          category: util.category,
          hmoProviderId: util.hmoProviderId,
          visitTypeId: util.visitTypeId,
          profileId: util.profileId,
          hmoProfilePlanId: util.hmoProfilePlanId,
          createdBy: mutator,
          creatorName: mutator.fullName,
          paUtilProcessed: !!util.utilisationStatus?.length,
          flags: util.flags,
          medicationCategory: util.medicationCategory,
          dosage: util.dosage,
          dosageUnit: util.dosageUnit,
          frequency: util.frequency,
          duration: util.duration,
          birthCount: util.birthCount,
          deliveryDateTime: util.deliveryDateTime,
          specialty: util.specialty,
          paymentModel: util?.paymentModel,
        });
      }),
    };
  }

  async updateClaimStatus(
    mutator: ProfileModel,
    status: string,
    claimId: string,
  ): Promise<HmoClaimModel> {
    const claim = await this.manager.findOne(HmoClaimModel, {
      where: { id: claimId },
      relations: [
        'utilizations',
        'hospital',
        'profile',
        'utilizations.transferFund',
        'utilizations.transferFund.createdBy',
      ],
    });
    if (!claim) {
      throw new NotAcceptableException('Claim Not Found');
    }

    if (claim.status === status) return claim;

    claim.status = status;
    claim.updatedBy = mutator;
    claim.updatedDate = new Date();
    claim.lastModifierName = mutator.fullName;
    if (status === ClaimStatus.Paid) {
      claim.paidBy = mutator.fullName;
    }
    if (
      [ClaimStatus.Paid, ClaimStatus.Rejected].includes(status as any) &&
      !claim.responseDateTime
    ) {
      claim.responseDateTime = new Date();
    }
    await this.manager.save(HmoClaimModel, claim);
    const preAuth = await this.manager.findOne(PreauthorisationModel, {
      where: {
        claimId: claim.claimId,
        providerId: claim.providerId,
        profileId: claim.profileId,
      },
    });

    if (
      status === ClaimStatus.Paid &&
      preAuth?.claimStatus &&
      preAuth.claimStatus?.toLowerCase() === 'partially submitted'
    ) {
      await this.manager
        .createQueryBuilder(PreauthorisationModel, 'pre_authorizations')
        .update(PreauthorisationModel)
        .where('pre_authorizations.claim_id = :claimId', {
          claimId: claim.claimId,
        })
        .andWhere('pre_authorizations.profile_id = :profileId', {
          profileId: claim.profileId,
        })
        .andWhere('pre_authorizations.provider_id = :providerId', {
          providerId: claim.providerId,
        })
        .set({ claimStatus: 'Partially Paid' })
        .execute();
    } else {
      await this.manager
        .createQueryBuilder(PreauthorisationModel, 'pre_authorizations')
        .update(PreauthorisationModel)
        .where('pre_authorizations.claim_id = :claimId', {
          claimId: claim.claimId,
        })
        .andWhere('pre_authorizations.profile_id = :profileId', {
          profileId: claim.profileId,
        })
        .andWhere('pre_authorizations.provider_id = :providerId', {
          providerId: claim.providerId,
        })
        .set({ claimStatus: status })
        .execute();
    }

    return claim;
  }
  async updateClaimRevertAction(
    dataSource: DataSource,
    mutator: ProfileModel,
    action: string,
    claimId: string,
  ): Promise<HmoClaimModel> {
    if (action !== 'Sent Back' && action !== 'Returned') {
      throw new BadRequestException('Invalid Status Provided');
    }
    const claim = await this.manager.findOne(HmoClaimModel, {
      where: {
        id: claimId,
      },
      relations: ['utilizations', 'hospital', 'profile'],
    });
    if (!claim) {
      throw new NotAcceptableException('Claim Not Found');
    }

    const hmoProviderRoles = await queryDSWithSlave(
      dataSource,
      `
        SELECT type, type_alias
        FROM profiles
        WHERE hmo_id = $1
          AND profiles.deleted_date IS NULL
        GROUP BY type, type_alias
      `,
      [mutator.hmoId],
    );
    const availableVettingGroups = hmoProviderRoles.map((v) => v.type);

    const receiverUserType = getTargetRole(
      action,
      mutator.type as UserType,
      availableVettingGroups,
    );

    if (!receiverUserType) {
      throw new NotAcceptableException('Cannot Update Status');
    }

    const TypeAliasMap = {};
    hmoProviderRoles.forEach((_item) => {
      if (!_item.type_alias) return;

      if (!TypeAliasMap[_item.type]) {
        TypeAliasMap[_item.type] = _item.type_alias;
      }
    });

    claim.revertAction = {
      action,
      receiverUserType,
      mutatorUserType: mutator.type as UserType,
      mutatorAlias: TypeAliasMap[mutator.type] || '',
      receiverAlias: TypeAliasMap[receiverUserType] || '',
    };
    claim.updatedBy = mutator;
    claim.updatedDate = new Date();
    claim.lastModifierName = mutator.fullName;

    return this.manager.save(HmoClaimModel, claim);
  }
  principalMembershipNoGenerator(
    providerCode: number,
    planTypeCode?: string,
  ): Promise<string> {
    const generator = {
      20: this.principalMembershipNoGeneratorLASHMA.bind(this),
      107: this.principalMembershipNoGeneratorLASHMA.bind(this),
    } as const;
    const generatorFn = generator[providerCode];
    if (!generatorFn) {
      throw new BadRequestException('Invalid Provider Code');
    }
    return generatorFn(providerCode, planTypeCode);
  }
  async principalMembershipNoGeneratorLASHMA(
    providerCode: number,
    planTypeCode?: string,
  ) {
    // Format: <Agency Code>-<12 digit incremental number with 0 padding>/<principal membership number "0">
    const res = await getPrincipalMembershipNoGeneratorLASHMA(providerCode);
    const num = res.value;
    const paddedNum = num.value.toString().padStart(12, '0');
    let prefix = CLINIFY_HMO_AGENCY_PREFIX_CODE[providerCode];

    if (planTypeCode && PLAN_TYPES_PREFIX[planTypeCode]) {
      prefix = PLAN_TYPES_PREFIX[planTypeCode];
    }
    return `${prefix}-${paddedNum}/0`;
  }
  async generateLASHMAPACode(args: {
    benefitCode: string;
    requestDateTime: Date;
    providerCode: string;
  }) {
    // Format: <Agency Code>/<Benefit Category Code>/<General Incremental Number>
    const num = await getPaCodeNumberCollection('LASHMA_PA_INCREMENTAL_NUMBER');
    return `${CLINIFY_HMO_AGENCY_PREFIX_CODE[Number(args.providerCode)]}/${
      args.benefitCode || ''
    }/${num.value.value}`;
  }

  async generatePACode(args: {
    providerCode: string;
    referringHospitalId: string;
    referredHospitalId?: string;
    requestDateTime: Date;
    type: 'REF' | 'PA';
    customPaFormatType: boolean;
    benefitCode?: string;
  }) {
    const {
      providerCode,
      referringHospitalId,
      referredHospitalId,
      requestDateTime,
      type,
      customPaFormatType,
      benefitCode,
    } = args;
    if (customPaFormatType) {
      const paGenerator = this.paGenerator[providerCode];
      if (!paGenerator) {
        throw new NotAcceptableException('Custom PA Code Format Not Supported');
      }
      return await paGenerator({
        benefitCode,
        requestDateTime,
        providerCode,
      });
    }
    const rrfHos = await this.manager.findOne(HmoHospitalModel, {
      where: {
        hospitalId: referringHospitalId,
      },
      relations: ['hospital'],
    });

    const rdfHos =
      type === 'PA'
        ? rrfHos
        : await this.manager.findOne(HmoHospitalModel, {
            where: {
              hospitalId: referredHospitalId,
            },
            relations: ['hospital'],
          });
    // first letter of the reqest date month
    const month = moment(requestDateTime).format('MMMM')[0].toUpperCase();
    const yearAndMonth = moment(requestDateTime).format('YYYYMM');
    const providerPrefix = CLINIFY_HMO_AGENCY_PREFIX_CODE[Number(providerCode)];
    const benefitPrefix = `${providerPrefix}/${rrfHos.hmoProviderId}/${type}/${yearAndMonth}`;
    const num = await getPaCodeNumberCollection(benefitPrefix);
    const strNum = `0${num.value.value}`;

    // date <day><month><year>  e.g 010223
    const date = moment(requestDateTime).format('DDMMYY');

    const ownershipCode =
      rdfHos.hospital?.ownership === 'Private' ? 'PRF' : 'PUF';
    if (type === 'REF') {
      const rrfHospitalCode = rrfHos.hmoProviderId;
      const rrfHospitalNumber = rrfHos.hmoProviderUniqueId || '';
      const rdfHospitalCode = rdfHos.hmoProviderId;
      // eslint-disable-next-line max-len
      // REF code: <Referriing Hospital Code><Referring Hospital Number><Referred Hospital Code><Number><Month>/<Date><Ownership Code><Referring Hospital Code>
      return `${rrfHospitalCode}${rrfHospitalNumber}${strNum}${month}/${date}${ownershipCode}${rdfHospitalCode}`;
    }
    const hospitalCode = rrfHos.hmoProviderId;
    const hospitalNumber = rrfHos.hmoProviderUniqueId || '';
    // PA code: <Ownership Code><Hospital Code><Hospital Number><Number><Month>/<Date><Ownership Code><Hospital Code>
    return `${ownershipCode}${hospitalCode}${hospitalNumber}${strNum}${month}/${date}${ownershipCode}${hospitalCode}`;
  }
  async createPreauthorizationReferralRequest(
    mutator: ProfileModel,
    input: PreauthorisationReferralInput,
  ) {
    const profilePlan = await this.manager.findOne(HmoProfilePlanModel, {
      where: {
        membershipNumber: getMembershipNoFromMemberNo(input.enrolleeId),
        hmoProviderId: input.providerId,
        status: HmoPlanStatus.Active,
      },
      relations: ['planType', 'hmoProvider'],
    });
    if (!profilePlan && !input.isExternalPlanType) {
      throw new NotAcceptableException('Plan Not Found');
    }

    const visit = await this.checkInEnrollee(
      mutator,
      input.providerId,
      input.enrolleeId,
    );

    const facilityPreference = await this.getProviderPreference(
      input.providerId,
    );

    const hmoProvider = profilePlan.hmoProvider;

    const paCodeNumber = await this.generatePACode({
      providerCode: hmoProvider.providerCode,
      referringHospitalId: input.referringProviderId,
      referredHospitalId: input.referredProviderId,
      requestDateTime: input.requestDateTime,
      type: 'REF',
      customPaFormatType: facilityPreference?.customPaFormatType,
      benefitCode: 'REF',
    });

    let utilizations: any[] = input.utilizations || [];

    if (utilizations.length) {
      utilizations = utilizations.map((u) => ({
        ...u,
        visitDetailsId: visit.visitationId,
        paCode: paCodeNumber,
        status: PaCodeStatus.Pending,
      }));
    } else {
      utilizations = [
        new PreAuthReferralUtilisationsModel({
          visitDetailsId: visit.visitationId,
          paCode: paCodeNumber,
          status: PaCodeStatus.Pending,
          utilizationCode: 'REF',
          category: 'Referral',
          type: 'Referral',
          quantity: '1',
          price: '0',
        }),
      ];
    }

    return Promise.resolve(
      new PreauthorisationReferralModel({
        ...input,
        visitDetailIds: [],
        createdBy: mutator,
        requestedBy: input.requestedBy,
        referredBy: mutator.fullName,
        utilizations,
      }),
    );
  }
  async updateHmoPharmacySupport(
    dataSource: DataSource,
    providerId: string,
    support: boolean,
    providers: string[],
  ): Promise<boolean> {
    if (!providerId) {
      throw new NotAcceptableException('Not Authorized To Update Record');
    }
    const hmoProvider = await this.manager.findOne(HmoProviderModel, {
      where: { id: providerId },
    });
    if (!hmoProvider) {
      throw new NotAcceptableException('Provider Not Found');
    }
    if (!hmoProvider.controlledPriceVisibility) {
      const res = await queryDSWithSlave(
        dataSource,
        `SELECT hospitals.id
        FROM hmo_hospitals
        INNER JOIN hospitals ON hmo_hospitals.hospital_id = hospitals.id
        WHERE hmo_hospitals.provider_id = '${providerId}'
       AND hospitals.plan IN ('${HospitalPlan.PharmacyPlusInventory}', '${HospitalPlan.PharmacyMinusInventory}')
        ORDER BY hospitals.name ASC`,
      );
      providers = res?.map((r) => r.id) || [];
    }
    await this.manager.update(HmoProviderModel, providerId, {
      controlledPriceVisibility: support,
      providersPriceVisibleTo: providers,
    });
    memCache.delete(`controlledPriceVisibility-${providerId}`);
    memCache.delete(new RegExp(`controlledPriceVisibility-${providerId}-.*`));
    return support;
  }
  async getHmoPharmacySupport(providerId: string): Promise<HmoProviderModel> {
    if (!providerId) {
      throw new UnauthorizedException('Not Authorized');
    }
    const hmoProvider = await this.manager.findOne(HmoProviderModel, {
      where: { id: providerId },
    });

    hmoProvider.providersPriceVisibleTo =
      hmoProvider.providersPriceVisibleTo || [];
    return hmoProvider;
  }
  async getBenefitPriceVisible(hospitalId: string, providerId: string) {
    const hmoProvider = await this.findOne({
      where: { id: providerId },
      select: ['controlledPriceVisibility', 'providersPriceVisibleTo', 'id'],
    });
    if (!hmoProvider)
      return {
        controlled: false,
        visibleTo: true,
      };
    if (!hmoProvider.controlledPriceVisibility)
      return {
        controlled: false,
        visibleTo: true,
      };
    return {
      controlled: true,
      visibleTo: hmoProvider.providersPriceVisibleTo?.includes(hospitalId),
    };
  }

  async getHospitalByProviderCode(
    datasource: DataSource,
    providerCode: string,
    filterOptions: HmoFilterOptions,
  ): Promise<HmoHospitalResponse> {
    const { skip, take, name } = filterOptions;
    const params = [providerCode, take, skip];
    if (name) params.push(`%${name}%`);
    const res: HospitalModel[] = await queryDSWithSlave(
      datasource,
      `SELECT hospitals.id, hospitals.name, hospitals.address
        FROM hmo_hospitals
        INNER JOIN hospitals ON hmo_hospitals.hospital_id = hospitals.id
        INNER JOIN hmo_providers ON hmo_hospitals.provider_id = hmo_providers.id
        WHERE hmo_providers.provider_code = $1
        ${name ? 'AND hospitals.name ILIKE $4' : ''}
        ORDER BY hospitals.name ASC
        LIMIT $2 OFFSET $3`,
      params,
    );

    return new HmoHospitalResponse(res, res.length);
  }

  async getHmoAgencyRoles(
    datasource: DataSource,
    hmoId?: string,
    providerCode?: string,
  ): Promise<string[]> {
    let whereClause = `profiles.hmo_id = $1`;
    let params: string[] = [hmoId];

    if (!hmoId && !providerCode) return [];

    if (!hmoId && providerCode) {
      whereClause = `hmo_providers.provider_code = $1`;
      params = [providerCode];
    }

    const res = await queryDSWithSlave(
      datasource,
      `
        SELECT DISTINCT profiles."type" FROM profiles
        INNER JOIN hmo_providers ON profiles.hmo_id = hmo_providers.id
        WHERE ${whereClause}
          AND profiles."type" IS NOT NULL
          AND profiles."type" <> ''
          AND profiles.deleted_date IS NULL
      `,
      params,
    );

    if (!res.length) return [];

    return res.map((_item) => _item?.type);
  }

  private setNewFlags(
    existingFlags: FlagDto[] | null,
    flag: string,
    unset: boolean,
    mutator: ProfileModel,
  ): FlagDto[] | null {
    const newFlags = unset
      ? (existingFlags || []).filter((f) => f.flag !== flag)
      : (existingFlags || []).concat({
          flag,
          flaggedById: mutator.id,
          flaggedByFullname: mutator.fullName,
          flaggedByRole: mutator.type,
          flagDateAndTime: new Date(),
        });

    return newFlags.length > 0 ? newFlags : null;
  }

  async getHmoHospitalById(
    dataSource: DataSource,
    id: string,
  ): Promise<HmoHospitalModel> {
    const hmoHospital = await dataSource
      .getRepository(HmoHospitalModel)
      .findOne({
        where: { id },
        relations: ['hospital', 'provider'],
      });

    if (!hmoHospital) {
      throw new NotFoundException('HMO Hospital not found');
    }

    return hmoHospital;
  }

  async flagHmoHospital(
    dataSource: DataSource,
    mutator: ProfileModel,
    id: string,
    flag: string,
    unset: boolean,
  ): Promise<HmoHospitalModel> {
    const hmoHospital = await this.getHmoHospitalById(dataSource, id);

    const updatedHmoHospital = await dataSource
      .getRepository(HmoHospitalModel)
      .save({
        ...hmoHospital,
        flags: this.setNewFlags(hmoHospital.flags, flag, unset, mutator),
      });

    return updatedHmoHospital;
  }

  async getHmoHospitalByHospitalIdAndProviderId(
    dataSource: DataSource,
    hospitalId: string,
    providerId: string,
  ): Promise<HmoHospitalModel> {
    const hmoHospital = await dataSource
      .getRepository(HmoHospitalModel)
      .findOne({
        where: { hospitalId, providerId },
        relations: ['hospital', 'provider'],
      });

    if (!hmoHospital) {
      throw new NotFoundException('HMO Hospital not found');
    }

    return hmoHospital;
  }

  async flagHmoHospitalByHospitalIdAndProviderId(
    dataSource: DataSource,
    mutator: ProfileModel,
    hospitalId: string,
    providerId: string,
    flag: string,
    unset: boolean,
  ): Promise<HmoHospitalModel> {
    const hmoHospital = await this.getHmoHospitalByHospitalIdAndProviderId(
      dataSource,
      hospitalId,
      providerId,
    );

    return await dataSource.getRepository(HmoHospitalModel).save({
      ...hmoHospital,
      flags: this.setNewFlags(hmoHospital.flags, flag, unset, mutator),
    });
  }

  async getExternalPlanType(
    providerId: string,
    fetchBenefits?: boolean,
  ): Promise<HmoPlanTypeModel> {
    const planType = await this.manager.findOne(HmoPlanTypeModel, {
      where: { hmoProviderId: providerId, isExternalPlan: true },
      relations: fetchBenefits ? ['benefits', 'benefits.visitType'] : undefined,
    });
    return planType;
  }

  async getCustomPlanTypePriceByHospitalId(
    dataSource: DataSource,
    hmoProviderId: string,
    hospitalId: string,
    planTypeId: string,
  ): Promise<BenefitCustomPrice[]> {
    try {
      const prices: BenefitCustomPrice[] = await queryDSWithSlave(
        dataSource,
        `SELECT 
        p->>'price' AS price,
        p->>'benefitId' AS "benefitId",
        p->>'utilisationCode' AS "utilisationCode",
        p->>'hospitalId' AS "hospitalId"
      FROM hmo_plan_types hpt
      LEFT JOIN LATERAL JSONB_ARRAY_ELEMENTS(hpt.provider_custom_prices) AS p ON TRUE
      WHERE hpt.hmo_provider_id = $1
      AND p->>'hospitalId' = $2 
      AND hpt.id = $3`,
        [hmoProviderId, hospitalId, planTypeId],
      );

      return prices.map((item: any) => ({
        hospitalId: item.hospitalId,
        benefitId: item.benefitId,
        utilisationCode: item.utilisationCode,
        price: Number(item.price),
      })) as BenefitCustomPrice[];
    } catch (error) {
      return [];
    }
  }
  async getCustomPlanTypePriceByHospitalIdAndBenefitId(
    dataSource: DataSource,
    hmoProviderId: string,
    hospitalId: string,
    planTypeId: string,
    benefitId: string,
    code: string,
  ): Promise<BenefitCustomPrice | null> {
    try {
      const [price]: BenefitCustomPrice[] = await queryDSWithSlave(
        dataSource,
        `SELECT 
        p->>'price' AS price,
        p->>'benefitId' AS "benefitId",
        p->>'utilisationCode' AS "utilisationCode",
        p->>'hospitalId' AS "hospitalId"
      FROM hmo_plan_types hpt
      LEFT JOIN LATERAL JSONB_ARRAY_ELEMENTS(hpt.provider_custom_prices) AS p ON TRUE
      WHERE hpt.hmo_provider_id = $1
      AND p->>'hospitalId' = $2 
      AND hpt.id = $3
      AND p->>'benefitId' = $4
      AND p->>'utilisationCode' = $5`,
        [hmoProviderId, hospitalId, planTypeId, benefitId, code],
      );
      if (!price) {
        return null;
      }
      return {
        hospitalId: price.hospitalId,
        benefitId: price.benefitId,
        utilisationCode: price.utilisationCode,
        price: Number(price.price),
      } as BenefitCustomPrice;
    } catch (error) {
      return null;
    }
  }

  async updateCustomPlanTypePriceByHospitalId(
    hmoProviderId: string,
    hospitalId: string,
    planTypeId: string,
    prices: UpdateBenefitCustomPriceInput[],
  ): Promise<BenefitCustomPrice[]> {
    if (!prices || prices.length === 0) {
      return [];
    }

    const newPricesJson = prices.map((price) => ({
      benefitId: price.benefitId,
      utilisationCode: price.utilisationCode,
      hospitalId,
      price: price.price,
    }));

    const tupleList = prices
      .map(
        (price) =>
          `('${price.benefitId}', '${price.utilisationCode}', '${hospitalId}')`,
      )
      .join(', ');

    const query = `UPDATE hmo_plan_types 
      SET provider_custom_prices = (
        SELECT jsonb_agg(elem)
        FROM (
          SELECT elem
          FROM jsonb_array_elements(COALESCE(provider_custom_prices, '[]'::jsonb)) AS elem
          WHERE (elem->>'benefitId', elem->>'utilisationCode', elem->>'hospitalId') 
                NOT IN (${tupleList})
          UNION ALL
          SELECT jsonb_build_object(
            'benefitId', value->>'benefitId',
            'utilisationCode', value->>'utilisationCode',
            'hospitalId', value->>'hospitalId',
            'price', (value->>'price')::float
          )
          FROM jsonb_array_elements($1::jsonb) AS value
        ) AS combined
      )
      WHERE hmo_provider_id = $2 
      AND id = $3
      RETURNING provider_custom_prices;
    `;

    const [result] = await this.query(query, [
      JSON.stringify(newPricesJson),
      hmoProviderId,
      planTypeId,
    ]);

    const updatedPrices = result[0]?.provider_custom_prices || [];

    return updatedPrices.map((item: any) => ({
      hospitalId: item.hospitalId,
      benefitId: item.benefitId,
      utilisationCode: item.utilisationCode,
      price: Number(item.price),
    })) as BenefitCustomPrice[];
  }
}

export const checkLimits = (args: CheckLimitArgs) => {
  const {
    annualLimitPerPerson,
    benefitLimit,
    checkAnnualLimitPerPerson,
    quantity,
    quantityUsed,
    quantityLimit,
    waitingPeriodDays,
    planStartDate,
    visitLimit,
    visitLimitUsed,
    totalAmountUsed,
    totalAmountUserByUser,
  } = args;
  const greaterThanBenefitLimit = totalAmountUsed > benefitLimit;
  const greaterThanAnnualLimitPerPerson =
    totalAmountUserByUser > annualLimitPerPerson;
  const greaterThanQuantityLimit = quantityUsed + quantity > quantityLimit;
  const greaterThanWaitingPeriod =
    moment().diff(moment(planStartDate), 'days') < waitingPeriodDays;
  if (greaterThanWaitingPeriod && waitingPeriodDays !== 0) {
    return {
      status: PaCodeStatus.Rejected,
      statusDescription: 'Waiting Period Not Met',
    };
  }

  if (
    greaterThanAnnualLimitPerPerson &&
    annualLimitPerPerson !== 0 &&
    checkAnnualLimitPerPerson
  ) {
    return {
      status: PaCodeStatus.Rejected,
      statusDescription: 'Amount Exceeded Annual Limit',
    };
  }

  if (greaterThanBenefitLimit && benefitLimit !== 0) {
    return {
      status: PaCodeStatus.Rejected,
      statusDescription: 'Amount Exceeded Benefit Limit',
    };
  }

  if (greaterThanQuantityLimit && quantityLimit !== 0) {
    return {
      status: PaCodeStatus.Rejected,
      statusDescription: 'Quantity Limit Exceeded',
    };
  }
  if (visitLimitUsed >= visitLimit && visitLimit !== 0) {
    throw new NotAcceptableException('Utilizations Exceeded For The Year');
  }
  return { status: PaCodeStatus.Pending };
};

export type CheckLimitArgs = {
  totalAmountUsed?: number;
  totalAmountUserByUser?: number;
  annualLimitPerPerson: number;
  benefitLimit: number;
  checkAnnualLimitPerPerson: boolean;
  quantity: number;
  quantityUsed: number;
  quantityLimit: number;
  waitingPeriodDays: number;
  planStartDate: Date;
  visitLimitUsed: number;
  visitLimit: number;
};
