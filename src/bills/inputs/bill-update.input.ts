import { Field, InputType } from '@nestjs/graphql';
import { ServiceType } from '@clinify/shared/enums/bill';

@InputType()
export class BillUpdate {
  @Field(() => String, { nullable: true })
  receiverClinifyId?: string;

  @Field(() => Number, { nullable: true })
  totalAmount?: number;

  @Field(() => Number, { nullable: true })
  discountAmount?: number;

  @Field(() => Number, { nullable: true })
  vatAmount?: number;

  @Field(() => String, { nullable: true })
  description?: string;

  @Field(() => ServiceType, { nullable: true })
  serviceType?: ServiceType;

  @Field(() => String, { nullable: true })
  invoiceNumber?: string;
}
