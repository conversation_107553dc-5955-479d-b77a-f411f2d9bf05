/* eslint-disable max-lines */
import { ApolloDriver, ApolloDriverConfig } from '@nestjs/apollo';
import { INestApplication, ValidationPipe } from '@nestjs/common';
import { EventEmitter2, EventEmitterModule } from '@nestjs/event-emitter';
import { GraphQLModule } from '@nestjs/graphql';
import { getModelToken } from '@nestjs/mongoose';
import { Test } from '@nestjs/testing';
import { getRepositoryToken, TypeOrmModule } from '@nestjs/typeorm';
import Chance from 'chance';
import request from 'supertest';
import { EntityManager } from 'typeorm';
import { AppointmentModule } from '../../appointments/module';
import { HmoClaimModule } from '../../hmo-claims/hmo-claims.module';
import { NotificationsService } from '../../notifications/services/notifications.service';
import { AdmissionModel } from '../models/admission.model';
import { AdmissionModule } from '../module';
import { billFactory } from '@clinify/__mocks__/factories/bill.factory';
import { buildUser } from '@clinify/__mocks__/factories/user.factory';
import { GqlAuthGuard } from '@clinify/authentication/guards/gql.auth.guard';
import { Action, Subject } from '@clinify/authorization/types/permission.type';
import { BillService } from '@clinify/bills/services/bill.service';
import { TestDataSourceOptions } from '@clinify/data-source';
import {
  DefaultInvImage,
  InventoryUpload,
} from '@clinify/integrations/inventory-blk/entities/FileUpload.entity';
import { PriceUpload } from '@clinify/integrations/price-blk/entities/PriceFileUpload.entity';
import { UserType } from '@clinify/shared/enums/users';
import { MAILER } from '@clinify/shared/mailer/constants';
import { ProfileModel } from '@clinify/users/models/profile.model';
import { PUB_SUB } from '@clinify/utils/subscriptions/pubSub';

import {
  admissionFactory,
  transferPatientFactory,
  bloodTransfusionsFactory,
  dischargePatientFactory,
  notesFactory,
  transferPatientMock,
  inputOutputFactory,
  admissionNoteMock,
} from '@mocks/factories/admission.factory';
import { mockProfile } from '@mocks/factories/profile.factory';
import gqlAuthGuardMock, { gqlUserMock } from '@mocks/gqlAuthGuard.mock';

jest.mock('@aws-sdk/client-bedrock-runtime', () => {
  const mockInvokeModelCommand = jest.fn();
  return {
    BedrockRuntimeClient: jest.fn(() => ({
      send: jest.fn(),
    })),
    InvokeModelCommand: mockInvokeModelCommand,
  };
});

const chance = new Chance();
const admissionInfo = admissionFactory.build();
let transferPatientInfo = transferPatientFactory.build();
transferPatientInfo = {
  ...transferPatientInfo,
  createdBy: mockProfile,
  updatedBy: mockProfile,
};
const bloodTransfusionInfo = bloodTransfusionsFactory.build();
const dischargePatientInfo = dischargePatientFactory.build();
const noteInfo = notesFactory.build();
const inputOutputData = inputOutputFactory.build();

const admissionRepo = {
  findByProfile: jest.fn(() => [admissionInfo]),
  updateAdmission: jest.fn(() => admissionInfo),
  deleteAdmission: jest.fn(() => [admissionInfo]),
  archiveAdmission: jest.fn(() => [admissionInfo]),
  getOneAdmission: jest.fn(() => admissionInfo),
  saveAdmissionWithSubRecords: jest.fn(() => admissionInfo),
  save: jest.fn(() => admissionInfo),
  saveBloodTransfusion: jest.fn(() => bloodTransfusionInfo),
  saveAdmissionNote: jest.fn(() => noteInfo),
  saveTransferPatient: jest.fn(() => transferPatientInfo),
  saveDischargePatient: jest.fn(() => dischargePatientInfo),
  saveInputOutput: jest.fn(() => inputOutputData),
  manager: {
    findOneOrFail: jest.fn(() => transferPatientMock),
    query: jest.fn().mockResolvedValue([{}]),
  },
};
const MockMailerService = {
  sendPatientSurveyEmail: jest.fn(),
  createTransport: jest.fn(),
  sendMail: jest.fn(),
};
const profileRepo = {
  findOne: jest.fn(() => gqlUserMock.defaultProfile),
  createQueryBuilder: jest.fn(() => ({
    update: jest.fn().mockReturnThis(),
    set: jest.fn().mockReturnThis(),
    where: jest.fn().mockReturnThis(),
    execute: jest.fn(),
    getOne: jest.fn(() => gqlUserMock.defaultProfile),
  })),
};

const pubSubMock = {
  publish: jest.fn(),
  asyncIterator: jest.fn(),
};

const BillServiceMock = {
  generateBill: jest.fn(() => billFactory.build()),
  deleteAutoGeneratedBill: jest.fn(),
  deleteAutoGeneratedBillItems: jest.fn(),
  generateEmptyBillIfNone: jest.fn(),
  generateMultipleBill: jest.fn(() => billFactory.build()),
  updateMultipleBill: jest.fn(() => billFactory.build()),
};

const ManagerMock = {
  createQueryBuilder: jest.fn(() => ({
    update: jest.fn().mockReturnThis(),
    set: jest.fn().mockReturnThis(),
    where: jest.fn().mockReturnThis(),
    execute: jest.fn(),
  })),
  queryRunner: { isTransactionActive: true },
  save: jest.fn(() => admissionInfo),
  update: jest.fn(() => admissionInfo),
  find: jest.fn(),
  query: jest.fn().mockResolvedValue([{}]),
  findOne: jest.fn(),
};

describe('AdmissionController', () => {
  let testHttpServer: request.SuperTest<request.Test>;
  let app: INestApplication;

  beforeAll(async () => {
    const builder = await Test.createTestingModule({
      imports: [
        AdmissionModule,
        TypeOrmModule.forRoot(TestDataSourceOptions),
        EventEmitterModule.forRoot(),
        GraphQLModule.forRoot<ApolloDriverConfig>({
          debug: false,
          playground: false,
          driver: ApolloDriver,
          autoSchemaFile: true,
          installSubscriptionHandlers: true,
          subscriptions: {
            'graphql-ws': {
              onConnect: (connectionParams: { [key: string]: any }) => {
                return {
                  headers: {
                    ...connectionParams,
                  },
                };
              },
            },
          },
          include: [AppointmentModule, AdmissionModule, HmoClaimModule],
          context: ({ req }) => ({ req }),
        }),
      ],
    })
      .overrideProvider(getRepositoryToken(AdmissionModel))
      .useValue(admissionRepo)
      .overrideProvider(getRepositoryToken(ProfileModel))
      .useValue(profileRepo)
      .overrideProvider(NotificationsService)
      .useValue({
        handleNoticationEvent: jest.fn(),
      })
      .overrideProvider(EntityManager)
      .useValue(ManagerMock)
      .overrideProvider(MAILER)
      .useValue(MockMailerService)
      .overrideProvider(BillService)
      .useValue(BillServiceMock)
      .overrideGuard(GqlAuthGuard)
      .useValue(gqlAuthGuardMock(UserType.Patient, gqlUserMock))
      .overrideProvider(getModelToken(InventoryUpload.name))
      .useValue({})
      .overrideProvider(getModelToken(DefaultInvImage.name))
      .useValue({})
      .overrideProvider(getModelToken(PriceUpload.name))
      .useValue({})
      .overrideProvider(PUB_SUB)
      .useValue(pubSubMock)
      .overrideProvider(EventEmitter2)
      .useValue({
        emit: jest.fn(),
        on: jest.fn(),
      })
      .compile();

    app = builder.createNestApplication();
    app.useGlobalPipes(new ValidationPipe());
    await app.init();

    testHttpServer = request(app.getHttpServer());
  });

  afterAll(async () => {
    await app.close();
    jest.clearAllMocks();
  });

  it('addAdmission', (done) => {
    jest
      .spyOn(EntityManager.prototype, 'findOneOrFail')
      .mockImplementationOnce(() =>
        Promise.resolve({ ...gqlUserMock.defaultProfile, id: chance.guid() }),
      );
    const clinicName = chance.sentence();
    profileRepo.findOne.mockImplementation(() => gqlUserMock.defaultProfile);
    testHttpServer
      .post('/graphql')
      .set('Authorization', 'Bearer token')
      .send({
        operationName: null,
        query: `mutation {
            addAdmission(
              admission: {
                clinifyId: "${gqlUserMock.defaultProfile.clinifyId}"
                clinicName: "${clinicName}"
                admissionDate: "2020-06-21"
                dischargeDate: "2020-07-21"
              }
            ) {
              id
              clinicName
              duration
            }
          }`,
      })
      .expect(({ body }) => {
        const data = body.data.addAdmission;
        expect(data.clinicName).toBe(admissionInfo.clinicName);
      })
      .expect(200)
      .end(done);
  });

  it('addAdmission should fail with invalid duration', (done) => {
    testHttpServer
      .post('/graphql')
      .set('Authorization', 'Bearer token')
      .send({
        operationName: null,
        query: `mutation {
            addAdmission(
              admission: {
                clinifyId: "${gqlUserMock.defaultProfile.clinifyId}"
                clinicName: "${chance.sentence()}"
                duration: "01:99:13"
              }
            ) {
              id
              clinicName
              duration
            }
          }`,
      })
      .expect(({ body }) => {
        expect(body.errors[0].message).toEqual('Bad Request Exception');
        expect(body.errors[0].extensions.response.message[0]).toEqual(
          'duration should be in the 00:00:00 format',
        );
      })
      .end(done);
  });

  it('addAdmission should fail with future admission date', (done) => {
    const admittedDate = new Date();
    admittedDate.setDate(admittedDate.getDate() + 1);
    testHttpServer
      .post('/graphql')
      .set('Authorization', 'Bearer token')
      .send({
        operationName: null,
        query: `mutation {
            addAdmission(
              admission: {
                clinifyId: "${gqlUserMock.defaultProfile.clinifyId}"
                clinicName: "${chance.sentence()}"
                admissionDate: "${admittedDate}"
              }
            ) {
              id
              clinicName
              duration
            }
          }`,
      })
      .expect(({ body }) => {
        expect(body.errors[0].message).toEqual('Bad Request Exception');
        expect(body.errors[0].extensions.response.message[0]).toEqual(
          '"admissionDate" should not be a future date',
        );
      })
      .end(done);
  });

  it('addAdmission should fail with transfer date before admission date', (done) => {
    testHttpServer
      .post('/graphql')
      .set('Authorization', 'Bearer token')
      .send({
        operationName: null,
        query: `mutation {
            addAdmission(
              admission: {
                clinifyId: "${gqlUserMock.defaultProfile.clinifyId}"
                clinicName: "${chance.sentence()}"
                transferDate: "2020-07-21"
              }
            ) {
              id
              clinicName
              duration
            }
          }`,
      })
      .expect(({ body }) => {
        expect(body.errors[0].message).toEqual('Bad Request Exception');
        expect(body.errors[0].extensions.response.message[0]).toEqual(
          '"transferDate" must be after "admissionDate"',
        );
      })
      .end(done);
  });

  it('addAdmission should fail with discharge date before admission date', (done) => {
    testHttpServer
      .post('/graphql')
      .set('Authorization', 'Bearer token')
      .send({
        operationName: null,
        query: `mutation {
            addAdmission(
              admission: {
                clinifyId: "${gqlUserMock.defaultProfile.clinifyId}"
                clinicName: "${chance.sentence()}"
                dischargeDate: "2020-07-21"
              }
            ) {
              id
              clinicName
              duration
            }
          }`,
      })
      .expect(({ body }) => {
        expect(body.errors[0].message).toEqual('Bad Request Exception');
        expect(body.errors[0].extensions.response.message[0]).toEqual(
          '"dischargeDate" must be after "admissionDate"',
        );
      })
      .end(done);
  });

  it('getAdmission', (done) => {
    ManagerMock.find.mockImplementationOnce(() =>
      Promise.resolve([admissionNoteMock]),
    );
    testHttpServer
      .post('/graphql')
      .set('Authorization', 'Bearer token')
      .send({
        operationName: null,
        query: `query {
                  admission(id: "${admissionInfo.id}", clinifyId: "${gqlUserMock.defaultProfile.clinifyId}") {
                    id
                    clinicName
                    duration
                    createdDate
                    updatedDate
                    admissionNotes {
                      id
                      note
                    }
                  }
                }`,
      })
      .expect(({ body }) => {
        const data = body.data.admission;
        expect(data.createdDate).toBeTruthy();
        expect(data.updatedDate).toBeTruthy();
      })
      .expect(200)
      .end(done);
  });

  /**
   * @Todo come back to fix
   */
  it.skip('updateAdmission', (done) => {
    const admittedBy = chance.sentence();
    testHttpServer
      .post('/graphql')
      .set('Authorization', 'Bearer token')
      .send({
        operationName: null,
        query: `mutation {
                  updateAdmission(
                    admission: {
                      clinifyId: "${gqlUserMock.defaultProfile.clinifyId}"
                      admittedBy: "${admittedBy}"
                    }
                    id: "${admissionInfo.id}"
                  ) {
                      id
                      clinicName
                      admittedBy
                    }
                }`,
      })
      .expect(({ body }) => {
        const data = body.data.updateAdmission;
        expect(data.id).toBe(admissionInfo.id);
        expect(data.clinicName).toBe(admissionInfo.clinicName);
        expect(data.admittedBy).toBe(admissionInfo.admittedBy);
      })
      .expect(200)
      .end(done);
  });

  it('deleteAdmission', (done) => {
    testHttpServer
      .post('/graphql')
      .set('Authorization', 'Bearer token')
      .send({
        operationName: null,
        query: `mutation {
                  deleteAdmissions(ids: ["${admissionInfo.id}"], clinifyId: "${gqlUserMock.defaultProfile.clinifyId}") {
                    id
                    clinicName
                    duration
                  }
                }`,
      })
      .expect(({ body }) => {
        const data = body.data.deleteAdmissions;
        expect(data[0].id).toBe(admissionInfo.id);
      })
      .expect(200)
      .end(done);
  });

  it('archiveAdmissions', (done) => {
    testHttpServer
      .post('/graphql')
      .set('Authorization', 'Bearer token')
      .send({
        operationName: null,
        query: `mutation {
                  archiveAdmissions(
                    ids: ["${admissionInfo.id}"]
                    archive: true
                    clinifyId: "${gqlUserMock.defaultProfile.clinifyId}"
                  ) {
                    id
                    clinicName
                    duration
                  }
                }`,
      })
      .expect(({ body }) => {
        const data = body.data.archiveAdmissions;
        expect(data[0].id).toBe(admissionInfo.id);
      })
      .expect(200)
      .end(done);
  });
});

describe('AdmissionController', () => {
  let testHttpServer: request.SuperTest<request.Test>;
  let app: INestApplication;

  const doctor = buildUser(UserType.Doctor);
  const patient = buildUser();

  // assigning manage permission
  doctor?.defaultProfile.permission?.rules.push({
    action: Action.Manage,
    subject: Subject.Admission,
    conditions: {
      clinifyId: patient.defaultProfile.clinifyId,
      editorId: doctor.defaultProfile.id,
    },
  });

  beforeAll(async () => {
    const builder = await Test.createTestingModule({
      imports: [
        AdmissionModule,
        TypeOrmModule.forRoot(TestDataSourceOptions),
        EventEmitterModule.forRoot(),
        GraphQLModule.forRoot<ApolloDriverConfig>({
          debug: false,
          driver: ApolloDriver,
          playground: false,
          autoSchemaFile: true,
          installSubscriptionHandlers: true,
          subscriptions: {
            'graphql-ws': {
              onConnect: (connectionParams: { [key: string]: any }) => {
                return {
                  headers: {
                    ...connectionParams,
                  },
                };
              },
            },
          },
          include: [AdmissionModule],
          context: ({ req }) => ({ req }),
        }),
      ],
    })
      .overrideProvider(getRepositoryToken(AdmissionModel))
      .useValue(admissionRepo)
      .overrideProvider(NotificationsService)
      .useValue({
        handleNoticationEvent: jest.fn(),
      })
      .overrideProvider(getRepositoryToken(ProfileModel))
      .useValue(profileRepo)
      .overrideGuard(GqlAuthGuard)
      .useValue(gqlAuthGuardMock(UserType.Doctor, doctor))
      .overrideProvider(getModelToken(InventoryUpload.name))
      .useValue({})
      .overrideProvider(getModelToken(DefaultInvImage.name))
      .useValue({})
      .overrideProvider(getModelToken(PriceUpload.name))
      .useValue({})
      .overrideProvider(EntityManager)
      .useValue(ManagerMock)
      .overrideProvider(BillService)
      .useValue(BillServiceMock)
      .overrideProvider(PUB_SUB)
      .useValue(pubSubMock)
      .compile();

    app = builder.createNestApplication();
    app.useGlobalPipes(new ValidationPipe());
    await app.init();

    testHttpServer = await request(app.getHttpServer());
  });

  afterAll(async () => {
    await app.close();
    jest.clearAllMocks();
  });
  it('getAdmission', (done) => {
    ManagerMock.find.mockImplementationOnce(() =>
      Promise.resolve([admissionNoteMock]),
    );
    profileRepo.findOne.mockImplementation(() => patient.defaultProfile);
    ManagerMock.findOne.mockImplementationOnce(() => patient.defaultProfile);
    testHttpServer
      .post('/graphql')
      .set('Authorization', 'Bearer token')
      .send({
        operationName: null,
        query: `query {
                  admission(id: "${admissionInfo.id}", clinifyId: "${patient.defaultProfile.clinifyId}") {
                    id
                    clinicName
                    duration
                    createdDate
                    updatedDate
                    admissionNotes {
                      id
                      note
                    }
                  }
                }`,
      })
      .expect(({ body }) => {
        const data = body.data.admission;
        expect(data.createdDate).toBeTruthy();
        expect(data.updatedDate).toBeTruthy();
      })
      .expect(200)
      .end(done);
  });

  it('addAdmission should add admission for user with manage permission', (done) => {
    profileRepo.findOne.mockImplementation(() => patient.defaultProfile);
    ManagerMock.findOne.mockImplementationOnce(() => patient.defaultProfile);
    const clinicName = chance.sentence();
    testHttpServer
      .post('/graphql')
      .set('Authorization', 'Bearer token')
      .send({
        operationName: null,
        query: `mutation {
            addAdmission(
              admission: {
                clinifyId: "${patient.defaultProfile.clinifyId}"
                clinicName: "${clinicName}"
                admissionDate: "2020-06-21"
                dischargeDate: "2020-07-21"
              }
            ) {
              id
              clinicName
            }
          }`,
      })
      .expect(({ body }) => {
        const data = body.data.addAdmission;
        expect(data.clinicName).toBe(admissionInfo.clinicName);
      })
      .expect(200)
      .end(done);
  });

  it('addAdmission should throw 403 error if you try to create for another user and do not have the permission to create', (done) => {
    profileRepo.findOne.mockImplementation(() => patient.defaultProfile);
    const clinicName = chance.sentence();
    testHttpServer
      .post('/graphql')
      .set('Authorization', 'Bearer token')
      .send({
        operationName: null,
        query: `mutation {
            addAdmission(
              admission: {
                clinifyId: "anotherUserClinifyId"
                clinicName: "${clinicName}"
                admissionDate: "2020-06-21"
                dischargeDate: "2020-07-21"
              }
            ) {
              id
              clinicName
            }
          }`,
      })
      .expect(({ body }) => {
        expect(body.errors[0].message).toEqual('Forbidden resource');
        expect(body.errors[0].extensions.response.statusCode).toEqual(403);
      })
      .expect(200)
      .end(done);
  });

  /**
   * @Todo fix later
   */
  it.skip('updateAdmission should call updateAdmission repository method and update admission if user have manage permission', (done) => {
    const admittedBy = chance.sentence();
    testHttpServer
      .post('/graphql')
      .set('Authorization', 'Bearer token')
      .send({
        operationName: null,
        query: `mutation {
                  updateAdmission(
                    admission: {
                      clinifyId: "${patient.defaultProfile.clinifyId}"
                      admittedBy: "${admittedBy}"
                    }
                    id: "${admissionInfo.id}"
                  ) {
                      id
                      clinicName
                      admittedBy
                    }
                }`,
      })
      .expect(({ body }) => {
        const data = body.data.updateAdmission;
        expect(data.id).toBe(admissionInfo.id);
        expect(data.clinicName).toBe(admissionInfo.clinicName);
        expect(data.admittedBy).toBe(admissionInfo.admittedBy);
      })
      .expect(200)
      .end(done);
  });

  it('deleteAdmission should call deleteAdmission repository method and delete admission if user have manage permission', (done) => {
    testHttpServer
      .post('/graphql')
      .set('Authorization', 'Bearer token')
      .send({
        operationName: null,
        query: `mutation {
                  deleteAdmissions(ids: ["${admissionInfo.id}"], clinifyId: "${patient.defaultProfile.clinifyId}") {
                    id
                    clinicName
                    duration
                  }
                }`,
      })
      .expect(({ body }) => {
        const data = body.data.deleteAdmissions;
        expect(data[0].id).toBe(admissionInfo.id);
      })
      .expect(200)
      .end(done);
  });

  it('archiveAdmission should call archiveAdmission repository method and archive admission if user have manage permission', (done) => {
    testHttpServer
      .post('/graphql')
      .set('Authorization', 'Bearer token')
      .send({
        operationName: null,
        query: `mutation {
                  archiveAdmissions(
                    ids: ["${admissionInfo.id}"],
                    clinifyId: "${patient.defaultProfile.clinifyId}"
                    archive: true
                  ) {
                    id
                    clinicName
                  }
                }`,
      })
      .expect(({ body }) => {
        const data = body.data.archiveAdmissions;
        expect(data[0].id).toBe(admissionInfo.id);
      })
      .expect(200)
      .end(done);
  });
});

describe('AdmissionController', () => {
  let testHttpServer: request.SuperTest<request.Test>;
  let app: INestApplication;

  const doctor = buildUser(UserType.Doctor);
  const patient = buildUser();

  doctor?.defaultProfile.permission?.rules.push(
    {
      action: Action.Manage,
      subject: Subject.BloodTransfusionSubRecord,
      conditions: {
        clinifyId: patient.defaultProfile.clinifyId,
        editorId: doctor.defaultProfile.id,
      },
    },
    {
      action: Action.Manage,
      subject: Subject.TransferPatientSubRecord,
      conditions: {
        clinifyId: patient.defaultProfile.clinifyId,
        editorId: doctor.defaultProfile.id,
      },
    },
    {
      action: Action.Manage,
      subject: Subject.DischargePatientSubRecord,
      conditions: {
        clinifyId: patient.defaultProfile.clinifyId,
        editorId: doctor.defaultProfile.id,
      },
    },
    {
      action: Action.Manage,
      subject: Subject.AdmissionNoteSubRecord,
      conditions: {
        clinifyId: patient.defaultProfile.clinifyId,
        editorId: doctor.defaultProfile.id,
      },
    },
    {
      action: Action.Manage,
      subject: Subject.InputOutputSubRecord,
      conditions: {
        clinifyId: patient.defaultProfile.clinifyId,
        editorId: doctor.defaultProfile.id,
      },
    },
  );

  beforeAll(async () => {
    const builder = await Test.createTestingModule({
      imports: [
        AdmissionModule,
        TypeOrmModule.forRoot(TestDataSourceOptions),
        EventEmitterModule.forRoot(),
        GraphQLModule.forRoot<ApolloDriverConfig>({
          debug: false,
          driver: ApolloDriver,
          playground: false,
          autoSchemaFile: true,
          installSubscriptionHandlers: true,
          subscriptions: {
            'graphql-ws': {
              onConnect: (connectionParams: { [key: string]: any }) => {
                return {
                  headers: {
                    ...connectionParams,
                  },
                };
              },
            },
          },
          include: [AdmissionModule],
          context: ({ req }) => ({ req }),
        }),
      ],
    })
      .overrideProvider(getRepositoryToken(AdmissionModel))
      .useValue(admissionRepo)
      .overrideProvider(NotificationsService)
      .useValue({
        handleNoticationEvent: jest.fn(),
      })
      .overrideProvider(getRepositoryToken(ProfileModel))
      .useValue(profileRepo)
      .overrideGuard(GqlAuthGuard)
      .useValue(gqlAuthGuardMock(UserType.Doctor, doctor))
      .overrideProvider(getModelToken(InventoryUpload.name))
      .useValue({})
      .overrideProvider(getModelToken(DefaultInvImage.name))
      .useValue({})
      .overrideProvider(getModelToken(PriceUpload.name))
      .useValue({})
      .overrideProvider(PUB_SUB)
      .useValue(pubSubMock)
      .overrideProvider(EntityManager)
      .useValue(ManagerMock)
      .compile();

    app = builder.createNestApplication();
    app.useGlobalPipes(new ValidationPipe());
    await app.init();

    testHttpServer = request(app.getHttpServer());
  });

  afterAll(async () => await app.close());

  it('saveBloodTransfusion should save blood transfusion for user with manage permission', (done) => {
    profileRepo.findOne.mockImplementation(() => patient.defaultProfile);
    testHttpServer
      .post('/graphql')
      .set('Authorization', 'Bearer token')
      .send({
        operationName: null,
        query: `mutation {
          saveBloodTransfusion(
              clinifyId: "${patient.defaultProfile.clinifyId}"
              input: {
                transfusionOrderGiven: "${chance.sentence()}"
                transfusionNote: "${chance.sentence()}"
              }
              admissionId: "${admissionInfo.id}"
            ) {
              id
              transfusionOrderGiven
              transfusionNote
            }
          }`,
      })
      .expect(({ body }) => {
        const data = body.data.saveBloodTransfusion;
        expect(data.id).toBe(bloodTransfusionInfo.id);
        expect(data.transfusionOrderGiven).toBe(
          bloodTransfusionInfo.transfusionOrderGiven,
        );
      })
      .expect(200)
      .end(done);
  });

  it('saveBloodTransfusion should throw 403 error if you try to create for another user and do not have the permission', (done) => {
    profileRepo.findOne.mockImplementation(() => patient.defaultProfile);
    testHttpServer
      .post('/graphql')
      .set('Authorization', 'Bearer token')
      .send({
        operationName: null,
        query: `mutation {
          saveBloodTransfusion(
              clinifyId: "djdjdjdjjd"
              input: {
                transfusionOrderGiven: "${chance.sentence()}"
              }
              admissionId: "${admissionInfo.id}"
            ) {
              id
              transfusionOrderGiven
            }
          }`,
      })
      .expect(({ body }) => {
        expect(body.errors[0].message).toEqual('Forbidden resource');
        expect(body.errors[0].extensions.response.statusCode).toEqual(403);
      })
      .expect(200)
      .end(done);
  });

  it('saveDischargePatient should discharge patient for user with manage permission', (done) => {
    profileRepo.findOne.mockImplementation(() => patient.defaultProfile);
    testHttpServer
      .post('/graphql')
      .set('Authorization', 'Bearer token')
      .send({
        operationName: null,
        query: `mutation {
          saveDischargePatient(
              clinifyId: "${patient.defaultProfile.clinifyId}"
              input: {
                dischargeSummary: "${chance.sentence()}"
              }
              admissionId: "${admissionInfo.id}"
            ) {
              id
              dischargeSummary
            }
          }`,
      })
      .expect(({ body }) => {
        const data = body.data.saveDischargePatient;
        expect(data.id).toBe(dischargePatientInfo.id);
        expect(data.dischargeSummary).toBe(
          dischargePatientInfo.dischargeSummary,
        );
      })
      .expect(200)
      .end(done);
  });

  it('saveDischargePatient should throw 403 error if you try to discharge another user and do not have the permission', (done) => {
    profileRepo.findOne.mockImplementation(() => patient.defaultProfile);
    testHttpServer
      .post('/graphql')
      .set('Authorization', 'Bearer token')
      .send({
        operationName: null,
        query: `mutation {
          saveDischargePatient(
              clinifyId: "djdjdjdjjd"
              input: {
                dischargeSummary: "${chance.sentence()}"
              }
              admissionId: "${admissionInfo.id}"
            ) {
              id
              dischargeSummary
            }
          }`,
      })
      .expect(({ body }) => {
        expect(body.errors[0].message).toEqual('Forbidden resource');
        expect(body.errors[0].extensions.response.statusCode).toEqual(403);
      })
      .expect(200)
      .end(done);
  });

  it('saveTransferPatient should transfer patient for user with manage permission', (done) => {
    profileRepo.findOne.mockImplementation(() => patient.defaultProfile);
    testHttpServer
      .post('/graphql')
      .set('Authorization', 'Bearer token')
      .send({
        operationName: null,
        query: `mutation {
          saveTransferPatient(
              clinifyId: "${patient.defaultProfile.clinifyId}"
              input: {
                transferReason: "${chance.sentence()}",
                transferHospital: "${chance.guid({ version: 4 })}"
              }
              admissionId: "${admissionInfo.id}"
            ) {
              id
              transferReason
            }
          }`,
      })
      .expect(({ body }) => {
        const data = body.data.saveTransferPatient;
        expect(data.id).toBe(transferPatientInfo.id);
        expect(data.transferReason).toBe(transferPatientInfo.transferReason);
      })
      .expect(200)
      .end(done);
  });

  it('saveInputOutput should save input record for user with manage permission', (done) => {
    profileRepo.findOne.mockImplementation(() => patient.defaultProfile);
    testHttpServer
      .post('/graphql')
      .set('Authorization', 'Bearer token')
      .send({
        operationName: null,
        query: `mutation {
          saveInputDetail(
              clinifyId: "${patient.defaultProfile.clinifyId}"
              input: {inputFluidType: "${chance.sentence()}"},
              admissionId: "${admissionInfo.id}"
            ) {
              id
            }
          }`,
      })
      .expect(({ body }) => {
        const data = body.data.saveInputDetail;
        expect(data.id).toBe(inputOutputData.inputDetails.id);
      })
      .expect(200)
      .end(done);
  });

  it('saveInputOutput should save output record for user with manage permission', (done) => {
    profileRepo.findOne.mockImplementation(() => patient.defaultProfile);
    testHttpServer
      .post('/graphql')
      .set('Authorization', 'Bearer token')
      .send({
        operationName: null,
        query: `mutation {
          saveOutputDetail(
              clinifyId: "${patient.defaultProfile.clinifyId}"
              input: {outputFluidType: "${chance.sentence()}"},
              admissionId: "${admissionInfo.id}"
            ) {
              id
              observations
              concealObservations
            }
          }`,
      })
      .expect(({ body }) => {
        const data = body.data.saveOutputDetail;
        expect(data.id).toBe(inputOutputData.outputDetails.id);
        expect(data.observations).toBe(
          inputOutputData.outputDetails.observations,
        );
      })
      .expect(200)
      .end(done);
  });
});
