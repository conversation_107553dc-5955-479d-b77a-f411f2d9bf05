/* eslint-disable max-lines */
import { NotFoundException } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Chance } from 'chance';
import moment from 'moment/moment';
import { DataSource, EntityManager } from 'typeorm';
import {
  CustomAdmissionRepoMethods,
  IAdmissionRepository,
} from './admission.repository';
import * as db from '../../database';
import { CustomProfileRepoMethods } from '../../users/repositories/profile.repository';
import { AdmissionModel } from '../models/admission.model';
import { AdmissionLinkedRecordType } from '@clinify/admissions/validators/admission.input';
import { AllergyModel } from '@clinify/allergies/models/allergy.model';
import { BillService } from '@clinify/bills/services/bill.service';
import { TestDataSourceOptions } from '@clinify/data-source';
import { extendDSRepo, extendModel } from '@clinify/database/extendModel';
import { AdmissionStatus } from '@clinify/shared/enums/admission';
import { UserType } from '@clinify/shared/enums/users';
import { RecordCreator } from '@clinify/shared/validators/filter.input';
import { ProfileModel } from '@clinify/users/models/profile.model';
import { createAdmissionLines } from '@clinify/utils/tests/admissionLines.fixtures';
import { createAdmissionNotes } from '@clinify/utils/tests/admissionNotes.fixtures';
import { createBloodTransfusions } from '@clinify/utils/tests/bloodTransfusion.fixtures';
import { createDischargePatients } from '@clinify/utils/tests/dischargePatient.fixtures';
import {
  createInputRecords,
  createOutputRecords,
} from '@clinify/utils/tests/input-output.fixture';
import { createSharedRecord } from '@clinify/utils/tests/shared-record.fixtures';
import { createTransferPatients } from '@clinify/utils/tests/transferPatient.fixtures';
import { createAdmissions } from '@fixtures/admission.fixtures';
import { createAllergies } from '@fixtures/allergy.fixtures';
import { createHmoProfileFixtures } from '@fixtures/hmo-profiles.fixtures';
import { createHospitals } from '@fixtures/hospital.fixtures';
import { createPartner } from '@fixtures/partner.fixture';
import { createUsers } from '@fixtures/user.fixtures';
import {
  admissionFactory,
  admissionLinesFactory,
  bloodTransfusionsFactory,
  dischargePatientFactory,
  inputOutputFactory,
  notesFactory,
  transferPatientFactory,
} from '@mocks/factories/admission.factory';

describe('AdmissionRepository', () => {
  let ds: DataSource;
  let admissionsForPatient: AdmissionModel[];
  let admissionForPatient: AdmissionModel;
  let patientAllergy: AllergyModel;
  let profile: ProfileModel;
  let manager: EntityManager;
  let repo: IAdmissionRepository;
  const spySlaveQuery = jest.spyOn(db, 'queryWithSlave');
  let module: TestingModule;
  const chance = new Chance();
  let billServiceMock: BillService | any;

  beforeAll(async () => {
    module = await Test.createTestingModule({
      imports: [TypeOrmModule.forRoot(TestDataSourceOptions)],
      providers: [
        extendModel(AdmissionModel, CustomAdmissionRepoMethods),
        extendModel(ProfileModel, CustomProfileRepoMethods),
      ],
    }).compile();

    ds = module.get<DataSource>(DataSource);
    manager = ds.manager;
    repo = extendDSRepo<IAdmissionRepository>(
      ds,
      AdmissionModel,
      CustomAdmissionRepoMethods,
    );

    spySlaveQuery.mockImplementation((qr, pr) => manager.query(qr, pr));
  });

  beforeEach(async () => {
    const hospitals = await createHospitals(manager, 1);
    const [user] = await createUsers(
      manager,
      1,
      hospitals[0],
      null,
      null,
      UserType.Patient,
    );
    const profileCreated = user.profiles[0];
    admissionsForPatient = await createAdmissions(
      manager,
      2,
      null,
      profileCreated,
    );
    [patientAllergy] = await createAllergies(
      manager,
      1,
      null,
      user,
      profileCreated,
    );
    admissionForPatient = admissionsForPatient[0];
    profile = admissionForPatient.profile;
    billServiceMock = { archiveBillsR: jest.fn() };
  });
  afterAll(async (done) => {
    await ds.destroy();
    await module.close();
    jest.clearAllMocks();
    done();
  });
  it('findByProfile() should find admissions for a particular user', async () => {
    const record = await repo.findByProfile(
      admissionForPatient.profile,
      admissionForPatient.profile.id,
      {
        skip: 0,
        take: 50,
      },
    );
    const { id, clinicName } = admissionForPatient;
    expect(record).toHaveProperty('list');
    expect(record.list).toContainEqual(
      expect.objectContaining({ id, clinicName }),
    );
  });

  it('findByProfile() should find admissions by status', async () => {
    const record = await repo.findByProfile(
      admissionForPatient.profile,
      admissionForPatient.profile.id,
      {
        status: AdmissionStatus.Admitted,
      },
    );
    const { id, clinicName } = admissionForPatient;
    expect(record).toHaveProperty('list');
    expect(record.list).toContainEqual(
      expect.objectContaining({ id, clinicName }),
    );
  });

  it('findByProfile() should find admissions by status', async () => {
    const dischargeInput = dischargePatientFactory.build();
    const { id, profile, clinicName } = admissionForPatient;
    await repo.saveDischargePatient(
      admissionForPatient.id,
      dischargeInput,
      undefined,
      profile,
    );
    const record = await repo.findByProfile(
      admissionForPatient.profile,
      admissionForPatient.profile.id,
      {
        status: AdmissionStatus.Discharged,
      },
    );
    expect(record).toHaveProperty('list');
    expect(record.list).toContainEqual(
      expect.objectContaining({ id, clinicName }),
    );
  });

  it('fetchAdmissionBloodTransfusionRecords(): should find transfusion sub records for an admission', async () => {
    const admission = admissionForPatient;
    await createBloodTransfusions(manager, 3, admission);
    const response = await repo.fetchAdmissionBloodTransfusionRecords(
      admission.profile,
      admission.id,
    );

    expect(response.length).toEqual(3);
  });

  it('fetching subrecords for an admission throws an error if admission Not Found', async () => {
    await expect(
      repo.fetchAdmissionBloodTransfusionRecords(
        admissionForPatient.profile,
        profile.id,
      ),
    ).rejects.toThrow('Record Not Found');
  });

  it('fetching subrecords for an admission throws error if patient user is not authorised', async () => {
    const admission = admissionForPatient;
    await createBloodTransfusions(manager, 3, admission);
    const unauthorisedPatient = await createUsers(manager, 1);
    await expect(
      repo.fetchAdmissionBloodTransfusionRecords(
        unauthorisedPatient[0].defaultProfile,
        admission.id,
      ),
    ).rejects.toThrow('Not Authorized To View This Record');
  });

  it('fetchAdmissionTransferPatientRecords(): should find transfer patients sub records for an admission', async () => {
    const admission = admissionForPatient;
    await createTransferPatients(manager, 2, admission, admission.profile);
    const response = await repo.fetchAdmissionTransferPatientRecords(
      admission.profile,
      admission.id,
    );

    expect(response.length).toEqual(2);
  });

  it('fetchAdmissionDischargePatientRecords() should find discharge patients sub records for an admission', async () => {
    const admission = admissionForPatient;
    await createDischargePatients(manager, 3, admission);
    const response = await repo.fetchAdmissionDischargePatientRecords(
      admission.profile,
      admission.id,
    );

    expect(response.length).toEqual(3);
  });

  it('saveDischargePatient(): should save discharge patient details on admission record', async () => {
    const dischargeInput = dischargePatientFactory.build();
    const { id, profile } = admissionForPatient;
    const dischargeRecord = await repo.saveDischargePatient(
      id,
      dischargeInput,
      profile,
      profile,
    );

    expect(dischargeRecord.admission.id).toEqual(id);
    expect(dischargeRecord.dischargeDate).toEqual(dischargeInput.dischargeDate);
  });

  it('getAdmissionStatus(): should get admission status Discharged when a dicharge record exists', async () => {
    const dischargeInput = dischargePatientFactory.build();
    const { id, profile } = admissionForPatient;
    await repo.saveDischargePatient(id, dischargeInput, profile, profile);
    const status = await repo.getAdmissionStatus(id);
    expect(status).toBe('Discharged');
  });

  it('getAdmissionStatus(): should get default admission status of Admitted', async () => {
    const { id } = admissionsForPatient[1];
    const status = await repo.getAdmissionStatus(id);
    expect(status).toBe('Admitted');
  });

  it('saveTransferPatient(): should save transfer patient details on admission record', async () => {
    const transferInput = transferPatientFactory.build();
    const [hospital] = await createHospitals(manager, 1);
    transferInput.transferHospital = hospital.id;
    const { id, profile } = admissionForPatient;
    const transferRecord = await repo.saveTransferPatient(
      id,
      transferInput,
      profile,
      profile,
    );

    expect(transferRecord.admission.id).toEqual(id);
    expect(transferRecord.transferReason).toEqual(transferInput.transferReason);
  });

  it('saveTransferPatient(): should save transfer patient details on admission record', async () => {
    const transferInput = transferPatientFactory.build();
    transferInput.transferHospital = chance.guid({ version: 4 });
    const { id, profile } = admissionForPatient;
    await expect(
      repo.saveTransferPatient(id, transferInput, profile, profile),
    ).rejects.toThrow(new NotFoundException('Facility Not Found'));
  });

  it('saveBloodTransfusion(): should save transfusion details on admission record', async () => {
    const transfusionInput = bloodTransfusionsFactory.build();
    const { id, profile } = admissionForPatient;
    const transfusionRecord = await repo.saveBloodTransfusion(
      id,
      transfusionInput,
      profile,
      profile,
    );

    expect(transfusionRecord.admission.id).toEqual(id);
    expect(transfusionRecord.bloodLabel).toEqual(transfusionInput.bloodLabel);
  });

  it('saveBloodTransfusion(): should throw error if patient is creating subRecord for another person admission', async () => {
    try {
      const transfusionInput = bloodTransfusionsFactory.build();
      const [user] = await createUsers(
        manager,
        1,
        null,
        null,
        null,
        UserType.Patient,
      );
      const anotherProfile = user.profiles[0];
      const { id, profile } = admissionForPatient;
      await repo.saveBloodTransfusion(
        id,
        transfusionInput,
        profile,
        anotherProfile,
      );
    } catch (err) {
      expect(err.message).toEqual('Not Authorized To Create Record');
    }
  });

  it('saveBloodTransfusion(): should throw error if doctor is creating subRecord for another hospital', async () => {
    try {
      const transfusionInput = bloodTransfusionsFactory.build();
      const [hospital, anotherHospital] = await createHospitals(manager);
      const [doctor] = await createUsers(
        manager,
        1,
        hospital,
        null,
        null,
        UserType.OrganizationDoctor,
      );
      const [anotherDoctor] = await createUsers(manager, 2, anotherHospital);

      const [admissionsByDoctor] = await createAdmissions(
        manager,
        2,
        doctor.defaultProfile,
      );
      const anotherProfile = anotherDoctor.profiles[0];
      const { id, profile } = admissionsByDoctor;
      await repo.saveBloodTransfusion(
        id,
        transfusionInput,
        profile,
        anotherProfile,
      );
    } catch (err) {
      expect(err.message).toEqual('Not Authorized To Create Record');
    }
  });

  it('saveBloodTransfusion(): should save transfusion details on admission record for doctor in the same hospital', async () => {
    const transfusionInput = bloodTransfusionsFactory.build();
    const [hospital] = await createHospitals(manager);
    const [doctor] = await createUsers(
      manager,
      1,
      hospital,
      null,
      null,
      UserType.OrganizationDoctor,
    );

    const [anotherDoctor] = await createUsers(manager, 1, hospital);
    const [admissionsByDoctor] = await createAdmissions(
      manager,
      2,
      doctor.defaultProfile,
    );
    const anotherProfile = anotherDoctor.defaultProfile;
    const { id, profile } = admissionsByDoctor;
    const transfusionRecord = await repo.saveBloodTransfusion(
      id,
      transfusionInput,
      profile,
      anotherProfile,
    );

    expect(transfusionRecord.admissionId).toEqual(id);
    expect(transfusionRecord.bloodLabel).toEqual(transfusionInput.bloodLabel);
  });

  it('saveAdmissionNotes(): should save admission note details on admission record', async () => {
    const notesInput = notesFactory.build();
    const { id, profile } = admissionForPatient;
    const admissionNoteRecord = await repo.saveAdmissionNote(
      id,
      notesInput,
      profile,
      profile,
    );

    expect(admissionNoteRecord.admission.id).toEqual(id);
    expect(admissionNoteRecord.note).toEqual(notesInput.note);
  });

  it('saveAdmissionNotes(): should throw an error if admission is not found', async () => {
    try {
      const notesInput = notesFactory.build();
      const { profile } = admissionForPatient;
      const id = chance.guid({ version: 4 });
      await repo.saveAdmissionNote(id, notesInput, profile, profile);
    } catch (error) {
      expect(error.message).toEqual('Admission Record Not Found');
    }
  });

  it('updateDischargePatient(): should update discharge patient details', async () => {
    const dischargeInput = dischargePatientFactory.build();
    const createdDischargeDetails = await createDischargePatients(
      manager,
      2,
      admissionForPatient,
    );
    const { id } = createdDischargeDetails[0];
    const dischargeRecord = await repo.updateDischargePatient(
      id,
      dischargeInput,
      admissionForPatient.profile,
    );

    expect(dischargeRecord.dischargedBy).toEqual(dischargeInput.dischargedBy);
    expect(dischargeRecord.dischargeDate).toEqual(dischargeInput.dischargeDate);
  });

  it('updateTransferPatient(): should update transfer patient details', async () => {
    const transferInput = transferPatientFactory.build();
    const [hospital] = await createHospitals(manager, 1);
    transferInput.transferHospital = hospital.id;
    const createdTransferDetails = await createTransferPatients(
      manager,
      2,
      admissionForPatient,
      admissionForPatient.profile,
    );
    const { id } = createdTransferDetails[0];
    const transferRecord = await repo.updateTransferPatient(
      id,
      transferInput,
      admissionForPatient.profile,
    );

    expect(transferRecord.transferDateTime).toEqual(
      transferInput.transferDateTime,
    );
    expect(transferRecord.transferReason).toEqual(transferInput.transferReason);
  });

  it('updateTransferPatient(): should update transfer patient details', async () => {
    const transferInput = transferPatientFactory.build();
    transferInput.transferHospital = chance.guid({ version: 4 });
    const createdTransferDetails = await createTransferPatients(
      manager,
      2,
      admissionForPatient,
      admissionForPatient.profile,
    );
    const { id } = createdTransferDetails[0];

    await expect(
      repo.updateTransferPatient(
        id,
        transferInput,
        admissionForPatient.profile,
      ),
    ).rejects.toThrow(new NotFoundException('Facility Not Found'));
  });

  it('updateBloodTransfusion(): should update transfusion details on admission record', async () => {
    const transfusionInput = bloodTransfusionsFactory.build();
    const createdTransfusionDetails = await createBloodTransfusions(
      manager,
      2,
      admissionForPatient,
    );
    const { id } = createdTransfusionDetails[0];
    const transfusionRecord = await repo.updateBloodTransfusion(
      id,
      transfusionInput,
      admissionForPatient.profile,
    );

    expect(transfusionRecord.bloodPint).toEqual(transfusionInput.bloodPint);
    expect(transfusionRecord.bloodLabel).toEqual(transfusionInput.bloodLabel);
  });

  it('updateAdmissionNotes(): should update admission note details on admission record', async () => {
    const notesInput = notesFactory.build();
    const createdAdmissionNotes = await createAdmissionNotes(
      manager,
      2,
      admissionForPatient,
    );
    const { id } = createdAdmissionNotes[0];
    const admissionNoteRecord = await repo.updateAdmissionNote(
      id,
      notesInput,
      admissionForPatient.profile,
    );

    expect(admissionNoteRecord.creatorProfileType).toEqual(
      notesInput.creatorProfileType,
    );
    expect(admissionNoteRecord.note).toEqual(notesInput.note);
  });

  it('deleteDischargePatient(): should delete discharge patient details', async () => {
    const createdDischargeDetails = await createDischargePatients(
      manager,
      2,
      admissionForPatient,
    );
    const { id } = createdDischargeDetails[0];
    const dischargeRecord = await repo.deleteDischargePatient(
      id,
      admissionForPatient.profile,
    );

    expect(dischargeRecord.id).toEqual(id);
    expect(dischargeRecord.dischargedBy).toEqual(
      createdDischargeDetails[0].dischargedBy,
    );
    expect(dischargeRecord.dischargeDate).toEqual(
      createdDischargeDetails[0].dischargeDate,
    );
  });

  it('deleteTransferPatient(): should delete transfer patient details', async () => {
    const createdTransferDetails = await createTransferPatients(
      manager,
      2,
      admissionForPatient,
      admissionForPatient.profile,
    );
    const { id } = createdTransferDetails[0];
    const transferRecord = await repo.deleteTransferPatient(
      id,
      admissionForPatient.profile,
    );

    expect(transferRecord.id).toEqual(id);
    expect(transferRecord.transferDateTime).toEqual(
      createdTransferDetails[0].transferDateTime,
    );
    expect(transferRecord.transferReason).toEqual(
      createdTransferDetails[0].transferReason,
    );
  });

  it('deleteBloodTransfusion(): should delete transfusion details', async () => {
    const createdTransfusionDetails = await createBloodTransfusions(
      manager,
      2,
      admissionForPatient,
    );
    const { id } = createdTransfusionDetails[0];
    const transfusionRecord = await repo.deleteBloodTransfusion(
      id,
      admissionForPatient.profile,
    );

    expect(transfusionRecord.id).toEqual(id);
    expect(transfusionRecord.bloodPint).toEqual(
      createdTransfusionDetails[0].bloodPint,
    );
    expect(transfusionRecord.bloodLabel).toEqual(
      createdTransfusionDetails[0].bloodLabel,
    );
  });

  it('deleteAdmissionNotes(): should delete admission note details', async () => {
    const createdAdmissionNotes = await createAdmissionNotes(
      manager,
      2,
      admissionForPatient,
    );
    const { id } = createdAdmissionNotes[0];
    const admissionNoteRecord = await repo.deleteAdmissionNote(
      id,
      admissionForPatient.profile,
    );

    expect(admissionNoteRecord.id).toEqual(id);
    expect(admissionNoteRecord.creatorProfileType).toEqual(
      createdAdmissionNotes[0].creatorProfileType,
    );
    expect(admissionNoteRecord.note).toEqual(createdAdmissionNotes[0].note);
  });

  it('deleting SubRecords throws an error if user is not creator of record', async () => {
    const createdAdmissionNotes = await createAdmissionNotes(
      manager,
      2,
      admissionForPatient,
    );
    const otherAdmissions = await createAdmissions(manager, 1);
    const { id } = createdAdmissionNotes[0];
    await expect(
      repo.deleteAdmissionNote(id, otherAdmissions[0].profile),
    ).rejects.toThrow('Record Cannot Be Deleted');
  });

  it('deleting SubRecords throws an error if record is not found', async () => {
    await createAdmissionNotes(manager, 2, admissionForPatient);
    await expect(
      repo.deleteAdmissionNote(
        admissionForPatient.id,
        admissionForPatient.profile,
      ),
    ).rejects.toThrow('Record Not Found');
  });

  it('updating SubRecords throws an error if user is not creator of record', async () => {
    const createdAdmissionNotes = await createAdmissionNotes(
      manager,
      2,
      admissionForPatient,
    );
    const { id } = createdAdmissionNotes[0];
    const otherAdmissions = await createAdmissions(manager, 1);
    const notesInput = notesFactory.build();
    await expect(
      repo.updateAdmissionNote(id, notesInput, otherAdmissions[0].profile),
    ).rejects.toThrow('Not Authorized To Modify This Record');
  });

  it('updating SubRecords throws an error if record is not found', async () => {
    await createAdmissionNotes(manager, 2, admissionForPatient);
    const notesInput = notesFactory.build();
    await expect(
      repo.updateAdmissionNote(
        admissionForPatient.id,
        notesInput,
        admissionForPatient.profile,
      ),
    ).rejects.toThrow('Record Not Found');
  });

  it('findByProfile() should find admissions for a particular user using filters', async () => {
    const { admittedBy, id, admissionDate } = admissionForPatient;
    const record = await repo.findByProfile(
      admissionForPatient.profile,
      admissionForPatient.profile.id,
      {
        skip: 0,
        take: 10,
        keyword: admittedBy,
        dateRange: {
          from: moment(admissionDate).startOf('day').toDate(),
          to: moment(admissionDate).endOf('day').toDate(),
        },
      },
    );
    expect(record).toHaveProperty('list');
    expect(record.list).toContainEqual(
      expect.objectContaining({ id, admittedBy }),
    );
  });

  it('findByProfile() should find admissions for a particular user created by them', async () => {
    const record = await repo.findByProfile(
      admissionForPatient.profile,
      admissionForPatient.profile.id,
      {
        creator: RecordCreator.SELF,
      },
    );
    expect(record).toHaveProperty('list');
    expect(
      record.list.every((item) => item.createdBy.id === profile.id),
    ).toBeTruthy();
  });

  it('findByProfile() should find admissions for a particular user created by others', async () => {
    const record = await repo.findByProfile(
      admissionForPatient.profile,
      admissionForPatient.profile.id,
      {
        creator: RecordCreator.OTHERS,
      },
    );
    expect(record).toHaveProperty('list');
    expect(
      record.list.every((item) => item.createdBy.id !== profile.id),
    ).toBeTruthy();
  });

  it('findByProfile(): should find transferred admissions', async () => {
    const [hospital] = await createHospitals(manager, 1);
    const transferInput = await transferPatientFactory.build();
    transferInput.transferHospital = hospital?.id;
    const admissions = await createAdmissions(manager, 3, profile, profile);
    await repo.saveTransferPatient(
      admissions?.[0]?.id,
      transferInput,
      profile,
      profile,
    );
    const records = await repo.findByProfile(
      admissionForPatient.profile,
      admissionForPatient.profile.id,
      {
        transferred: true,
      },
    );
    expect(records.totalCount).toEqual(1);
  });

  it('findByHospital(): find hospital admission', async () => {
    const [hospital] = await createHospitals(manager, 1);
    const [doctor] = await createUsers(manager, 1, hospital);
    await createAdmissions(manager, 3, doctor.profiles[0]);
    const records = await repo.findByHospital(
      doctor.profiles[0],
      doctor.profiles[0].hospital.id,
      {},
    );
    expect(records).toHaveProperty('list');
  });

  it('findByHospital(): find with partnerId options passed', async () => {
    const [partner] = await createPartner(manager);
    const [hospitalA, hospitalB] = await createHospitals(
      manager,
      2,
      null,
      partner,
    );
    const [doctorA] = await createUsers(manager, 1, hospitalA);
    const [doctorB] = await createUsers(manager, 1, hospitalB);
    await createAdmissions(
      manager,
      5,
      doctorA.profiles[0],
      undefined,
      undefined,
      hospitalA,
    );
    await createAdmissions(
      manager,
      5,
      doctorB.profiles[0],
      undefined,
      undefined,
      hospitalB,
    );
    const records = await repo.findByHospital(
      doctorA.profiles[0],
      hospitalA.id,
      {},
      undefined,
      partner.id,
    );
    expect(records.totalCount).toEqual(10);
  });

  it('findByHospital(): find with hmoId options passed', async () => {
    const [hospital] = await createHospitals(manager, 1, null);
    const [hmoProfile] = await createHmoProfileFixtures(manager);

    const [doctorA] = await createUsers(manager, 1, hospital);
    const [enrollee] = await createUsers(
      manager,
      1,
      undefined,
      undefined,
      hmoProfile,
    );
    await createAdmissions(
      manager,
      5,
      doctorA.profiles[0],
      enrollee.profiles[0],
      undefined,
      hospital,
    );
    const records = await repo.findByHospital(
      doctorA.profiles[0],
      hospital.id,
      {},
      hmoProfile.provider.id,
    );
    expect(records.totalCount).toEqual(5);
  });

  it('findByHospital(): should find transferred admissions', async () => {
    const [hospital] = await createHospitals(manager, 1);
    const transferInput = await transferPatientFactory.build();
    transferInput.transferHospital = hospital?.id;
    const [doctor] = await createUsers(manager, 1, hospital);
    const admissions = await createAdmissions(
      manager,
      3,
      doctor.profiles[0],
      profile,
    );
    await repo.saveTransferPatient(
      admissions?.[0]?.id,
      transferInput,
      profile,
      doctor.profiles[0],
    );
    const records = await repo.findByHospital(doctor.profiles[0], hospital.id, {
      transferred: true,
    });
    expect(records.totalCount).toEqual(1);
  });

  it('findByHospital(): should find admissions for a particular hospital using filters', async () => {
    const [hospital] = await createHospitals(manager, 1);
    const [doctor] = await createUsers(manager, 1, hospital);
    await createAdmissions(manager, 3, doctor.profiles[0], profile);
    const record = await repo.findByHospital(profile, profile.id, {
      skip: 0,
      take: 10,
      keyword: profile.clinifyId,
    });
    expect(record).toHaveProperty('list');
    expect(record.totalCount).toBe(3);
  });

  it('findByHospital(): should find admissions with branches', async () => {
    const [hospital] = await createHospitals(manager, 1);
    const [doctor] = await createUsers(manager, 1, hospital);
    await createAdmissions(manager, 3, doctor.profiles[0], profile);
    profile.branchIds = [hospital.id];
    const record = await repo.findByHospital(profile, profile.id, {
      skip: 0,
      take: 10,
      keyword: profile.clinifyId,
    });
    expect(record).toHaveProperty('list');
    expect(record.totalCount).toBe(3);
  });

  it('saveAdmissionWithSubrecords() should allow patient to create sub-records simultaneously', async () => {
    const [hospital] = await createHospitals(manager);

    const admissionInput = admissionFactory.build();
    delete admissionInput.profile;
    admissionInput.dischargePatients = dischargePatientFactory.buildList(2);
    admissionInput.admissionNotes = notesFactory.buildList(2);
    admissionInput.bloodTransfusions = bloodTransfusionsFactory.buildList(2);
    admissionInput.transferPatients = [
      { ...transferPatientFactory.build(), transferHospital: hospital },
    ];

    const createdAdmission = await repo.saveAdmissionWithSubRecords(
      admissionInput,
      admissionForPatient.profile,
      admissionForPatient.profile,
    );
    expect(createdAdmission.dischargePatients.length).toEqual(2);
    expect(createdAdmission.admissionNotes.length).toEqual(2);
    expect(createdAdmission.bloodTransfusions.length).toEqual(2);
    expect(createdAdmission.transferPatients.length).toEqual(1);
  });

  it('saveAdmissionWithSubrecords() should allow patient to create sub-records simultaneously', async () => {
    const admissionInput = admissionFactory.build();
    delete admissionInput.profile;
    admissionInput.dischargePatients = dischargePatientFactory.buildList(2);
    admissionInput.admissionNotes = notesFactory.buildList(2);
    admissionInput.bloodTransfusions = bloodTransfusionsFactory.buildList(2);

    const createdAdmission = await repo.saveAdmissionWithSubRecords(
      admissionInput,
      admissionForPatient.profile,
      {
        ...admissionForPatient.profile,
        type: UserType.OrganizationDoctor,
      } as any,
    );
    expect(createdAdmission.dischargePatients.length).toEqual(2);
    expect(createdAdmission.admissionNotes.length).toEqual(0);
    expect(createdAdmission.bloodTransfusions.length).toEqual(2);
  });

  it('updateAdmission() should return undefined if id Not Found', async () => {
    const admissionInput = admissionFactory.build();
    delete admissionInput.profile;
    await expect(
      repo.updateAdmission(
        admissionForPatient.profile,
        {
          ...admissionInput,
          id: chance.guid({ version: 4 }),
        },
        chance.guid({ version: 4 }),
      ),
    ).rejects.toThrow('Record Not Found');
  });

  it('updateAdmission() should allow patient to update admission', async () => {
    const admissionInput = admissionFactory.build();
    admissionInput.clinifyId = admissionForPatient.profile.clinifyId;
    delete admissionInput.profile;
    admissionInput.id = admissionForPatient.id;
    const updatedAdmission = await repo.updateAdmission(
      admissionForPatient.profile,
      admissionInput,
      admissionInput.id,
    );
    expect(updatedAdmission[0].admittedBy).toEqual(admissionInput.admittedBy);
    expect(updatedAdmission[0].clinicName).toEqual(admissionInput.clinicName);
    expect(updatedAdmission[0].admissionDate).toEqual(
      admissionInput.admissionDate,
    );
  });

  it('updateAdmission(): should allow patient to create sub-records simultaneously', async () => {
    const admissionInput = admissionFactory.build();
    admissionInput.clinifyId = admissionForPatient.profile.clinifyId;
    delete admissionInput.profile;
    admissionInput.id = admissionForPatient.id;
    admissionInput.dischargePatients = dischargePatientFactory.buildList(2);
    admissionInput.admissionNotes = notesFactory.buildList(2);
    admissionInput.bloodTransfusions = bloodTransfusionsFactory.buildList(2);

    const updatedAdmission = await repo.updateAdmission(
      admissionForPatient.profile,
      admissionInput,
      admissionInput.id,
    );
    expect(updatedAdmission[0].dischargePatients.length).toEqual(2);
    expect(updatedAdmission[0].admissionNotes.length).toEqual(2);
    expect(updatedAdmission[0].bloodTransfusions.length).toEqual(2);
  });

  it('updateAdmission() should not allow patient to update another patient admission', async () => {
    const admissionsForAnotherPatient = await createAdmissions(manager, 1);
    const admissionForAnotherPatient = admissionsForAnotherPatient[0];

    const admissionInput = admissionFactory.build();
    admissionInput.id = admissionForAnotherPatient.id;
    delete admissionInput.profile;

    await expect(
      repo.updateAdmission(
        admissionForPatient.profile,
        admissionInput,
        admissionInput.id,
      ),
    ).rejects.toThrow('Record Not Found');
  });

  it('updateAdmission() should not allow organization doctors to update records for another hospital', async () => {
    const admissionInput = admissionFactory.build();
    const [hospitalCreator, hospital] = await createHospitals(manager, 2);
    const [doctor] = await createUsers(manager, 1, hospitalCreator);
    const [anotherDoctorInAnotherHospital] = await createUsers(
      manager,
      1,
      hospital,
    );
    const [admissionByDoctor] = await createAdmissions(
      manager,
      1,
      doctor.defaultProfile,
    );

    admissionInput.createdBy = admissionByDoctor.createdBy;
    admissionInput.id = admissionByDoctor.id;
    delete admissionInput.profile;

    await expect(
      repo.updateAdmission(
        anotherDoctorInAnotherHospital.defaultProfile,
        admissionInput,
        admissionInput.id,
      ),
    ).rejects.toThrow('Record Not Found');
  });

  it('updateAdmission() should not allow organization billing officer to update records', async () => {
    const admissionInput = admissionFactory.build();
    const [hospital] = await createHospitals(manager);
    const [doctor] = await createUsers(manager, 1, hospital);
    const [billingOfficer] = await createUsers(
      manager,
      1,
      hospital,
      undefined,
      undefined,
      UserType.OrganizationBillingOfficer,
    );

    const [admissionByDoctor] = await createAdmissions(
      manager,
      1,
      doctor.defaultProfile,
    );

    admissionInput.id = admissionByDoctor.id;
    admissionInput.clinifyId = admissionByDoctor.profile.clinifyId;
    delete admissionInput.profile;
    delete admissionInput.createdBy;

    await expect(
      repo.updateAdmission(
        billingOfficer.defaultProfile,
        admissionInput,
        admissionInput.id,
      ),
    ).rejects.toThrow('Not Authorized To Modify This Record');
  });

  it('updateAdmission() should allow organization billing officer to update records', async () => {
    const admissionInput = admissionFactory.build();
    const [hospital] = await createHospitals(manager);
    const [doctor] = await createUsers(manager, 1, hospital);
    const [billingOfficer] = await createUsers(
      manager,
      1,
      hospital,
      undefined,
      undefined,
      UserType.OrganizationBillingOfficer,
    );

    const [admissionByDoctor] = await createAdmissions(
      manager,
      1,
      doctor.defaultProfile,
      undefined,
      undefined,
      hospital,
    );

    admissionInput.id = admissionByDoctor.id;
    admissionInput.clinifyId = admissionByDoctor.profile.clinifyId;
    delete admissionInput.profile;
    delete admissionInput.createdBy;

    const [response] = await repo.updateAdmission(
      billingOfficer.defaultProfile,
      admissionInput,
      admissionInput.id,
      true,
    );

    expect(response.admittedBy).toEqual(admissionInput.admittedBy);
    expect(response.clinicName).toEqual(admissionInput.clinicName);
    expect(response.admissionDate).toEqual(admissionInput.admissionDate);
  });

  it('updateHospitalAdmission() should allow organization record officer to update same hospital admission record', async () => {
    const admissionInput = admissionFactory.build();
    const [hospital] = await createHospitals(manager);
    const [doctor] = await createUsers(manager, 1, hospital);
    const [recordOfficer] = await createUsers(
      manager,
      1,
      hospital,
      undefined,
      undefined,
      UserType.OrganizationRecordOfficer,
    );

    const [admissionByDoctor] = await createAdmissions(
      manager,
      1,
      doctor.defaultProfile,
      undefined,
      undefined,
      hospital,
    );

    admissionInput.id = admissionByDoctor.id;
    admissionInput.clinifyId = admissionByDoctor.profile.clinifyId;
    delete admissionInput.profile;
    delete admissionInput.createdBy;

    const response = await repo.updateHospitalAdmission(
      recordOfficer.defaultProfile,
      admissionInput,
      admissionInput.id,
    );

    expect(response.admittedBy).toEqual(admissionInput.admittedBy);
    expect(response.clinicName).toEqual(admissionInput.clinicName);
    expect(response.admissionDate).toEqual(admissionInput.admissionDate);
  });

  it('getOneAdmission() should get one admission', async () => {
    const record = await repo.getOneAdmission(
      admissionForPatient.profile,
      admissionForPatient.id,
    );
    expect(record.id).toEqual(admissionForPatient.id);
    expect(record.clinicName).toEqual(admissionForPatient.clinicName);
  });

  it('deleteAdmission() should delete one admission', async () => {
    const [record] = await repo.deleteAdmission(admissionForPatient.profile, [
      admissionForPatient.id,
    ]);
    expect(record.id).toEqual(admissionForPatient.id);
    expect(record.clinicName).toEqual(admissionForPatient.clinicName);
  });

  it('deleteAdmission() should only delete record they have access to (created by me)', async () => {
    const [hospital] = await createHospitals(manager);
    const [doctor] = await createUsers(manager, 1, hospital);
    const [admissionsByDoctor, nextAdmissionByDoctor] = await createAdmissions(
      manager,
      2,
      doctor.defaultProfile,
    );
    const record = await repo.deleteAdmission(admissionForPatient.profile, [
      admissionForPatient.id,
      admissionsByDoctor.id,
      nextAdmissionByDoctor.id,
    ]);
    expect(record.length).toEqual(1);
  });

  it('deleteAdmission() should only delete record they have access to (created by them/organization)', async () => {
    const fakeAdmission = admissionFactory.build();
    const [hospital] = await createHospitals(manager);
    const [doctor] = await createUsers(manager, 1, hospital);

    const [admissionsByDoctor, nextAdmissionByDoctor] = await createAdmissions(
      manager,
      2,
      doctor.defaultProfile,
    );
    const record = await repo.deleteAdmission(doctor.defaultProfile, [
      admissionForPatient.id,
      admissionsByDoctor.id,
      nextAdmissionByDoctor.id,
      fakeAdmission.id,
    ]);
    expect(record.length).toEqual(2);
  });

  it('deleteAdmission() should not delete record created by another', async () => {
    const fakeAdmission = admissionFactory.build();
    const [hospital] = await createHospitals(manager);
    const [doctor] = await createUsers(manager, 1, hospital);
    const [anotherDoctorInSameHospital] = await createUsers(
      manager,
      2,
      hospital,
    );

    const [admissionsByDoctor, nextAdmissionByDoctor] = await createAdmissions(
      manager,
      2,
      doctor.defaultProfile,
    );
    const record = await repo.deleteAdmission(
      anotherDoctorInSameHospital.defaultProfile,
      [
        admissionForPatient.id,
        admissionsByDoctor.id,
        nextAdmissionByDoctor.id,
        fakeAdmission.id,
      ],
    );
    expect(record.length).toEqual(0);
  });

  it('deleteAdmission() should allow organizationAdmin delete an hospital record', async () => {
    const fakeAdmission = admissionFactory.build();
    const newProfile = { ...profile } as ProfileModel;
    newProfile.type = UserType.OrganizationAdmin;
    const [hospital] = await createHospitals(manager);
    newProfile.hospital = hospital;
    newProfile.hospitalId = hospital?.id;
    const [doctor] = await createUsers(manager, 1, hospital);
    const [admissionsByDoctor, nextAdmissionByDoctor] = await createAdmissions(
      manager,
      2,
      doctor.defaultProfile,
    );
    const record = await repo.deleteAdmission(newProfile, [
      admissionForPatient.id,
      admissionsByDoctor.id,
      nextAdmissionByDoctor.id,
      fakeAdmission.id,
    ]);
    expect(record.length).toEqual(1);
  });

  it('deleteAdmission() should only delete record they have access to (created by them/organization)', async () => {
    const [hospital, clinic] = await createHospitals(manager);
    const [doctor] = await createUsers(manager, 1, hospital);
    const [anotherDoctorInAnotherHospital] = await createUsers(
      manager,
      1,
      clinic,
    );

    const [admissionsByDoctor] = await createAdmissions(
      manager,
      2,
      doctor.defaultProfile,
    );
    const record = await repo.deleteAdmission(
      anotherDoctorInAnotherHospital.defaultProfile,
      [admissionForPatient.id, admissionsByDoctor.id],
    );
    expect(record.length).toBeFalsy();
  });

  it('archiveAdmission() should only archive/unarchive record created within hospital', async () => {
    const [hospital] = await createHospitals(manager);
    const [anotherHospital] = await createHospitals(manager);
    const [doctor] = await createUsers(manager, 1, hospital);
    const [doctorFromAnotherHospital] = await createUsers(
      manager,
      1,
      anotherHospital,
    );
    const [admissionsByDoctor, nextAdmissionByDoctor] = await createAdmissions(
      manager,
      2,
      doctor.defaultProfile,
    );
    const [admissionFromAnotherHospital] = await createAdmissions(
      manager,
      1,
      doctorFromAnotherHospital.defaultProfile,
    );
    let record = await repo.archiveAdmission(
      doctor.defaultProfile,
      [
        admissionsByDoctor.id,
        nextAdmissionByDoctor.id,
        admissionFromAnotherHospital.id,
      ],
      true,
      billServiceMock,
    );
    expect(record.length).toEqual(2);
    expect(record).toContainEqual(expect.objectContaining({ archived: true }));

    record = await repo.archiveAdmission(
      doctor.defaultProfile,
      [
        admissionsByDoctor.id,
        nextAdmissionByDoctor.id,
        admissionFromAnotherHospital.id,
      ],
      false,
      billServiceMock,
    );
    expect(record.length).toEqual(2);
    expect(record).toContainEqual(expect.objectContaining({ archived: false }));
  });

  it('archiveAdmission() should not allow any doctor to archive a record', async () => {
    const fakeAdmission = admissionFactory.build();
    const [hospital] = await createHospitals(manager);
    const [anotherHospital] = await createHospitals(manager);
    const [doctor] = await createUsers(manager, 1, hospital);
    const [anotherDoctorInDifferent] = await createUsers(
      manager,
      2,
      anotherHospital,
    );

    const [admissionsByDoctor, nextAdmissionByDoctor] = await createAdmissions(
      manager,
      2,
      doctor.defaultProfile,
    );
    const record = await repo.archiveAdmission(
      anotherDoctorInDifferent.defaultProfile,
      [
        admissionForPatient.id,
        admissionsByDoctor.id,
        nextAdmissionByDoctor.id,
        fakeAdmission.id,
      ],
      true,
      billServiceMock,
    );
    expect(record.length).toEqual(0);
  });

  it('findByProfile() should find archived admissions for a particular user using filters', async () => {
    await repo.archiveAdmission(
      profile,
      [admissionForPatient.id, admissionsForPatient[1].id],
      true,
      billServiceMock,
    );
    const record = await repo.findByProfile(profile, profile.id, {
      archive: true,
    });
    expect(record).toHaveProperty('list');
    expect(record.list).toContainEqual(
      expect.objectContaining({ archived: true }),
    );
  });

  it('getSharedAdmissions() should return admissions linked to a shared record', async () => {
    const [shared] = await createSharedRecord(manager, 1, {
      admissions: [admissionForPatient],
    });
    const record = await repo.getSharedAdmissions(shared.id, {});
    expect(record.list.length).toEqual(1);
    expect(record.list[0].id).toEqual(admissionForPatient.id);
  });

  it('getSharedAdmissions() should return admissions linked to a shared record created by creator', async () => {
    const [hospital] = await createHospitals(manager);
    const [doctor] = await createUsers(manager, 1, hospital);
    const [admissionsFromDoctor] = await createAdmissions(
      manager,
      1,
      doctor.defaultProfile,
    );
    const [shared] = await createSharedRecord(manager, 1, {
      admissions: [admissionForPatient, admissionsFromDoctor],
    });
    let record = await repo.getSharedAdmissions(shared.id, {
      creator: RecordCreator.OTHERS,
    });
    expect(record.list.length).toEqual(1);
    expect(record.list[0].id).toEqual(admissionsFromDoctor.id);
    record = await repo.getSharedAdmissions(shared.id, {
      creator: RecordCreator.SELF,
    });
    expect(record.list.length).toEqual(1);
    expect(record.list[0].id).toEqual(admissionForPatient.id);
  });

  it('getLinkedInvestigationRecords(): should return radiology records linked to this record', async () => {
    const linkedRecords = await repo.getLinkedInvestigationRecords(
      profile,
      admissionForPatient.id,
    );

    // TODO handle this appropriately and others alike
    expect(linkedRecords?.length).toEqual(0);
    expect(linkedRecords[0]?.id).toEqual(
      admissionForPatient.admission_investigation[0]?.investigationId,
    );
  });

  it('getLinkedRadiologyInvestigationRecords(): should return radiology records linked to this record', async () => {
    const linkedRecords = await repo.getLinkedRadiologyInvestigationRecords(
      profile,
      admissionForPatient.id,
    );

    // TODO handle this appropriately and others alike
    expect(linkedRecords?.length).toEqual(0);
    expect(linkedRecords[0]?.id).toEqual(
      admissionForPatient.admission_investigation[0]?.investigationId,
    );
  });

  it('getLinkedLaboratoryInvestigationRecords(): should return labResults linked to this record', async () => {
    const linkedRecords = await repo.getLinkedLaboratoryInvestigationRecords(
      profile,
      admissionForPatient.id,
    );

    expect(linkedRecords?.length).toEqual(0);
    expect(linkedRecords[0]?.id).toEqual(
      admissionForPatient.admission_investigation[0]?.investigationId,
    );
  });

  it('getLinkedInvestigationRecords(): should return investigations linked to this record', async () => {
    const linkedRecords = await repo.getLinkedInvestigationRecords(
      profile,
      admissionForPatient.id,
    );

    // TODO handle this appropriately and others alike
    expect(linkedRecords?.length).toEqual(0);
    expect(linkedRecords[0]?.id).toEqual(
      admissionForPatient.admission_investigation[0]?.investigationId,
    );
  });

  it('isAllergiesLinked() should return TRUE if any allergy record is linked', async () => {
    const isLinked = await repo.isAllergiesLinked(
      ds,
      profile,
      admissionForPatient.id,
    );

    expect(isLinked).toBeTruthy();
  });

  it('isConsultationsLinked() should return TRUE is any consultation record is linked', async () => {
    const isLinked = await repo.isConsultationsLinked(
      ds,
      profile,
      admissionForPatient.id,
    );

    expect(isLinked).toBeFalsy();
  });

  it('isMedicationsLinked(): should return TRUE if any medication record is linked', async () => {
    const isLinked = await repo.isMedicationsLinked(
      ds,
      profile,
      admissionForPatient.id,
    );

    expect(isLinked).toBeFalsy();
  });

  it('isSurgeriesLinked(): should return TRUE if any surgery record is linked to this record', async () => {
    const isLinked = await repo.isSurgeriesLinked(
      ds,
      profile,
      admissionForPatient.id,
    );

    expect(isLinked).toBeFalsy();
  });

  it('isVitalsLinked(): should return TRUE if any vitals record is linked', async () => {
    const isLinked = await repo.isVitalsLinked(
      ds,
      profile,
      admissionForPatient.id,
    );

    expect(isLinked).toBeFalsy();
  });

  it('isLaboratoryInvestigationLinked(): should return TRUE if any is linked', async () => {
    const isLinked = await repo.isLaboratoryInvestigationLinked(
      ds,
      profile,
      admissionForPatient.id,
    );

    expect(isLinked).toBeFalsy();
  });

  it('isRadiologyInvestigationLinked(): should return TRUE if any is linked', async () => {
    const isLinked = await repo.isRadiologyInvestigationLinked(
      ds,
      profile,
      admissionForPatient.id,
    );

    expect(isLinked).toBeFalsy();
  });

  it('linkRecordsToAdmission(): should link allergy record to Admission record', async () => {
    const linkedAdmissionRecord = await repo.linkRecordsToAdmission(
      admissionForPatient,
      [patientAllergy.id],
      profile,
      AdmissionLinkedRecordType.Allergy,
    );

    const linkedAllergiesIDs = linkedAdmissionRecord.allergies.map(
      (allergy) => allergy.id,
    );
    expect(linkedAllergiesIDs).toContain(patientAllergy.id);
  });

  it('concealAdmissionNote should conceal admissionNote from patient', async () => {
    const createdAdmissionNotes = await createAdmissionNotes(
      manager,
      2,
      admissionForPatient,
    );
    const { id } = createdAdmissionNotes[0];
    const result = await repo.concealAdmissionNote(profile, id, true);
    expect(result.conceal).toBeTruthy();
  });

  it('concealAdmissionNote(): should throw error when Record Not Found', async () => {
    await expect(
      repo.concealAdmissionNote(profile, chance.guid({ version: 4 }), true),
    ).rejects.toThrow('Record Not Found');
  });

  it('concealDischargeSummary should conceal dischargeSummary from Patient', async () => {
    const createdDischargePatient = await createDischargePatients(
      manager,
      1,
      admissionForPatient,
      profile,
    );
    const { id } = createdDischargePatient[0];
    const response = await repo.concealDischargeSummary(profile, id, true);
    expect(response.concealDischargeSummary).toBeTruthy();
  });

  it('concealDischargeSummary(): should throw error when Record Not Found', async () => {
    await expect(
      repo.concealDischargeSummary(profile, chance.guid({ version: 4 }), true),
    ).rejects.toThrow('Record Not Found');
  });

  it('concealReasonForTransfer should conceal reason for transfer from patient', async () => {
    const createdTransferPatient = await createTransferPatients(
      manager,
      1,
      admissionForPatient,
      profile,
    );
    const { id } = createdTransferPatient[0];
    const result = await repo.concealReasonForTransfer(profile, id, true);
    expect(result.concealTransferReason).toBeTruthy();
  });

  it('concealReasonForTransfer(): should throw error when Record Not Found', async () => {
    await expect(
      repo.concealReasonForTransfer(profile, chance.guid({ version: 4 }), true),
    ).rejects.toThrow('Record Not Found');
  });

  it('concealTransfusionNote should conceal transfusion note for transfer from patient', async () => {
    const createdBloodTransfusion = await createBloodTransfusions(
      manager,
      1,
      admissionForPatient,
      profile,
    );
    const { id } = createdBloodTransfusion[0];
    const result = await repo.concealTransfusionNote(profile, id, true);
    expect(result.concealTransfusionNote).toBeTruthy();
  });

  it('concealPostTransfusionFBC() should ', async () => {
    const [{ id }] = await createBloodTransfusions(
      manager,
      1,
      admissionForPatient,
      profile,
    );
    let res = await repo.concealPostTransfusionFBC(profile, id, true);
    expect(res.concealPostTransfusionFBC).toBeTruthy();
    res = await repo.concealPostTransfusionFBC(profile, id, false);
    expect(res.concealPostTransfusionFBC).toBeFalsy();
  });

  it('concealPostTransfusionFBC() should throw error when record is not found', async () => {
    await expect(
      repo.concealPostTransfusionFBC(
        profile,
        chance.guid({ version: 4 }),
        true,
      ),
    ).rejects.toThrow('Record Not Found');
  });

  it('concealTransfusionNote(): should throw error when Record Not Found', async () => {
    await expect(
      repo.concealTransfusionNote(profile, chance.guid({ version: 4 }), true),
    ).rejects.toThrow('Record Not Found');
  });

  it('fetchInputOutput() should find input output sub records for an admission', async () => {
    const admission = admissionForPatient;
    await createInputRecords(manager, 3, admission);
    await createOutputRecords(manager, 3, admission);
    const response = await repo.fetchInputOutput(
      admission.profile,
      admission.id,
    );

    expect(response.inputDetails.length).toEqual(3);
    expect(response.outputDetails.length).toEqual(3);
  });

  it('fetchInputOutput() should throw error when admission record Not Found', async () => {
    await expect(
      repo.fetchInputOutput(
        admissionForPatient.profile,
        chance.guid({ version: 4 }),
      ),
    ).rejects.toThrow('Record Not Found');
  });

  it('fetchInputOutput() should throw error when patient want to fetch another patient record', async () => {
    const [localPatient] = await createUsers(
      manager,
      1,
      null,
      null,
      null,
      UserType.Patient,
    );

    await expect(
      repo.fetchInputOutput(
        localPatient.defaultProfile,
        admissionForPatient.id,
      ),
    ).rejects.toThrow('Not Authorized To View This Record');
  });

  it('saveInputOutput(): should save input record on admission record', async () => {
    const inputOutputRecord = inputOutputFactory.build();
    const { id, profile } = admissionForPatient;
    const inputOutput = await repo.saveInputOutput(
      id,
      inputOutputRecord,
      profile,
      profile,
      'input',
    );

    expect(inputOutput.inputDetails.admission.id).toEqual(id);
    expect(typeof inputOutput.inputDetails).toBe('object');
    expect(inputOutput.inputDetails).toBeTruthy();
    expect(typeof inputOutput.outputDetails).toBe('object');
    expect(inputOutput.outputDetails).toBeFalsy();
  });

  it('saveInputOutput(): should save output record on admission record', async () => {
    const inputOutputRecord = inputOutputFactory.build();
    const { id, profile } = admissionForPatient;
    const inputOutput = await repo.saveInputOutput(
      id,
      inputOutputRecord,
      profile,
      profile,
      'output',
    );

    expect(inputOutput.outputDetails.admission.id).toEqual(id);
    expect(typeof inputOutput.inputDetails).toBe('object');
    expect(inputOutput.inputDetails).toBeFalsy();
    expect(typeof inputOutput.outputDetails).toBe('object');
    expect(inputOutput.outputDetails).toBeTruthy();
  });

  it('updateInputOutput(): should update output record', async () => {
    const inputOutputRecord = inputOutputFactory.build();
    const createdInputOutput = await createInputRecords(
      manager,
      2,
      admissionForPatient,
    );
    const { id } = createdInputOutput[0];
    const inputOutput = await repo.updateInputOutput(
      id,
      inputOutputRecord,
      admissionForPatient.profile,
      'input',
    );

    expect(typeof inputOutput.inputDetails).toBe('object');
    expect(inputOutput.inputDetails).toBeTruthy();
    expect(typeof inputOutput.outputDetails).toBe('object');
    expect(inputOutput.outputDetails).toBeFalsy();
  });

  it('updateInputOutput(): should update output record', async () => {
    const inputOutputRecord = inputOutputFactory.build();
    const createdInputOutput = await createOutputRecords(
      manager,
      2,
      admissionForPatient,
    );
    const { id } = createdInputOutput[0];
    const inputOutput = await repo.updateInputOutput(
      id,
      inputOutputRecord,
      admissionForPatient.profile,
      'output',
    );

    expect(typeof inputOutput.inputDetails).toBe('object');
    expect(inputOutput.inputDetails).toBeFalsy();
    expect(typeof inputOutput.outputDetails).toBe('object');
    expect(inputOutput.outputDetails).toBeTruthy();
  });

  it('deleteInputOutput(): should delete input record', async () => {
    const createdInputOutput = await createInputRecords(
      manager,
      2,
      admissionForPatient,
    );
    const { id } = createdInputOutput[0];
    const inputOutputRecord = await repo.deleteInputOutput(
      admissionForPatient.profile,
      id,
      'input',
    );

    expect(inputOutputRecord.inputDetails.id).toEqual(id);
    expect(typeof inputOutputRecord.inputDetails).toBe('object');
    expect(inputOutputRecord.inputDetails).toBeTruthy();
    expect(typeof inputOutputRecord.outputDetails).toBe('object');
    expect(inputOutputRecord.outputDetails).toBeFalsy();
  });

  it('deleteInputOutput(): should delete output record', async () => {
    const createdInputOutput = await createOutputRecords(
      manager,
      2,
      admissionForPatient,
    );
    const { id } = createdInputOutput[0];
    const inputOutputRecord = await repo.deleteInputOutput(
      admissionForPatient.profile,
      id,
      'output',
    );

    expect(inputOutputRecord.outputDetails.id).toEqual(id);
    expect(typeof inputOutputRecord.inputDetails).toBe('object');
    expect(inputOutputRecord.inputDetails).toBeFalsy();
    expect(typeof inputOutputRecord.outputDetails).toBe('object');
    expect(inputOutputRecord.outputDetails).toBeTruthy();
  });

  it('concealObservations should conceal observation from Patient', async () => {
    const createdOutputDetail = await createOutputRecords(
      manager,
      1,
      admissionForPatient,
      profile,
    );
    const response = await repo.concealObservations(
      profile,
      createdOutputDetail[0].id,
      true,
    );
    expect(response.concealObservations).toBeTruthy();
  });

  it('concealObservations(): should throw error when record is not found', async () => {
    await expect(
      repo.concealObservations(profile, chance.guid({ version: 4 }), true),
    ).rejects.toThrow('Record Not Found');
  });

  it('fetchAdmissionLineRecords() should find admission line sub records for an admission', async () => {
    await createAdmissionLines(manager, 3, admissionForPatient);
    const response = await repo.fetchAdmissionLineRecords(
      admissionForPatient.profile,
      admissionForPatient.id,
    );

    expect(response.length).toEqual(3);
  });

  it('saveAdmissionLine(): should save line record on admission record', async () => {
    const admissionLineInput = admissionLinesFactory.build();
    const { id, profile } = admissionForPatient;
    const admissionLineRecord = await repo.saveAdmissionLine(
      id,
      admissionLineInput,
      profile,
      profile,
    );

    expect(admissionLineRecord.admission.id).toEqual(id);
    expect(admissionLineRecord.placementDateTime).toEqual(
      admissionLineInput.placementDateTime,
    );
  });

  it('updateAdmissionLine(): should update existing lines record', async () => {
    const admissionLineInput = admissionLinesFactory.build();
    const createdAdmissionLines = await createAdmissionLines(
      manager,
      2,
      admissionForPatient,
    );
    const { id } = createdAdmissionLines[0];
    const admissionLineRecord = await repo.updateAdmissionLine(
      id,
      admissionLineInput,
      admissionForPatient.profile,
    );

    expect(admissionLineRecord.placedBy).toEqual(admissionLineInput.placedBy);
    expect(admissionLineRecord.placementDateTime).toEqual(
      admissionLineInput.placementDateTime,
    );
  });

  it('deleteAdmissionLine(): should delete existing lines record', async () => {
    const createdAdmissionLines = await createAdmissionLines(
      manager,
      2,
      admissionForPatient,
    );
    const { id } = createdAdmissionLines[0];
    const admissionLineRecord = await repo.deleteAdmissionLine(
      id,
      admissionForPatient.profile,
    );

    expect(admissionLineRecord.id).toEqual(id);
    expect(admissionLineRecord.placedBy).toEqual(
      createdAdmissionLines[0].placedBy,
    );
    expect(admissionLineRecord.placementDateTime).toEqual(
      createdAdmissionLines[0].placementDateTime,
    );
  });

  it('updateAdmissionConsentSignature(): throw error when admission id provided is not found', async () => {
    await expect(
      repo.updateAdmissionConsentSignature(
        admissionForPatient.profile,
        chance.guid({ version: 4 }),
        chance.name(),
        'typed',
      ),
    ).rejects.toThrow('Record Not Found');
  });

  it('updateAdmissionConsentSignature(): should update signature', async () => {
    const record = await repo.getOneAdmission(
      admissionForPatient.profile,
      admissionForPatient.id,
    );
    expect(record.patientConsent).not.toBe('Yes');

    const res = await repo.updateAdmissionConsentSignature(
      admissionForPatient.profile,
      admissionForPatient.id,
      'John Doe',
      'typed',
      true,
    );

    expect(res.patientConsent).toBe('Yes');
    expect(res.patientConsentSignature).toBe('John Doe');
    expect(res.patientConsentSignatureType).toBe('typed');

    const resp = await repo.updateAdmissionConsentSignature(
      admissionForPatient.profile,
      admissionForPatient.id,
      'signature-upload-url',
      'draw',
    );

    expect(resp.patientConsentSignature).toBe('signature-upload-url');
    expect(resp.patientConsentSignatureType).toBe('draw');
  });

  it('updateBloodTransfusionConsentSignature(): throw error when blood transfusion id provided is not found', async () => {
    await expect(
      repo.updateBloodTransfusionConsentSignature(
        admissionForPatient.profile,
        chance.guid({ version: 4 }),
        chance.name(),
        'typed',
      ),
    ).rejects.toThrow('Record Not Found');
  });

  it('updateBloodTransfusionConsentSignature(): should update signature', async () => {
    const [bloodTransfusion] = await createBloodTransfusions(
      manager,
      1,
      admissionForPatient,
    );
    expect(bloodTransfusion.patientConsent).not.toBe('Yes');

    const res = await repo.updateBloodTransfusionConsentSignature(
      admissionForPatient.profile,
      bloodTransfusion.id,
      'John Doe',
      'typed',
      true,
    );

    expect(res.patientConsent).toBe('Yes');
    expect(res.patientConsentSignature).toBe('John Doe');
    expect(res.patientConsentSignatureType).toBe('typed');

    const resp = await repo.updateBloodTransfusionConsentSignature(
      admissionForPatient.profile,
      bloodTransfusion.id,
      'signature-upload-url',
      'upload',
    );

    expect(resp.patientConsentSignature).toBe('signature-upload-url');
    expect(resp.patientConsentSignatureType).toBe('upload');
  });

  it('updateVTEAndBleedingRiskAssessment(): should update VTE and Bleeding Risk Assessment', async () => {
    const [hospital] = await createHospitals(manager);
    const [orgUser] = await createUsers(
      manager,
      1,
      hospital,
      undefined,
      undefined,
      UserType.OrganizationDoctor,
    );
    const [admission] = await createAdmissions(
      manager,
      1,
      orgUser.defaultProfile,
      undefined,
      undefined,
      hospital,
    );
    const record = await repo.getOneAdmission(admission.profile, admission.id);
    expect(record.vteAndBleedingRiskAssessment).toBeFalsy();

    const res = await repo.updateVTEAndBleedingRiskAssessment(
      orgUser.defaultProfile,
      admission.id,
      {
        field: 'onAdmission',
        changeFor: 'bleedingRiskAssessed',
        assessment: {
          bleedingRiskAssessed: true,
          vteRiskAssessed: true,
        },
      },
    );

    const updatedAdmission = await repo.getOneAdmission(
      admission.createdBy,
      admission.id,
    );
    expect(updatedAdmission.vteAndBleedingRiskAssessment.onAdmission).toEqual(
      expect.objectContaining({
        bleedingRiskAssessed: true,
        vteRiskAssessed: true,
      }),
    );
  });

  it('updateVTEAndBleedingRiskAssessment(): should update VTE and Bleeding Risk Assessment', async () => {
    const [hospital] = await createHospitals(manager);
    const [orgUser] = await createUsers(
      manager,
      1,
      hospital,
      undefined,
      undefined,
      UserType.OrganizationDoctor,
    );
    const [admission] = await createAdmissions(
      manager,
      1,
      orgUser.defaultProfile,
      undefined,
      undefined,
      hospital,
    );
    const record = await repo.getOneAdmission(admission.profile, admission.id);
    expect(record.vteAndBleedingRiskAssessment).toBeFalsy();

    const res = await repo.updateVTEAndBleedingRiskAssessment(
      orgUser.defaultProfile,
      admission.id,
      {
        field: 'furtherAssessment',
        changeFor: 'vteRiskAssessed',
        ref: '123',
        assessment: {
          bleedingRiskAssessed: true,
          vteRiskAssessed: true,
        },
      },
    );

    const updatedAdmission = await repo.getOneAdmission(
      admission.createdBy,
      admission.id,
    );
    expect(
      updatedAdmission.vteAndBleedingRiskAssessment.furtherAssessment[0],
    ).toEqual(
      expect.objectContaining({
        bleedingRiskAssessed: true,
        vteRiskAssessed: true,
        ref: '123',
      }),
    );
  });

  it('updateVTEAndBleedingRiskAssessment(): should fail if assessment type is furtherAssessment without ref', async () => {
    const [hospital] = await createHospitals(manager);
    const [orgUser] = await createUsers(
      manager,
      1,
      hospital,
      undefined,
      undefined,
      UserType.OrganizationDoctor,
    );
    const [admission] = await createAdmissions(
      manager,
      1,
      orgUser.defaultProfile,
      undefined,
      undefined,
      hospital,
    );
    const record = await repo.getOneAdmission(admission.profile, admission.id);
    expect(record.vteAndBleedingRiskAssessment).toBeFalsy();

    await expect(
      repo.updateVTEAndBleedingRiskAssessment(
        orgUser.defaultProfile,
        admission.id,
        {
          field: 'furtherAssessment',
          changeFor: 'bleedingRiskAssessed',
          assessment: {
            bleedingRiskAssessed: true,
            vteRiskAssessed: true,
          },
        },
      ),
    ).rejects.toThrow('Not Authorized To Modify This Record');
  });

  it('updateVTEAndBleedingRiskAssessment(): should fail if mutator is not in same facility as record', async () => {
    const [hospital] = await createHospitals(manager);
    const [orgUser] = await createUsers(
      manager,
      1,
      hospital,
      undefined,
      undefined,
      UserType.OrganizationDoctor,
    );
    const [admission] = await createAdmissions(manager, 1);
    const record = await repo.getOneAdmission(admission.profile, admission.id);
    expect(record.vteAndBleedingRiskAssessment).toBeFalsy();

    await expect(
      repo.updateVTEAndBleedingRiskAssessment(
        orgUser.defaultProfile,
        admission.id,
        {
          field: 'furtherAssessment',
          ref: '123',
          changeFor: 'bleedingRiskAssessed',
          assessment: {
            bleedingRiskAssessed: true,
            vteRiskAssessed: true,
          },
        },
      ),
    ).rejects.toThrow();
  });
});
