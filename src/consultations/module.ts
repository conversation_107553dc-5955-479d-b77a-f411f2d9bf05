import { forwardRef, Logger, Module } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SharedModule } from './../shared/module';
import { ConsultationModel } from './models/consultation.model';
import { ConsultationToInvestigation } from './models/consultation_investigation.model';
import { TreatmentPlanModel } from './models/treatment-plan.model';
import { CustomConsultationRepoMethods } from './repositories/consultation.repository';
import { CustomeConsultationToInvestigationMethods } from './repositories/consultation_investigation.repository';
import { ConsultationResolver } from './resolvers/consultation.resolver';
import { ConsultationService } from './services/consultation.service';
import { AdmissionModel } from '../admissions/models/admission.model';
import { CustomAdmissionRepoMethods } from '../admissions/repositories/admission.repository';
import { AllergyModel } from '../allergies/models/allergy.model';
import { CustomAllergyRepoMethods } from '../allergies/repositories/allergy.repository';
import { HmoClaimModule } from '../hmo-claims/hmo-claims.module';
import { MedicationModel } from '../medications/models/medication.model';
import { CustomMedicationMethods } from '../medications/repositories/medication.repository';
import { NotificationsService } from '../notifications/services/notifications.service';
import { SurgeryModel } from '../surgeries/models/surgery.model';
import { CustomSurgeryRepoMethods } from '../surgeries/repositories/surgery.repository';
import { VitalModel } from '../vitals/models/vital.model';
import { CustomVitalRepoMethods } from '../vitals/repositories/vital.repository';
import { OrganisationAppointmentModel } from '@clinify/appointments/models/organisation_appointment.model';
import { CustomOrganisationAppointmentRepoMethods } from '@clinify/appointments/repositories/organisation_appointment.repository';
import { AuthorizationModule } from '@clinify/authorization/authorization.module';
import { BillModule } from '@clinify/bills/bill.module';
import { TreatmentPlanResolver } from '@clinify/consultations/resolvers/treatment_plan.resolver';
import { extendModel } from '@clinify/database/extendModel';
import { HmoProfileModel } from '@clinify/hmo-profiles/models/hmo-profile.model';
import { CustomHmoProfileRepoMethods } from '@clinify/hmo-profiles/repositories/hmo-profile.repository';
import { NotificationsModel } from '@clinify/notifications/models/notifications.model';
import { NursingServiceModel } from '@clinify/nursing-services/models/nursing-services.model';
import { CustomNursingServiceMethods } from '@clinify/nursing-services/repositories/nursing-services.repository';
import { PreauthorizationDetailsModule } from '@clinify/preauthorization-details/preauthorization-details.module';
import { PricesModel } from '@clinify/prices/models/price.model';
import { CustomPriceRepoMethods } from '@clinify/prices/repositories/price.repository';
import { ProfileModel } from '@clinify/users/models/profile.model';
import PubSub, { PUB_SUB } from '@clinify/utils/subscriptions/pubSub';

@Module({
  imports: [
    forwardRef(() => SharedModule),
    AuthorizationModule,
    forwardRef(() => BillModule),
    TypeOrmModule.forFeature([
      ConsultationModel,
      ProfileModel,
      ConsultationToInvestigation,
      NotificationsModel,
      OrganisationAppointmentModel,
    ]),
    PreauthorizationDetailsModule,
    forwardRef(() => HmoClaimModule),
  ],
  providers: [
    extendModel(ConsultationModel, CustomConsultationRepoMethods),
    extendModel(
      ConsultationToInvestigation,
      CustomeConsultationToInvestigationMethods,
    ),
    extendModel(VitalModel, CustomVitalRepoMethods),
    extendModel(AllergyModel, CustomAllergyRepoMethods),
    extendModel(MedicationModel, CustomMedicationMethods),
    extendModel(SurgeryModel, CustomSurgeryRepoMethods),
    extendModel(AdmissionModel, CustomAdmissionRepoMethods),
    extendModel(NursingServiceModel, CustomNursingServiceMethods),
    extendModel(
      OrganisationAppointmentModel,
      CustomOrganisationAppointmentRepoMethods,
    ),
    extendModel(TreatmentPlanModel, {}),
    extendModel(HmoProfileModel, CustomHmoProfileRepoMethods),
    extendModel(PricesModel, CustomPriceRepoMethods),
    ConsultationService,
    ConsultationResolver,
    TreatmentPlanResolver,
    { provide: PUB_SUB, useFactory: () => PubSub },
    Logger,
    EventEmitter2,
    NotificationsService,
  ],
  exports: [ConsultationService],
})
export class ConsultationModule {}
